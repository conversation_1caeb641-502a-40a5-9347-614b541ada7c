# gRPC 拦截器集成架构设计

## 🎯 设计目标

基于架构合理性优先的原则，将gRPC上下文传递拦截器集成到现有的 `global_manager.go` 中，实现统一、简洁、高效的架构。

## 🏗️ 架构设计

### 核心组件

#### 1. InterceptorManager（拦截器管理器）
```go
type InterceptorManager struct {
    logger logiface.Logger
}
```

**职责**：
- 管理和配置gRPC客户端拦截器
- 提供统一的拦截器获取接口
- 确保拦截器执行顺序的正确性

**核心方法**：
- `GetClientInterceptors()`: 获取客户端拦截器列表
- `GetClientDialOptions()`: 获取连接选项

#### 2. GlobalManager（全局管理器）
```go
type GlobalManager struct {
    managers map[string]*ClientManager
    mutex    sync.RWMutex
    logger   logiface.Logger
    interceptorManager *InterceptorManager  // 新增
}
```

**职责**：
- 管理所有服务的客户端管理器
- 提供全局拦截器管理
- 统一的服务注册和获取接口

#### 3. ClientManager（客户端管理器）
**职责**：
- 管理单个服务的连接池
- 自动使用全局拦截器配置
- 处理服务发现和负载均衡

## 🔄 工作流程

### 1. 初始化阶段
```go
// 初始化全局管理器（自动初始化拦截器管理器）
InitGlobalManager(logger)
```

### 2. 服务注册阶段
```go
// 注册服务（自动使用全局拦截器）
SubscribeServiceGlobal("user-service", config)
```

### 3. 连接创建阶段
```go
// 获取连接（自动应用拦截器）
conn, err := GetConnectionGlobal(ctx, "user-service")
```

### 4. 客户端使用阶段
```go
// 获取客户端（上下文自动传递）
client, err := GetClientGlobal(ctx, "user-service", clientFactory)
```

## ✨ 架构优势

### 1. **统一管理**
- 拦截器配置集中在 `GlobalManager` 中
- 所有服务自动使用相同的拦截器配置
- 避免重复配置和代码分散

### 2. **简洁接口**
- 现有API无需修改
- 拦截器功能透明集成
- 向后兼容现有代码

### 3. **高效执行**
- 拦截器按正确顺序执行
- 链式拦截器优化性能
- 自动处理连接池管理

### 4. **易于扩展**
- 新增拦截器只需修改 `InterceptorManager`
- 支持动态配置拦截器
- 模块化设计便于维护

## 🚀 使用示例

### 基础使用
```go
// 1. 初始化
logger := logiface.GetLogger()
InitGlobalManager(logger)

// 2. 注册服务
config := &ClientManagerConfig{
    ServiceName: "user-service",
    // ... 其他配置
}
SubscribeServiceGlobal("user-service", config)

// 3. 使用服务（上下文自动传递）
ctx := createMockHTTPContext()
client, err := GetClientGlobal(ctx, "user-service", clientFactory)
```

### 高级使用
```go
// 获取全局拦截器管理器
globalManager := GetGlobalManager()
interceptorManager := globalManager.GetInterceptorManager()

// 获取拦截器连接选项
interceptorOptions := interceptorManager.GetClientDialOptions()
```

## 🔧 拦截器配置

### 默认拦截器
1. **上下文传递拦截器**（最先执行）
   - 从HTTP中间件设置的上下文中获取用户信息
   - 将用户和租户信息注入到gRPC元数据中

2. **访问日志拦截器**（记录请求）
   - 记录gRPC调用的详细信息
   - 过滤敏感字段（password, token）

### 拦截器执行顺序
```
1. 上下文传递拦截器 → 注入用户信息
2. 访问日志拦截器 → 记录请求日志
3. 业务逻辑执行
```

## 📊 性能优化

### 1. **连接池复用**
- 拦截器配置在连接创建时应用
- 连接池中的连接已包含拦截器
- 避免重复配置开销

### 2. **链式拦截器**
- 使用 `grpc.WithChainUnaryInterceptor`
- 减少拦截器调用开销
- 优化执行性能

### 3. **懒加载**
- 拦截器管理器在需要时创建
- 避免不必要的初始化开销

## 🔒 安全性

### 1. **上下文隔离**
- 每个请求的上下文独立
- 避免上下文信息泄露
- 支持多租户隔离

### 2. **敏感信息过滤**
- 自动过滤密码、令牌等敏感字段
- 保护用户隐私信息
- 符合安全审计要求

## 🎉 总结

通过将拦截器管理集成到 `GlobalManager` 中，我们实现了：

1. **架构合理性**：拦截器管理成为系统的核心组件
2. **代码简洁性**：现有API无需修改，功能透明集成
3. **性能高效性**：链式拦截器优化，连接池复用
4. **扩展灵活性**：模块化设计，易于维护和扩展
5. **向后兼容性**：现有代码无需修改即可使用新功能

这种设计既满足了您的需求（集成拦截器），又保持了架构的合理性和代码的简洁性。 