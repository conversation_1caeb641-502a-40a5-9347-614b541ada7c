package grpcmiddleware

import (
	"context"
	"strconv"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"gitee.com/heiyee/platforms/pkg/common"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// ServerContextInterceptor 服务端上下文拦截器
type ServerContextInterceptor struct {
	logger logiface.Logger
}

// NewServerContextInterceptor 创建服务端上下文拦截器
func NewServerContextInterceptor(logger logiface.Logger) *ServerContextInterceptor {
	return &ServerContextInterceptor{
		logger: logger,
	}
}

// UnaryServerInterceptor 一元调用服务端拦截器
func (i *ServerContextInterceptor) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 1. 提取上下文信息
		ctx = i.extractContext(ctx)

		// 2. 记录请求开始
		start := time.Now()
		requestID, _ := usercontext.GetRequestID(ctx)

		i.logger.Info(ctx, "gRPC server request started",
			logiface.String("method", info.FullMethod),
			logiface.String("request_id", requestID),
		)

		// 3. 执行处理
		resp, err := handler(ctx, req)

		// 4. 记录请求结果
		duration := time.Since(start)
		if err != nil {
			st, _ := status.FromError(err)
			i.logger.Error(ctx, "gRPC server request failed",
				logiface.String("method", info.FullMethod),
				logiface.String("request_id", requestID),
				logiface.Duration("duration", duration),
				logiface.Error(err),
				logiface.Int("code", int(st.Code())),
			)
		} else {
			i.logger.Info(ctx, "gRPC server request completed",
				logiface.String("method", info.FullMethod),
				logiface.String("request_id", requestID),
				logiface.Duration("duration", duration),
			)
		}

		return resp, err
	}
}

// StreamServerInterceptor 流式调用服务端拦截器
func (i *ServerContextInterceptor) StreamServerInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// 1. 提取上下文信息
		ctx := i.extractContext(ss.Context())

		// 2. 包装流以传递上下文
		wrappedStream := &contextServerStream{
			ServerStream: ss,
			ctx:          ctx,
		}

		// 3. 记录流请求开始
		requestID, _ := usercontext.GetRequestID(ctx)
		i.logger.Info(ctx, "gRPC server stream started",
			logiface.String("method", info.FullMethod),
			logiface.String("request_id", requestID),
		)

		// 4. 执行流处理
		err := handler(srv, wrappedStream)
		if err != nil {
			st, _ := status.FromError(err)
			i.logger.Error(ctx, "gRPC server stream failed",
				logiface.String("method", info.FullMethod),
				logiface.String("request_id", requestID),
				logiface.Error(err),
				logiface.Int("code", int(st.Code())),
			)
		}

		return err
	}
}

// extractContext 从gRPC元数据中提取上下文信息
func (i *ServerContextInterceptor) extractContext(ctx context.Context) context.Context {
	// 1. 获取元数据
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return ctx
	}

	// 2. 提取请求ID和追踪ID
	requestID := i.getMetadataValue(md, common.HeaderRequestID)
	traceID := i.getMetadataValue(md, "x-trace-id")

	// 3. 设置请求ID
	if requestID != "" {
		ctx = usercontext.SetRequestID(ctx, requestID)
	}

	// 4. 设置追踪ID
	if traceID != "" {
		ctx = usercontext.SetTraceID(ctx, traceID)
	}

	// 5. 提取用户信息
	userIDStr := i.getMetadataValue(md, common.HeaderUserID)
	username := i.getMetadataValue(md, "x-username")
	realName := i.getMetadataValue(md, "x-real-name")
	userEmail := i.getMetadataValue(md, "x-email")

	// 6. 提取应用信息
	tenantIDStr := i.getMetadataValue(md, common.HeaderTenantID)

	// 7. 设置用户信息
	if userIDStr != "" || username != "" || realName != "" || userEmail != "" {
		userInfo := &usercontext.UserInfo{}

		// 解析用户ID
		if userIDStr != "" {
			if userID, err := strconv.ParseInt(userIDStr, 10, 64); err == nil {
				userInfo.UserID = userID
			}
		}

		// 设置用户名
		if username != "" {
			userInfo.Username = username
		}

		// 设置真实姓名
		if realName != "" {
			userInfo.RealName = realName
		}

		// 设置用户邮箱
		if userEmail != "" {
			userInfo.Email = userEmail
		}
		// 设置用户信息到上下文
		ctx = usercontext.SetUserInfo(ctx, userInfo)
	}

	// 8. 设置应用信息
	if tenantIDStr != "" {
		appInfo := &usercontext.AppInfo{}

		// 解析租户ID
		if tenantIDStr != "" {
			if tenantID, err := strconv.ParseInt(tenantIDStr, 10, 64); err == nil {
				appInfo.TenantID = tenantID
			}
		}

		// 设置应用信息到上下文
		ctx = usercontext.SetAppInfo(ctx, appInfo)
	}

	return ctx
}

// getMetadataValue 安全获取元数据值
func (i *ServerContextInterceptor) getMetadataValue(md metadata.MD, key string) string {
	if values := md.Get(key); len(values) > 0 {
		return values[0]
	}
	return ""
}

// contextServerStream 包装流以传递上下文
type contextServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

// Context 返回包装的上下文
func (s *contextServerStream) Context() context.Context {
	return s.ctx
}
