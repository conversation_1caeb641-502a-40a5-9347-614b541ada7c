package grpcmiddleware

import (
	"gitee.com/heiyee/platforms/pkg/logiface"

	"google.golang.org/grpc"
)

// GRPCMiddlewareManager gRPC中间件管理器
type GRPCMiddlewareManager struct {
	clientInterceptor *ClientContextInterceptor
	serverInterceptor *ServerContextInterceptor
	logger            logiface.Logger
}

// NewGRPCMiddlewareManager 创建gRPC中间件管理器
func NewGRPCMiddlewareManager(logger logiface.Logger) *GRPCMiddlewareManager {
	return &GRPCMiddlewareManager{
		clientInterceptor: NewClientContextInterceptor(logger),
		serverInterceptor: NewServerContextInterceptor(logger),
		logger:            logger,
	}
}

// GetClientInterceptors 获取客户端拦截器
func (m *GRPCMiddlewareManager) GetClientInterceptors() []grpc.UnaryClientInterceptor {
	return []grpc.UnaryClientInterceptor{
		m.clientInterceptor.UnaryClientInterceptor(),
	}
}

// GetStreamClientInterceptors 获取流式客户端拦截器
func (m *GRPCMiddlewareManager) GetStreamClientInterceptors() []grpc.StreamClientInterceptor {
	return []grpc.StreamClientInterceptor{
		m.clientInterceptor.StreamClientInterceptor(),
	}
}

// GetServerInterceptors 获取服务端拦截器
func (m *GRPCMiddlewareManager) GetServerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{
		m.serverInterceptor.UnaryServerInterceptor(),
	}
}

// GetStreamServerInterceptors 获取流式服务端拦截器
func (m *GRPCMiddlewareManager) GetStreamServerInterceptors() []grpc.StreamServerInterceptor {
	return []grpc.StreamServerInterceptor{
		m.serverInterceptor.StreamServerInterceptor(),
	}
}

// WithClientInterceptors 为客户端连接添加拦截器
func (m *GRPCMiddlewareManager) WithClientInterceptors(opts ...grpc.DialOption) []grpc.DialOption {
	interceptors := m.GetClientInterceptors()
	streamInterceptors := m.GetStreamClientInterceptors()

	// 添加一元调用拦截器
	opts = append(opts, grpc.WithUnaryInterceptor(interceptors[0]))

	// 添加流式调用拦截器
	opts = append(opts, grpc.WithStreamInterceptor(streamInterceptors[0]))

	return opts
}

// WithServerInterceptors 为服务端添加拦截器
func (m *GRPCMiddlewareManager) WithServerInterceptors(opts ...grpc.ServerOption) []grpc.ServerOption {
	interceptors := m.GetServerInterceptors()
	streamInterceptors := m.GetStreamServerInterceptors()

	// 添加一元调用拦截器
	opts = append(opts, grpc.UnaryInterceptor(interceptors[0]))

	// 添加流式调用拦截器
	opts = append(opts, grpc.StreamInterceptor(streamInterceptors[0]))

	return opts
}
