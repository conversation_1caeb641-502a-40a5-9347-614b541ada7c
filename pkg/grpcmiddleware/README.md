# gRPC 服务间上下文传递实现

## 概述

本包提供了完整的gRPC服务间上下文传递解决方案，支持在微服务架构中自动传递用户上下文、请求ID、链路追踪等信息。

## 核心特性

- **自动上下文传递**：客户端和服务端拦截器自动处理上下文信息
- **统一元数据格式**：使用标准化的gRPC元数据字段
- **链路追踪支持**：集成OpenTelemetry追踪ID和Span ID
- **结构化日志**：自动记录请求日志和错误信息
- **流式调用支持**：支持一元调用和流式调用

## 架构设计

### 上下文信息结构

```go
type UserContext struct {
    RequestID string // 请求ID
    UserID    int64  // 用户ID
    TenantID  int64  // 租户ID
    TraceID   string // 链路追踪ID
    SpanID    string // Span ID
    UserEmail string // 用户邮箱
    UserRole  string // 用户角色
}
```

### 元数据字段映射

| 字段名 | 类型 | 说明 |
|--------|------|------|
| X-Request-Id | string | 请求唯一标识 |
| X-User-Id | int64 | 用户ID |
| X-Tenant-Id | int64 | 租户ID |
| X-Tenant-Code | string | 租户代码 |
| x-trace-id | string | 链路追踪ID |
| x-username | string | 用户名 |
| x-real-name | string | 真实姓名 |
| x-email | string | 用户邮箱 |
| x-user-role | string | 用户角色 |
| x-tenant-name | string | 租户名称 |
| x-timestamp | int64 | 请求时间戳 |

## 使用方法

### 1. 服务端设置

```go
package main

import (
    "google.golang.org/grpc"
    "platforms/pkg/grpcmiddleware"
    "platforms/pkg/logiface"
)

func main() {
    // 创建日志器
    logger := logiface.NewLogger("user-service")
    
    // 创建中间件管理器
    middlewareManager := grpcmiddleware.NewGRPCMiddlewareManager(logger)
    
    // 创建gRPC服务器
    server := grpc.NewServer(
        middlewareManager.WithServerInterceptors()...,
    )
    
    // 注册服务
    // pb.RegisterUserServiceServer(server, &userService{})
    
    // 启动服务器
    // ...
}
```

### 2. 客户端设置

```go
package main

import (
    "context"
    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
    "platforms/pkg/grpcmiddleware"
    "platforms/pkg/usercontext"
)

func main() {
    // 创建中间件管理器
    middlewareManager := grpcmiddleware.NewGRPCMiddlewareManager(logger)
    
    // 创建客户端连接
    conn, err := grpc.Dial(
        "localhost:50051",
        append(
            []grpc.DialOption{
                grpc.WithTransportCredentials(insecure.NewCredentials()),
            },
            middlewareManager.WithClientInterceptors()...,
        )...,
    )
    if err != nil {
        log.Fatalf("Failed to connect: %v", err)
    }
    defer conn.Close()
    
    // 创建客户端
    client := pb.NewUserServiceClient(conn)
    
    // 创建带有用户上下文的请求
    ctx := createUserContext()
    
    // 调用服务
    resp, err := client.GetUser(ctx, &pb.GetUserRequest{UserId: 123})
    if err != nil {
        log.Printf("Failed to get user: %v", err)
        return
    }
}

func createUserContext() context.Context {
    userCtx := &usercontext.UserContext{
        RequestID: "req-12345",
        UserID:    123,
        TenantID:  456,
        TraceID:   "trace-67890",
        SpanID:    "span-11111",
        UserEmail: "<EMAIL>",
        UserRole:  "admin",
    }
    
    return usercontext.WithContext(context.Background(), userCtx)
}
```

### 3. 服务实现中使用上下文

```go
type UserService struct {
    pb.UnimplementedUserServiceServer
}

func (s *UserService) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserResponse, error) {
    // 从上下文中获取用户信息
    userInfo, hasUser := usercontext.GetUserInfo(ctx)
    tenantInfo, hasTenant := usercontext.GetTenantInfo(ctx)
    requestID, hasRequestID := usercontext.GetRequestID(ctx)
    
    if hasUser {
        log.Printf("Processing request for user: %d (%s), tenant: %d (%s)", 
            userInfo.UserID, userInfo.RealName,
            userInfo.TenantID, userInfo.TenantName)
    }
    
    if hasTenant {
        log.Printf("Tenant info: ID=%d, Code=%s, Name=%s",
            tenantInfo.TenantID, tenantInfo.TenantCode, tenantInfo.TenantName)
    }
    
    if hasRequestID {
        log.Printf("Request ID: %s", requestID)
    }
    
    // 业务逻辑处理
    // ...
    
    return &pb.GetUserResponse{
        User: &pb.User{
            Id:   req.UserId,
            Name: "John Doe",
        },
    }, nil
}
```

## 中间件组件

### 1. ClientContextInterceptor

客户端拦截器负责：
- 从上下文提取用户信息
- 将信息注入到gRPC元数据
- 记录请求日志
- 处理错误响应

### 2. ServerContextInterceptor

服务端拦截器负责：
- 从gRPC元数据提取上下文信息
- 设置到请求上下文中
- 记录请求日志
- 处理流式调用

### 3. GRPCMiddlewareManager

中间件管理器提供：
- 统一的拦截器管理
- 便捷的配置方法
- 支持链式调用

## 最佳实践

### 1. 错误处理

```go
// 在服务实现中处理错误
func (s *Service) Method(ctx context.Context, req *pb.Request) (*pb.Response, error) {
    userCtx := usercontext.FromContext(ctx)
    
    if err := validateRequest(req); err != nil {
        // 记录错误日志
        s.logger.Error(ctx, "Request validation failed",
            "user_id", userCtx.UserID,
            "error", err.Error(),
        )
        return nil, status.Error(codes.InvalidArgument, "Invalid request")
    }
    
    return &pb.Response{}, nil
}
```

### 2. 链路追踪

```go
// 在服务中创建子Span
func (s *Service) Method(ctx context.Context, req *pb.Request) (*pb.Response, error) {
    tracer := otel.Tracer("user-service")
    
    ctx, span := tracer.Start(ctx, "GetUser")
    defer span.End()
    
    // 添加Span属性
    span.SetAttributes(
        attribute.Int64("user.id", req.UserId),
        attribute.String("request.id", usercontext.GetRequestID(ctx)),
    )
    
    // 业务逻辑
    return &pb.Response{}, nil
}
```

### 3. 性能监控

```go
// 记录性能指标
func (s *Service) Method(ctx context.Context, req *pb.Request) (*pb.Response, error) {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        s.metrics.RecordLatency("get_user", duration)
    }()
    
    // 业务逻辑
    return &pb.Response{}, nil
}
```

## 配置选项

### 1. 日志级别

```go
logger := logiface.NewLogger("user-service", 
    logiface.WithLevel("debug"),
    logiface.WithFormat("json"),
)
```

### 2. 中间件配置

```go
middlewareManager := grpcmiddleware.NewGRPCMiddlewareManager(logger)

// 可以添加更多拦截器
serverOpts := append(
    middlewareManager.WithServerInterceptors(),
    grpc.ChainUnaryInterceptor(
        // 其他拦截器
    ),
)
```

## 故障排除

### 1. 上下文信息丢失

检查：
- 客户端是否正确设置了用户上下文
- 服务端是否正确注册了拦截器
- 元数据字段名是否正确

### 2. 日志不显示

检查：
- 日志级别设置
- 日志格式配置
- 日志输出目标

### 3. 性能问题

检查：
- 拦截器是否过于复杂
- 日志记录频率
- 元数据大小

## 扩展功能

### 1. 自定义元数据

```go
// 在客户端拦截器中添加自定义字段
func (i *ClientContextInterceptor) injectContext(ctx context.Context) context.Context {
    userCtx := usercontext.FromContext(ctx)
    if userCtx == nil {
        return ctx
    }
    
    md := metadata.New(map[string]string{
        "x-request-id": userCtx.RequestID,
        "x-custom-field": "custom-value", // 自定义字段
    })
    
    return metadata.NewOutgoingContext(ctx, md)
}
```

### 2. 条件拦截器

```go
// 根据条件决定是否应用拦截器
func (i *ClientContextInterceptor) UnaryClientInterceptor() grpc.UnaryClientInterceptor {
    return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
        // 检查是否需要传递上下文
        if shouldPropagateContext(ctx, method) {
            ctx = i.injectContext(ctx)
        }
        
        return invoker(ctx, method, req, reply, cc, opts...)
    }
}
```

## 统一业务代码使用

### 🎯 **核心目标**

无论通过HTTP还是gRPC调用，业务代码获取用户和租户信息的方式完全一致，无需区分调用方式。

### 📝 **统一的使用方式**

```go
// 业务服务 - 无需区分HTTP还是gRPC
type BusinessService struct {
    // 业务依赖...
}

// 获取用户资料 - 统一接口
func (s *BusinessService) GetUserProfile(ctx context.Context, userID int64) (*UserProfile, error) {
    // 统一的用户信息获取方式 - 无需区分HTTP还是gRPC
    userInfo, hasUser := usercontext.GetUserInfo(ctx)
    tenantInfo, hasTenant := usercontext.GetTenantInfo(ctx)
    requestID, _ := usercontext.GetRequestID(ctx)

    if !hasUser {
        return nil, fmt.Errorf("user not authenticated")
    }

    if !hasTenant {
        return nil, fmt.Errorf("tenant information not found")
    }

    // 业务逻辑：检查用户是否属于当前租户
    if userInfo.TenantID != tenantInfo.TenantID {
        return nil, fmt.Errorf("user does not belong to current tenant")
    }

    // 构建用户资料
    profile := &UserProfile{
        UserID:      userInfo.UserID,
        Username:    userInfo.Username,
        RealName:    userInfo.RealName,
        Email:       userInfo.Email,
        TenantID:    tenantInfo.TenantID,
        TenantCode:  tenantInfo.TenantCode,
        TenantName:  tenantInfo.TenantName,
        Roles:       userInfo.Roles,
        RequestID:   requestID,
    }

    return profile, nil
}

// 创建资源 - 统一接口
func (s *BusinessService) CreateResource(ctx context.Context, resourceName string) (*Resource, error) {
    // 统一的用户信息获取方式
    userInfo, hasUser := usercontext.GetUserInfo(ctx)
    tenantInfo, hasTenant := usercontext.GetTenantInfo(ctx)

    if !hasUser || !hasTenant {
        return nil, fmt.Errorf("authentication required")
    }

    // 检查用户权限
    if !usercontext.HasRole(ctx, "admin") && !usercontext.HasRole(ctx, "user") {
        return nil, fmt.Errorf("insufficient permissions")
    }

    // 业务逻辑：创建资源
    resource := &Resource{
        ID:         generateResourceID(),
        Name:       resourceName,
        CreatedBy:  userInfo.UserID,
        TenantID:   tenantInfo.TenantID,
        TenantCode: tenantInfo.TenantCode,
        Status:     "active",
    }

    return resource, nil
}
```

### 🔄 **HTTP和gRPC的统一调用**

#### HTTP调用
```go
// HTTP处理器
func (h *Handler) GetUserProfile(c *gin.Context) {
    // 通过HTTP中间件，上下文已经包含用户信息
    ctx := c.Request.Context()
    
    // 业务代码完全相同
    profile, err := h.businessService.GetUserProfile(ctx, userID)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, profile)
}
```

#### gRPC调用
```go
// gRPC服务实现
func (s *UserService) GetUserProfile(ctx context.Context, req *pb.GetUserProfileRequest) (*pb.GetUserProfileResponse, error) {
    // 通过gRPC拦截器，上下文已经包含用户信息
    // 业务代码完全相同
    profile, err := s.businessService.GetUserProfile(ctx, req.UserId)
    if err != nil {
        return nil, status.Error(codes.InvalidArgument, err.Error())
    }
    
    return &pb.GetUserProfileResponse{
        Profile: convertToProto(profile),
    }, nil
}
```

### ✨ **核心优势**

1. **完全统一**：业务代码无需区分HTTP还是gRPC调用
2. **透明传递**：用户信息通过中间件自动传递
3. **一致体验**：相同的API调用方式
4. **易于维护**：业务逻辑集中，无需重复实现
5. **权限统一**：使用相同的权限检查机制

### 🎉 **总结**

本gRPC上下文传递实现提供了：

1. **完整的上下文传递机制**：支持用户信息、请求ID、链路追踪等
2. **统一的中间件管理**：简化配置和使用
3. **丰富的日志记录**：自动记录请求和错误信息
4. **灵活的扩展性**：支持自定义元数据和条件拦截
5. **最佳实践指导**：提供错误处理、性能监控等示例
6. **统一业务代码**：HTTP和gRPC使用完全一致的业务逻辑

通过使用这套中间件，可以确保在微服务架构中实现透明、可靠的上下文传递，同时保持业务代码的统一性。 