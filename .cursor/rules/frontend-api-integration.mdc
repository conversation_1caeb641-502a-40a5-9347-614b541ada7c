---
description: frontend/api-integration
globs: frontend/**/*.ts,frontend/**/*.tsx
---

## Frontend API Integration

- Centralize HTTP calls in the service layer under `frontend/src/services/`.
- Use a single API client with interceptors for auth headers, request ID correlation, and error normalization.
- Determine success by the protocol `code` field; display server `message` for non-success codes.
- Do not invent HTTP status–based logic; rely on the unified response contract.
- Handle auth token refresh and 401 globally in the client; redirect to login on hard failures.

References

- Services: [frontend/src/services](mdc:frontend/src/services)
- Auth context: [frontend/src/contexts/AuthContext.tsx](mdc:frontend/src/contexts/AuthContext.tsx)
- Proxy/dev setup: [frontend/setupProxy.js](mdc:frontend/setupProxy.js)

