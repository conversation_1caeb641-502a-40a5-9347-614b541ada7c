---
alwaysApply: true
---

## Project Structure and Conventions

- Services follow a Go microservices, Clean Architecture, and DDD layout. Key entry points: 
  - [users/cmd/main.go](mdc:users/cmd/main.go)
  - [email/cmd/main.go](mdc:email/cmd/main.go)
  - [auto-coding-system/cmd/main.go](mdc:auto-coding-system/cmd/main.go)
  - Shared libs in [pkg/](mdc:pkg/README.md) and [common/](mdc:common/README.md)

- Layering (Clean Architecture):
  - Interfaces/transport (HTTP/gRPC/web): `interfaces` or `api`
  - Application/use-cases: `internal/application`
  - Domain models/repositories: `internal/domain`
  - Infrastructure implementations: `internal/infrastructure`

- API endpoints:
  - Only GET and POST are used; avoid path parameters in routes.
  - Do not add new endpoints unless their necessity is explicitly analyzed and justified.
  - Use the unified response spec; do not leak internal errors.

- Observability & context:
  - Use OpenTelemetry tracing and propagate `context.Context` across boundaries.
  - Correlate logs with trace and request IDs via middleware.

- Dependency injection:
  - Use constructor-based DI; do not perform runtime `nil` checks on injected deps.

- Data access:
  - Use GORM without automatic preloading; control association queries manually to avoid N+1.

Useful references:

- Unified response helpers: [pkg/common/response/response.go](mdc:pkg/common/response/response.go)
- HTTP middleware: [pkg/httpmiddleware/common_middleware.go](mdc:pkg/httpmiddleware/common_middleware.go)
- OpenTelemetry setup: [pkg/otel/otel.go](mdc:pkg/otel/otel.go), [common/tracing/otel_tracer.go](mdc:common/tracing/otel_tracer.go)
- User context utilities: [pkg/usercontext/usercontext.go](mdc:pkg/usercontext/usercontext.go)
- GORM DB init: [pkg/db/database.go](mdc:pkg/db/database.go)
- DI container example: [users/internal/infrastructure/container/dependency_container.go](mdc:users/internal/infrastructure/container/dependency_container.go)

