---
description: frontend/component-patterns
globs: frontend/**/*.ts,frontend/**/*.tsx
---

## Frontend Component Patterns

- Follow the List Page design spec: stats area, search/filter (real-time), and table area using a unified Card style with dark mode support.
- Prefer composition, small focused components, and hooks. Use `React.memo`, `useMemo`, `useCallback` appropriately.
- Consolidate related table info into fewer columns; use tags and custom renderers for clarity.
- Use text buttons with tooltips for actions; ensure responsive layout.

References

- User management page: [frontend/src/pages/user/UserPage.tsx](mdc:frontend/src/pages/user/UserPage.tsx)
- Role management page: [frontend/src/pages/role/RolePage.tsx](mdc:frontend/src/pages/role/RolePage.tsx)
- API management page: [frontend/src/pages/api-management/ApiManagementPage.tsx](mdc:frontend/src/pages/api-management/ApiManagementPage.tsx)

