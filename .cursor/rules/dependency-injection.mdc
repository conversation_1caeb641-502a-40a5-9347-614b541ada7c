---
globs: *.go
---

## Dependency Injection Rules

- Inject all required dependencies via constructors; do not use global state.
- Prohibit runtime `nil` checks on injected clients (fail fast at startup instead).
- Keep dependencies immutable after construction.
- Interact via interfaces for testability and flexibility.

References

- DI container example: [users/internal/infrastructure/container/dependency_container.go](mdc:users/internal/infrastructure/container/dependency_container.go)
- User context middleware: [pkg/usercontext/usercontext.go](mdc:pkg/usercontext/usercontext.go)

