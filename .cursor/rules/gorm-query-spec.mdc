---
globs: *.go
---

## GORM Query Specification

- Do not use automatic association preload (no `Preload(...)` by default).
- Query base entities first; perform association lookups explicitly as needed.
- Avoid N+1 by batching IDs and fetching related data in bulk.
- Keep domain entities free of GORM association tags; define relations only in models if required.
- Set timeouts via `context.Context` and monitor query performance.

References

- DB initialization: [pkg/db/database.go](mdc:pkg/db/database.go)

