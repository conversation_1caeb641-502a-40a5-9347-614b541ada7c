---
globs: *.go
---

## REST API Unified Response

- All HTTP handlers must return the unified response structure. Do not return raw/internal error details.
- Use helpers in [pkg/common/response/response.go](mdc:pkg/common/response/response.go).

```go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    *Meta       `json:"meta,omitempty"`
}

type Meta struct {
    RequestID  string                 `json:"request_id,omitempty"`
    Timestamp  int64                  `json:"timestamp"`
    Pagination *Pagination            `json:"pagination,omitempty"`
    Extra      map[string]interface{} `json:"extra,omitempty"`
}
```

Usage

- Success: `response.Success(c, data)`, `response.Created(c, data)`, `response.Paginated(...)`.
- Errors: `response.ValidationError(c, errs)`, `response.Unauthorized(c, msg)`, `response.Forbidden(c, msg)`, `response.NotFound(c, entity)`, `response.TooManyRequests(c, seconds)`, `response.InternalError(c, err)`.
- Log full internal errors, but only return safe messages. Correlate with request/trace IDs.

Middleware

- Ensure access log and tracing middleware are enabled: 
  - [pkg/httpmiddleware/common_middleware.go](mdc:pkg/httpmiddleware/common_middleware.go)
  - [pkg/httpmiddleware/otel.go](mdc:pkg/httpmiddleware/otel.go)

