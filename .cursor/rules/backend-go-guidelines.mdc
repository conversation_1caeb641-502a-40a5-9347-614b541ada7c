---
globs: *.go
---

## Backend Go Guidelines

- Architecture
  - Adopt Clean Architecture with clear separation: interfaces → application → domain → infrastructure.
  - Prefer small, focused interfaces and explicit dependency injection via constructors.
  - Keep domain models free from framework details; repositories are defined by interfaces in domain and implemented in infrastructure.

- Error handling
  - Always wrap errors with context, e.g., `fmt.Errorf("context: %w", err)`.
  - Never expose internal errors in HTTP responses; use unified response helpers.
  - Log with structured fields and include request/trace IDs.

- Concurrency & context
  - Always accept a `context.Context`; respect deadlines/cancellations.
  - Guard shared state with channels or sync primitives; avoid goroutine leaks.

- DI & lifecycle
  - Inject all required dependencies in constructors; fail fast on missing deps.
  - Do not perform runtime `nil` checks on injected clients/services.

- Data access
  - Avoid GORM automatic preloading; control association queries manually.
  - Batch-load where possible to avoid N+1 queries.

- Testing
  - Use table-driven tests, parallelize where safe.
  - Mock external interfaces; ensure coverage for exported functions.

- Observability
  - Instrument HTTP/gRPC/DB with OpenTelemetry.
  - Use `otel.Tracer` and `otel.Meter`; propagate context across boundaries.

Key files

- Responses: [pkg/common/response/response.go](mdc:pkg/common/response/response.go)
- HTTP middleware: [pkg/httpmiddleware/otel.go](mdc:pkg/httpmiddleware/otel.go)
- Tracing setup: [pkg/otel/otel.go](mdc:pkg/otel/otel.go)
- DB: [pkg/db/database.go](mdc:pkg/db/database.go)
- Logging: [pkg/logiface](mdc:pkg/logiface/README.md)

