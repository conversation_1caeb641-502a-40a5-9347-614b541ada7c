---
description: frontend/quick-reference
globs: frontend/**/*.ts,frontend/**/*.tsx
---

## Frontend Quick Reference

- Theme and styles: prefer shared tokens and dark mode compatibility.
- Real-time search resets pagination to page 1.
- Display totals and current range on pagination; support page size changes.
- Use the unified API service client and code-based response handling.

References

- Theme tokens: [frontend/src/styles/theme.ts](mdc:frontend/src/styles/theme.ts)
- API services: [frontend/src/services](mdc:frontend/src/services)

