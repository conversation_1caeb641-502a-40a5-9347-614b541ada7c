---
description: frontend/authentication
globs: frontend/**/*.ts,frontend/**/*.tsx
---

## Frontend Authentication & Authorization

- Use `AuthContext` to store auth state, tokens, and user info; avoid duplicating auth state.
- Read tokens from secure storage and inject as headers in the API client.
- Guard routes at the router level; handle 401/403 centrally and show friendly messages.
- Respect role/permission checks via context rather than ad-hoc inline checks.

References

- Auth context: [frontend/src/contexts/AuthContext.tsx](mdc:frontend/src/contexts/AuthContext.tsx)
- Routes: [frontend/src/routes/index.tsx](mdc:frontend/src/routes/index.tsx)

