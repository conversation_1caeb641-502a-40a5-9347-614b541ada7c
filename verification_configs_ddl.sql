-- VerificationConfig 统一验证配置表 DDL
-- 创建时间: 2025-07-27
-- 说明: 支持静态配置和动态配置两种模式的统一验证配置管理

DROP TABLE IF EXISTS `verification_configs`;

CREATE TABLE `verification_configs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '配置ID，主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  
  -- 配置模式
  `config_mode` ENUM('static', 'dynamic') NOT NULL DEFAULT 'static' COMMENT '配置模式：static-静态配置，dynamic-动态配置',
  
  -- 静态配置标识字段
  `purpose` TINYINT NULL COMMENT '验证用途：1-注册激活，2-密码重置，3-邮箱变更，4-手机变更，5-登录验证，6-MFA验证（仅静态配置使用）',
  `target_type` TINYINT NOT NULL COMMENT '目标类型：1-邮箱，2-手机号，3-MFA',
  
  -- 动态策略标识字段
  `business_scene` VARCHAR(100) NULL COMMENT '业务场景标识（仅动态配置使用）',
  `judgment_dimension` VARCHAR(100) NULL COMMENT '判定维度（仅动态配置使用）',
  `condition_expr` TEXT NULL COMMENT '条件表达式（仅动态配置使用）',
  
  -- 验证配置参数
  `token_type` TINYINT NOT NULL COMMENT '令牌类型：1-链接，2-验证码',
  `token_length` INT NOT NULL DEFAULT 6 COMMENT '令牌长度',
  `expire_minutes` INT NOT NULL DEFAULT 30 COMMENT '过期时间（分钟）',
  `max_attempts` INT NOT NULL DEFAULT 5 COMMENT '最大尝试次数',
  
  -- 频率限制配置
  `rate_limit_per_minute` INT NOT NULL DEFAULT 3 COMMENT '每分钟发送限制',
  `rate_limit_per_hour` INT NOT NULL DEFAULT 10 COMMENT '每小时发送限制',
  `rate_limit_per_day` INT NOT NULL DEFAULT 50 COMMENT '每日发送限制',
  
  -- 模板配置
  `template_code` VARCHAR(100) NOT NULL COMMENT '模板代码',
  
  -- 策略控制参数（主要用于动态配置）
  `require_verification` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否需要验证',
  `verification_level` INT NOT NULL DEFAULT 1 COMMENT '验证级别：1-5，数字越大要求越严格',
  `priority` INT NOT NULL DEFAULT 0 COMMENT '优先级：数字越大优先级越高',
  
  -- 状态和描述
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
  `description` TEXT NULL COMMENT '配置描述',
  `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
  
  -- 审计字段
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` BIGINT NULL COMMENT '创建人ID',
  `updated_by` BIGINT NULL COMMENT '更新人ID',
  
  PRIMARY KEY (`id`),
  
  -- 索引设计
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_config_mode` (`config_mode`),
  INDEX `idx_tenant_purpose_target` (`tenant_id`, `purpose`, `target_type`), -- 静态配置查找索引
  INDEX `idx_tenant_scene` (`tenant_id`, `business_scene`), -- 动态配置查找索引
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_priority` (`priority`),
  INDEX `idx_deleted_at` (`deleted_at`),
  INDEX `idx_created_at` (`created_at`),
  
  -- 唯一约束
  UNIQUE KEY `uk_tenant_static_config` (`tenant_id`, `purpose`, `target_type`, `deleted_at`), -- 静态配置唯一性约束
  UNIQUE KEY `uk_tenant_dynamic_config` (`tenant_id`, `business_scene`, `judgment_dimension`, `deleted_at`) -- 动态配置唯一性约束
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证配置统一管理表';

-- 初始化默认数据（系统级配置，tenant_id = 0）
INSERT INTO `verification_configs` (
  `tenant_id`, `config_mode`, `purpose`, `target_type`, `token_type`, `token_length`, 
  `expire_minutes`, `max_attempts`, `rate_limit_per_minute`, `rate_limit_per_hour`, 
  `rate_limit_per_day`, `template_code`, `priority`, `description`
) VALUES 
-- 邮箱验证默认配置
(0, 'static', 1, 1, 1, 32, 30, 3, 1, 5, 20, 'email_register_link', 10, '邮箱注册激活默认配置'),
(0, 'static', 2, 1, 1, 32, 15, 3, 1, 3, 10, 'email_password_reset', 10, '邮箱密码重置默认配置'),
(0, 'static', 3, 1, 1, 32, 30, 3, 1, 3, 10, 'email_change_verify', 10, '邮箱变更验证默认配置'),
(0, 'static', 5, 1, 2, 6, 5, 3, 1, 10, 50, 'email_login_code', 10, '邮箱登录验证默认配置'),

-- 手机号验证默认配置
(0, 'static', 1, 2, 2, 6, 5, 3, 1, 5, 20, 'sms_register_code', 10, '手机号注册激活默认配置'),
(0, 'static', 2, 2, 2, 6, 5, 3, 1, 3, 10, 'sms_password_reset', 10, '手机号密码重置默认配置'),
(0, 'static', 4, 2, 2, 6, 5, 3, 1, 3, 10, 'sms_phone_change', 10, '手机号变更验证默认配置'),
(0, 'static', 5, 2, 2, 6, 5, 3, 1, 10, 50, 'sms_login_code', 10, '手机号登录验证默认配置'),

-- MFA验证默认配置
(0, 'static', 6, 3, 2, 6, 5, 3, 1, 20, 100, 'mfa_verify_code', 10, 'MFA验证默认配置');

-- 示例动态配置
INSERT INTO `verification_configs` (
  `tenant_id`, `config_mode`, `business_scene`, `judgment_dimension`, `condition_expr`,
  `target_type`, `token_type`, `token_length`, `expire_minutes`, `max_attempts`,
  `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `template_code`,
  `verification_level`, `require_verification`, `priority`, `description`
) VALUES 
(0, 'dynamic', 'high_risk_login', 'user_risk_score', 'user.risk_score > 80', 1, 2, 6, 5, 3, 1, 5, 20, 'email_high_risk_verify', 3, TRUE, 20, '高风险登录动态验证配置'),
(0, 'dynamic', 'suspicious_location', 'login_location', 'location.is_new_location == true', 2, 2, 6, 5, 3, 1, 3, 15, 'sms_location_verify', 2, TRUE, 15, '可疑位置登录动态验证配置'),
(0, 'dynamic', 'large_amount_operation', 'operation_amount', 'operation.amount > 10000', 1, 1, 32, 10, 2, 1, 2, 5, 'email_amount_verify', 4, TRUE, 30, '大额操作动态验证配置');

-- 添加触发器以确保数据一致性
DELIMITER //

CREATE TRIGGER `verification_configs_before_insert` 
BEFORE INSERT ON `verification_configs`
FOR EACH ROW
BEGIN
  -- 验证静态配置必须有purpose，动态配置必须有business_scene
  IF NEW.config_mode = 'static' AND NEW.purpose IS NULL THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Static config must have purpose';
  END IF;
  
  IF NEW.config_mode = 'dynamic' AND (NEW.business_scene IS NULL OR NEW.judgment_dimension IS NULL OR NEW.condition_expr IS NULL) THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Dynamic config must have business_scene, judgment_dimension and condition_expr';
  END IF;
  
  -- 静态配置不应该有动态字段
  IF NEW.config_mode = 'static' AND (NEW.business_scene IS NOT NULL OR NEW.judgment_dimension IS NOT NULL OR NEW.condition_expr IS NOT NULL) THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Static config should not have dynamic fields';
  END IF;
  
  -- 动态配置不应该有purpose字段
  IF NEW.config_mode = 'dynamic' AND NEW.purpose IS NOT NULL THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Dynamic config should not have purpose field';
  END IF;
END//

CREATE TRIGGER `verification_configs_before_update` 
BEFORE UPDATE ON `verification_configs`
FOR EACH ROW
BEGIN
  -- 验证静态配置必须有purpose，动态配置必须有business_scene
  IF NEW.config_mode = 'static' AND NEW.purpose IS NULL THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Static config must have purpose';
  END IF;
  
  IF NEW.config_mode = 'dynamic' AND (NEW.business_scene IS NULL OR NEW.judgment_dimension IS NULL OR NEW.condition_expr IS NULL) THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Dynamic config must have business_scene, judgment_dimension and condition_expr';
  END IF;
  
  -- 静态配置不应该有动态字段
  IF NEW.config_mode = 'static' AND (NEW.business_scene IS NOT NULL OR NEW.judgment_dimension IS NOT NULL OR NEW.condition_expr IS NOT NULL) THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Static config should not have dynamic fields';
  END IF;
  
  -- 动态配置不应该有purpose字段
  IF NEW.config_mode = 'dynamic' AND NEW.purpose IS NOT NULL THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Dynamic config should not have purpose field';
  END IF;
END//

DELIMITER ;

-- 常用查询示例和说明

/*
1. 获取租户的静态配置
SELECT * FROM verification_configs 
WHERE tenant_id = ? AND config_mode = 'static' AND purpose = ? AND target_type = ? AND deleted_at IS NULL AND is_active = TRUE
ORDER BY priority DESC, id ASC
LIMIT 1;

2. 获取租户的动态配置
SELECT * FROM verification_configs 
WHERE tenant_id = ? AND config_mode = 'dynamic' AND business_scene = ? AND deleted_at IS NULL AND is_active = TRUE
ORDER BY priority DESC, id ASC;

3. 获取系统默认配置（tenant_id = 0）
SELECT * FROM verification_configs 
WHERE tenant_id = 0 AND config_mode = 'static' AND purpose = ? AND target_type = ? AND deleted_at IS NULL AND is_active = TRUE
ORDER BY priority DESC, id ASC
LIMIT 1;

4. 分页查询租户配置列表
SELECT * FROM verification_configs 
WHERE tenant_id = ? AND deleted_at IS NULL 
ORDER BY config_mode, priority DESC, created_at DESC
LIMIT ? OFFSET ?;

5. 统计租户配置数量
SELECT 
  config_mode,
  COUNT(*) as total,
  SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_count
FROM verification_configs 
WHERE tenant_id = ? AND deleted_at IS NULL 
GROUP BY config_mode;
*/

-- 性能优化建议：
-- 1. 定期清理软删除的数据（deleted_at IS NOT NULL）
-- 2. 根据实际查询模式调整索引
-- 3. 考虑对于大租户数据进行分区
-- 4. 监控慢查询并优化

-- 数据一致性检查查询：
/*
-- 检查静态配置数据一致性
SELECT * FROM verification_configs 
WHERE config_mode = 'static' AND (
  purpose IS NULL OR 
  business_scene IS NOT NULL OR 
  judgment_dimension IS NOT NULL OR 
  condition_expr IS NOT NULL
);

-- 检查动态配置数据一致性  
SELECT * FROM verification_configs 
WHERE config_mode = 'dynamic' AND (
  business_scene IS NULL OR 
  judgment_dimension IS NULL OR 
  condition_expr IS NULL OR 
  purpose IS NOT NULL
);
*/