import { test, expect } from '@playwright/test';

test.describe('Debug Tests', () => {
  test('should check if React app loads', async ({ page }) => {
    await page.goto('http://localhost:5173/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    console.log('Page title:', await page.title());
    
    // 检查是否有React根元素
    const rootElement = page.locator('#root');
    await expect(rootElement).toBeVisible();
    
    // 检查根元素的内容
    const rootContent = await rootElement.innerHTML();
    console.log('Root content length:', rootContent.length);
    console.log('Root content preview:', rootContent.substring(0, 200));
    
    // 检查是否有JavaScript错误
    const errors = await page.evaluate(() => {
      return (window as any).console?.errors || [];
    });
    
    if (errors.length > 0) {
      console.log('JavaScript errors found:', errors);
    } else {
      console.log('No JavaScript errors found');
    }
    
    // 检查网络请求
    const requests = await page.evaluate(() => {
      return performance.getEntriesByType('resource').map(r => ({
        name: r.name,
        duration: r.duration
      }));
    });
    
    console.log('Network requests:', requests.slice(0, 5));
  });

  test('should check login page specifically', async ({ page }) => {
    await page.goto('http://localhost:5173/login');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    console.log('Login page title:', await page.title());
    
    // 检查URL
    console.log('Current URL:', page.url());
    
    // 检查页面内容
    const bodyContent = await page.content();
    console.log('Body content length:', bodyContent.length);
    
    // 检查是否有React组件渲染
    const reactElements = page.locator('[data-testid], [class*="ant-"], [class*="login"], [class*="Login"]');
    const count = await reactElements.count();
    console.log('React elements found:', count);
    
    if (count > 0) {
      console.log('React elements:', await reactElements.allTextContents());
    }
  });

  test('should check API proxy', async ({ page }) => {
    // 测试API代理是否工作
    const response = await page.request.get('http://localhost:5173/api/health');
    console.log('API health check status:', response.status());
    console.log('API health check body:', await response.text());
  });
}); 