import { test, expect } from '@playwright/test';

const FRONTEND_BASE = 'http://localhost:5173';

test.describe('Basic Authentication Tests', () => {
  test('should load login page', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/login`);
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题（可选，因为可能没有设置）
    const title = await page.title();
    console.log('Login page title:', title);
    
    // 检查基本元素是否存在
    const usernameInput = page.locator('input[name="username"], input[placeholder*="用户名"], input[placeholder*="用户名或邮箱"]');
    const passwordInput = page.locator('input[name="password"], input[placeholder*="密码"]');
    const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    await expect(usernameInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    await expect(loginButton).toBeVisible();
    
    console.log('✅ Login page loaded successfully');
  });

  test('should load register page', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/register`);
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题（可选，因为可能没有设置）
    const title = await page.title();
    console.log('Register page title:', title);
    
    // 检查基本元素是否存在
    const usernameInput = page.locator('input[name="username"], input[placeholder*="用户名"]');
    const emailInput = page.locator('input[name="email"], input[placeholder*="邮箱"]');
    const passwordInput = page.locator('input[name="password"], input[placeholder*="密码"]');
    const confirmPasswordInput = page.locator('input[name="confirmPassword"], input[placeholder*="确认密码"]');
    const registerButton = page.locator('button[type="submit"], button:has-text("注册"), button:has-text("Register")');
    
    await expect(usernameInput).toBeVisible();
    await expect(emailInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    await expect(confirmPasswordInput).toBeVisible();
    await expect(registerButton).toBeVisible();
    
    console.log('✅ Register page loaded successfully');
  });

  test('should show form validation errors on empty submit', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    // 直接点击登录按钮，不填写任何内容
    const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    await loginButton.click();
    
    // 等待验证错误出现
    await page.waitForTimeout(1000);
    
    // 检查是否有验证错误信息
    const errorMessages = page.locator('.ant-form-item-explain-error, .error-message, [role="alert"]');
    const errorCount = await errorMessages.count();
    
    if (errorCount > 0) {
      console.log(`✅ Form validation working - found ${errorCount} error messages`);
    } else {
      console.log('⚠️ No validation errors found - this might be expected behavior');
    }
  });

  test('should navigate between login and register pages', async ({ page }) => {
    // 从登录页面开始
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    // 查找注册链接
    const registerLink = page.locator('a[href="/register"], a:has-text("注册"), a:has-text("Register"), a:has-text("没有账号")');
    
    if (await registerLink.isVisible()) {
      await registerLink.click();
      await page.waitForLoadState('networkidle');
      
      // 验证跳转到注册页面
      await expect(page).toHaveURL(/.*\/register/);
      console.log('✅ Navigation from login to register works');
      
      // 查找返回登录的链接
      const loginLink = page.locator('a[href="/login"], a:has-text("登录"), a:has-text("Login"), a:has-text("已有账号")');
      
      if (await loginLink.isVisible()) {
        await loginLink.click();
        await page.waitForLoadState('networkidle');
        
        // 验证跳转回登录页面
        await expect(page).toHaveURL(/.*\/login/);
        console.log('✅ Navigation from register to login works');
      }
    } else {
      console.log('⚠️ Register link not found on login page');
    }
  });

  test('should test basic form interaction', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    // 查找输入框
    const usernameInput = page.locator('input[name="username"], input[placeholder*="用户名"], input[placeholder*="用户名或邮箱"]');
    const passwordInput = page.locator('input[name="password"], input[placeholder*="密码"]');
    
    // 测试输入功能
    await usernameInput.fill('testuser');
    await passwordInput.fill('testpassword');
    
    // 验证输入内容
    await expect(usernameInput).toHaveValue('testuser');
    await expect(passwordInput).toHaveValue('testpassword');
    
    console.log('✅ Form input interaction working');
  });

  test('should check for authentication guards', async ({ page }) => {
    // 尝试访问需要认证的页面
    await page.goto(`${FRONTEND_BASE}/workbench`);
    await page.waitForLoadState('networkidle');
    
    // 检查是否被重定向到登录页面
    const currentUrl = page.url();
    
    if (currentUrl.includes('/login')) {
      console.log('✅ Authentication guard working - redirected to login');
    } else if (currentUrl.includes('/workbench')) {
      console.log('⚠️ No authentication guard detected - able to access protected page');
    } else {
      console.log(`ℹ️ Current URL: ${currentUrl}`);
    }
  });

  test('should check API endpoints availability', async ({ request }) => {
    const API_BASE = 'http://localhost:8083';
    
    // 测试登录API端点
    try {
      const response = await request.post(`${API_BASE}/api/auth/login`, {
        data: {
          username: 'testuser',
          password: 'testpassword'
        }
      });
      
      console.log(`✅ Login API endpoint accessible - Status: ${response.status()}`);
      
      if (response.status() === 200) {
        const data = await response.json();
        console.log(`✅ Login API response: ${JSON.stringify(data).substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ Login API endpoint error: ${error.message}`);
    }
    
    // 测试注册API端点
    try {
      const response = await request.post(`${API_BASE}/api/auth/register`, {
        data: {
          username: `testuser_${Date.now()}`,
          email: `testuser_${Date.now()}@example.com`,
          password: 'Test123456!'
        }
      });
      
      console.log(`✅ Register API endpoint accessible - Status: ${response.status()}`);
      
      if (response.status() === 200) {
        const data = await response.json();
        console.log(`✅ Register API response: ${JSON.stringify(data).substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ Register API endpoint error: ${error.message}`);
    }
  });
}); 