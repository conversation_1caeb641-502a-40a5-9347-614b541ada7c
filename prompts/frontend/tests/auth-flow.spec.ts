import { test, expect } from '@playwright/test';

const FRONTEND_BASE = 'http://localhost:5173';

// 生成唯一的测试用户数据
const testUser = {
  username: `testuser_${Date.now()}`,
  email: `testuser_${Date.now()}@example.com`,
  password: 'Test123456!',
  confirmPassword: 'Test123456!'
};

test.describe('Complete Authentication Flow', () => {
  test('should complete full registration and login flow', async ({ page }) => {
    console.log('🧪 Starting complete authentication flow test');
    console.log(`📝 Test user: ${testUser.username}`);
    
    // 步骤1: 访问注册页面
    await page.goto(`${FRONTEND_BASE}/register`);
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Registration page loaded');
    
    // 验证注册页面元素
    const usernameInput = page.locator('input[name="username"], input[placeholder*="用户名"]');
    const emailInput = page.locator('input[name="email"], input[placeholder*="邮箱"]');
    const passwordInput = page.locator('input[placeholder*="密码（至少8位"], input[placeholder*="密码"]').first();
    const confirmPasswordInput = page.locator('input[placeholder*="确认密码"], input[name="confirmPassword"]');
    const registerButton = page.locator('button[type="submit"], button:has-text("注册"), button:has-text("Register")');
    
    await expect(usernameInput).toBeVisible();
    await expect(emailInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    await expect(confirmPasswordInput).toBeVisible();
    await expect(registerButton).toBeVisible();
    
    console.log('✅ Registration form elements found');
    
    // 步骤2: 填写注册表单
    await usernameInput.fill(testUser.username);
    await emailInput.fill(testUser.email);
    await passwordInput.fill(testUser.password);
    await confirmPasswordInput.fill(testUser.confirmPassword);
    
    console.log('✅ Registration form filled');
    
    // 步骤3: 提交注册表单
    await registerButton.click();
    
    // 等待注册响应
    await page.waitForTimeout(2000);
    
    // 检查是否有成功消息或错误消息
    const successMessage = page.locator('.ant-message-success, .success-message');
    const errorMessage = page.locator('.ant-message-error, .error-message');
    
    if (await successMessage.isVisible()) {
      console.log('✅ Registration successful');
    } else if (await errorMessage.isVisible()) {
      const errorText = await errorMessage.textContent();
      console.log(`⚠️ Registration error: ${errorText}`);
    } else {
      console.log('ℹ️ No immediate feedback from registration');
    }
    
    // 步骤4: 访问登录页面
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Login page loaded');
    
    // 验证登录页面元素
    const loginUsernameInput = page.locator('input[name="username"], input[placeholder*="用户名"], input[placeholder*="用户名或邮箱"]');
    const loginPasswordInput = page.locator('input[name="password"], input[placeholder*="密码"]');
    const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    await expect(loginUsernameInput).toBeVisible();
    await expect(loginPasswordInput).toBeVisible();
    await expect(loginButton).toBeVisible();
    
    console.log('✅ Login form elements found');
    
    // 步骤5: 填写登录表单
    await loginUsernameInput.fill(testUser.username);
    await loginPasswordInput.fill(testUser.password);
    
    console.log('✅ Login form filled');
    
    // 步骤6: 提交登录表单
    await loginButton.click();
    
    // 等待登录响应
    await page.waitForTimeout(3000);
    
    // 检查登录结果
    const loginSuccessMessage = page.locator('.ant-message-success, .success-message');
    const loginErrorMessage = page.locator('.ant-message-error, .error-message');
    
    if (await loginSuccessMessage.isVisible()) {
      console.log('✅ Login successful');
    } else if (await loginErrorMessage.isVisible()) {
      const errorText = await loginErrorMessage.textContent();
      console.log(`⚠️ Login error: ${errorText}`);
    } else {
      console.log('ℹ️ No immediate feedback from login');
    }
    
    // 步骤7: 验证是否成功登录（检查URL变化或页面内容）
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    // 检查是否跳转到主页或工作台
    if (currentUrl.includes('/home') || currentUrl.includes('/workbench') || currentUrl.includes('/dashboard')) {
      console.log('✅ Successfully redirected to authenticated page');
    } else if (currentUrl.includes('/login')) {
      console.log('⚠️ Still on login page - may need to check authentication');
    } else {
      console.log(`ℹ️ Current page: ${currentUrl}`);
    }
    
    // 步骤8: 检查页面内容是否显示已登录状态
    const authenticatedContent = page.locator('text=🧠 提示词管理, text=工作台, text=Workbench, text=Dashboard');
    if (await authenticatedContent.isVisible()) {
      console.log('✅ Authenticated content found');
    } else {
      console.log('ℹ️ No authenticated content found - checking for other indicators');
      
      // 检查是否有用户信息显示
      const userInfo = page.locator('[data-testid="user-info"], .user-info, .user-menu');
      if (await userInfo.isVisible()) {
        console.log('✅ User info found');
      } else {
        console.log('ℹ️ No user info found');
      }
    }
    
    console.log('🎉 Authentication flow test completed');
  });

  test('should test form validation on registration', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/register`);
    await page.waitForLoadState('networkidle');
    
    const registerButton = page.locator('button[type="submit"], button:has-text("注册"), button:has-text("Register")');
    
    // 测试空表单提交
    await registerButton.click();
    await page.waitForTimeout(1000);
    
    // 检查验证错误
    const validationErrors = page.locator('.ant-form-item-explain-error, .error-message, [role="alert"]');
    const errorCount = await validationErrors.count();
    
    if (errorCount > 0) {
      console.log(`✅ Form validation working - found ${errorCount} validation errors`);
      const errors = await validationErrors.allTextContents();
      console.log('Validation errors:', errors);
    } else {
      console.log('ℹ️ No validation errors found');
    }
  });

  test('should test form validation on login', async ({ page }) => {
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    // 测试空表单提交
    await loginButton.click();
    await page.waitForTimeout(1000);
    
    // 检查验证错误
    const validationErrors = page.locator('.ant-form-item-explain-error, .error-message, [role="alert"]');
    const errorCount = await validationErrors.count();
    
    if (errorCount > 0) {
      console.log(`✅ Login form validation working - found ${errorCount} validation errors`);
      const errors = await validationErrors.allTextContents();
      console.log('Login validation errors:', errors);
    } else {
      console.log('ℹ️ No login validation errors found');
    }
  });

  test('should test navigation between auth pages', async ({ page }) => {
    // 从登录页面开始
    await page.goto(`${FRONTEND_BASE}/login`);
    await page.waitForLoadState('networkidle');
    
    // 查找注册链接
    const registerLink = page.locator('a[href="/register"], a:has-text("注册"), a:has-text("Register"), a:has-text("没有账号"), a:has-text("立即注册")');
    
    if (await registerLink.isVisible()) {
      await registerLink.click();
      await page.waitForLoadState('networkidle');
      
      // 验证跳转到注册页面
      await expect(page).toHaveURL(/.*\/register/);
      console.log('✅ Navigation from login to register works');
      
      // 查找返回登录的链接
      const loginLink = page.locator('a[href="/login"], a:has-text("登录"), a:has-text("Login"), a:has-text("已有账号")');
      
      if (await loginLink.isVisible()) {
        await loginLink.click();
        await page.waitForLoadState('networkidle');
        
        // 验证跳转回登录页面
        await expect(page).toHaveURL(/.*\/login/);
        console.log('✅ Navigation from register to login works');
      } else {
        console.log('⚠️ Login link not found on register page');
      }
    } else {
      console.log('⚠️ Register link not found on login page');
    }
  });

  test('should test authentication guards', async ({ page }) => {
    // 尝试访问需要认证的页面
    await page.goto(`${FRONTEND_BASE}/workbench`);
    await page.waitForLoadState('networkidle');
    
    // 检查是否被重定向到登录页面
    const currentUrl = page.url();
    
    if (currentUrl.includes('/login')) {
      console.log('✅ Authentication guard working - redirected to login');
      
      // 检查重定向参数
      if (currentUrl.includes('redirect=')) {
        console.log('✅ Redirect parameter preserved');
      }
    } else if (currentUrl.includes('/workbench')) {
      console.log('⚠️ No authentication guard detected - able to access protected page');
    } else {
      console.log(`ℹ️ Current URL: ${currentUrl}`);
    }
  });
}); 