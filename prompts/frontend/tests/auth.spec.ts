import { test, expect } from '@playwright/test';

const API_BASE = 'http://localhost:8083';
const FRONTEND_BASE = 'http://localhost:5173';

// 测试数据
const testUsers = {
  valid: {
    username: `testuser_${Date.now()}`,
    email: `testuser_${Date.now()}@example.com`,
    password: 'Test123456!',
    confirmPassword: 'Test123456!'
  },
  invalid: {
    shortPassword: {
      username: 'testuser_short',
      email: '<EMAIL>',
      password: '123',
      confirmPassword: '123'
    },
    mismatchedPassword: {
      username: 'testuser_mismatch',
      email: '<EMAIL>',
      password: 'Test123456!',
      confirmPassword: 'DifferentPassword123!'
    },
    invalidEmail: {
      username: 'testuser_invalid_email',
      email: 'invalid-email',
      password: 'Test123456!',
      confirmPassword: 'Test123456!'
    }
  }
};

test.describe('Authentication Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 清除cookies
    await page.context().clearCookies();
  });

  test.describe('Registration Tests', () => {
    test('should register a new user successfully', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/register`);
      
      // 等待页面加载
      await page.waitForLoadState('networkidle');
      
      // 验证注册页面元素
      await expect(page.locator('text=用户注册')).toBeVisible();
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="email"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
      
      // 填写注册表单
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="email"]').fill(testUsers.valid.email);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      await page.locator('input[name="confirmPassword"]').fill(testUsers.valid.confirmPassword);
      
      // 点击注册按钮
      await page.locator('button[type="submit"]').click();
      
      // 等待注册成功
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 10000 });
      
      // 验证跳转到登录页面或首页
      await expect(page).toHaveURL(/.*\/(login|home)/);
      
      console.log(`✅ Successfully registered user: ${testUsers.valid.username}`);
    });

    test('should show validation error for short password', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/register`);
      
      // 填写无效密码
      await page.locator('input[name="username"]').fill(testUsers.invalid.shortPassword.username);
      await page.locator('input[name="email"]').fill(testUsers.invalid.shortPassword.email);
      await page.locator('input[name="password"]').fill(testUsers.invalid.shortPassword.password);
      await page.locator('input[name="confirmPassword"]').fill(testUsers.invalid.shortPassword.confirmPassword);
      
      // 点击注册按钮
      await page.locator('button[type="submit"]').click();
      
      // 验证错误信息
      await expect(page.locator('.ant-form-item-explain-error')).toContainText('密码长度至少为8位');
    });

    test('should show validation error for mismatched passwords', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/register`);
      
      // 填写不匹配的密码
      await page.locator('input[name="username"]').fill(testUsers.invalid.mismatchedPassword.username);
      await page.locator('input[name="email"]').fill(testUsers.invalid.mismatchedPassword.email);
      await page.locator('input[name="password"]').fill(testUsers.invalid.mismatchedPassword.password);
      await page.locator('input[name="confirmPassword"]').fill(testUsers.invalid.mismatchedPassword.confirmPassword);
      
      // 点击注册按钮
      await page.locator('button[type="submit"]').click();
      
      // 验证错误信息
      await expect(page.locator('.ant-form-item-explain-error')).toContainText('两次输入的密码不一致');
    });

    test('should show validation error for invalid email', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/register`);
      
      // 填写无效邮箱
      await page.locator('input[name="username"]').fill(testUsers.invalid.invalidEmail.username);
      await page.locator('input[name="email"]').fill(testUsers.invalid.invalidEmail.email);
      await page.locator('input[name="password"]').fill(testUsers.invalid.invalidEmail.password);
      await page.locator('input[name="confirmPassword"]').fill(testUsers.invalid.invalidEmail.confirmPassword);
      
      // 点击注册按钮
      await page.locator('button[type="submit"]').click();
      
      // 验证错误信息
      await expect(page.locator('.ant-form-item-explain-error')).toContainText('请输入有效的邮箱地址');
    });
  });

  test.describe('Login Tests', () => {
    test('should login with valid credentials', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/login`);
      
      // 等待页面加载
      await page.waitForLoadState('networkidle');
      
      // 验证登录页面元素
      await expect(page.locator('text=用户登录')).toBeVisible();
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      
      // 填写登录表单
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      
      // 点击登录按钮
      await page.locator('button[type="submit"]').click();
      
      // 等待登录成功
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 10000 });
      
      // 验证跳转到首页或工作台
      await expect(page).toHaveURL(/.*\/(home|workbench)/);
      
      // 验证用户已登录状态
      await expect(page.locator('text=🧠 提示词管理')).toBeVisible();
      
      console.log(`✅ Successfully logged in user: ${testUsers.valid.username}`);
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/login`);
      
      // 填写无效凭据
      await page.locator('input[name="username"]').fill('nonexistentuser');
      await page.locator('input[name="password"]').fill('wrongpassword');
      
      // 点击登录按钮
      await page.locator('button[type="submit"]').click();
      
      // 验证错误信息
      await expect(page.locator('.ant-message-error')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('.ant-message-error')).toContainText('用户名或密码错误');
    });

    test('should show validation error for empty fields', async ({ page }) => {
      await page.goto(`${FRONTEND_BASE}/login`);
      
      // 不填写任何内容，直接点击登录
      await page.locator('button[type="submit"]').click();
      
      // 验证表单验证错误
      await expect(page.locator('.ant-form-item-explain-error')).toContainText('请输入用户名');
      await expect(page.locator('.ant-form-item-explain-error')).toContainText('请输入密码');
    });
  });

  test.describe('Authentication Flow Tests', () => {
    test('should redirect unauthenticated user to login page', async ({ page }) => {
      // 直接访问需要认证的页面
      await page.goto(`${FRONTEND_BASE}/workbench`);
      
      // 验证重定向到登录页面
      await expect(page).toHaveURL(/.*\/login/);
      
      // 验证重定向参数
      await expect(page).toHaveURL(/.*redirect=.*workbench/);
    });

    test('should maintain authentication state after page refresh', async ({ page }) => {
      // 先登录
      await page.goto(`${FRONTEND_BASE}/login`);
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      await page.locator('button[type="submit"]').click();
      
      // 等待登录成功
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 10000 });
      
      // 刷新页面
      await page.reload();
      
      // 验证仍然保持登录状态
      await expect(page.locator('text=🧠 提示词管理')).toBeVisible();
      await expect(page).toHaveURL(/.*\/(home|workbench)/);
    });

    test('should logout successfully', async ({ page }) => {
      // 先登录
      await page.goto(`${FRONTEND_BASE}/login`);
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      await page.locator('button[type="submit"]').click();
      
      // 等待登录成功
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 10000 });
      
      // 查找并点击登出按钮（可能在用户菜单中）
      const userMenu = page.locator('.ant-dropdown-trigger, .user-menu, [data-testid="user-menu"]');
      if (await userMenu.isVisible()) {
        await userMenu.click();
        await page.locator('text=退出登录, text=Logout').click();
      } else {
        // 如果没有用户菜单，尝试直接访问登出API
        await page.goto(`${FRONTEND_BASE}/logout`);
      }
      
      // 验证登出成功
      await expect(page).toHaveURL(/.*\/login/);
    });
  });

  test.describe('API Integration Tests', () => {
    test('should test registration API directly', async ({ request }) => {
      const response = await request.post(`${API_BASE}/api/auth/register`, {
        data: {
          username: `apitest_${Date.now()}`,
          email: `apitest_${Date.now()}@example.com`,
          password: 'Test123456!'
        }
      });
      
      expect(response.status()).toBe(200);
      const data = await response.json();
      expect(data.code).toBe(200);
      expect(data.message).toContain('注册成功');
    });

    test('should test login API directly', async ({ request }) => {
      const response = await request.post(`${API_BASE}/api/auth/login`, {
        data: {
          username: testUsers.valid.username,
          password: testUsers.valid.password
        }
      });
      
      expect(response.status()).toBe(200);
      const data = await response.json();
      expect(data.code).toBe(200);
      expect(data.data).toHaveProperty('access_token');
      expect(data.data).toHaveProperty('user');
    });

    test('should test protected endpoint with authentication', async ({ request }) => {
      // 先登录获取token
      const loginResponse = await request.post(`${API_BASE}/api/auth/login`, {
        data: {
          username: testUsers.valid.username,
          password: testUsers.valid.password
        }
      });
      
      const loginData = await loginResponse.json();
      const token = loginData.data.access_token;
      
      // 使用token访问受保护的端点
      const protectedResponse = await request.get(`${API_BASE}/api/prompts/list`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-user-id': '1',
          'x-tenant-id': '1'
        }
      });
      
      expect(protectedResponse.status()).toBe(200);
      const data = await protectedResponse.json();
      expect(data.code).toBe(200);
    });
  });

  test.describe('Error Handling Tests', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // 模拟网络断开
      await page.context().setOffline(true);
      
      await page.goto(`${FRONTEND_BASE}/login`);
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      await page.locator('button[type="submit"]').click();
      
      // 验证网络错误处理
      await expect(page.locator('.ant-message-error')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('.ant-message-error')).toContainText('网络连接失败');
      
      // 恢复网络
      await page.context().setOffline(false);
    });

    test('should handle server errors gracefully', async ({ page }) => {
      // Mock 500 错误
      await page.route('**/api/auth/login', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            code: 500,
            message: '服务器内部错误',
            data: null
          })
        });
      });
      
      await page.goto(`${FRONTEND_BASE}/login`);
      await page.locator('input[name="username"]').fill(testUsers.valid.username);
      await page.locator('input[name="password"]').fill(testUsers.valid.password);
      await page.locator('button[type="submit"]').click();
      
      // 验证服务器错误处理
      await expect(page.locator('.ant-message-error')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('.ant-message-error')).toContainText('服务器内部错误');
    });
  });
}); 