# 本地开发代理配置

## 概述

本项目使用 Vite 开发服务器进行本地开发，配置了多个API代理来支持微服务架构。

## 代理配置

### 开发服务器
- **前端应用**: http://localhost:5173
- **开发工具**: Vite Dev Server

### API代理规则

| 路径前缀 | 目标服务 | 端口 | 说明 |
|---------|---------|------|------|
| `/api/user/*` | 用户服务 | 8084 | 认证、用户管理相关API |
| `/api/prompts/*` | 提示词服务 | 8083 | 提示词、分类、标签等业务API |
| `/api/*` | 提示词服务 | 8083 | 默认API代理（兜底） |

## 配置详情

### Vite配置 (vite.config.ts)

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: 'localhost',
    proxy: {
      // api/user 代理到 8084 端口
      '/api/user': {
        target: 'http://localhost:8084',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/user/, '/api/user')
      },
      // api/prompts 代理到 8083 端口
      '/api/prompts': {
        target: 'http://localhost:8083',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/prompts/, '/api/prompts')
      },
      // 其他 /api 请求默认代理到 8083
      '/api': {
        target: 'http://localhost:8083',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
```

### API配置 (src/utils/request.ts)

```typescript
export const API_CONFIG = {
  BASE_URL: '', // 开发环境下为空，使用代理
  
  PREFIXES: {
    AUTH: '/api/user/auth',        // 代理到 8084
    PROMPTS: '/api/prompts',       // 代理到 8083
    CATEGORIES: '/api/prompts/categories',
    TAGS: '/api/prompts/tags',
    FAVORITES: '/api/prompts/favorites',
    SHARE: '/api/prompts/share',
    ANALYTICS: '/api/prompts/analytics',
    VERSION: '/api/prompts/version',
  },
} as const;
```

## 使用示例

### 前端代码中的API调用

```typescript
// 用户认证API (代理到 8084)
const loginResponse = await apiService.post('/api/user/auth/login', credentials);

// 提示词API (代理到 8083)
const promptsResponse = await apiService.get('/api/prompts/list');

// 分类API (代理到 8083)
const categoriesResponse = await apiService.get('/api/prompts/categories/list');
```

### 浏览器中的直接访问

```bash
# 用户服务API
curl http://localhost:5173/api/user/auth/login

# 提示词服务API
curl http://localhost:5173/api/prompts/list

# 分类API
curl http://localhost:5173/api/prompts/categories/list
```

## 服务启动要求

### 必需的后端服务

1. **用户服务** (端口 8084)
   - 提供认证、用户管理功能
   - API路径: `/api/user/*`

2. **提示词服务** (端口 8083)
   - 提供提示词、分类、标签等业务功能
   - API路径: `/api/prompts/*`

### 启动命令

```bash
# 启动前端开发服务器
npm run dev

# 确保后端服务运行在对应端口
# 用户服务: localhost:8084
# 提示词服务: localhost:8083
```

## 故障排除

### 常见问题

1. **404错误**
   - 检查后端服务是否启动
   - 检查API路径是否正确

2. **403错误**
   - 检查认证token是否正确
   - 检查用户权限

3. **连接超时**
   - 检查后端服务端口是否正确
   - 检查防火墙设置

### 调试方法

1. **查看网络请求**
   - 打开浏览器开发者工具
   - 查看Network标签页

2. **查看代理日志**
   - 在Vite开发服务器控制台查看代理日志

3. **测试代理**
   ```bash
   # 测试用户服务
   curl -v http://localhost:5173/api/user/auth/login
   
   # 测试提示词服务
   curl -v http://localhost:5173/api/prompts/list
   ```

## 生产环境

生产环境中，代理配置会被忽略，前端会直接访问配置的API服务器地址。

```typescript
// 生产环境配置
PRODUCTION: {
  BASE_URL: 'https://api.example.com',
  API_PREFIX: '/api/prompts',
}
``` 