#!/bin/bash

echo "🧪 运行 Prompts 登录注册流程测试"
echo "=================================="

# 检查前端服务
echo "📋 检查前端服务..."
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务未运行"
    exit 1
fi

# 检查后端服务
echo "📋 检查后端服务..."
if curl -s http://localhost:8083/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "⚠️  后端服务需要认证或未运行"
fi

echo ""
echo "🚀 开始运行认证测试..."

# 运行基础认证测试
echo "1️⃣ 基础认证功能测试..."
npx playwright test tests/basic-auth.spec.ts --reporter=line

echo ""
echo "2️⃣ 完整认证流程测试..."
npx playwright test tests/auth-flow.spec.ts --reporter=line

echo ""
echo "🎉 认证流程测试完成！"
echo "📊 测试结果已输出到控制台" 