# Prompts 前端项目租户代码请求头实现

## 概述

在 prompts 前端项目的所有 HTTP 请求中添加了 `X-Tenant-Code: prompts` 请求头，以确保后端能够正确识别请求来源的租户。

## 修改的文件

### 1. `src/services/api.ts`
- **位置**: 第 38 行
- **修改内容**: 在请求拦截器中添加租户代码请求头
- **代码变更**:
```typescript
// 添加租户代码
config.headers['X-Tenant-Code'] = 'prompts';
```

### 2. `src/services/authService.ts`
- **位置**: 第 38 行
- **修改内容**: 在认证服务的请求拦截器中添加租户代码请求头
- **代码变更**:
```typescript
// 添加租户代码
config.headers['X-Tenant-Code'] = 'prompts';
```

### 3. `src/utils/apiClient.ts`
- **位置**: 第 48 行
- **修改内容**: 在统一 API 客户端的请求拦截器中添加租户代码请求头
- **代码变更**:
```typescript
// 添加租户代码
config.headers['X-Tenant-Code'] = 'prompts';
```

## 实现细节

### 请求拦截器配置
所有三个 HTTP 客户端实例都在请求拦截器中添加了相同的租户代码：

```typescript
// 请求拦截器
client.interceptors.request.use(
  (config) => {
    // 添加请求ID
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 添加租户代码
    config.headers['X-Tenant-Code'] = 'prompts';
    
    // 其他配置...
    
    return config;
  },
  (error) => Promise.reject(error)
);
```

### 覆盖范围
- ✅ 主要 API 服务 (`ApiService`)
- ✅ 认证服务 (`AuthService`)
- ✅ 统一认证服务 (`UnifiedAuthService`)
- ✅ 所有通过 `createUnifiedApiClient` 创建的客户端

## 验证

### 构建测试
```bash
cd prompts/frontend
npm run build
```
✅ 构建成功，无 TypeScript 错误

### 请求头验证
所有 HTTP 请求现在都会自动包含以下请求头：
- `X-Tenant-Code: prompts`
- `X-Request-ID: req_[timestamp]_[random]`
- `Content-Type: application/json`
- `Authorization: Bearer [token]` (如果存在)

## 影响范围

### 受影响的 API 端点
- 所有 `/api/prompts/*` 端点
- 所有认证相关端点 (`/auth/*`)
- 所有通过统一 API 客户端发送的请求

### 后端处理
后端服务现在可以通过 `X-Tenant-Code` 请求头来：
1. 识别请求来源的租户
2. 应用相应的租户配置
3. 进行租户级别的权限验证
4. 路由到正确的数据源

## 注意事项

1. **硬编码值**: 租户代码 `prompts` 是硬编码的，如果需要支持多租户，可以考虑从环境变量或配置文件中读取
2. **向后兼容**: 此修改不会影响现有的 API 调用，只是添加了额外的请求头
3. **调试**: 在开发环境中，可以通过浏览器开发者工具查看网络请求，确认请求头已正确添加

## 后续优化建议

1. **环境配置**: 将租户代码移到环境变量中
2. **动态租户**: 支持运行时切换租户
3. **租户验证**: 在前端添加租户代码的验证逻辑
4. **错误处理**: 为租户相关的错误添加特殊处理 