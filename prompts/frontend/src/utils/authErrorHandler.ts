import { message } from 'antd';
import type { FormInstance } from 'antd';
import type { ApiResponse } from '../types/api';
import { 
  VALIDATION_ERROR, 
  INVALID_CREDENTIALS, 
  UNAUTHENTICATED, 
  TOKEN_EXPIRED,
  RESOURCE_EXISTS,
  getErrorMessage 
} from '../constants/errorCodes';

/**
 * 认证错误处理结果
 */
export interface AuthErrorResult {
  shouldShowFieldErrors: boolean;  // 是否显示字段错误
  shouldShowGlobalError: boolean;  // 是否显示全局错误
  shouldRedirect: boolean;         // 是否应该跳转
  redirectPath?: string;           // 跳转路径
  fieldErrors?: Array<{ name: string; errors: string[] }>; // 字段错误
  globalMessage?: string;          // 全局错误消息
}

/**
 * 处理登录错误
 */
export function handleLoginError(
  response: ApiResponse<any>, 
  form?: FormInstance
): AuthErrorResult {
  const { code, message: apiMessage, data } = response;
  
  // 成功情况
  if (code === 0) {
    return {
      shouldShowFieldErrors: false,
      shouldShowGlobalError: false,
      shouldRedirect: true,
      redirectPath: '/'
    };
  }
  
  // 验证错误 - 显示字段错误 (错误码10001)
  if (code === VALIDATION_ERROR) {
    // 参考根目录frontend项目的处理方式
    // 优先检查response.errors字段
    if ((response as any).errors && Array.isArray((response as any).errors)) {
      const fieldErrors = convertToFormErrors((response as any).errors);
      if (form && fieldErrors.length > 0) {
        form.setFields(fieldErrors);
      }
      
      return {
        shouldShowFieldErrors: true,
        shouldShowGlobalError: false,
        shouldRedirect: false,
        fieldErrors
      };
    }
    
    // 备用：从data中提取字段错误
    const fieldErrors = extractFieldErrors(data);
    if (form && fieldErrors.length > 0) {
      form.setFields(fieldErrors);
    }
    
    return {
      shouldShowFieldErrors: true,
      shouldShowGlobalError: false,
      shouldRedirect: false,
      fieldErrors
    };
  }
  
  // 用户名或密码错误
  if (code === INVALID_CREDENTIALS) {
    return {
      shouldShowFieldErrors: false,
      shouldShowGlobalError: true,
      shouldRedirect: false,
      globalMessage: '用户名或密码错误'
    };
  }
  
  // 认证相关错误
  if (code === UNAUTHENTICATED || code === TOKEN_EXPIRED) {
    return {
      shouldShowFieldErrors: false,
      shouldShowGlobalError: true,
      shouldRedirect: false,
      globalMessage: '登录状态异常，请重新登录'
    };
  }
  
  // 其他错误
  return {
    shouldShowFieldErrors: false,
    shouldShowGlobalError: true,
    shouldRedirect: false,
    globalMessage: apiMessage || getErrorMessage(code)
  };
}

/**
 * 处理注册错误
 */
export function handleRegisterError(
  response: ApiResponse<any>, 
  form?: FormInstance
): AuthErrorResult {
  const { code, message: apiMessage, data } = response;
  
  // 成功情况
  if (code === 0) {
    return {
      shouldShowFieldErrors: false,
      shouldShowGlobalError: false,
      shouldRedirect: true,
      redirectPath: '/login'
    };
  }
  
  // 验证错误 - 显示字段错误 (错误码10001)
  if (code === VALIDATION_ERROR) {
    // 参考根目录frontend项目的处理方式
    // 优先检查response.errors字段
    if ((response as any).errors && Array.isArray((response as any).errors)) {
      const fieldErrors = convertToFormErrors((response as any).errors);
      if (form && fieldErrors.length > 0) {
        form.setFields(fieldErrors);
      }
      
      return {
        shouldShowFieldErrors: true,
        shouldShowGlobalError: false,
        shouldRedirect: false,
        fieldErrors
      };
    }
    
    // 备用：从data中提取字段错误
    const fieldErrors = extractFieldErrors(data);
    if (form && fieldErrors.length > 0) {
      form.setFields(fieldErrors);
    }
    
    return {
      shouldShowFieldErrors: true,
      shouldShowGlobalError: false,
      shouldRedirect: false,
      fieldErrors
    };
  }
  
  // 用户名或邮箱已存在
  if (code === RESOURCE_EXISTS) {
    const fieldErrors = extractExistsErrors(data);
    if (form && fieldErrors.length > 0) {
      form.setFields(fieldErrors);
    }
    
    return {
      shouldShowFieldErrors: true,
      shouldShowGlobalError: false,
      shouldRedirect: false,
      fieldErrors
    };
  }
  
  // 其他错误
  return {
    shouldShowFieldErrors: false,
    shouldShowGlobalError: true,
    shouldRedirect: false,
    globalMessage: apiMessage || getErrorMessage(code)
  };
}

/**
 * 将API错误格式转换为表单错误格式
 * 参考根目录frontend项目的applyFieldErrorsToForm函数
 */
function convertToFormErrors(errors: Array<{ field: string; message: string }>): Array<{ name: string; errors: string[] }> {
  if (!Array.isArray(errors)) return [];
  
  return errors.map(err => ({
    name: err.field,
    errors: [err.message],
  }));
}

/**
 * 从API响应中提取字段错误
 */
function extractFieldErrors(data: any): Array<{ name: string; errors: string[] }> {
  const fieldErrors: Array<{ name: string; errors: string[] }> = [];
  
  if (!data) return fieldErrors;
  
  // 处理不同的错误格式
  if (data.errors && Array.isArray(data.errors)) {
    data.errors.forEach((err: any) => {
      if (err.field && err.message) {
        fieldErrors.push({
          name: err.field,
          errors: [err.message]
        });
      }
    });
  }
  
  if (data.details?.field_errors) {
    Object.entries(data.details.field_errors).forEach(([field, messages]) => {
      fieldErrors.push({
        name: field,
        errors: Array.isArray(messages) ? messages : [messages as string]
      });
    });
  }
  
  return fieldErrors;
}

/**
 * 从已存在错误中提取字段错误
 */
function extractExistsErrors(data: any): Array<{ name: string; errors: string[] }> {
  const fieldErrors: Array<{ name: string; errors: string[] }> = [];
  
  if (!data) return fieldErrors;
  
  // 根据错误消息判断是哪个字段已存在
  const message = data.message || '';
  
  if (message.includes('用户名') || message.includes('username')) {
    fieldErrors.push({
      name: 'username',
      errors: ['用户名已存在']
    });
  }
  
  if (message.includes('邮箱') || message.includes('email')) {
    fieldErrors.push({
      name: 'email',
      errors: ['邮箱已被注册']
    });
  }
  
  return fieldErrors;
}

/**
 * 应用错误处理结果
 */
export function applyAuthErrorResult(
  result: AuthErrorResult,
  form?: FormInstance,
  onRedirect?: (path: string) => void
) {
  // 显示全局错误消息
  if (result.shouldShowGlobalError && result.globalMessage) {
    message.error(result.globalMessage);
  }
  
  // 显示字段错误
  if (result.shouldShowFieldErrors && result.fieldErrors && form) {
    form.setFields(result.fieldErrors);
  }
  
  // 执行跳转
  if (result.shouldRedirect && result.redirectPath && onRedirect) {
    onRedirect(result.redirectPath);
  }
} 