import type { FormInstance } from 'antd';

/**
 * 将API错误应用到表单字段
 * 参考根目录frontend项目的实现
 * @param errors 错误数组
 * @param form 表单实例
 */
export function applyFieldErrorsToForm(errors: Array<{ field: string; message: string }>, form: FormInstance) {
  if (errors == null) {
    form.setFields([]);
    return;
  }
  
  if (!Array.isArray(errors)) return;
  
  form.setFields(
    errors.map(err => ({
      name: err.field,
      errors: [err.message],
    }))
  );
}

/**
 * 从API响应中提取字段错误
 * @param response API响应
 * @returns 字段错误数组
 */
export function extractFieldErrors(response: any): Array<{ field: string; message: string }> {
  const errors: Array<{ field: string; message: string }> = [];
  
  // 处理不同的错误格式
  if (response.errors && Array.isArray(response.errors)) {
    errors.push(...response.errors);
  }
  
  if (response.details?.field_errors && Array.isArray(response.details.field_errors)) {
    errors.push(...response.details.field_errors);
  }
  
  if (response.data?.errors && Array.isArray(response.data.errors)) {
    errors.push(...response.data.errors);
  }
  
  return errors;
}

/**
 * 处理API错误并应用到表单
 * @param error API错误
 * @param form 表单实例
 * @returns 是否处理了字段错误
 */
export function handleApiErrorToForm(error: any, form: FormInstance): boolean {
  const fieldErrors = extractFieldErrors(error);
  
  if (fieldErrors.length > 0) {
    applyFieldErrorsToForm(fieldErrors, form);
    return true;
  }
  
  return false;
} 