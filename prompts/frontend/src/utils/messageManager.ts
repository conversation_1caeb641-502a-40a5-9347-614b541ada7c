import { message } from 'antd';

class MessageManager {
  private errorMessages: Map<string, number> = new Map();
  private readonly DEBOUNCE_TIME = 3000; // 3秒内相同错误消息不重复显示

  error(content: string, key?: string): void {
    const messageKey = key || content;
    const now = Date.now();
    const lastTime = this.errorMessages.get(messageKey);
    if (lastTime && now - lastTime < this.DEBOUNCE_TIME) {
      return;
    }
    this.errorMessages.set(messageKey, now);
    message.error({
      content,
      key: messageKey,
      duration: 4,
    });
  }

  success(content: string, key?: string): void {
    const messageKey = key || content;
    message.success({ content, key: messageKey, duration: 3 });
  }

  warning(content: string, key?: string): void {
    const messageKey = key || content;
    message.warning({ content, key: messageKey, duration: 4 });
  }

  info(content: string, key?: string): void {
    const messageKey = key || content;
    message.info({ content, key: messageKey, duration: 3 });
  }

  destroy(): void {
    message.destroy();
    this.errorMessages.clear();
  }

  destroyByKey(key: string): void {
    message.destroy(key);
    this.errorMessages.delete(key);
  }

  clearErrorRecords(): void {
    this.errorMessages.clear();
  }
}

export const messageManager = new MessageManager();
export const showError = (content: string, key?: string) => messageManager.error(content, key);
export const showSuccess = (content: string, key?: string) => messageManager.success(content, key);
export const showWarning = (content: string, key?: string) => messageManager.warning(content, key);
export const showInfo = (content: string, key?: string) => messageManager.info(content, key); 