import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Prompt, 
  FavoriteFolder
} from '../types/api';

// 收藏服务类
export class FavoriteService {
  // 添加收藏
  static async addFavorite(uid: number, promptId: number, folderId?: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(API_ENDPOINTS.FAVORITES.ADD, { 
      uid, 
      prompt_id: promptId, 
      folder_id: folderId 
    });
  }

  // 移除收藏
  static async removeFavorite(uid: number, promptId: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(API_ENDPOINTS.FAVORITES.REMOVE, { 
      uid, 
      prompt_id: promptId 
    });
  }

  // 获取收藏列表
  static async getFavoriteList(uid: number, folderId?: number): Promise<ApiResponse<{ list: Prompt[] }>> {
    return await apiService.get<{ list: Prompt[] }>(API_ENDPOINTS.FAVORITES.LIST, {
      params: { uid, folder_id: folderId }
    });
  }

  // 创建收藏夹
  static async createFavoriteFolder(uid: number, name: string, description?: string): Promise<ApiResponse<FavoriteFolder>> {
    return await apiService.post<FavoriteFolder>(API_ENDPOINTS.FAVORITES.FOLDER_CREATE, {
      uid, name, description
    });
  }
}

// 导出便捷方法
export const {
  addFavorite,
  removeFavorite,
  getFavoriteList,
  createFavoriteFolder,
} = FavoriteService;

export default FavoriteService; 