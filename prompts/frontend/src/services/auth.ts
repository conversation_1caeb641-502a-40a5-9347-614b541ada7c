import { apiService, API_ENDPOINTS } from '../utils/request';
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  RefreshTokenRequest,
  LogoutRequest
} from '../types/auth';
import type { ApiResponse } from '../types/api';

// 重新导出类型以供其他模块使用
export type { LoginRequest, RegisterRequest, AuthResponse, User, RefreshTokenRequest, LogoutRequest };

// 安全的token存储类
class SecureTokenStorage {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private static readonly USER_INFO_KEY = 'user_info';
  private static readonly TOKEN_EXPIRY_KEY = 'token_expiry';

  // 存储访问令牌
  static setAccessToken(token: string, expiresIn: number): void {
    try {
      // 计算过期时间
      const expiryTime = Date.now() + expiresIn * 1000;
      
      // 使用 sessionStorage 存储敏感信息（页面关闭后清除）
      sessionStorage.setItem(this.ACCESS_TOKEN_KEY, token);
      sessionStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryTime.toString());
    } catch (error) {
      console.error('Failed to store access token:', error);
    }
  }

  // 获取访问令牌
  static getAccessToken(): string | null {
    try {
      const token = sessionStorage.getItem(this.ACCESS_TOKEN_KEY);
      const expiryTime = sessionStorage.getItem(this.TOKEN_EXPIRY_KEY);
      
      if (!token || !expiryTime) {
        return null;
      }

      // 检查是否过期
      if (Date.now() > parseInt(expiryTime)) {
        this.clearTokens();
        return null;
      }

      return token;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  // 存储刷新令牌
  static setRefreshToken(token: string): void {
    try {
      // 使用 localStorage 存储刷新令牌（持久化）
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
    }
  }

  // 获取刷新令牌
  static getRefreshToken(): string | null {
    try {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  // 存储用户信息
  static setUserInfo(userInfo: User): void {
    try {
      sessionStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo));
    } catch (error) {
      console.error('Failed to store user info:', error);
    }
  }

  // 获取用户信息
  static getUserInfo(): User | null {
    try {
      const userInfo = sessionStorage.getItem(this.USER_INFO_KEY);
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('Failed to get user info:', error);
      return null;
    }
  }

  // 清除所有令牌
  static clearTokens(): void {
    try {
      sessionStorage.removeItem(this.ACCESS_TOKEN_KEY);
      sessionStorage.removeItem(this.TOKEN_EXPIRY_KEY);
      sessionStorage.removeItem(this.USER_INFO_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  // 检查是否已登录
  static isLoggedIn(): boolean {
    return this.getAccessToken() !== null;
  }

  // 检查令牌是否即将过期（5分钟内）
  static isTokenExpiringSoon(): boolean {
    try {
      const expiryTime = sessionStorage.getItem(this.TOKEN_EXPIRY_KEY);
      if (!expiryTime) return true;
      
      const timeUntilExpiry = parseInt(expiryTime) - Date.now();
      return timeUntilExpiry < 5 * 60 * 1000; // 5分钟
    } catch (error) {
      return true;
    }
  }
}

// 登录函数
export async function login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
  try {
    const response = await apiService.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);
    
    // 如果登录成功，存储认证信息
    if (response.code === 0 && response.data) {
      const { access_token, refresh_token, expires_in, user } = response.data;
      
      if (access_token) {
        SecureTokenStorage.setAccessToken(access_token, expires_in);
        SecureTokenStorage.setRefreshToken(refresh_token);
        SecureTokenStorage.setUserInfo(user);
      }
    }
    
    return response;
  } catch (error: any) {
    // 特殊处理404错误，提供友好的错误信息
    if (error.response?.status === 404) {
      return {
        code: 404,
        message: '登录服务暂时不可用，请稍后重试或联系管理员',
        data: {} as AuthResponse,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    // 处理其他网络错误
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '登录失败，请稍后重试',
        data: {} as AuthResponse,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    // 处理网络连接错误
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: {} as AuthResponse,
      meta: {
        timestamp: Date.now(),
        request_id: ''
      }
    };
  }
}

// 注册函数
export async function register(userData: RegisterRequest): Promise<ApiResponse<void>> {
  try {
    return await apiService.post<void>(API_ENDPOINTS.AUTH.REGISTER, userData);
  } catch (error: any) {
    // 处理注册错误
    if (error.response?.status === 404) {
      return {
        code: 404,
        message: '注册服务暂时不可用，请稍后重试或联系管理员',
        data: undefined,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '注册失败，请稍后重试',
        data: undefined,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: undefined,
      meta: {
        timestamp: Date.now(),
        request_id: ''
      }
    };
  }
}

// 登出函数
export async function logout(): Promise<void> {
  try {
    // 调用后端登出接口
    const token = SecureTokenStorage.getAccessToken();
    if (token) {
      await apiService.post(API_ENDPOINTS.AUTH.LOGOUT, {
        all: true // 撤销所有会话
      });
    }
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // 清除本地存储的令牌
    SecureTokenStorage.clearTokens();
  }
}

// 刷新令牌
export async function refreshToken(): Promise<AuthResponse | null> {
  try {
    const refreshToken = SecureTokenStorage.getRefreshToken();
    if (!refreshToken) {
      return null;
    }

    const response = await apiService.post<AuthResponse>(API_ENDPOINTS.AUTH.REFRESH, {
      refresh_token: refreshToken
    });

    // 检查业务状态码
    if (response.code !== 0) {
      console.error('Refresh token failed:', response.message);
      SecureTokenStorage.clearTokens();
      return null;
    }

    const authData = response.data;

    // 更新存储的令牌
    if (authData.access_token) {
      SecureTokenStorage.setAccessToken(authData.access_token, authData.expires_in);
      SecureTokenStorage.setRefreshToken(authData.refresh_token);
      SecureTokenStorage.setUserInfo(authData.user);
    }

    return authData;
  } catch (error) {
    console.error('Refresh token error:', error);
    // 刷新失败，清除所有令牌
    SecureTokenStorage.clearTokens();
    return null;
  }
}

// 获取当前用户信息
export async function getCurrentUser(): Promise<ApiResponse<User>> {
  try {
    return await apiService.post<User>(API_ENDPOINTS.AUTH.ME, {});
  } catch (error: any) {
    // 处理获取用户信息错误
    if (error.response?.status === 401) {
      return {
        code: 401,
        message: '登录已过期，请重新登录',
        data: {} as User,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: error.response?.status || 500,
      message: error.response?.data?.message || '获取用户信息失败',
      data: {} as User,
      meta: {
        timestamp: Date.now(),
        request_id: error.response?.headers?.['x-request-id'] || ''
      }
    };
  }
}

// 获取本地存储的用户信息
export function getLocalUserInfo(): User | null {
  return SecureTokenStorage.getUserInfo();
}

// 检查是否已登录
export function isLoggedIn(): boolean {
  return SecureTokenStorage.isLoggedIn();
}

// 获取访问令牌
export function getAccessToken(): string | null {
  return SecureTokenStorage.getAccessToken();
}

// 清除认证信息
export function clearAuth(): void {
  SecureTokenStorage.clearTokens();
}

// 验证令牌是否有效
export async function validateToken(token: string): Promise<boolean> {
  try {
    // 临时设置token进行验证
    const originalToken = SecureTokenStorage.getAccessToken();
    SecureTokenStorage.setAccessToken(token, 3600); // 临时设置1小时过期
    
    const response = await getCurrentUser();
    
    // 恢复原始token
    if (originalToken) {
      SecureTokenStorage.setAccessToken(originalToken, 3600);
    } else {
      SecureTokenStorage.clearTokens();
    }
    
    return response.code === 0;
  } catch {
    return false;
  }
}

// 检查用户名是否可用
export async function checkUsernameAvailability(username: string): Promise<boolean> {
  try {
    // TODO: 实现检查用户名可用性的API
    // const response = await apiService.get(`/check-username?username=${username}`);
    // return response.data.available;
    return true;
  } catch (error) {
    console.error('Check username availability failed:', error);
    return false;
  }
}

// 检查邮箱是否可用
export async function checkEmailAvailability(email: string): Promise<boolean> {
  try {
    // TODO: 实现检查邮箱可用性的API
    // const response = await apiService.get(`/check-email?email=${email}`);
    // return response.data.available;
    return true;
  } catch (error) {
    console.error('Check email availability failed:', error);
    return false;
  }
}

// 导出安全存储类（用于测试）
export { SecureTokenStorage }; 