import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  PersonalStats
} from '../types/api';

// 统计分析服务类
export class AnalyticsService {
  // 获取个人统计数据
  static async getPersonalStats(uid: number): Promise<ApiResponse<PersonalStats>> {
    return await apiService.get<PersonalStats>(API_ENDPOINTS.ANALYTICS.PERSONAL, {
      params: { uid }
    });
  }

  // 获取趋势统计数据
  static async getTrendingStats(uid: number): Promise<ApiResponse<any>> {
    return await apiService.get<any>(API_ENDPOINTS.ANALYTICS.TRENDING, {
      params: { uid }
    });
  }
}

// 导出便捷方法
export const {
  getPersonalStats,
  getTrendingStats,
} = AnalyticsService;

export default AnalyticsService; 