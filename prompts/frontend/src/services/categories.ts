import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Category, 
  CategoryCreateRequest,
  CategoryUpdateRequest,
  CategoryTreeRequest
} from '../types/api';

// 分类服务类
export class CategoryService {
  // 创建分类
  static async createCategory(request: CategoryCreateRequest): Promise<ApiResponse<Category>> {
    return await apiService.post<Category>(API_ENDPOINTS.CATEGORIES.CREATE, request);
  }

  // 获取分类详情
  static async getCategory(id: number): Promise<ApiResponse<Category>> {
    return await apiService.get<Category>(`${API_ENDPOINTS.CATEGORIES.GET}?id=${id}`);
  }

  // 更新分类
  static async updateCategory(id: number, request: CategoryUpdateRequest): Promise<ApiResponse<Category>> {
    return await apiService.post<Category>(`${API_ENDPOINTS.CATEGORIES.UPDATE}?id=${id}`, request);
  }

  // 删除分类
  static async deleteCategory(id: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`${API_ENDPOINTS.CATEGORIES.DELETE}?id=${id}`);
  }

  // 获取分类树
  static async getCategoryTree(request?: CategoryTreeRequest): Promise<ApiResponse<Category[]>> {
    return await apiService.get<Category[]>(API_ENDPOINTS.CATEGORIES.TREE, {
      params: request
    });
  }

  // 获取分类列表（别名）
  static async getCategoryList(): Promise<ApiResponse<Category[]>> {
    return this.getCategoryTree();
  }

  // 获取全局分类
  static async getGlobalCategories(): Promise<ApiResponse<Category[]>> {
    return await apiService.get<Category[]>(API_ENDPOINTS.CATEGORIES.GLOBAL);
  }

  // 更新分类排序
  static async updateCategorySort(): Promise<ApiResponse<void>> {
    return await apiService.post<void>(API_ENDPOINTS.CATEGORIES.SORT);
  }
}

// 导出便捷方法
export const {
  createCategory,
  getCategory,
  updateCategory,
  deleteCategory,
  getCategoryTree,
  getCategoryList,
  getGlobalCategories,
  updateCategorySort,
} = CategoryService;

export default CategoryService; 