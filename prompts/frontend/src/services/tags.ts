import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Tag, 
  TagCreateRequest,
  TagUpdateRequest,
  TagListRequest,
  TagSearchRequest
} from '../types/api';

// 标签服务类
export class TagService {
  // 创建标签
  static async createTag(request: TagCreateRequest): Promise<ApiResponse<Tag>> {
    return await apiService.post<Tag>(API_ENDPOINTS.TAGS.CREATE, request);
  }

  // 获取标签详情
  static async getTag(id: number): Promise<ApiResponse<Tag>> {
    return await apiService.get<Tag>(`${API_ENDPOINTS.TAGS.GET}?id=${id}`);
  }

  // 更新标签
  static async updateTag(id: number, request: TagUpdateRequest): Promise<ApiResponse<Tag>> {
    return await apiService.post<Tag>(`${API_ENDPOINTS.TAGS.UPDATE}?id=${id}`, request);
  }

  // 删除标签
  static async deleteTag(id: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`${API_ENDPOINTS.TAGS.DELETE}?id=${id}`);
  }

  // 获取标签列表
  static async getTagList(request?: TagListRequest): Promise<ApiResponse<{ list: Tag[]; total: number }>> {
    return await apiService.get<{ list: Tag[]; total: number }>(API_ENDPOINTS.TAGS.LIST, {
      params: request
    });
  }

  // 获取全局标签
  static async getGlobalTags(): Promise<ApiResponse<Tag[]>> {
    return await apiService.get<Tag[]>(API_ENDPOINTS.TAGS.GLOBAL);
  }

  // 搜索标签
  static async searchTags(request: TagSearchRequest): Promise<ApiResponse<Tag[]>> {
    return await apiService.get<Tag[]>(API_ENDPOINTS.TAGS.SEARCH, {
      params: request
    });
  }

  // 获取热门标签
  static async getPopularTags(limit: number = 10): Promise<ApiResponse<Tag[]>> {
    return await apiService.get<Tag[]>(API_ENDPOINTS.TAGS.POPULAR, {
      params: { limit }
    });
  }
}

// 导出便捷方法
export const {
  createTag,
  getTag,
  updateTag,
  deleteTag,
  getTagList,
  getGlobalTags,
  searchTags,
  getPopularTags,
} = TagService;

export default TagService; 