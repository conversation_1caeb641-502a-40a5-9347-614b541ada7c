import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Prompt
} from '../types/api';

// 版本管理服务类
export class VersionService {
  // 获取版本历史
  static async getVersionHistory(uid: number, promptId: number): Promise<ApiResponse<{ versions: any[] }>> {
    return await apiService.get<{ versions: any[] }>(API_ENDPOINTS.VERSION.HISTORY, {
      params: { uid, prompt_id: promptId }
    });
  }

  // 比较版本
  static async compareVersions(uid: number, promptId: number, version1: number, version2: number): Promise<ApiResponse<any>> {
    return await apiService.get<any>(API_ENDPOINTS.VERSION.COMPARE, {
      params: { uid, prompt_id: promptId, version1, version2 }
    });
  }

  // 回滚版本
  static async rollbackVersion(uid: number, promptId: number, version: number): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(API_ENDPOINTS.VERSION.ROLLBACK, {
      uid, prompt_id: promptId, version
    });
  }
}

// 导出便捷方法
export const {
  getVersionHistory,
  compareVersions,
  rollbackVersion,
} = VersionService;

export default VersionService; 