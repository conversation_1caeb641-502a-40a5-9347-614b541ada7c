import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Prompt, 
  Share
} from '../types/api';

// 分享服务类
export class ShareService {
  // 创建分享
  static async createShare(
    uid: number, 
    promptId: number, 
    shareType: 'link' | 'password',
    options: {
      expireAt?: string;
      password?: string;
      permissions?: string[];
    } = {}
  ): Promise<ApiResponse<Share>> {
    return await apiService.post<Share>(API_ENDPOINTS.SHARE.CREATE, {
      uid,
      prompt_id: promptId,
      share_type: shareType,
      ...options
    });
  }

  // 获取分享详情
  static async getShareDetail(shareCode: string, password?: string): Promise<ApiResponse<Prompt>> {
    return await apiService.get<Prompt>(API_ENDPOINTS.SHARE.DETAIL, {
      params: { share_code: shareCode, password }
    });
  }
}

// 导出便捷方法
export const {
  createShare,
  getShareDetail,
} = ShareService;

export default ShareService; 