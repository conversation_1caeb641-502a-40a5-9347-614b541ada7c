import { apiService, API_ENDPOINTS } from '../utils/request';
import type { 
  ApiResponse,
  Prompt, 
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptListRequest,
  SearchRequest
} from '../types/api';

// 提示词服务类
export class PromptService {
  // 创建提示词
  static async createPrompt(request: PromptCreateRequest): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(API_ENDPOINTS.PROMPTS.CREATE, request);
  }

  // 保存草稿
  static async saveDraft(request: PromptCreateRequest): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(API_ENDPOINTS.PROMPTS.DRAFT_SAVE, request);
  }

  // 更新草稿
  static async updateDraft(id: number, request: PromptUpdateRequest): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(`${API_ENDPOINTS.PROMPTS.DRAFT_UPDATE}?id=${id}`, request);
  }

  // 发布提示词
  static async publishPrompt(id: number, request: PromptUpdateRequest): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(`${API_ENDPOINTS.PROMPTS.PUBLISH}?id=${id}`, request);
  }

  // 获取提示词详情
  static async getPrompt(id: number): Promise<ApiResponse<Prompt>> {
    return await apiService.get<Prompt>(`${API_ENDPOINTS.PROMPTS.GET}?id=${id}`);
  }

  // 获取提示词详情（别名）
  static async getPromptDetail(id: number): Promise<ApiResponse<Prompt>> {
    return this.getPrompt(id);
  }

  // 更新提示词
  static async updatePrompt(id: number, request: PromptUpdateRequest): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(`${API_ENDPOINTS.PROMPTS.UPDATE}?id=${id}`, request);
  }

  // 删除提示词
  static async deletePrompt(id: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`${API_ENDPOINTS.PROMPTS.DELETE}?id=${id}`);
  }

  // 获取提示词列表
  static async getPromptList(request: PromptListRequest): Promise<ApiResponse<{ list: Prompt[]; total: number }>> {
    return await apiService.get<{ list: Prompt[]; total: number }>(API_ENDPOINTS.PROMPTS.LIST, {
      params: request
    });
  }

  // 获取公开提示词
  static async getPublicPrompts(request: PromptListRequest): Promise<ApiResponse<{ list: Prompt[]; total: number }>> {
    return await apiService.get<{ list: Prompt[]; total: number }>(API_ENDPOINTS.PROMPTS.PUBLIC, {
      params: request
    });
  }

  // 搜索提示词
  static async searchPrompts(request: SearchRequest): Promise<ApiResponse<{ list: Prompt[]; total: number }>> {
    return await apiService.get<{ list: Prompt[]; total: number }>(API_ENDPOINTS.PROMPTS.SEARCH, {
      params: request
    });
  }

  // 使用提示词
  static async usePrompt(id: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`${API_ENDPOINTS.PROMPTS.USE}?id=${id}`);
  }

  // 复制提示词
  static async copyPrompt(id: number): Promise<ApiResponse<Prompt>> {
    return await apiService.post<Prompt>(`${API_ENDPOINTS.PROMPTS.COPY}?id=${id}`);
  }
}

// 导出便捷方法
export const {
  createPrompt,
  saveDraft,
  updateDraft,
  publishPrompt,
  getPrompt,
  getPromptDetail,
  updatePrompt,
  deletePrompt,
  getPromptList,
  getPublicPrompts,
  searchPrompts,
  usePrompt,
  copyPrompt,
} = PromptService;

export default PromptService; 