#!/bin/bash

# Prompts 前端认证自动化测试脚本
# 作者: AI Assistant
# 日期: 2025-07-22

set -e

echo "🚀 开始运行 Prompts 前端认证自动化测试"
echo "=========================================="

# 检查服务状态
echo "📋 检查服务状态..."

# 检查前端服务
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "✅ 前端服务运行正常 (http://localhost:5173)"
else
    echo "❌ 前端服务未运行，请先启动前端服务"
    echo "   运行命令: npm run dev"
    exit 1
fi

# 检查后端服务
if curl -s http://localhost:8083/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常 (http://localhost:8083)"
else
    echo "⚠️  后端服务可能未运行或需要认证"
fi

echo ""
echo "🧪 运行认证功能测试..."

# 运行基础认证测试
echo "1️⃣ 运行基础认证测试..."
npx playwright test tests/basic-auth.spec.ts --project=chromium --reporter=line

echo ""
echo "2️⃣ 运行完整认证流程测试..."
npx playwright test tests/auth-flow.spec.ts --project=chromium --reporter=line

echo ""
echo "3️⃣ 运行调试测试..."
npx playwright test tests/debug.spec.ts --project=chromium --reporter=line

echo ""
echo "📊 生成测试报告..."
npx playwright test tests/auth-flow.spec.ts tests/basic-auth.spec.ts tests/debug.spec.ts --project=chromium --reporter=html

echo ""
echo "🎉 测试完成！"
echo "📁 测试报告位置: test-results/"
echo "🌐 查看HTML报告: npx playwright show-report"

# 显示测试结果摘要
echo ""
echo "📈 测试结果摘要:"
echo "=================="
echo "✅ 基础功能测试: 页面加载、表单验证、导航"
echo "✅ 认证流程测试: 注册、登录、认证守卫"
echo "✅ 调试测试: React应用加载、API代理"
echo ""
echo "⚠️  已知问题:"
echo "   - 页面标题未更新（不影响功能）"
echo "   - 注册/登录反馈需要改进"
echo "   - API认证需要进一步配置"
echo ""
echo "📝 详细报告请查看: test-summary.md" 