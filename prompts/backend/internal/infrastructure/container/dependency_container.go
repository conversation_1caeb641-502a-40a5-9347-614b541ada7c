package container

import (
	"context"
	gormdb "gitee.com/heiyee/platforms/pkg/db"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/prompts-backend/internal/application/category/service"
	promptService "gitee.com/heiyee/platforms/prompts-backend/internal/application/prompt/service"
	tagService "gitee.com/heiyee/platforms/prompts-backend/internal/application/tag/service"
	"gitee.com/heiyee/platforms/prompts-backend/internal/infrastructure/config"
	"gitee.com/heiyee/platforms/prompts-backend/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/prompts-backend/internal/infrastructure/persistence/repository"
	"gitee.com/heiyee/platforms/prompts-backend/internal/interfaces/http/handlers"

	"gorm.io/gorm"
)

// InfrastructureContainer 提示词系统基础设施容器
// 负责管理数据库、日志、gRPC客户端等基础设施组件
// 【依赖方向说明】
// - 基础设施层（infrastructure）只能依赖 domain 层，不得依赖 application 层及以上。
// - 该层仅为技术实现，禁止反向依赖业务逻辑。

type InfrastructureContainer struct {
	AppConfig   *config.Config
	Logger      logiface.Logger
	DB          *gorm.DB
	UserClient  *external.UserServiceClient
	IDGenClient *external.IDGeneratorClient
}

// ApplicationContainer 提示词系统应用服务容器
// 负责管理所有应用服务
// 【依赖方向说明】
// - 应用层（application）只能依赖 domain 层，不得依赖 interface 层及基础设施实现（仅依赖接口）。
// - 该层仅协调用例和业务流程，不包含具体技术实现。

type ApplicationContainer struct {
	TagService      *tagService.TagApplicationService
	PromptService   *promptService.PromptApplicationService
	CategoryService *service.CategoryApplicationService
}

// InterfaceContainer 提示词系统接口容器
// 负责管理HTTP/gRPC处理器等
// 【依赖方向说明】
// - 接口层（interface）可依赖 application 层和 domain 层，不得被下层依赖。
// - 该层仅作为系统入口，负责协议适配和请求分发。

type InterfaceContainer struct {
	TagHandler      *handlers.TagHandler
	PromptHandler   *handlers.PromptHandler
	CategoryHandler *handlers.CategoryHandler
}

// DependencyContainer 提示词系统主依赖注入容器
// 组合各个子容器，提供统一的依赖管理
// 【依赖方向说明】
// - 依赖注入容器作为组合根（Composition Root），仅在启动阶段组装各层依赖。
// - 任何业务代码不得直接依赖容器本身，避免依赖反转。

type DependencyContainer struct {
	Infrastructure *InfrastructureContainer
	Applications   *ApplicationContainer
	Interfaces     *InterfaceContainer
}

// NewDependencyContainer 创建依赖注入容器（只做结构体初始化，不做资源连接）
func NewDependencyContainer(appConfig *config.Config, logger logiface.Logger) *DependencyContainer {
	infra := &InfrastructureContainer{
		AppConfig:   appConfig,
		Logger:      logger,
		DB:          nil, // 延迟初始化
		UserClient:  nil, // 延迟初始化
		IDGenClient: nil, // 延迟初始化
	}
	return &DependencyContainer{
		Infrastructure: infra,
		Applications:   &ApplicationContainer{},
		Interfaces:     &InterfaceContainer{},
	}
}

// Initialize 初始化所有依赖（推荐在main中调用）
func (c *DependencyContainer) Initialize(ctx context.Context) error {
	// 1. 初始化数据库（可选，失败时继续）
	if c.Infrastructure.DB == nil {
		gormLogger := gormdb.NewGormLoggerAdapter(c.Infrastructure.Logger, 2) // 2=Info
		// 转换配置格式
		mysqlConfig := &gormdb.MySQLConfig{
			Host:            c.Infrastructure.AppConfig.Database.MySQL.Host,
			Port:            c.Infrastructure.AppConfig.Database.MySQL.Port,
			Database:        c.Infrastructure.AppConfig.Database.MySQL.Database,
			Username:        c.Infrastructure.AppConfig.Database.MySQL.Username,
			Password:        c.Infrastructure.AppConfig.Database.MySQL.Password,
			Charset:         c.Infrastructure.AppConfig.Database.MySQL.Charset,
			ParseTime:       c.Infrastructure.AppConfig.Database.MySQL.ParseTime,
			Loc:             c.Infrastructure.AppConfig.Database.MySQL.Loc,
			MaxOpenConns:    c.Infrastructure.AppConfig.Database.MySQL.MaxOpenConns,
			MaxIdleConns:    c.Infrastructure.AppConfig.Database.MySQL.MaxIdleConns,
			ConnMaxLifetime: c.Infrastructure.AppConfig.Database.MySQL.ConnMaxLifetime,
			Params:          c.Infrastructure.AppConfig.Database.MySQL.Params,
		}

		// 尝试连接数据库，失败时记录警告但继续
		func() {
			defer func() {
				if r := recover(); r != nil {
					c.Infrastructure.Logger.Warn(ctx, "Failed to connect to database, continuing without database",
						logiface.Any("error", r))
				}
			}()
			db := gormdb.NewDB(mysqlConfig, gormLogger)
			c.Infrastructure.DB = db
			c.Infrastructure.Logger.Info(ctx, "Database connection established")
		}()
	}

	// 2. 初始化gRPC客户端
	if c.Infrastructure.UserClient == nil {
		c.Infrastructure.UserClient = external.NewUserServiceClient(c.Infrastructure.Logger)
	}
	if c.Infrastructure.IDGenClient == nil {
		c.Infrastructure.IDGenClient = external.NewIDGeneratorClient(c.Infrastructure.Logger)
	}

	// 3. 初始化仓储
	tagRepo := repository.NewTagRepository(c.Infrastructure.DB)
	promptRepo := repository.NewPromptRepository(c.Infrastructure.DB, c.GetLogger())
	categoryRepo := repository.NewCategoryRepository(c.Infrastructure.DB)

	// 4. 初始化应用服务
	c.Applications.TagService = tagService.NewTagApplicationService(tagRepo)
	c.Applications.PromptService = promptService.NewPromptApplicationService(promptRepo)
	c.Applications.CategoryService = service.NewCategoryApplicationService(categoryRepo)

	// 5. 初始化 handler
	c.Interfaces.TagHandler = handlers.NewTagHandler(c.Applications.TagService)
	c.Interfaces.PromptHandler = handlers.NewPromptHandler(c.Applications.PromptService)
	c.Interfaces.CategoryHandler = handlers.NewCategoryHandler(c.Applications.CategoryService)

	return nil
}

// Close 关闭所有资源（如数据库连接、gRPC连接等）
func (c *DependencyContainer) Close() error {
	if c.Infrastructure.DB != nil {
		sqlDB, err := c.Infrastructure.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	// 可扩展关闭gRPC客户端等
	return nil
}

// GetDB 获取数据库实例
func (c *DependencyContainer) GetDB() *gorm.DB {
	return c.Infrastructure.DB
}

// GetLogger 获取日志实例
func (c *DependencyContainer) GetLogger() logiface.Logger {
	return c.Infrastructure.Logger
}

// GetUserClient 获取用户服务gRPC客户端
func (c *DependencyContainer) GetUserClient() *external.UserServiceClient {
	return c.Infrastructure.UserClient
}

// GetIDGenClient 获取ID生成器gRPC客户端
func (c *DependencyContainer) GetIDGenClient() *external.IDGeneratorClient {
	return c.Infrastructure.IDGenClient
}
