package external

import (
	"context"
	"fmt"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// IDGeneratorClient ID生成器gRPC客户端
type IDGeneratorClient struct {
	logger logiface.Logger
}

// NewIDGeneratorClient 创建ID生成器客户端
func NewIDGeneratorClient(logger logiface.Logger) *IDGeneratorClient {
	return &IDGeneratorClient{
		logger: logger,
	}
}

// GenerateID 生成ID
func (c *IDGeneratorClient) GenerateID(ctx context.Context) (string, error) {
	// 这里应该调用实际的gRPC服务
	// 暂时返回模拟数据
	return "123456789", nil
}

// GenerateBatchIDs 批量生成ID
func (c *IDGeneratorClient) GenerateBatchIDs(ctx context.Context, count int) ([]string, error) {
	// 这里应该调用实际的gRPC服务
	// 暂时返回模拟数据
	ids := make([]string, count)
	for i := 0; i < count; i++ {
		ids[i] = fmt.Sprintf("123456789%d", i)
	}
	return ids, nil
}
