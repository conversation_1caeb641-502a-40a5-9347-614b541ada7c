package routes

import (
	"time"

	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/prompts-backend/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/prompts-backend/internal/interfaces/http/handlers"

	"github.com/gin-gonic/gin"
)

// 全局URL前缀常量
const (
	APIPrefix = "/api/prompts" // 提示词系统API前缀
)

// RouterConfig 路由配置 - 统一提示词系统路由管理
type RouterConfig struct {
	TagHandler      *handlers.TagHandler
	PromptHandler   *handlers.PromptHandler
	CategoryHandler *handlers.CategoryHandler
	ServiceName     string
	AppLogger       logiface.Logger
	Container       *container.DependencyContainer
}

// SetupRoutes 设置所有提示词系统路由 (前后端分离架构)
func SetupRoutes(r *gin.RouterGroup, config *RouterConfig) {
	httpmiddleware.SetupCommonMiddleware(r, &httpmiddleware.MiddlewareConfig{
		ServiceName:           config.ServiceName,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      false,
		EnableTimeout:         true,
		RequestTimeout:        10 * time.Second,
		EnableTraceID:         true,
		Logger:                config.AppLogger,
		EnableAccessLog:       true,
		EnableUserInfo:        true,
		UserInfoProvider:      &UserInfoProviderAdapter{},
		AppInfoProvider:       &AppInfoProviderAdapter{},
	})

	// 全局校验登录
	r.Use(httpmiddleware.RequireAuthedMiddleware())

	// 提示词系统API路由组
	promptsAPI := r.Group(APIPrefix)
	{
		// 标签管理
		if config.TagHandler != nil {
			setupTagRoutes(promptsAPI, config.TagHandler)
		}

		// 提示词管理
		if config.PromptHandler != nil {
			setupPromptRoutes(promptsAPI, config.PromptHandler)
		}

		// 分类管理
		if config.CategoryHandler != nil {
			setupCategoryRoutes(promptsAPI, config.CategoryHandler)
		}
	}
}

// setupTagRoutes 设置标签管理路由
// 路径: /api/prompts/tags/*
func setupTagRoutes(rg *gin.RouterGroup, handler *handlers.TagHandler) {
	tags := rg.Group("/tags")
	{
		// 基础CRUD操作
		tags.POST("/create", handler.CreateTag)
		tags.POST("/update", handler.UpdateTag)
		tags.POST("/delete", handler.DeleteTag)
		tags.GET("/detail", handler.GetTag)
		tags.POST("/get", handler.GetTag)
		tags.GET("/list", handler.GetTagList)
		tags.POST("/list", handler.GetTagList)
		tags.GET("/search", handler.SearchTags)
		tags.GET("/popular", handler.GetPopularTags)
		tags.GET("/stats", handler.GetTagStats)
		tags.POST("/batch/create", handler.BatchCreateTags)
		tags.POST("/batch/delete", handler.BatchDeleteTags)
		tags.GET("/global", handler.GetGlobalTags)
	}
}

// setupPromptRoutes 设置提示词管理路由
// 路径: /api/prompts/prompts/*
func setupPromptRoutes(rg *gin.RouterGroup, handler *handlers.PromptHandler) {
	prompts := rg.Group("/prompts")
	{
		// 基础CRUD操作
		prompts.POST("/create", handler.CreatePrompt)
		prompts.POST("/update", handler.UpdatePrompt)
		prompts.POST("/delete", handler.DeletePrompt)
		prompts.GET("/detail", handler.GetPrompt)
		prompts.POST("/get", handler.GetPrompt)
		prompts.GET("/list", handler.GetPromptList)
		prompts.POST("/list", handler.GetPromptList)
		prompts.POST("/use", handler.UsePrompt)

		// 草稿管理
		prompts.POST("/draft/save", handler.SaveDraft)
		prompts.POST("/draft/update", handler.UpdateDraft)
		prompts.POST("/publish", handler.PublishPrompt)

		// 公开接口
		prompts.GET("/public", handler.GetPublicPrompts)
		prompts.GET("/search", handler.SearchPrompts)
	}
}

// setupCategoryRoutes 设置分类管理路由
// 路径: /api/prompts/categories/*
func setupCategoryRoutes(rg *gin.RouterGroup, handler *handlers.CategoryHandler) {
	categories := rg.Group("/categories")
	{
		// 基础CRUD操作
		categories.POST("/create", handler.CreateCategory)
		categories.POST("/update", handler.UpdateCategory)
		categories.POST("/delete", handler.DeleteCategory)
		categories.GET("/detail", handler.GetCategory)
		categories.POST("/get", handler.GetCategory)
		categories.GET("/list", handler.GetCategoryList)
		categories.POST("/list", handler.GetCategoryList)
		categories.GET("/tree", handler.GetCategoryTree)
		categories.POST("/sort", handler.UpdateCategorySort)

		// 公开接口
		categories.GET("/global", handler.GetGlobalCategories)
	}
}
