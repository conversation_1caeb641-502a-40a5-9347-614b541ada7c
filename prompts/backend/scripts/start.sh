#!/bin/bash

# 提示词系统启动脚本
# 参考 email 项目的启动方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 服务配置
SERVICE_NAME="platforms-prompts"
BUILD_DIR="./bin"
MAIN_FILE="./cmd/main.go"
CONFIG_DIR="./configs"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 创建构建目录
create_build_dir() {
    log_info "创建构建目录..."
    mkdir -p $BUILD_DIR
}

# 下载依赖
download_dependencies() {
    log_info "下载 Go 依赖..."
    go mod download
    go mod tidy
}

# 构建服务
build_service() {
    log_info "构建 $SERVICE_NAME 服务..."
    
    # 设置构建参数
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S_UTC')
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    # 构建命令 - 使用当前平台
    CGO_ENABLED=0 go build \
        -ldflags "-X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT -X main.GitBranch=$GIT_BRANCH" \
        -o $BUILD_DIR/$SERVICE_NAME \
        $MAIN_FILE
    
    if [ $? -eq 0 ]; then
        log_info "构建成功: $BUILD_DIR/$SERVICE_NAME"
    else
        log_error "构建失败"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -d "$CONFIG_DIR" ]; then
        log_warn "配置目录不存在: $CONFIG_DIR"
        log_info "创建配置目录..."
        mkdir -p $CONFIG_DIR
    fi
    
    if [ ! -f "$CONFIG_DIR/app.toml" ]; then
        log_warn "配置文件不存在: $CONFIG_DIR/app.toml"
        log_info "请确保配置文件存在"
    else
        log_info "配置文件检查完成"
    fi
}

# 启动服务
start_service() {
    log_info "启动 $SERVICE_NAME 服务..."
    
    # 检查可执行文件
    if [ ! -f "$BUILD_DIR/$SERVICE_NAME" ]; then
        log_error "可执行文件不存在: $BUILD_DIR/$SERVICE_NAME"
        log_info "请先运行构建步骤"
        exit 1
    fi
    
    # 设置环境变量
    export USE_NACOS=${USE_NACOS:-"true"}
    export NACOS_DATA_ID=${NACOS_DATA_ID:-"platforms-prompts"}
    export APP_ENV=${APP_ENV:-"dev"}
    
    # 启动服务
    cd $BUILD_DIR
    ./$SERVICE_NAME
}

# 显示帮助信息
show_help() {
    echo "提示词系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建服务"
    echo "  start     启动服务"
    echo "  run       构建并启动服务"
    echo "  clean     清理构建文件"
    echo "  help      显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  USE_NACOS      是否使用 Nacos 配置中心 (默认: true)"
    echo "  NACOS_DATA_ID  Nacos 配置 ID (默认: platforms-prompts)"
    echo "  APP_ENV        应用环境 (默认: dev)"
    echo ""
}

# 清理构建文件
clean_build() {
    log_info "清理构建文件..."
    rm -rf $BUILD_DIR
    log_info "清理完成"
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_dependencies
            create_build_dir
            download_dependencies
            build_service
            ;;
        "start")
            check_config
            start_service
            ;;
        "run")
            check_dependencies
            create_build_dir
            download_dependencies
            build_service
            check_config
            start_service
            ;;
        "clean")
            clean_build
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@" 