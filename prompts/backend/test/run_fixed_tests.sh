#!/bin/bash

# 修复版API测试运行脚本
# 用于运行修复和改进后的API接口测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本信息
SCRIPT_NAME="修复版API测试运行脚本"
VERSION="2.0.0"
AUTHOR="AI Assistant"

# 默认配置
TEST_MODE="all"
VERBOSE=false
COVERAGE=false
PERFORMANCE=false
BENCHMARK=false
CLEAN=false

# 显示帮助信息
show_help() {
    echo -e "${BLUE}${SCRIPT_NAME} v${VERSION}${NC}"
    echo -e "${BLUE}作者: ${AUTHOR}${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE        测试模式 (all|simple|enhanced|fixed|unit|integration)"
    echo "  -v, --verbose          详细输出"
    echo "  -c, --coverage         生成覆盖率报告"
    echo "  -p, --performance      运行性能测试"
    echo "  -b, --benchmark        运行基准测试"
    echo "  --clean                清理测试缓存"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "测试模式说明:"
    echo "  all        运行所有测试 (默认)"
    echo "  simple     运行简化版测试"
    echo "  enhanced   运行增强版测试"
    echo "  fixed      运行修复版测试"
    echo "  unit       运行单元测试"
    echo "  integration 运行集成测试"
    echo ""
    echo "示例:"
    echo "  $0                    # 运行所有测试"
    echo "  $0 -m fixed -v        # 运行修复版测试，详细输出"
    echo "  $0 -c -p              # 生成覆盖率报告并运行性能测试"
    echo "  $0 --clean            # 清理测试缓存"
}

# 显示脚本头部
show_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  ${SCRIPT_NAME} v${VERSION}${NC}"
    echo -e "${BLUE}  作者: ${AUTHOR}${NC}"
    echo -e "${BLUE}  时间: $(date)${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

# 显示测试开始信息
show_test_start() {
    local mode=$1
    echo -e "${YELLOW}开始运行 ${mode} 测试...${NC}"
    echo -e "${YELLOW}测试时间: $(date)${NC}"
    echo ""
}

# 显示测试结果
show_test_result() {
    local exit_code=$1
    local test_name=$2
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✓ ${test_name} 测试通过${NC}"
    else
        echo -e "${RED}✗ ${test_name} 测试失败${NC}"
    fi
}

# 清理测试缓存
clean_test_cache() {
    echo -e "${YELLOW}清理测试缓存...${NC}"
    
    # 清理Go测试缓存
    go clean -testcache 2>/dev/null || true
    
    # 清理覆盖率文件
    rm -f coverage.out coverage.html 2>/dev/null || true
    
    # 清理测试报告
    rm -f test-report.txt 2>/dev/null || true
    
    echo -e "${GREEN}测试缓存清理完成${NC}"
    echo ""
}

# 运行简化版测试
run_simple_tests() {
    echo -e "${BLUE}运行简化版API测试...${NC}"
    
    local cmd="go test -v . -run 'TestHealthCheck|TestPromptAPIs|TestCategoryAPIs|TestTagAPIs|TestErrorHandling|TestResponseFormat'"
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "简化版API"
    else
        show_test_result 1 "简化版API"
        return 1
    fi
}

# 运行增强版测试
run_enhanced_tests() {
    echo -e "${BLUE}运行增强版API测试...${NC}"
    
    local cmd="go test -v . -run TestEnhancedAPITestSuite"
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "增强版API"
    else
        show_test_result 1 "增强版API"
        return 1
    fi
}

# 运行修复版测试
run_fixed_tests() {
    echo -e "${BLUE}运行修复版API测试...${NC}"
    
    local cmd="go test -v . -run TestFixedAPITestSuite"
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "修复版API"
    else
        show_test_result 1 "修复版API"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    echo -e "${BLUE}运行所有API测试...${NC}"
    
    local cmd="go test -v ."
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "所有API"
    else
        show_test_result 1 "所有API"
        return 1
    fi
}

# 运行覆盖率测试
run_coverage_tests() {
    echo -e "${BLUE}运行覆盖率测试...${NC}"
    
    local cmd="go test -v -coverprofile=coverage.out -covermode=atomic ."
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "覆盖率"
        
        # 生成HTML覆盖率报告
        if command -v go tool cover >/dev/null 2>&1; then
            echo -e "${BLUE}生成HTML覆盖率报告...${NC}"
            go tool cover -html=coverage.out -o coverage.html
            echo -e "${GREEN}覆盖率报告已生成: coverage.html${NC}"
        fi
        
        # 显示覆盖率统计
        echo -e "${BLUE}覆盖率统计:${NC}"
        go tool cover -func=coverage.out | tail -1
    else
        show_test_result 1 "覆盖率"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    echo -e "${BLUE}运行性能测试...${NC}"
    
    local cmd="go test -v -bench=. -benchmem ."
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "性能"
    else
        show_test_result 1 "性能"
        return 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    echo -e "${BLUE}运行基准测试...${NC}"
    
    local cmd="go test -v -bench=BenchmarkAPIs -benchmem ."
    
    if [ "$VERBOSE" = true ]; then
        echo "执行命令: $cmd"
    fi
    
    if eval $cmd; then
        show_test_result 0 "基准"
    else
        show_test_result 1 "基准"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    echo -e "${BLUE}生成测试报告...${NC}"
    
    local report_file="test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "提示词系统API接口测试报告"
        echo "生成时间: $(date)"
        echo "测试模式: $TEST_MODE"
        echo "========================================"
        echo ""
        
        echo "测试文件:"
        ls -la *.go | grep -E "(test|mock)" || echo "无测试文件"
        echo ""
        
        echo "测试统计:"
        go test -v . 2>&1 | grep -E "(PASS|FAIL|RUN)" | tail -10 || echo "无法获取测试统计"
        echo ""
        
        if [ -f coverage.out ]; then
            echo "覆盖率统计:"
            go tool cover -func=coverage.out | tail -1 || echo "无法获取覆盖率统计"
            echo ""
        fi
        
        echo "测试完成时间: $(date)"
    } > "$report_file"
    
    echo -e "${GREEN}测试报告已生成: $report_file${NC}"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                TEST_MODE="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--coverage)
                COVERAGE=true
                shift
                ;;
            -p|--performance)
                PERFORMANCE=true
                shift
                ;;
            -b|--benchmark)
                BENCHMARK=true
                shift
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查环境
check_environment() {
    echo -e "${BLUE}检查测试环境...${NC}"
    
    # 检查Go环境
    if ! command -v go >/dev/null 2>&1; then
        echo -e "${RED}错误: 未找到Go环境${NC}"
        exit 1
    fi
    
    # 检查Go版本
    local go_version=$(go version | awk '{print $3}')
    echo -e "${GREEN}Go版本: $go_version${NC}"
    
    # 检查测试目录
    if [ ! -f go.mod ]; then
        echo -e "${RED}错误: 未找到go.mod文件，请确保在正确的测试目录中${NC}"
        exit 1
    fi
    
    # 检查依赖
    echo -e "${BLUE}检查测试依赖...${NC}"
    go mod tidy
    
    echo -e "${GREEN}环境检查完成${NC}"
    echo ""
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 显示脚本头部
    show_header
    
    # 检查环境
    check_environment
    
    # 清理测试缓存
    if [ "$CLEAN" = true ]; then
        clean_test_cache
        exit 0
    fi
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据测试模式运行测试
    case $TEST_MODE in
        simple)
            show_test_start "简化版"
            run_simple_tests
            ;;
        enhanced)
            show_test_start "增强版"
            run_enhanced_tests
            ;;
        fixed)
            show_test_start "修复版"
            run_fixed_tests
            ;;
        unit)
            show_test_start "单元"
            run_simple_tests
            ;;
        integration)
            show_test_start "集成"
            run_enhanced_tests
            ;;
        all)
            show_test_start "所有"
            run_all_tests
            ;;
        *)
            echo -e "${RED}错误: 未知的测试模式 '$TEST_MODE'${NC}"
            show_help
            exit 1
            ;;
    esac
    
    # 运行覆盖率测试
    if [ "$COVERAGE" = true ]; then
        run_coverage_tests
    fi
    
    # 运行性能测试
    if [ "$PERFORMANCE" = true ]; then
        run_performance_tests
    fi
    
    # 运行基准测试
    if [ "$BENCHMARK" = true ]; then
        run_benchmark_tests
    fi
    
    # 计算执行时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 生成测试报告
    generate_test_report
    
    # 显示测试总结
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}测试执行完成${NC}"
    echo -e "${BLUE}执行时间: ${duration}秒${NC}"
    echo -e "${BLUE}测试模式: $TEST_MODE${NC}"
    echo -e "${BLUE}完成时间: $(date)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    echo ""
    echo -e "${GREEN}所有测试已成功完成！${NC}"
}

# 运行主函数
main "$@" 