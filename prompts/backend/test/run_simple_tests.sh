#!/bin/bash

# 提示词系统简化API测试运行脚本
# 作者: AI Assistant
# 日期: 2024-12-19

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_DIR="$PROJECT_ROOT/test"
LOG_DIR="$PROJECT_ROOT/logs"
REPORT_DIR="$PROJECT_ROOT/test-reports"

# 创建必要的目录
mkdir -p "$LOG_DIR"
mkdir -p "$REPORT_DIR"

# 日志文件
LOG_FILE="$LOG_DIR/simple_api_test_$(date +%Y%m%d_%H%M%S).log"
COVERAGE_FILE="$REPORT_DIR/simple_coverage_$(date +%Y%m%d_%H%M%S).out"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    提示词系统简化API测试开始执行${NC}"
echo -e "${BLUE}========================================${NC}"
echo "项目根目录: $PROJECT_ROOT"
echo "测试目录: $TEST_DIR"
echo "日志文件: $LOG_FILE"
echo "覆盖率报告: $COVERAGE_FILE"
echo ""

# 检查Go环境
echo -e "${YELLOW}检查Go环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go命令${NC}"
    exit 1
fi

GO_VERSION=$(go version)
echo -e "${GREEN}Go版本: $GO_VERSION${NC}"

# 检查测试文件
echo -e "${YELLOW}检查测试文件...${NC}"
if [ ! -f "$TEST_DIR/simple_api_test.go" ]; then
    echo -e "${RED}错误: 未找到简化API测试文件${NC}"
    exit 1
fi

if [ ! -f "$TEST_DIR/go.mod" ]; then
    echo -e "${RED}错误: 未找到测试模块文件${NC}"
    exit 1
fi

echo -e "${GREEN}测试文件检查通过${NC}"

# 进入测试目录并下载依赖
echo -e "${YELLOW}下载测试依赖...${NC}"
cd "$TEST_DIR"
go mod download
go mod tidy

# 运行简化测试
echo -e "${YELLOW}运行简化API测试...${NC}"
go test -v . \
    -coverprofile="$COVERAGE_FILE" \
    -covermode=atomic \
    -timeout=2m \
    -race \
    -vet=all \
    2>&1 | tee "$LOG_FILE"

TEST_EXIT_CODE=${PIPESTATUS[0]}

# 生成覆盖率报告
if [ -f "$COVERAGE_FILE" ]; then
    echo -e "${YELLOW}生成覆盖率报告...${NC}"
    go tool cover -html="$COVERAGE_FILE" -o "$REPORT_DIR/simple_coverage.html"
    go tool cover -func="$COVERAGE_FILE" > "$REPORT_DIR/simple_coverage.txt"
    
    # 显示覆盖率摘要
    echo -e "${BLUE}覆盖率摘要:${NC}"
    tail -n 1 "$REPORT_DIR/simple_coverage.txt"
fi

# 检查测试结果
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}    所有简化测试通过！${NC}"
    echo -e "${GREEN}========================================${NC}"
    
    # 显示测试统计
    echo -e "${BLUE}测试统计:${NC}"
    grep -E "(PASS|FAIL|RUN)" "$LOG_FILE" | tail -n 10
    
    # 显示测试覆盖的功能
    echo -e "${BLUE}测试覆盖的功能:${NC}"
    echo "✓ 健康检查API"
    echo "✓ 提示词管理API (列表、创建)"
    echo "✓ 分类管理API (列表)"
    echo "✓ 标签管理API (列表)"
    echo "✓ 错误处理测试 (404、405)"
    echo "✓ 响应格式验证"
    echo "✓ 性能基准测试"
    
    echo ""
    echo -e "${GREEN}测试报告已生成:${NC}"
    echo "  日志文件: $LOG_FILE"
    echo "  覆盖率报告: $REPORT_DIR/simple_coverage.html"
    echo "  覆盖率文本: $REPORT_DIR/simple_coverage.txt"
    
else
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}    简化测试失败！${NC}"
    echo -e "${RED}========================================${NC}"
    
    # 显示失败信息
    echo -e "${RED}失败的测试:${NC}"
    grep -A 5 -B 5 "FAIL" "$LOG_FILE" || true
    
    echo ""
    echo -e "${YELLOW}详细日志请查看: $LOG_FILE${NC}"
    exit 1
fi

# 性能测试（可选）
if [ "$1" = "--performance" ]; then
    echo -e "${YELLOW}运行性能测试...${NC}"
    go test -v . -bench=. -benchmem -run=^$ 2>&1 | tee "$LOG_DIR/simple_benchmark_$(date +%Y%m%d_%H%M%S).log"
fi

# 清理临时文件
echo -e "${YELLOW}清理临时文件...${NC}"
find "$TEST_DIR" -name "*.tmp" -delete 2>/dev/null || true

echo -e "${GREEN}简化测试执行完成！${NC}" 