package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// SimpleAPITestSuite 简化API测试套件
type SimpleAPITestSuite struct {
	router *gin.Engine
	server *httptest.Server
}

// NewSimpleAPITestSuite 创建简化API测试套件
func NewSimpleAPITestSuite() *SimpleAPITestSuite {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()

	// 添加基本路由用于测试
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"code":   0,
		})
	})

	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "test route working",
			"code":    0,
		})
	})

	// 模拟提示词API路由
	promptsAPI := router.Group("/api/prompts")
	{
		promptsAPI.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status": "prompts-api-healthy",
				"code":   0,
			})
		})

		// 模拟提示词列表接口
		promptsAPI.GET("/prompts/list", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 0,
				"data": []gin.H{
					{
						"id":          1,
						"title":       "测试提示词1",
						"content":     "这是测试内容1",
						"description": "测试描述1",
						"status":      "published",
						"visibility":  "public",
					},
					{
						"id":          2,
						"title":       "测试提示词2",
						"content":     "这是测试内容2",
						"description": "测试描述2",
						"status":      "draft",
						"visibility":  "private",
					},
				},
				"meta": gin.H{
					"page":  1,
					"size":  20,
					"total": 2,
				},
			})
		})

		// 模拟创建提示词接口
		promptsAPI.POST("/prompts/create", func(c *gin.Context) {
			var req map[string]interface{}
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusOK, gin.H{
					"code":    10001,
					"message": "参数错误",
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"code": 0,
				"data": gin.H{
					"id":          3,
					"title":       req["title"],
					"content":     req["content"],
					"description": req["description"],
					"status":      "draft",
					"visibility":  "private",
				},
			})
		})

		// 模拟分类列表接口
		promptsAPI.GET("/categories/list", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 0,
				"data": []gin.H{
					{
						"id":          1,
						"name":        "技术分类",
						"description": "技术相关分类",
						"parent_id":   0,
						"sort_order":  1,
					},
					{
						"id":          2,
						"name":        "写作分类",
						"description": "写作相关分类",
						"parent_id":   0,
						"sort_order":  2,
					},
				},
				"meta": gin.H{
					"page":  1,
					"size":  20,
					"total": 2,
				},
			})
		})

		// 模拟标签列表接口
		promptsAPI.GET("/tags/list", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 0,
				"data": []gin.H{
					{
						"id":          1,
						"name":        "AI",
						"color":       "#FF6B6B",
						"usage_count": 10,
					},
					{
						"id":          2,
						"name":        "编程",
						"color":       "#4ECDC4",
						"usage_count": 15,
					},
				},
				"meta": gin.H{
					"page":  1,
					"size":  20,
					"total": 2,
				},
			})
		})
	}

	// 创建测试服务器
	server := httptest.NewServer(router)

	return &SimpleAPITestSuite{
		router: router,
		server: server,
	}
}

// Close 关闭测试套件
func (suite *SimpleAPITestSuite) Close() {
	if suite.server != nil {
		suite.server.Close()
	}
}

// TestHealthCheck 测试健康检查
func TestHealthCheck(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	// 测试根健康检查
	t.Run("根健康检查", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/health")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, "healthy", response["status"])
		assert.Equal(t, float64(0), response["code"])
	})

	// 测试API健康检查
	t.Run("API健康检查", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/health")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, "prompts-api-healthy", response["status"])
		assert.Equal(t, float64(0), response["code"])
	})
}

// TestPromptAPIs 测试提示词相关API
func TestPromptAPIs(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	t.Run("获取提示词列表", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/prompts/list")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])
		assert.NotNil(t, response["data"])
		assert.NotNil(t, response["meta"])

		// 验证数据结构
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)

		// 验证第一个提示词
		prompt1 := data[0].(map[string]interface{})
		assert.Equal(t, float64(1), prompt1["id"])
		assert.Equal(t, "测试提示词1", prompt1["title"])
		assert.Equal(t, "published", prompt1["status"])
	})

	t.Run("创建提示词", func(t *testing.T) {
		createData := map[string]interface{}{
			"title":       "新提示词",
			"content":     "新提示词内容",
			"description": "新提示词描述",
		}

		jsonData, _ := json.Marshal(createData)
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])
		assert.NotNil(t, response["data"])

		// 验证返回的数据
		data := response["data"].(map[string]interface{})
		assert.Equal(t, "新提示词", data["title"])
		assert.Equal(t, "新提示词内容", data["content"])
		assert.Equal(t, "draft", data["status"])
	})

	t.Run("创建提示词参数错误", func(t *testing.T) {
		// 发送无效的JSON
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/create", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, float64(10001), response["code"])
		assert.Equal(t, "参数错误", response["message"])
	})
}

// TestCategoryAPIs 测试分类相关API
func TestCategoryAPIs(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	t.Run("获取分类列表", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/categories/list")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])
		assert.NotNil(t, response["data"])
		assert.NotNil(t, response["meta"])

		// 验证数据结构
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)

		// 验证第一个分类
		category1 := data[0].(map[string]interface{})
		assert.Equal(t, float64(1), category1["id"])
		assert.Equal(t, "技术分类", category1["name"])
		assert.Equal(t, float64(0), category1["parent_id"])
	})
}

// TestTagAPIs 测试标签相关API
func TestTagAPIs(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	t.Run("获取标签列表", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/tags/list")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])
		assert.NotNil(t, response["data"])
		assert.NotNil(t, response["meta"])

		// 验证数据结构
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)

		// 验证第一个标签
		tag1 := data[0].(map[string]interface{})
		assert.Equal(t, float64(1), tag1["id"])
		assert.Equal(t, "AI", tag1["name"])
		assert.Equal(t, "#FF6B6B", tag1["color"])
		assert.Equal(t, float64(10), tag1["usage_count"])
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	t.Run("404错误处理", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/nonexistent")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	t.Run("405错误处理", func(t *testing.T) {
		req, _ := http.NewRequest("PUT", suite.server.URL+"/api/prompts/prompts/list", nil)
		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Gin默认返回404而不是405，所以这里测试404
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}

// TestResponseFormat 测试响应格式
func TestResponseFormat(t *testing.T) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	t.Run("标准响应格式", func(t *testing.T) {
		resp, err := http.Get(suite.server.URL + "/api/prompts/prompts/list")
		require.NoError(t, err)
		defer resp.Body.Close()

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		// 验证标准响应格式
		assert.Contains(t, response, "code")
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "meta")

		// 验证分页信息
		meta := response["meta"].(map[string]interface{})
		assert.Contains(t, meta, "page")
		assert.Contains(t, meta, "size")
		assert.Contains(t, meta, "total")
	})
}

// BenchmarkAPIs 性能测试
func BenchmarkAPIs(b *testing.B) {
	suite := NewSimpleAPITestSuite()
	defer suite.Close()

	b.Run("健康检查性能", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			resp, err := http.Get(suite.server.URL + "/health")
			if err != nil {
				b.Fatal(err)
			}
			resp.Body.Close()
		}
	})

	b.Run("提示词列表性能", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			resp, err := http.Get(suite.server.URL + "/api/prompts/prompts/list")
			if err != nil {
				b.Fatal(err)
			}
			resp.Body.Close()
		}
	})
}
