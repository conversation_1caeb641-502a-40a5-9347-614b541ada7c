# Design Document

## Overview

This design implements login and registration pages for the prompts frontend application that directly integrate with the existing users service HTTP API. The solution provides a complete authentication flow while maintaining consistency with the existing prompts application architecture and design patterns.

The implementation leverages the existing users service authentication endpoints (`/api/user/auth/login` and `/api/user/auth/register`) and follows the established patterns in the prompts frontend codebase, including the use of React Router, Ant Design components, Zustand for state management, and React Query for API calls.

## Architecture

### Frontend Architecture
- **Framework**: React 19 with TypeScript
- **UI Library**: Ant Design 5.26.3 with Chinese locale
- **Routing**: React Router DOM 6.30.1
- **State Management**: Zustand 5.0.6 for authentication state
- **API Client**: Axios 1.10.0 with React Query 5.81.5
- **Styling**: CSS modules with Ant Design theming

### API Integration
- **Direct API Calls**: Frontend directly calls users service on port 8084
- **Authentication Endpoints**:
  - `POST http://localhost:8084/api/user/auth/register`
  - `POST http://localhost:8084/api/user/auth/login`
  - `POST http://localhost:8084/api/user/auth/logout`
  - `POST http://localhost:8084/api/user/auth/refresh`

### Proxy Configuration
The Vite development server will be configured to proxy authentication requests to the users service:
```typescript
'/api/user': {
  target: 'http://localhost:8084',
  changeOrigin: true
}
```

## Components and Interfaces

### 1. Authentication Pages

#### LoginPage Component
- **Location**: `src/pages/auth/LoginPage.tsx`
- **Layout**: Centered form with application branding
- **Form Fields**:
  - Username/Email (required)
  - Password (required, with visibility toggle)
  - Remember Me checkbox
- **Actions**:
  - Login button with loading state
  - Link to registration page
  - Forgot password link (future enhancement)

#### RegisterPage Component
- **Location**: `src/pages/auth/RegisterPage.tsx`
- **Layout**: Centered form matching login design
- **Form Fields**:
  - Username (required, validation for uniqueness)
  - Email (required, email format validation)
  - Password (required, strength validation)
  - Confirm Password (required, match validation)
- **Actions**:
  - Register button with loading state
  - Link to login page

### 2. Authentication Service

#### AuthService Class
- **Location**: `src/services/authService.ts`
- **Methods**:
  - `login(credentials: LoginRequest): Promise<AuthResponse>`
  - `register(userData: RegisterRequest): Promise<void>`
  - `logout(): Promise<void>`
  - `refreshToken(): Promise<AuthResponse>`
  - `getCurrentUser(): Promise<User>`

### 3. Authentication Store

#### useAuthStore Hook
- **Location**: `src/stores/useAuthStore.ts`
- **State**:
  - `user: User | null`
  - `token: string | null`
  - `refreshToken: string | null`
  - `isAuthenticated: boolean`
  - `isLoading: boolean`
- **Actions**:
  - `login(credentials: LoginRequest)`
  - `register(userData: RegisterRequest)`
  - `logout()`
  - `refreshAuth()`
  - `setUser(user: User)`

### 4. Route Protection

#### AuthGuard Component
- **Location**: `src/components/auth/AuthGuard.tsx`
- **Purpose**: Protect routes that require authentication
- **Behavior**: Redirect to login if not authenticated

#### GuestGuard Component
- **Location**: `src/components/auth/GuestGuard.tsx`
- **Purpose**: Redirect authenticated users away from auth pages
- **Behavior**: Redirect to home if already authenticated

## Data Models

### Authentication Request/Response Types

```typescript
// Login Request
interface LoginRequest {
  username: string; // Can be username or email
  password: string;
}

// Register Request
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  real_name?: string;
}

// Authentication Response
interface AuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

// User Model
interface User {
  id: number;
  username: string;
  email: string;
  real_name?: string;
  avatar?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// API Response Wrapper
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  meta?: {
    request_id: string;
    timestamp: number;
  };
}
```

### Form Validation Rules

#### Login Form
- Username/Email: Required, min 3 characters
- Password: Required, min 6 characters

#### Registration Form
- Username: Required, 3-20 characters, alphanumeric + underscore
- Email: Required, valid email format
- Password: Required, min 8 characters, must contain letter and number
- Confirm Password: Required, must match password

## Error Handling

### Client-Side Validation
- Real-time field validation using Ant Design form validation
- Custom validation rules for password strength and username format
- Visual feedback with error messages below form fields

### API Error Handling
- Network errors: Display user-friendly connection error message
- Validation errors: Map server field errors to form fields
- Authentication errors: Clear stored tokens and redirect to login
- Rate limiting: Display appropriate retry message

### Error Display Strategy
- Form field errors: Inline validation messages
- General errors: Toast notifications using Ant Design message component
- Critical errors: Modal dialogs for important failures

## Testing Strategy

### Unit Tests
- Authentication service methods
- Form validation logic
- Store state management
- Component rendering and interactions

### Integration Tests
- Login flow end-to-end
- Registration flow end-to-end
- Token refresh mechanism
- Route protection behavior

### API Integration Tests
- Mock users service responses
- Test error scenarios
- Validate request/response formats
- Test authentication state persistence

### User Experience Tests
- Form accessibility
- Mobile responsiveness
- Loading states and feedback
- Error message clarity

## Security Considerations

### Token Management
- Store JWT tokens in memory (Zustand store)
- Implement automatic token refresh
- Clear tokens on logout or expiration
- Secure token transmission over HTTPS in production

### Input Validation
- Client-side validation for user experience
- Rely on server-side validation for security
- Sanitize user inputs before API calls
- Prevent XSS through proper escaping

### Authentication Flow
- Redirect to intended page after login
- Prevent access to auth pages when authenticated
- Handle concurrent login sessions
- Implement proper logout cleanup

## Implementation Notes

### Styling Approach
- Follow existing prompts application design patterns
- Use Ant Design theme configuration for consistency
- Implement responsive design for mobile devices
- Maintain accessibility standards

### State Persistence
- Authentication state persists across browser sessions
- Automatic token refresh on application startup
- Graceful handling of expired sessions
- Clear state on explicit logout

### Development Workflow
- Use existing development server proxy configuration
- Implement proper error boundaries
- Add comprehensive logging for debugging
- Follow existing code organization patterns