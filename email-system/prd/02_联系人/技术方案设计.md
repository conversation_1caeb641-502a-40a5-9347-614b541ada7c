## 技术方案设计｜联系人（Contacts）

说明
- 本设计基于 MRD 与 PRD 目标（导入成功率>99%，百万级 CSV 异步导入，事件延迟 P95<2s），细化联系人模块的架构、数据、接口、流程、可靠性与合规。
- 与外部用户系统解耦：权限、用户与租户体系由外部系统提供，本模块通过 `tenant_id`、`actor_user_id` 等外部引用接入，不建立本地用户/角色表。
- 与统一数据库设计对齐：复用 `prd/数据库设计.md` 已定义的表：`contacts`、`contact_field_defs`、`contact_attributes`、`import_jobs`、`import_job_errors`、`contact_timeline_events`，以及与列表/标签/投诉与退订等表的协作。
- **新增功能**：支持标记语言（preferred_language）和地理位置（location）字段，提升国际化营销能力。
- **相关文档**：详细的技术实现方案请参考 [标记语言与地理位置_技术细节.md](./标记语言与地理位置_技术细节.md)

## 1. 目标与范围

### 1.1 目标（可量化）
- 批量导入吞吐≥5k rows/s（后台并行），单任务可处理≥1M 行。
- 导入成功率≥99%，错误行可下载并可回放。
- 去重与合并准确率≥95%，可视化预览差异。
- 查询联系人明细/时间线 P95<100ms（索引命中）。

### 1.2 范围
- 联系人主档、扩展字段（定义与取值）、导入/错误行/回放、时间线事件聚合、列表/标签赋值、基础查询与批量更新。
- 不包含：外部身份/权限系统（上游负责）、邮件发送执行（独立模块）。

## 2. 架构设计（HLD）

组件
- 上传服务（Upload Service）：前端分片上传到对象存储（如 S3/OSS），产生 `file_url`，支持续传/秒传。
- 导入编排器（Import Orchestrator）：根据 `import_jobs` 拉起校验/分片解析/并行批处理，维护进度、重试与收敛。
- 映射器与校验器（Mapper & Validator）：根据 `mapping_json` 将原始列映射到标准字段，做类型/格式/约束校验（邮箱、必填、长度、枚举等）。
- 去重合并器（Dedupe & Merge Engine）：基于 `tenant_id+LOWER(email)` 唯一键做 Upsert 与字段级合并策略。
- 异步写入队列（Redis Streams/List）：解析校验后不直接写库，按分区键入队，支持回压与消费确认。
- 写入工作器（Batch Writer Worker）：从 Redis 消费消息，批量写入（默认 1000 行/批），使用 INSERT ... ON DUPLICATE KEY UPDATE；支持幂等与失败重试。
- 错误收集器（Error Collector）：将失败行记录到 `import_job_errors`，供下载与回放。
- 结果汇报器（Reporter）：持续更新 `import_jobs.status/progress/error_count`，任务完成后产出摘要。

数据与存储（MySQL 8.0）
- `contacts`：联系人主档（email 唯一/租户);
- `contact_field_defs`：自定义字段定义（类型/校验/枚举等）；
- `contact_attributes`：EAV 取值（稀疏大规模字段存储）；
- `import_jobs` / `import_job_errors`：导入任务与错误行；
- `contact_timeline_events`：行为时间线；
- `contact_notes`：联系人备注（见 3. 数据模型与约束）。
- 关联使用：`lists`、`tags`、`contact_tags`（可选在导入阶段赋值）。
- 中间件：Redis（Streams 或 List+Lua）承载写入队列与进度心跳；可选 Kafka 兼容实现。

数据流时序
```mermaid
sequenceDiagram
participant FE as 前端
participant UP as Upload Service
participant IMP as Import Orchestrator
participant VAL as Mapper/Validator
participant Q as Redis Queue
participant DB as MySQL
FE->>UP: 分片上传大文件
UP-->>FE: 返回 file_url
FE->>IMP: 创建导入任务(import_jobs)
IMP->>VAL: 读取样本行生成预校验报告
VAL-->>FE: 字段映射建议/校验结果
FE->>IMP: 确认提交任务
IMP->>DB: 标记 job=running，分片并行处理
loop 每个分片
  IMP->>VAL: 解析/映射/校验
  VAL->>Q: 生产消息（分区键 hash(lower(email))）
  Q-->>DB: （由 Writer Worker 消费）批量写 contacts/attributes
  VAL->>DB: 写入 import_job_errors（如有）
  IMP->>DB: 更新 progress/heartbeat
end
IMP-->>FE: 完成/失败，提供错误行下载
```

## 3. 数据模型与约束

主表与关键索引
- `contacts(tenant_id, email)` 唯一键；常用索引：`(tenant_id)`、`(tenant_id, last_activity_at)`。
- `contact_field_defs(tenant_id, field_key)` 唯一键；类型 text/number/bool/enum/date。
- `contact_attributes(tenant_id, contact_id, field_key)` 唯一键；按 `field_key` 与 `contact_id` 均建立查询索引。
- `import_jobs(tenant_id)`、`import_job_errors(job_id)`；
- `contact_timeline_events(tenant_id, contact_id, occurred_at)`。
- `contact_notes(id, tenant_id, contact_id, content, created_by, created_at)`；索引：`(tenant_id, contact_id, created_at)`。

### 3.1 新增标准字段定义

**标记语言字段（preferred_language）**
- 字段类型：`enum`
- 存储位置：`contacts.preferred_language`（新增列）
- 默认值：`'zh-CN'`（简体中文）
- 支持值：ISO 639-1 语言代码 + 国家/地区代码
  - `zh-CN`：简体中文（中国大陆）
  - `zh-TW`：繁体中文（台湾）
  - `zh-HK`：繁体中文（香港）
  - `en-US`：英语（美国）
  - `en-GB`：英语（英国）
  - `ja-JP`：日语（日本）
  - `ko-KR`：韩语（韩国）
  - `fr-FR`：法语（法国）
  - `de-DE`：德语（德国）
  - `es-ES`：西班牙语（西班牙）
  - `pt-BR`：葡萄牙语（巴西）
  - `ru-RU`：俄语（俄罗斯）
  - `ar-SA`：阿拉伯语（沙特阿拉伯）
  - `hi-IN`：印地语（印度）
  - `th-TH`：泰语（泰国）
- 索引：`(tenant_id, preferred_language)` 用于按语言筛选
- 校验规则：必须为预定义的语言代码之一

**地理位置字段（location）**
- 字段类型：`object`（JSON 存储）
- 存储位置：`contact_attributes` 表
- 字段键：`location`
- 数据结构：
```json
{
  "country": "CN",           // ISO 3166-1 alpha-2 国家代码
  "region": "Beijing",       // 省/州/地区名称
  "city": "Beijing",         // 城市名称
  "timezone": "Asia/Shanghai", // IANA 时区标识符
  "coordinates": {           // 可选，GPS 坐标
    "lat": 39.9042,
    "lng": 116.4074
  }
}
```
- 校验规则：
  - `country`：必须为有效的 ISO 3166-1 alpha-2 代码
  - `region`、`city`：最大长度 100 字符
  - `timezone`：必须为有效的 IANA 时区标识符
  - `coordinates`：可选，lat 范围 -90 到 90，lng 范围 -180 到 180
- 索引：`(tenant_id, field_key, country)` 用于按国家筛选

### 3.2 字段映射规则增强

**标记语言自动识别规则**
1. **精确匹配**：`language`、`lang`、`preferred_language`、`语言`、`标记语言`
2. **值标准化**：
   - 中文：`zh`、`chinese`、`中文` → `zh-CN`
   - 英文：`en`、`english`、`英文` → `en-US`
   - 日文：`ja`、`japanese`、`日语` → `ja-JP`
   - 韩文：`ko`、`korean`、`韩语` → `ko-KR`
3. **浏览器语言检测**：导入时如未指定，根据用户浏览器语言设置默认值
4. **IP 地理位置推断**：可选功能，根据 IP 地址推断默认语言

**地理位置自动识别规则**
1. **精确匹配**：`location`、`address`、`country`、`city`、`region`、`地理位置`、`地址`
2. **国家代码标准化**：
   - `中国`、`China`、`CN` → `CN`
   - `美国`、`United States`、`USA`、`US` → `US`
   - `日本`、`Japan`、`JP` → `JP`
3. **时区自动推断**：根据国家/城市自动设置对应时区
4. **坐标解析**：支持地址字符串自动解析为 GPS 坐标（可选服务）

字段规范与大小限制（默认可配置）
- 单联系人扩展字段数量建议≤200；单字段文本≤2KB；单任务最大文件≤2GB，列数≤200，行数≥1M 时建议并发≤3。
- Email 标准化：去空白、统一小写、RFC5322 基本校验；国际化邮箱暂不支持（可配置）。
- **新增限制**：location 字段 JSON 大小≤1KB，preferred_language 字段长度≤10 字符。

## 4. 导入/映射/校验/写入

### 4.1 字段映射
- 前端展示样本行，左：原始列；右：系统字段；支持创建/选择自定义字段（写入 `contact_field_defs`）。
- 自动匹配（Auto-Mapping）：
  - 规则优先级：精确别名匹配（email/e-mail/邮箱）> 规范化相似度（去空格/大小写/符号）> 语义别名词典（name/full_name/姓名）> 统计/历史映射模板。
  - 列名规范化：全角/半角统一、汉字与英文别名映射、下划线/驼峰切分、拼音辅助匹配（可选）。
  - 类型推断：采样前 N 行（默认 200）进行值分布检测，辅助判定 `number/date/bool/email`。
  - 置信度评分：给出每列的匹配候选及分值，低于阈值（默认 0.7）需人工确认。
- 支持保存/管理租户级映射模板，供后续复用。

**新增：标记语言与地理位置映射规则**
- **标记语言字段映射**：
  - 精确匹配：`language`、`lang`、`preferred_language`、`语言`、`标记语言`、`pref_lang`
  - 模糊匹配：包含 `language`、`lang`、`语言` 的列名
  - 置信度计算：精确匹配 0.95，模糊匹配 0.75
  - 值标准化处理：自动将常见语言表示转换为标准 ISO 代码

- **地理位置字段映射**：
  - 精确匹配：`location`、`address`、`country`、`city`、`region`、`地理位置`、`地址`
  - 模糊匹配：包含 `location`、`address`、`country`、`city`、`region` 的列名
  - 置信度计算：精确匹配 0.90，模糊匹配 0.70
  - 复合字段识别：自动识别多个地理位置相关列并组合为 location 对象

### 4.2 校验规则
- Email：格式校验、重复行校验、抑制（退订/投诉/硬退）名单联动检查（如在 `suppression_list` 命中可标记/跳过）。
- 类型：按 `contact_field_defs.field_type` 做强校验；enum 必须在 `options_json` 中；日期支持 `YYYY-MM-DD`、`YYYY/MM/DD`、ISO8601（严格化）。
- 必填：email 必须；其余按配置。
- 字段长度/范围：文本、数值上下限、布尔/日期合法性。

**新增：标记语言与地理位置校验规则**
- **标记语言校验**：
  - 格式校验：必须为预定义的 ISO 639-1 + 国家代码格式
  - 值校验：必须在支持的语言列表中
  - 默认值处理：空值或无效值自动设置为 `zh-CN`
  - 大小写处理：自动转换为小写格式

- **地理位置校验**：
  - 国家代码校验：必须为有效的 ISO 3166-1 alpha-2 代码
  - 时区校验：必须为有效的 IANA 时区标识符
  - 坐标校验：lat 范围 -90 到 90，lng 范围 -180 到 180
  - 字段长度：region、city 最大长度 100 字符
  - 自动补全：根据国家代码自动推断时区
  - 坐标解析：支持地址字符串自动解析为 GPS 坐标（可选）

同步与可重复（Parse/Validate 一致性）
- 单一校验引擎：预校验（Preview）与正式执行（Run）使用同一版本的解析/映射/校验库，版本号写入 `import_jobs.validator_version`。
- 规则快照：执行前将 `mapping_json`、`field_defs` 摘要（hash）写入 `import_jobs`，保证回放与追责可还原。
- 错误口径一致：预校验与执行阶段错误分类与计数维度一致（行号、错误类型、字段键）。

### 4.3 去重与合并策略（重要）
- 唯一键：`tenant_id + LOWER(email)`。
- 合并策略：
  - 默认：空值不覆盖非空；非空覆盖空；发生冲突时以新值优先，除非在“合并策略”中指定按字段优先级（例如 `last_updated` 更近者优先）。
  - 自定义：在 `import_jobs.mapping_json` 或任务级 `merge_policy` 中携带字段合并规则（如 keep_existing/new_overwrite/most_recent/concat 去重）。
  - 属性层（EAV）：同上策略逐字段生效。
- 幂等：以 `tenant_id + email` 作为行级幂等键；同一 job 重放不产生重复，跨 job 重复导入遵循合并策略。

### 4.4 批处理与异步写入（Redis 队列）
- 解析批次（chunk）大小：默认 10k 行；每行映射/校验通过后构造写入消息，按 `hash(lower(email)) % N` 分区入队，保证同一联系人的顺序性。
- 队列：
  - Redis Streams（推荐）：`stream=contact_writes`，字段包含 `tenant_id,email,upsert_payload,merge_policy,trace_id,job_id,row_idx`；使用消费组 `cg=writer`，多分区并行。
  - 备选 Redis List+Lua：提供批量 pop 与可见性超时的近似实现。
- 消费与写库：Writer Worker 按分区批量聚合（默认 1000 条/批或 2s 超时），执行：
  - 主表：`INSERT ... ON DUPLICATE KEY UPDATE`，字段合并遵循 `merge_policy`；
  - EAV：对 `contact_attributes` 批量 UPSERT；
  - 标签/列表：如 `assign.tags/lists` 在消息中携带，写 `contact_tags/contact_lists`。
- 幂等：消息 `dedupe_key=tenant_id+lower(email)+job_id+row_idx`；写前可选基于 Redis `SETNX` 保护，或依赖数据库唯一键实现幂等。
- 失败处理：
  - 可重试错误（锁冲突/暂时性失败）指数退避重试 3 次；
  - 不可重试错误（约束/校验遗漏）落 `import_job_errors`；
  - 消费确认：成功后 `XACK`，失败超过阈值移至死信 `contact_writes_dlq`。
- 回压与限速：基于 Streams 长度与处理耗时自动调整解析并发；队列长度越界触发告警。

### 4.4.1 超大 Excel（XLSX）归一化与分片（不引入新接口）
- 触发条件：当 `import_jobs.source_file_type = 'xlsx'` 时，先进入 `phase=normalizing`。
- 归一化：采用流式 XML 解析，将指定 `sheet_name` 的内容（`header_row` 为表头）转换为多个 CSV 对象；分片建议 64MB 或 100k 行。
- 分片登记：为每个 CSV 分片写入 `import_job_shards(job_id, shard_index, row_start, row_end, object_url, status=pending)`。
- 并行执行：Worker 基于“数据库租约”领取分片：
  - 抢占：`SELECT ... FOR UPDATE SKIP LOCKED` + `lease_owner/lease_until` 字段更新为 `leased`；超时自动转移。
  - 处理：下载分片 CSV 流式解析→映射→校验→入队（Redis），持续更新 `progress_rows`；成功置 `succeeded`，失败累加 `attempt` 并回退。
- 任务推进：所有分片完成后将 `phase=importing`，待 Writer 消费完毕后任务 `status=done/failed`。
- 观测：补充 `phase/shard_count/total_rows` 到 `GET /api/contacts/import/status` 的响应中。

### 4.5 错误行与回放
- 错误行记录：`import_job_errors(job_id,row_number,raw_row,error_message)`。
- 下载：提供错误 CSV；
- 回放：支持“仅错误行重试”与“修正映射/策略后重跑”。

### 4.6 自定义字段自动创建与类型推断
- 在映射确认时，可勾选“创建为自定义字段”，后端在事务内：
  - 若租户不存在 `field_key`，写入 `contact_field_defs(tenant_id, field_key, field_type, options_json, created_by)`；
  - 将列值写入 `contact_attributes`；
- 类型推断与冲突：当推断与现有定义冲突时阻止提交并给出修复建议（如转换为 text）。

## 5. 接口设计（遵循平台统一响应 {code,message,data,errors,meta}）

### 5.1 创建/更新联系人
- `POST /api/contacts/create`
  - 请求：`{ email, status?, attributes?, lists?, tags? }`
  - 行为：创建或冲突时报错（create 语义严格）；
  - 响应：`{ code:0, data:{ id } }`

- `POST /api/contacts/update`
  - 请求：`{ id? | email, attributes?, status?, merge_policy? }`
  - 行为：按合并策略更新；
  - 响应：`{ code:0 }`

### 5.2 查询联系人/搜索
- `GET /api/contacts/get?id=... | email=...`
  - 返回联系人主档、部分 attributes（或指定字段集）。
- `POST /api/contacts/search`
  - 请求：`{ filters:{ status?, lists?, tags?, attributes? }, sort?, page, size }`
  - 注意：分页由服务层处理（MySQL LIMIT/OFFSET 或游标）；
  - 响应：列表 + 总数。

### 5.3 导入（异步）
- `POST /api/contacts/import`
  - 请求：`{ file_url, mapping, dedupe: "merge|skip|overwrite", merge_policy?, assign:{ lists?:[], tags?:[] } }`
  - 行为：创建 `import_jobs`，status=queued。
  - 响应：`{ code:0, data:{ job_id } }`

- `GET /api/contacts/import/status?job_id=...`
  - 返回：`{ status, phase, progress, shard_count, total_rows, error_count, created_at, updated_at }`

- `GET /api/contacts/import/errors/download?job_id=...`
  - 返回：错误行 CSV 临时下载链接。

- `POST /api/contacts/import/plan`
  - 请求：`{ file_url, sample_size?:200 }`
  - 返回：`{ suggested_mapping:[{ source, target, confidence, type_guess }], sample_preview:{ rows:[], issues:[...] }, validator_version, estimated_total_rows }`
  - 作用：供前端展示自动匹配与预校验，不产生写入。

- `POST /api/contacts/import/preview`
  - 请求：`{ file_url, mapping, merge_policy?, sample_size?:200 }`
  - 返回：`{ issues_by_type, first_rows_with_marks, estimated_inserts, estimated_updates }`

### 5.4 列表/标签赋值
- `POST /api/contacts/tags/update`
  - 请求：`{ contact_ids|emails, add?:[], remove?:[] }`
- `POST /api/contacts/lists/update`
  - 请求：`{ contact_ids|emails, add?:[], remove?:[] }`

- `POST /api/contacts/bulk/tags/update`
  - 请求：`{ scope:{ ids?:[], emails?:[], filter?:{...} }, add?:[], remove?:[] }`
  - 行为：当选择器为 filter 时，触发批处理任务与队列；返回 `task_id` 并可查询进度。

### 5.5 联系人备注（Notes）
- `POST /api/contacts/notes/create`
  - 请求：`{ contact_id, content }`
  - 响应：`{ id }`
- `GET /api/contacts/notes/list?contact_id=...&page=1&size=20`
  - 返回：备注列表（按时间倒序）。
- `DELETE /api/contacts/notes/delete?id=...`
  - 行为：软删除或硬删除（按合规策略）。

接口校验
- Email、必填字段、参数类型；`page/size` 上限；幂等键（如 `Idempotency-Key` 头）。

## 6. 性能与扩展性

并行策略
- 分片：按文件大小切分分片（默认 64MB），多个 worker 并行处理；
- 分区路由：按 `hash(lower(email)) % N` 路由，减少同一联系人多次并发写冲突；
- 批处理：解析批与写入批解耦，内存队列限压，防止 OOM。

队列扩展
- Redis Streams 水平扩展：按分区键扩展消费组成员数（N=分区数），单分区单线程保证顺序；
- 峰值保护：`XLEN(stream)` 超阈值触发限流与导入并发降级；
- 观测指标：生产/消费速率、滞后长度、重试率、死信数。

数据库优化
- 预建唯一索引与必要二级索引；关闭不必要的外键；
- 批量写入使用多值 INSERT；`innodb_flush_log_at_trx_commit=1`，控制批次大小；
- 热点规避：将 attributes 更新与 contacts 主档分离，降低行级锁竞争。

容量规划
- 典型：1000 万联系人、200 自定义字段、日增量 50 万事件；主表与 EAV 分离 + 分区（可选依据 `tenant_id` 或时间线按月 RANGE 分区）。

### 6.1 套餐限额校验（按行数与能力判断是否支持）
- 生效来源：`billing_plans`（套餐模板） + `tenant_subscriptions`（租户覆盖）。
- 导入前校验：
  - 估算行数 `estimated_total_rows` 与 `import_max_rows_per_job` 比较，不足则拒绝并提示升级/拆分。
  - 当前联系人总数 + 估算新增量 ≤ `max_contacts`。
  - 导入映射中的“新增字段数量” ≤ `max_contact_custom_fields`（仅对新增字段生效）。
- 圈选任务：并发提交数 ≤ `max_segment_jobs_concurrent`。
- 功能开关：当 `enable_open_tracking = 0` 时，活动配置禁止打开追踪。
- 发送与验证：按 `monthly_email_quota`、`monthly_email_verification_quota` 做周期配额校验（结合 `usage_counters`）。

## 7. 可观测性与告警

度量
- 导入：吞吐（rows/s）、进度%、错误率、TopN 错误类型、批处理耗时、MySQL 锁等待；
- API：QPS、P95/P99、错误码分布；
- 数据质量：重复率、空值率、字段不合规率。

队列与Worker
- 生产/消费速率、滞后（lag）、消息驻留时长、消费者崩溃重平衡次数；
- DLQ 条数与示例采样；
- Writer 批大小、单批耗时、DB 成功/失败比。

日志与审计
- 关键操作写 `audit_logs`（创建/更新/批量导入、错误下载）；
- 追踪：请求携带 `trace_id`，导入任务贯穿链路。

告警
- 导入失败率>1%、批处理 P95>2s、锁等待>500ms、错误类型激增（域名解析失败/非法邮箱）。

## 8. 安全与合规

隐私与合规
- PII 展示脱敏（前端），敏感字段可接入字段级加密策略（参见 `field_encryption_policies`）。
- 投诉与退订联动：命中 `suppression_list` 时，`contacts.status` 置为 suppressed 或跳过导入（策略可配）。
- 同意记录：与 `consent_records` 联动（表单渠道导入自动写 consent）。

权限与多租户
- 通过上游颁发的 `tenant_id`、用户上下文校验；本模块不实现 RBAC。

## 9. 失败处理与幂等

幂等
- Job 幂等：同一 `job_id` 重试不重复创建；
- 行幂等：`tenant_id+email` 唯一；多次导入遵循合并策略。

重试与死信
- 批处理失败退避重试（3 次）；仍失败的行记录到 `import_job_errors`，供回放。

一致性
- 最终一致：导入完成后，搜索索引（如有）异步刷新；
- 统计一致：`usage_counters(metric=contacts)` 在导入完成后异步增量刷新。

## 10. 前端交互与体验

流程
- 上传 → 字段映射（自动匹配与置信度提示/手动修正/自定义字段创建）→ 预校验报告（错误示例/建议修复）→ 提交任务 → 进度条（队列滞后提示）→ 完成/错误下载 → 可回放。

体验要点
- 映射页提供自动匹配建议与样本行预览，低置信度需确认；
- 错误报告可按类型聚合展示，支持筛选/下载；
- 大任务分片状态与预计剩余时间估算；
- 导入完成后提供“去重合并摘要”与“新增/更新数量”。
- 列表页提供“批量标记标签”工具条；联系人详情支持“备注”查看/新增。

## 11. 验收标准与测试

功能测试
- 正向导入/空值/枚举校验/非法邮箱/重复行/错误行下载/回放；
- 自定义字段创建、EAV 取值写入与查询；
- 列表/标签批量赋值；
- 搜索与分页；

性能测试
- 1M 行 CSV，3 并发任务，各自≥5k rows/s；
- 冲突比例 20% 的合并场景，P95<2s；

故障注入
- 对象存储短暂不可用、数据库锁等待、批次失败退避、部分分片损坏；

## 12. 发布与回滚

发布
- feature flag 控制导入新引擎开关；
- 按租户灰度，逐步提升并发阈值；

回滚
- 关闭开关回退旧实现；
- 清理处于 running 状态且超过阈值的 job（标记失败并保留上下文）。

## 13. 开发清单（DoD）
- API：`create/update/get/search/import/plan/preview/status/errors/download/tags.update/lists.update/bulk.tags.update/notes.create/notes.list/notes.delete` 实现与文档；
- 导入编排器 worker 与批处理器；
- Redis 写入队列与 Writer Worker；
- 字段映射 UI 及预校验报告；
- 指标/日志/审计接入；
- 错误行下载与回放；
- 单元/集成/性能测试用例。

## 14. 风险与对策
- 大文件导致内存峰值：流式解析、分片处理、限压；
- 热点冲突（同一 email 高并发）：分区路由、批量合并、退避重试；
- 数据口径不一致：规则 DSL 与字段定义版本化，导入摘要给出口径说明；
- 合规风险：导入前/发布前校验、审计全覆盖。

## 15. 依赖与对接
- 对象存储（S3/OSS）读权限、预签名；
- 外部用户/租户系统上下文注入；
- 投诉与退订模块（可选联动）。

## 16. TODO
- 映射模板的租户级管理与授权。
- attributes 热门字段的列式冗余策略评估与查询加速。
- 导入任务取消/暂停与断点续传指针持久化。
- CSV 编码/分隔符自动识别与混合类型检测增强。

