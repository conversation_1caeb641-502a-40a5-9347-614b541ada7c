# 标记语言与地理位置字段技术细节

- 版本：v1.0
- 作者：技术团队
- 更新时间：2025-01-15
- 模块：联系人管理

## 1. 概述

本文档详细描述联系人模块中新增的标记语言（preferred_language）和地理位置（location）字段的技术实现方案，包括数据模型、字段映射、校验规则、导入处理等核心功能。

## 2. 标记语言字段（preferred_language）

### 2.1 数据模型设计

**数据库表结构**
```sql
-- 在 contacts 表中新增列
ALTER TABLE contacts ADD COLUMN preferred_language VARCHAR(10) NOT NULL DEFAULT 'zh-CN';

-- 创建索引用于按语言筛选
CREATE INDEX idx_contacts_language ON contacts(tenant_id, preferred_language);
```

**支持的语言列表**
```json
{
  "zh-CN": "简体中文（中国大陆）",
  "zh-TW": "繁体中文（台湾）", 
  "zh-HK": "繁体中文（香港）",
  "en-US": "英语（美国）",
  "en-GB": "英语（英国）",
  "ja-JP": "日语（日本）",
  "ko-KR": "韩语（韩国）",
  "fr-FR": "法语（法国）",
  "de-DE": "德语（德国）",
  "es-ES": "西班牙语（西班牙）",
  "pt-BR": "葡萄牙语（巴西）",
  "ru-RU": "俄语（俄罗斯）",
  "ar-SA": "阿拉伯语（沙特阿拉伯）",
  "hi-IN": "印地语（印度）",
  "th-TH": "泰语（泰国）"
}
```

### 2.2 字段映射规则

**自动识别规则**
1. **精确匹配**：`language`、`lang`、`preferred_language`、`语言`、`标记语言`、`pref_lang`
2. **模糊匹配**：包含 `language`、`lang`、`语言` 的列名
3. **置信度计算**：
   - 精确匹配：0.95
   - 模糊匹配：0.75
   - 其他：0.50

**值标准化处理**
```python
def normalize_language(value):
    """标准化语言值为 ISO 639-1 + 国家代码格式"""
    if not value:
        return 'zh-CN'
    
    # 转换为小写并去除空格
    value = value.lower().strip()
    
    # 常见语言映射
    language_map = {
        'zh': 'zh-CN',
        'chinese': 'zh-CN', 
        '中文': 'zh-CN',
        'en': 'en-US',
        'english': 'en-US',
        '英文': 'en-US',
        'ja': 'ja-JP',
        'japanese': 'ja-JP',
        '日语': 'ja-JP',
        'ko': 'ko-KR',
        'korean': 'ko-KR',
        '韩语': 'ko-KR',
        'fr': 'fr-FR',
        'french': 'fr-FR',
        '法语': 'fr-FR',
        'de': 'de-DE',
        'german': 'de-DE',
        '德语': 'de-DE'
    }
    
    return language_map.get(value, value)
```

### 2.3 校验规则

**格式校验**
- 必须为预定义的 ISO 639-1 + 国家代码格式
- 长度限制：最大 10 字符
- 大小写：自动转换为小写格式

**值校验**
```python
def validate_language(value):
    """校验语言值是否有效"""
    valid_languages = {
        'zh-CN', 'zh-TW', 'zh-HK', 'en-US', 'en-GB', 
        'ja-JP', 'ko-KR', 'fr-FR', 'de-DE', 'es-ES',
        'pt-BR', 'ru-RU', 'ar-SA', 'hi-IN', 'th-TH'
    }
    
    if not value:
        return 'zh-CN'  # 默认值
    
    normalized = normalize_language(value)
    if normalized not in valid_languages:
        raise ValueError(f"不支持的语言代码: {value}")
    
    return normalized
```

## 3. 地理位置字段（location）

### 3.1 数据模型设计

**存储方式**
- 字段类型：JSON 对象
- 存储位置：`contact_attributes` 表
- 字段键：`location`

**数据结构**
```json
{
  "country": "CN",           // ISO 3166-1 alpha-2 国家代码
  "region": "Beijing",       // 省/州/地区名称
  "city": "Beijing",         // 城市名称
  "timezone": "Asia/Shanghai", // IANA 时区标识符
  "coordinates": {           // 可选，GPS 坐标
    "lat": 39.9042,
    "lng": 116.4074
  }
}
```

**数据库索引**
```sql
-- 在 contact_attributes 表中创建索引
CREATE INDEX idx_contact_attrs_location ON contact_attributes(tenant_id, field_key, country) 
WHERE field_key = 'location';
```

### 3.2 字段映射规则

**自动识别规则**
1. **精确匹配**：`location`、`address`、`country`、`city`、`region`、`地理位置`、`地址`
2. **模糊匹配**：包含 `location`、`address`、`country`、`city`、`region` 的列名
3. **置信度计算**：
   - 精确匹配：0.90
   - 模糊匹配：0.70
   - 其他：0.50

**复合字段识别**
```python
def detect_location_fields(columns):
    """检测地理位置相关列并组合"""
    location_fields = {}
    
    for col in columns:
        col_lower = col.lower()
        
        if any(keyword in col_lower for keyword in ['country', '国家']):
            location_fields['country'] = col
        elif any(keyword in col_lower for keyword in ['region', 'province', '省', '州']):
            location_fields['region'] = col
        elif any(keyword in col_lower for keyword in ['city', '城市']):
            location_fields['city'] = col
        elif any(keyword in col_lower for keyword in ['address', '地址']):
            location_fields['address'] = col
    
    return location_fields
```

### 3.3 值标准化处理

**国家代码标准化**
```python
def normalize_country(value):
    """标准化国家代码为 ISO 3166-1 alpha-2 格式"""
    if not value:
        return None
    
    # 常见国家名称映射
    country_map = {
        '中国': 'CN', 'China': 'CN',
        '美国': 'US', 'United States': 'US', 'USA': 'US',
        '日本': 'JP', 'Japan': 'JP',
        '韩国': 'KR', 'Korea': 'KR',
        '英国': 'GB', 'United Kingdom': 'GB', 'UK': 'GB',
        '法国': 'FR', 'France': 'FR',
        '德国': 'DE', 'Germany': 'DE',
        '西班牙': 'ES', 'Spain': 'ES',
        '巴西': 'BR', 'Brazil': 'BR',
        '俄罗斯': 'RU', 'Russia': 'RU',
        '印度': 'IN', 'India': 'IN',
        '泰国': 'TH', 'Thailand': 'TH'
    }
    
    value = value.strip()
    return country_map.get(value, value.upper())
```

**时区自动推断**
```python
def infer_timezone(country, city=None):
    """根据国家/城市推断时区"""
    timezone_map = {
        'CN': 'Asia/Shanghai',
        'TW': 'Asia/Taipei', 
        'HK': 'Asia/Hong_Kong',
        'US': 'America/New_York',
        'GB': 'Europe/London',
        'JP': 'Asia/Tokyo',
        'KR': 'Asia/Seoul',
        'FR': 'Europe/Paris',
        'DE': 'Europe/Berlin',
        'ES': 'Europe/Madrid',
        'BR': 'America/Sao_Paulo',
        'RU': 'Europe/Moscow',
        'IN': 'Asia/Kolkata',
        'TH': 'Asia/Bangkok'
    }
    
    return timezone_map.get(country, 'UTC')
```

### 3.4 校验规则

**格式校验**
```python
def validate_location(location_data):
    """校验地理位置数据"""
    errors = []
    
    # 国家代码校验
    if 'country' in location_data:
        country = location_data['country']
        if not is_valid_country_code(country):
            errors.append(f"无效的国家代码: {country}")
    
    # 时区校验
    if 'timezone' in location_data:
        timezone = location_data['timezone']
        if not is_valid_timezone(timezone):
            errors.append(f"无效的时区: {timezone}")
    
    # 坐标校验
    if 'coordinates' in location_data:
        coords = location_data['coordinates']
        if 'lat' in coords and not (-90 <= coords['lat'] <= 90):
            errors.append("纬度必须在 -90 到 90 之间")
        if 'lng' in coords and not (-180 <= coords['lng'] <= 180):
            errors.append("经度必须在 -180 到 180 之间")
    
    # 字段长度校验
    if 'region' in location_data and len(location_data['region']) > 100:
        errors.append("地区名称长度不能超过 100 字符")
    if 'city' in location_data and len(location_data['city']) > 100:
        errors.append("城市名称长度不能超过 100 字符")
    
    return errors
```

## 4. 导入处理流程

### 4.1 字段映射增强

**标记语言映射**
```python
def map_language_field(source_column, sample_values):
    """映射标记语言字段"""
    mapping = {
        'target': 'preferred_language',
        'type': 'enum',
        'confidence': calculate_confidence(source_column, sample_values),
        'normalizer': normalize_language,
        'validator': validate_language
    }
    
    return mapping
```

**地理位置映射**
```python
def map_location_fields(columns, sample_data):
    """映射地理位置字段"""
    location_fields = detect_location_fields(columns)
    
    mappings = []
    for field_type, source_col in location_fields.items():
        mapping = {
            'target': f'location.{field_type}',
            'type': 'text',
            'confidence': calculate_confidence(source_col, sample_data[source_col]),
            'normalizer': get_location_normalizer(field_type),
            'validator': get_location_validator(field_type)
        }
        mappings.append(mapping)
    
    return mappings
```

### 4.2 数据转换处理

**标记语言转换**
```python
def transform_language_data(value, mapping):
    """转换标记语言数据"""
    try:
        normalized = mapping['normalizer'](value)
        validated = mapping['validator'](normalized)
        return validated
    except ValueError as e:
        raise ValidationError(f"标记语言转换失败: {e}")
```

**地理位置转换**
```python
def transform_location_data(row_data, mappings):
    """转换地理位置数据"""
    location = {}
    
    for mapping in mappings:
        field_type = mapping['target'].split('.')[-1]
        source_col = mapping['source']
        value = row_data.get(source_col)
        
        if value:
            try:
                normalized = mapping['normalizer'](value)
                location[field_type] = normalized
            except Exception as e:
                raise ValidationError(f"地理位置字段 {field_type} 转换失败: {e}")
    
    # 自动推断时区
    if 'country' in location and 'timezone' not in location:
        location['timezone'] = infer_timezone(location['country'])
    
    return location
```

## 5. API 接口设计

### 5.1 联系人创建/更新接口

**请求示例**
```json
{
  "email": "<EMAIL>",
  "name": "张三",
  "preferred_language": "zh-CN",
  "location": {
    "country": "CN",
    "region": "北京",
    "city": "北京",
    "timezone": "Asia/Shanghai"
  },
  "attributes": {
    "company": "示例公司",
    "custom:vip": true
  }
}
```

**响应示例**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "contact_123",
    "email": "<EMAIL>",
    "preferred_language": "zh-CN",
    "location": {
      "country": "CN",
      "region": "北京", 
      "city": "北京",
      "timezone": "Asia/Shanghai"
    }
  }
}
```

### 5.2 联系人搜索接口

**请求示例**
```json
{
  "filters": {
    "preferred_language": "zh-CN",
    "location": {
      "country": "CN"
    }
  },
  "sort": {
    "field": "created_at",
    "order": "desc"
  },
  "page": 1,
  "size": 20
}
```

## 6. 性能优化

### 6.1 索引优化

**标记语言索引**
- 主索引：`(tenant_id, preferred_language)` 用于按语言筛选
- 复合索引：`(tenant_id, preferred_language, created_at)` 用于时间范围查询

**地理位置索引**
- 主索引：`(tenant_id, field_key, country)` 用于按国家筛选
- 复合索引：`(tenant_id, field_key, country, city)` 用于城市级别查询

### 6.2 缓存策略

**语言列表缓存**
```python
# Redis 缓存支持的语言列表
CACHE_KEY_LANGUAGES = "contact:languages"
CACHE_TTL_LANGUAGES = 3600  # 1小时

def get_supported_languages():
    """获取支持的语言列表（带缓存）"""
    cached = redis.get(CACHE_KEY_LANGUAGES)
    if cached:
        return json.loads(cached)
    
    languages = load_languages_from_db()
    redis.setex(CACHE_KEY_LANGUAGES, CACHE_TTL_LANGUAGES, json.dumps(languages))
    return languages
```

**地理位置数据缓存**
```python
# 缓存国家代码映射
CACHE_KEY_COUNTRY_MAP = "contact:country_map"
CACHE_TTL_COUNTRY_MAP = 86400  # 24小时

def get_country_mapping():
    """获取国家代码映射（带缓存）"""
    cached = redis.get(CACHE_KEY_COUNTRY_MAP)
    if cached:
        return json.loads(cached)
    
    mapping = load_country_mapping_from_db()
    redis.setex(CACHE_KEY_COUNTRY_MAP, CACHE_TTL_COUNTRY_MAP, json.dumps(mapping))
    return mapping
```

## 7. 监控与告警

### 7.1 关键指标

**标记语言指标**
- 各语言联系人分布统计
- 语言字段映射成功率
- 语言校验失败率

**地理位置指标**
- 各国家联系人分布统计
- 地理位置字段映射成功率
- 坐标解析成功率

### 7.2 告警规则

```yaml
# 标记语言相关告警
alerts:
  - name: "language_mapping_failure_rate_high"
    condition: "language_mapping_failure_rate > 0.05"
    message: "标记语言字段映射失败率超过5%"
    
  - name: "location_validation_failure_rate_high"  
    condition: "location_validation_failure_rate > 0.10"
    message: "地理位置字段校验失败率超过10%"
```

## 8. 测试用例

### 8.1 标记语言测试

**正常用例**
```python
def test_language_normalization():
    assert normalize_language("zh") == "zh-CN"
    assert normalize_language("English") == "en-US"
    assert normalize_language("日本語") == "ja-JP"
    assert normalize_language("") == "zh-CN"  # 默认值
```

**异常用例**
```python
def test_language_validation():
    with pytest.raises(ValueError):
        validate_language("invalid-lang")
    
    with pytest.raises(ValueError):
        validate_language("xx-XX")
```

### 8.2 地理位置测试

**正常用例**
```python
def test_location_normalization():
    assert normalize_country("中国") == "CN"
    assert normalize_country("United States") == "US"
    assert infer_timezone("CN") == "Asia/Shanghai"
```

**异常用例**
```python
def test_location_validation():
    invalid_location = {
        "country": "XX",
        "coordinates": {"lat": 100, "lng": 200}
    }
    errors = validate_location(invalid_location)
    assert len(errors) > 0
```

## 9. 部署注意事项

### 9.1 数据库迁移

**标记语言字段迁移**
```sql
-- 1. 添加新列
ALTER TABLE contacts ADD COLUMN preferred_language VARCHAR(10) NOT NULL DEFAULT 'zh-CN';

-- 2. 创建索引
CREATE INDEX idx_contacts_language ON contacts(tenant_id, preferred_language);

-- 3. 数据迁移（如有历史数据需要设置语言）
UPDATE contacts SET preferred_language = 'zh-CN' WHERE preferred_language IS NULL;
```

### 9.2 配置更新

**系统配置**
```yaml
# 新增配置项
contact:
  language:
    default: "zh-CN"
    supported_languages:
      - "zh-CN"
      - "zh-TW"
      - "en-US"
      # ... 其他语言
  
  location:
    enable_coordinates: true
    enable_timezone_inference: true
    max_field_length: 100
```

### 9.3 依赖服务

**外部服务依赖**
- 地理编码服务（可选）：用于地址解析为坐标
- 时区数据库：用于时区推断
- 国家代码数据库：用于国家代码验证

## 10. 后续扩展

### 10.1 功能扩展

**多语言支持扩展**
- 支持更多语言代码
- 语言偏好权重设置
- 自动语言检测

**地理位置功能扩展**
- 支持更精确的地理位置（街道级别）
- 地理围栏功能
- 距离计算功能

### 10.2 性能优化

**查询优化**
- 地理位置空间索引
- 语言分布统计缓存
- 批量操作优化

**存储优化**
- 地理位置数据压缩
- 历史数据归档策略
- 冷热数据分离
