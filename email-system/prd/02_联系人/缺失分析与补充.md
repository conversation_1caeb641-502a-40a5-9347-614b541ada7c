# 缺失分析与补充｜联系人

## 自检与缺失
- 接口字段表与错误码：需补
- 兼容性与极限值：需补（文件大小、列数、行数、字段长度）
- 性能指标：目标已列，需压测口径
- 异常路径：部分已含，需更全（重复、格式、网络中断）

## 补充
### 1) 限制与阈值
- 单文件 ≤ 2GB；列数 ≤ 200；单字段 ≤ 2KB；批处理分片 10k 行

### 2) 错误码
| code | message | 场景 |
|---|---|---|
| 400201 | invalid_mapping | 字段映射非法 |
| 400202 | invalid_email | 邮箱格式错误 |
| 413001 | file_too_large | 文件过大 |
| 409201 | duplicate_email | 重复冲突 |

### 3) API 字段
- POST `/api/contacts/import`
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| file_url | string | Y | 临时存储地址 |
| mapping | object | Y | 源->目标字段映射 |
| dedupe | string | N | skip/overwrite/merge |
| notify | bool | N | 完成后通知 |

### 4) 压测口径
- 数据模型：10M 联系人，属性均值 10 字段；导入 1M 行、并发 3 任务；目标 1h 内完成
