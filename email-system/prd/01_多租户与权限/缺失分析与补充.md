# 缺失分析与补充｜多租户与权限

## 一、缺失项自检
- 产品类型/目标用户：已明确
- 功能需求：对象级 ACL 冲突策略与回滚未细化（补）
- 非功能需求：威胁模型与加固项不全（补）
- 用户场景：≥3 已覆盖
- 流程/用例：已含
- 界面/原型：已含
- 数据结构：字段表更细化（补）
- 性能/安全/兼容/可扩展：安全与兼容矩阵需补
- 竞品/背景/约束：已含
- API 字段与错误码：需补充
- 里程碑/资源：已有概述，可补测试和风险清单

## 二、补充内容
### 1) 兼容性矩阵
| 维度 | 范围 | 说明 |
|---|---|---|
| 浏览器 | Chrome 2 版本内/Edge/Safari/Firefox | 矩阵回归覆盖 |
| 区域 | 时区/语言 | 权限提示文案 i18n |

### 2) 错误码（节选）
| code | message | 说明 |
|---|---|---|
| 403001 | forbidden | 越权访问 |
| 409010 | acl_conflict | 对象级授权与角色冲突 |
| 429001 | rate_limited | 导出限流 |

### 3) API 字段表（节选）
- POST `/api/acl/upsert`
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| object_type | string | Y | template/campaign/... |
| object_id | string | Y | 目标对象ID |
| principal_type | string | Y | role/user |
| principal_id | string | Y | 目标主体ID |
| actions | array<string> | Y | read/write/publish/delete/export/approve |
| scope | string | N | tenant/project |

### 4) 威胁模型与加固
- 伪造权限变更请求 → 强制二次确认与签名、审计比对
- 导出链接滥用 → 一次性短期 URL + IP 绑定 + 水印

### 5) 回滚与灰度
- 权限策略版本化；回滚影响面预估；灰度在小组内先行

### 6) 指标与告警
- 权限拒绝率、403 峰值、审计入库延迟、导出失败率；阈值与趋势告警
