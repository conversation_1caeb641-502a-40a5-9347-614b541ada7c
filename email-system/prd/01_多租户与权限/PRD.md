# PRD｜多租户与权限（RBAC/ACL/审计）

- 版本/状态：v2.0（Draft）
- 模块：多租户与权限
- 面向用户：企业管理员、团队负责人、合规审计、IT 安全
- 解决问题：多租户数据隔离、最小权限控制、对象级授权、全链路审计与合规留痕
- 方法论合规：本 PRD 按“明确产品类型与用户-细化功能与非功能-结构化输出-补充背景与约束-举例与场景-统一 Markdown/表格/流程图”执行

## 1. 产品背景与目标
- 背景：企业客户需要按租户（Workspace）隔离数据，按角色与对象精细授权，所有关键操作可审计与导出。
- 目标（可量化）：
  - 零跨租户访问事件；权限校验 P95 < 10ms；审计覆盖率 100%；敏感操作 100% 二次确认。

## 2. 用户画像与用户需求
- 管理员：创建租户、配置角色策略、对象授权、导出审计、设置配额。
- 负责人：为本团队资源做对象级授权（如指定模板只给某小组编辑）。
- 合规审计：按时间范围导出操作记录，支持前后值对比与 IP/TraceID 关联。

## 3. 主要功能模块
- 3.1 租户与成员管理
  - 流程：创建租户 → 设置品牌/域名/限额 → 邀请成员 → 分配角色
  - 边界：同一邮箱可加入多个租户；成员状态（active/suspended/left）
- 3.2 角色与权限矩阵（RBAC）
  - 资源×动作矩阵：联系人/模板/活动/旅程/域名/报告/设置 × 读/写/发布/删除/导出/审批
  - 预置角色：管理员/运营/设计/分析/开发/只读；支持自定义
  - 异常：冲突策略从严；权限更新立即生效，提供回滚
- 3.3 对象级授权（ACL）
  - 细粒度到对象（如模板 T1 仅组 A 可写）
  - 批量规则与搜索；继承与覆盖说明
- 3.4 审计日志与导出
  - 覆盖对象：模板、活动、旅程、域名、权限、集成、导入导出、Webhook 等
  - 字段：actor、action、object_type/id、before/after、time、ip、trace_id
  - 导出：异步生成、一次性签名 URL、到期失效
- 3.5 配额与用量
  - 展示：当月额度、已用、预测、超量策略提示

### 界面与操作流程（示意）
- 角色矩阵页：左侧资源树、右侧动作勾选、顶部保存/回滚。
- 对象授权抽屉：搜索对象 → 指定角色/成员 → 指定动作 → 生效范围。

### 核心数据结构（示例）
- Role：id、name、policies{resource:[actions]}
- ACL：object_type、object_id、principal_type（role/user）、actions[]、scope
- AuditLog：id、actor、object、action、before/after、ip、trace_id、created_at

### 异常处理
- 越权访问：返回 403，附带 request_id；审计记录尝试。
- 导出超时：后台继续运行，前端轮询，失败可重试且保留上下文。

## 4. 非功能需求
- 性能：权限判定 P95 < 10ms；审计写入异步落盘
- 安全：最小权限、二次确认、签名导出、IP 白名单（可选）
- 兼容性：多浏览器；API 版本化（v1）
- 可扩展性：新资源与动作通过配置扩展

## 5. 用户故事与使用场景（≥3）
1) 作为管理员，我创建“分析只读”角色，仅允许查看报表与导出，禁止编辑模板/活动。
2) 作为模板 Owner，我为模板 T1 设置对象级授权，仅“品牌B小组”可编辑。
3) 作为合规审计，我导出过去 180 天的所有权限变更与发布操作，带前后值对比。

## 6. 业务流程图
```mermaid
flowchart TD
  A[创建租户] --> B[邀请成员]
  B --> C[分配角色]
  C --> D[对象级授权]
  D --> E[成员执行操作]
  E --> F{权限校验}
  F -- 允许 --> G[写审计]
  F -- 拒绝 --> H[403+审计]
  G --> I[导出审计]
```

## 7. 交互设计与界面原型（文字）
- 权限矩阵：资源为行、动作为列；支持批量勾选、搜索过滤、差异高亮。
- 审计列表：支持对象/操作者/时间/动作过滤；详情页展示 before/after Diff。

## 8. 数据流与接口设计（GET/POST）
- 列表（节选）：
  - POST `/api/roles/create|update|assign|delete`
  - GET `/api/audit/search`，POST `/api/audit/export`
  - POST `/api/acl/upsert|delete`
- 请求示例
```json
POST /api/acl/upsert
{"object_type":"template","object_id":"tpl_123","principal_type":"role","principal_id":"role_analyst","actions":["read"],"scope":"tenant"}
```
- 响应示例（统一格式）
```json
{"code":0,"message":"ok","data":{"id":"acl_001"},"errors":null,"meta":{"request_id":"..."}}
```

## 9. 竞品分析与差异化
- Salesforce/HubSpot 粒度细但重；本产品以“对象级 ACL + 易用矩阵 + 审计导出一键化”为差异。

## 10. 项目里程碑与开发计划（阶段）
- P1：RBAC 与审计（矩阵、导出）
- P2：对象级 ACL 与回滚
- P3：配额与租户运营看板

## 附：缺失分析与补充（并入）
- 兼容性矩阵：浏览器与多语言支持已补充；权限提示 i18n。
- 错误码补充：403001 forbidden、409010 acl_conflict、429001 rate_limited。
- API 字段表补充：`POST /api/acl/upsert` 增加 `principal_type/principal_id/actions/scope` 说明。
- 威胁模型：权限变更伪造与导出滥用 → 二次确认、签名、短期一次性 URL、IP 绑定、水印。
- 回滚与灰度：策略版本化、影响面预估、团队级灰度发布。
- 指标与告警：权限拒绝率、403 峰值、审计入库延迟、导出失败率。
