# 原型设计｜多租户与权限（RBAC/ACL/审计）

## 页面地图
| 页面 | 入口 | 目标 | 关键操作 |
|---|---|---|---|
| 租户总览 | 设置/租户 | 查看租户与配额 | 创建租户、查看配额与用量 |
| 团队与成员 | 设置/团队 | 管理成员与角色 | 邀请成员、禁用/启用、分配角色 |
| 角色与权限矩阵 | 设置/权限 | 配置资源×动作矩阵 | 新建/编辑角色、批量勾选、版本回滚 |
| 对象级授权（ACL） | 模板/活动/旅程详情侧栏 | 给对象设置特定授权 | 添加授权、继承与覆盖、批量移除 |
| 审计日志 | 设置/审计 | 查询与导出操作记录 | 过滤、导出（异步）、详情 Diff |
| 配额与用量 | 设置/配额 | 查看当月额度与预测 | 配置阈值提醒 |

## 关键页面原型说明
### 角色与权限矩阵
- 左侧：资源树（联系人/模板/活动/旅程/域名/报告/设置）。
- 右侧：动作列（读/写/发布/删除/导出/审批），支持全选/取消/差异高亮。
- 顶部：保存、回滚、搜索角色。保存后弹出“影响面预览”和二次确认。

### 对象级授权抽屉
- 触发：在对象详情页点击“授权”。
- 区域：搜索用户/角色 → 勾选动作 → 选择作用域（租户/项目）→ 生效按钮。
- 冲突提示：展示与角色矩阵冲突项，按从严原则处理。

### 审计详情
- 展示 before/after JSON Diff，包含 actor、time、ip、trace_id；支持一键复制 request_id。

## 交互流程（权限校验与审计）
```mermaid
flowchart TD
  A[发起操作] --> B{权限判定}
  B -- 允许 --> C[执行]
  C --> D[写审计]
  B -- 拒绝 --> E[403 提示]
  E --> D
```

## 组件与状态
- 组件：资源树、矩阵表、对象授权抽屉、Diff 视图、导出进度条。
- 状态：加载、空态（“暂无角色/暂无审计”）、错误（权限不足/网络失败）。

## 表单字段与校验（节选）
| 字段 | 类型 | 规则 |
|---|---|---|
| 角色名称 | 文本 | 必填，1-64 字，唯一 |
| 资源动作 | 多选 | 至少 1 项 |
| ACL 主体 | 用户/角色 | 至少 1 个主体，主体存在校验 |

## 边界/空态/错误
- 冲突策略：对象 ACL 与角色矩阵冲突时从严；
- 导出：大于 100 万行异步，生成一次性链接；
- 越权：给出申诉入口（联系管理员）。

## 无障碍与可用性
- 键盘导航、ARIA 标签；矩阵单元格可聚焦；对比度达标。

## 埋点与指标
- 点击：保存/回滚/导出/授权；
- 错误：403 次数；
- 转化：完成角色配置到首个成功授权的漏斗。

## 原型链接（占位）
- 低保真原型：Figma 链接（占位）
- 交互说明：Confluence 链接（占位）
