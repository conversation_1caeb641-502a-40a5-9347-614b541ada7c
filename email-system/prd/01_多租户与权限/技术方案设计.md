# 技术方案设计｜多租户与权限

## 1. 项目背景与目标
- 背景：企业级数据隔离与权限治理需要统一能力层，满足最小权限与审计合规。
- 目标：权限判定 P95<10ms、零越权、审计覆盖 100%、导出安全可控。
- 关联 PRD：RBAC/ACL/审计/配额（PRD 第 3、4、5、8 章）。

## 2. 整体架构设计（HLD）
- 组件：Auth 网关（JWT）、RBAC 服务、ACL 评估器、审计流水（Kafka）、导出作业器、缓存层（Redis）
- 数据：`tenants`、`users`、`roles`、`role_policies`、`object_acl`、`audit_logs`、`quotas`
 - 技术选型：Go/GIN、Kafka、MySQL(+分区)、Redis、OTel、S3 兼容对象存储。

## 3. 功能模块设计与关键时序
- 模块输入/输出：
  - RBAC：in(user, resource, action) -> allow/deny
  - ACL：in(object, principal, actions) -> allow/deny
  - 审计：in(context, before, after) -> append-only log
  - 导出：in(query) -> signed_url

```mermaid
sequenceDiagram
actor U as 用户
participant API as API Gateway
participant RBAC as RBAC/ACL
participant SVC as 目标服务
participant AUD as Audit Log
U->>API: 操作请求
API->>RBAC: 鉴权+权限判定
RBAC-->>API: allow/deny
API->>SVC: 执行业务
SVC->>AUD: 记录审计
API-->>U: 响应
```

## 4. 接口设计
- 内部：`rbac.check(user_id, resource, action)`；`acl.check(object_type, object_id, principal)`
- 外部：
  - POST `/api/roles/create|update|assign|delete`
  - POST `/api/acl/upsert|delete`
  - GET `/api/audit/search`，POST `/api/audit/export`
- 协议：REST（JSON），统一响应。
- 字段与错误码：见 PRD 附录“缺失分析与补充”。

## 5. 数据设计
- 权限判定：先 RBAC 再 ACL 从严；缓存 key `perm:{tenant}:{user}:{object}` TTL 60s
- 审计：异步写入（Kafka→批量落库），失败回补队列
- 导出：任务表 + 分片生成 + 一次性签名 URL
- ER 与表结构（节选）：
  - roles(id, tenant_id, name)
  - role_policies(role_id, resource, actions json)
  - object_acl(object_type, object_id, principal_type, principal_id, actions json)
  - audit_logs(id, tenant_id, actor, object_type, object_id, before, after, ts)

## 6. 模型与索引
- `audit_logs(tenant_id, object_type, object_id, actor, ts)` 复合索引
- `object_acl(object_type, object_id, principal_type, principal_id)` 索引

## 7. 缓存设计
- Redis 判定缓存 + 本地 LRU，穿透保护与失效通知（pub/sub）。

## 8. 关键技术难点与解决方案
- 高并发判定：多级缓存 + 向量化批量判定接口（可选）
- 分布式一致性：审计采用追加日志，允许最终一致；导出任务幂等 job_id
- 回滚：策略版本表与影子发布，提供一键回滚

## 9. 可观测
- 指标：判定耗时、缓存命中率、导出耗时、审计吞吐
- 日志：拒绝原因、before/after Diff
- Trace：request_id/trace_id 贯穿

## 10. 安全与合规
- 最小权限、二次确认、IP 白名单（可选）、数据加密（静态/传输）

## 11. 容量与性能
- 10k QPS 判定：Redis 缓存 + 本地 LRU；批量判定 API（可选）

## 12. 失败与回滚
- 策略回滚按钮；导出任务断点续传

## 13. 测试与灰度
- 权限矩阵用例库；回放审计; 灰度按团队级开关
