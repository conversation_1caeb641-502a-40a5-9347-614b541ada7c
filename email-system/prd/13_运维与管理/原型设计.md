# 原型设计｜运维与管理

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 系统看板 | 态势与健康 | 队列/吞吐/错误/域名/IP 健康 |
| 告警中心 | 告警治理 | 规则管理/抑制/值班/渠道 |
| 任务中心 | 批量任务 | 新建任务/进度/暂停/重试/审计 |

## 关键原型
- 看板：多图表卡片，支持筛选域名/租户；
- 任务：任务 DAG 视图与日志查看。

## 流程
```mermaid
flowchart LR
  Mon[监控] --> Rule[规则]
  Rule --> Alert[告警]
  Task[任务创建] --> Exec[执行]
  Exec --> Audit[审计]
```

## 组件/状态
- 图表、任务卡、日志抽屉、值班计划表。

## 校验
- 任务并发与配额校验；危险操作二次确认。

## 空态/错误
- 无告警时显示“平稳运行”；任务失败提供重试方案。