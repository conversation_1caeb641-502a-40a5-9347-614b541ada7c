# PRD｜追踪与分析与报表

- 技术细节补充：
  - `prd/10_追踪与分析与报表/打开率追踪_技术细节.md`
  - `prd/10_追踪与分析与报表/发件渠道时间表与A_B测试_技术细节.md`

## 1. 背景与目标
- 背景：统一事件追踪（发送/投递/打开/点击/退信/投诉/退订/转化），标准化口径。
- 目标：事件延迟 P95<2s；报表分钟级聚合；导出与订阅稳定。

## 2. 用户画像与需求
- 运营/分析：多维报表、热力图与归因；
- 开发：稳定事件 API 与防重复。

## 3. 功能模块
- 事件采集：像素打开、链接重写点击、JS/后端转化
- 报表：活动/旅程/联系人/域名；链接热力图、设备/地域/客户端/域名维度
- 导出与订阅：CSV/Excel、邮件订阅、Webhook 推送
- 指标口径：到达率、唯一打开/点击、CTOR、退订/硬退/投诉、转化率

### 流程与异常
- 链接重写失败回退到直链；像素被拦截时以点击替代打开判断（可配置）。
- 打开口径采用双口径：Gross（像素命中）与 Estimated Human（估算人类，含点击回填与代理估算），详见技术细节文档。
- 代理说明：Apple MPP/Gmail Proxy/安全扫描器将引入机器打开，系统会识别与标注，并在报表中展示代理占比与估算占比。

### 数据结构
- Event：id、type、properties、contact_id、message_id、occurred_at、source
- Aggregation：by campaign/journey/domain/time_window

## 4. 非功能
- 吞吐与延迟目标；幂等去重；冷热分层与物化视图。

## 5. 用户故事
1) 运营查看 A 活动链接热力图识别高点击区域；
2) 分析按域名维度对比到达率与投诉率；
3) 导出近 30 天报表并订阅周报。

## 6. 流程图
```mermaid
flowchart LR
  Send[发送事件] --> Track[追踪采集]
  Track --> Queue[队列]
  Queue --> Agg[聚合]
  Agg --> Report[报表]
  Agg --> Export[导出订阅]
```

## 7. 数据与接口
- 事件：POST `/api/events/ingest`
```json
{"type":"click","message_id":"msg_1","contact_id":"c_1","url":"https://...","ts":1730000000}
```
- 报表：POST `/api/reports/export|subscribe`，`/api/campaigns/report`

## 8. 竞品与差异
- 口径透明 + 事件与报表对齐；链接热力与域名维度并重。

## 9. 里程碑
- P1 事件与基础报表；P2 维度与热力；P3 导出订阅与归因强化。