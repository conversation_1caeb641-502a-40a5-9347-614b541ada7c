# 发件渠道时间表与A/B测试技术细节

## 概述

本文档详细描述了邮件营销系统中新增的核心功能技术实现，包括：
- 发件渠道时间表配置
- A/B测试策略管理
- 多语言模板支持
- 自动化旅程增强

## 1. 发件渠道时间表

### 1.1 功能概述

发件渠道时间表功能允许为不同国家和语言的人群配置发送时间，支持：
- 统一配置基准时间（如上午10:00）
- 自动匹配时区发送
- 国家/地区特定时间配置
- 智能时间优化

### 1.2 技术架构

#### 1.2.1 数据库设计

```sql
-- 渠道时间表配置
CREATE TABLE channel_schedules (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  channel_id        BIGINT NOT NULL,
  schedule_type     VARCHAR(32) NOT NULL, -- fixed/timezone/immediate/custom
  base_time         TIME NULL,            -- 基准时间（如 10:00）
  timezone_strategy VARCHAR(32) NULL,     -- user_timezone/business_hours/optimal_hours
  timezone_sources  JSON NULL,            -- 时区数据来源配置
  custom_rules      TEXT NULL,            -- 自定义规则描述
  smart_optimization TINYINT(1) NOT NULL DEFAULT 0, -- 启用智能时间优化
  avoid_holidays    TINYINT(1) NOT NULL DEFAULT 1,  -- 避免节假日发送
  workdays_only     TINYINT(1) NOT NULL DEFAULT 0,  -- 工作日限制
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_channel_schedule (tenant_id, channel_id),
  KEY idx_schedule_type (tenant_id, schedule_type)
);

-- 国家/地区特定时间配置
CREATE TABLE channel_country_times (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  channel_id        BIGINT NOT NULL,
  country_code      VARCHAR(8) NOT NULL,  -- CN/US/EU/JP
  country_name      VARCHAR(64) NOT NULL,
  send_time         TIME NOT NULL,        -- 该国家的发送时间
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_country_time (tenant_id, channel_id, country_code),
  KEY idx_country_time (tenant_id, country_code)
);
```

#### 1.2.2 时区处理逻辑

```python
class TimezoneHandler:
    def __init__(self, channel_schedule):
        self.schedule = channel_schedule
        self.timezone_sources = channel_schedule.timezone_sources
    
    def get_user_timezone(self, contact):
        """获取用户时区"""
        timezone = None
        
        # 1. 用户注册时区（优先级最高）
        if 'user_registration' in self.timezone_sources:
            timezone = contact.preferred_timezone
        
        # 2. IP地理位置
        if not timezone and 'ip_geolocation' in self.timezone_sources:
            timezone = self.get_timezone_by_ip(contact.last_ip)
        
        # 3. 浏览器时区
        if not timezone and 'browser_timezone' in self.timezone_sources:
            timezone = contact.browser_timezone
        
        return timezone or 'UTC'
    
    def calculate_send_time(self, contact, base_time):
        """计算用户实际发送时间"""
        user_timezone = self.get_user_timezone(contact)
        
        if self.schedule.timezone_strategy == 'business_hours':
            return self.adjust_to_business_hours(base_time, user_timezone)
        elif self.schedule.timezone_strategy == 'optimal_hours':
            return self.adjust_to_optimal_hours(base_time, user_timezone)
        else:
            return self.adjust_to_user_timezone(base_time, user_timezone)
    
    def adjust_to_business_hours(self, base_time, timezone):
        """调整为工作时间（9:00-18:00）"""
        # 实现逻辑：确保发送时间在工作时间内
        pass
    
    def adjust_to_optimal_hours(self, base_time, timezone):
        """调整为最佳时间（10:00-14:00）"""
        # 实现逻辑：确保发送时间在最佳时间窗口内
        pass
```

#### 1.2.3 智能时间优化

```python
class SmartTimeOptimizer:
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
    
    def get_optimal_send_time(self, contact, channel_id):
        """获取用户的最佳发送时间"""
        # 基于历史数据计算最佳发送时间
        historical_data = self.get_historical_performance(contact, channel_id)
        
        if historical_data:
            return self.calculate_optimal_time(historical_data)
        else:
            return self.get_default_optimal_time(contact.timezone)
    
    def get_historical_performance(self, contact, channel_id):
        """获取历史发送表现数据"""
        # 查询该用户的历史打开率、点击率等数据
        # 按小时分组统计
        pass
    
    def calculate_optimal_time(self, historical_data):
        """基于历史数据计算最佳时间"""
        # 使用机器学习算法或统计方法
        # 考虑打开率、点击率、转化率等指标
        pass
```

### 1.3 前端实现

#### 1.3.1 时间表配置界面

```javascript
// 时间表类型切换
function toggleScheduleType() {
  const scheduleType = document.getElementById('schedule-type').value;
  const fixedConfig = document.getElementById('fixed-time-config');
  const timezoneConfig = document.getElementById('timezone-config');
  const customConfig = document.getElementById('custom-config');
  
  // 隐藏所有配置
  fixedConfig.style.display = 'none';
  timezoneConfig.style.display = 'none';
  customConfig.style.display = 'none';
  
  // 显示对应配置
  switch(scheduleType) {
    case 'fixed':
      fixedConfig.style.display = 'block';
      break;
    case 'timezone':
      timezoneConfig.style.display = 'block';
      break;
    case 'custom':
      customConfig.style.display = 'block';
      break;
  }
}

// 加载渠道信息
function loadChannelInfo() {
  const channelId = document.getElementById('campaign-channel').value;
  const channelInfo = document.getElementById('channel-info');
  
  if (channelId) {
    // 模拟加载渠道信息
    const channelData = {
      '1': {
        domain: 'marketing.example.com',
        quota: '50,000 (已用 32,450)',
        schedule: '时区匹配 (10:00)'
      }
    };
    
    const data = channelData[channelId];
    document.getElementById('channel-domain').textContent = data.domain;
    document.getElementById('channel-quota').textContent = data.quota;
    document.getElementById('channel-schedule').textContent = data.schedule;
    
    channelInfo.style.display = 'block';
  } else {
    channelInfo.style.display = 'none';
  }
}
```

## 2. A/B测试策略管理

### 2.1 功能概述

A/B测试策略管理功能支持：
- 创建和管理A/B测试实验
- 多种测试类型（主题行、发送时间、内容等）
- 灵活的流量分配策略
- 策略库管理
- 实时监控和自动优化

### 2.2 技术架构

#### 2.2.1 数据库设计

```sql
-- A/B测试实验
CREATE TABLE ab_experiments (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  target_type       VARCHAR(32) NOT NULL,  -- campaign/template/strategy
  target_id         BIGINT NULL,           -- 目标对象ID
  experiment_type   VARCHAR(32) NOT NULL,  -- subject/sender/send_time/content/template
  sample_percentage  INT NOT NULL DEFAULT 20, -- 总样本比例
  variant_count     INT NOT NULL DEFAULT 2,   -- 变体数量
  traffic_allocation VARCHAR(32) NOT NULL DEFAULT 'equal', -- equal/weighted/bayesian/manual
  winning_metric    VARCHAR(32) NOT NULL,  -- open_rate/click_rate/conversion_rate/revenue
  observation_hours INT NOT NULL DEFAULT 6, -- 观察期（小时）
  winning_strategy  VARCHAR(32) NOT NULL DEFAULT 'auto', -- auto/significance/manual
  statistical_significance DECIMAL(5,4) NOT NULL DEFAULT 0.05, -- 统计显著性
  min_sample_size   INT NOT NULL DEFAULT 1000, -- 最小样本量
  real_time_monitoring TINYINT(1) NOT NULL DEFAULT 1, -- 启用实时监控
  auto_stop_invalid TINYINT(1) NOT NULL DEFAULT 1, -- 自动停止无效实验
  save_as_strategy  TINYINT(1) NOT NULL DEFAULT 0, -- 保存为策略模板
  completion_notification TINYINT(1) NOT NULL DEFAULT 1, -- 实验完成通知
  anomaly_alert     TINYINT(1) NOT NULL DEFAULT 0, -- 异常情况告警
  status            VARCHAR(32) NOT NULL DEFAULT 'active', -- active/paused/completed/stopped
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_experiment (tenant_id, name),
  KEY idx_ab_experiment_target (tenant_id, target_type, target_id),
  KEY idx_ab_experiment_status (tenant_id, status)
);

-- A/B测试变体
CREATE TABLE ab_experiment_variants (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  experiment_id     BIGINT NOT NULL,
  variant_key       VARCHAR(16) NOT NULL,  -- A/B/C/D/E
  variant_name      VARCHAR(64) NOT NULL,
  variant_value     TEXT NOT NULL,         -- 变体内容
  variant_description VARCHAR(255) NULL,   -- 变体描述
  traffic_percentage INT NOT NULL DEFAULT 50, -- 流量分配比例
  status            VARCHAR(32) NOT NULL DEFAULT 'active', -- active/inactive
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_variant (tenant_id, experiment_id, variant_key),
  KEY idx_ab_variant_experiment (tenant_id, experiment_id)
);

-- A/B测试策略库
CREATE TABLE ab_strategies (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  description       VARCHAR(512) NULL,
  strategy_type     VARCHAR(32) NOT NULL,  -- subject/sender/send_time/content/template
  config_json       JSON NOT NULL,         -- 策略配置
  validation_status VARCHAR(32) NOT NULL DEFAULT 'testing', -- testing/validated/failed
  usage_count       INT NOT NULL DEFAULT 0, -- 使用次数
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_strategy (tenant_id, name),
  KEY idx_ab_strategy_type (tenant_id, strategy_type),
  KEY idx_ab_strategy_status (tenant_id, validation_status)
);
```

#### 2.2.2 流量分配算法

```python
class TrafficAllocation:
    def __init__(self, experiment):
        self.experiment = experiment
        self.allocation_type = experiment.traffic_allocation
    
    def allocate_variant(self, contact_id):
        """为联系人分配变体"""
        if self.allocation_type == 'equal':
            return self.equal_allocation(contact_id)
        elif self.allocation_type == 'weighted':
            return self.weighted_allocation(contact_id)
        elif self.allocation_type == 'bayesian':
            return self.bayesian_allocation(contact_id)
        elif self.allocation_type == 'manual':
            return self.manual_allocation(contact_id)
    
    def equal_allocation(self, contact_id):
        """平均分配"""
        variants = self.experiment.variants
        variant_count = len(variants)
        return variants[contact_id % variant_count]
    
    def weighted_allocation(self, contact_id):
        """加权分配"""
        variants = self.experiment.variants
        weights = [v.traffic_percentage for v in variants]
        
        # 使用加权随机选择
        return random.choices(variants, weights=weights)[0]
    
    def bayesian_allocation(self, contact_id):
        """贝叶斯多臂赌博机"""
        # 实现贝叶斯优化算法
        # 基于历史表现动态调整分配
        pass
    
    def manual_allocation(self, contact_id):
        """手动分配"""
        # 根据预设的分配比例进行分配
        pass
```

#### 2.2.3 统计显著性计算

```python
class StatisticalSignificance:
    def __init__(self, significance_level=0.05):
        self.significance_level = significance_level
    
    def calculate_significance(self, variant_a_data, variant_b_data):
        """计算两个变体之间的统计显著性"""
        # 使用卡方检验或t检验
        if self.is_continuous_metric(variant_a_data):
            return self.t_test(variant_a_data, variant_b_data)
        else:
            return self.chi_square_test(variant_a_data, variant_b_data)
    
    def t_test(self, data_a, data_b):
        """t检验"""
        from scipy import stats
        t_stat, p_value = stats.ttest_ind(data_a, data_b)
        return {
            'significant': p_value < self.significance_level,
            'p_value': p_value,
            't_statistic': t_stat
        }
    
    def chi_square_test(self, data_a, data_b):
        """卡方检验"""
        from scipy import stats
        chi2_stat, p_value = stats.chi2_contingency([data_a, data_b])[:2]
        return {
            'significant': p_value < self.significance_level,
            'p_value': p_value,
            'chi2_statistic': chi2_stat
        }
```

### 2.3 前端实现

#### 2.3.1 实验创建界面

```javascript
// 加载实验配置
function loadExperimentConfig() {
  const expType = document.getElementById('exp-type').value;
  const variantConfig = document.getElementById('variant-config');
  
  // 清空现有配置
  variantConfig.innerHTML = '';
  
  if (expType) {
    // 根据实验类型生成默认变体
    const variants = getDefaultVariants(expType);
    variants.forEach((variant, index) => {
      addExperimentVariant(variant);
    });
  }
}

// 获取默认变体
function getDefaultVariants(expType) {
  const variants = {
    'subject': [
      { name: '变体A', value: '🎉 双11预热，限时优惠等你来！', description: 'emoji + 优惠信息' },
      { name: '变体B', value: '🔥 双11大促即将开始，抢先一步！', description: 'emoji + 紧迫感' }
    ],
    'sender': [
      { name: '变体A', value: 'Marketing Team', description: '团队名称' },
      { name: '变体B', value: 'Customer Success', description: '客户成功' }
    ],
    'send-time': [
      { name: '变体A', value: '09:00', description: '上午发送' },
      { name: '变体B', value: '14:00', description: '下午发送' }
    ]
  };
  
  return variants[expType] || [];
}

// 添加实验变体
function addExperimentVariant(defaultVariant = null) {
  const variantConfig = document.getElementById('variant-config');
  const variantIndex = variantConfig.children.length;
  
  const variantDiv = document.createElement('div');
  variantDiv.className = 'card';
  variantDiv.style.marginBottom = '10px';
  
  const variantName = defaultVariant ? defaultVariant.name : `变体${String.fromCharCode(65 + variantIndex)}`;
  const variantValue = defaultVariant ? defaultVariant.value : '';
  const variantDesc = defaultVariant ? defaultVariant.description : '';
  
  variantDiv.innerHTML = `
    <div class="grid cols-3">
      <div>
        <label>变体名称</label>
        <input placeholder="变体名称" value="${variantName}" />
      </div>
      <div>
        <label>变体内容</label>
        <input placeholder="变体内容" value="${variantValue}" />
      </div>
      <div>
        <label>描述</label>
        <input placeholder="变体描述" value="${variantDesc}" />
      </div>
    </div>
    <div style="margin-top: 10px;">
      <button class="btn mini secondary" onclick="removeExperimentVariant(this)">删除变体</button>
    </div>
  `;
  
  variantConfig.appendChild(variantDiv);
}
```

## 3. 多语言模板支持

### 3.1 功能概述

多语言模板支持功能允许：
- 为同一模板创建多个语言版本
- 自动根据用户语言偏好选择模板
- 支持资源覆盖和本地化
- 语言回退机制

### 3.2 技术架构

#### 3.2.1 数据库设计

```sql
-- 多语言模板变体
CREATE TABLE template_locales (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  template_id       BIGINT NOT NULL,
  version_no        INT NOT NULL,
  locale            VARCHAR(16) NOT NULL,  -- zh-CN/en-US/es-ES
  subject           VARCHAR(512) NOT NULL,
  preheader         VARCHAR(255) NULL,
  content_html      MEDIUMTEXT NOT NULL,
  assets_overrides  JSON NULL,             -- 资源覆盖配置
  is_default        TINYINT(1) NOT NULL DEFAULT 0, -- 是否默认语言
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tpl_locale (tenant_id, template_id, version_no, locale),
  KEY idx_tpl_locale_template (tenant_id, template_id, version_no),
  KEY idx_tpl_locale_default (tenant_id, template_id, is_default)
);
```

#### 3.2.2 语言选择逻辑

```python
class LocaleSelector:
    def __init__(self, template_id, version_no):
        self.template_id = template_id
        self.version_no = version_no
    
    def select_locale(self, contact, campaign_config):
        """选择最适合的语言版本"""
        # 1. 检查活动是否强制使用特定语言
        if campaign_config.get('force_locale'):
            return self.get_locale(campaign_config['force_locale'])
        
        # 2. 根据用户偏好选择
        user_locale = contact.preferred_locale
        if user_locale:
            locale = self.get_locale(user_locale)
            if locale:
                return locale
        
        # 3. 使用默认语言
        default_locale = self.get_default_locale()
        if default_locale:
            return default_locale
        
        # 4. 回退到第一个可用语言
        return self.get_first_available_locale()
    
    def get_locale(self, locale_code):
        """获取指定语言版本"""
        return TemplateLocale.query.filter_by(
            template_id=self.template_id,
            version_no=self.version_no,
            locale=locale_code
        ).first()
    
    def get_default_locale(self):
        """获取默认语言版本"""
        return TemplateLocale.query.filter_by(
            template_id=self.template_id,
            version_no=self.version_no,
            is_default=True
        ).first()
    
    def get_first_available_locale(self):
        """获取第一个可用语言版本"""
        return TemplateLocale.query.filter_by(
            template_id=self.template_id,
            version_no=self.version_no
        ).first()
```

### 3.3 前端实现

#### 3.3.1 多语言配置界面

```javascript
// 加载模板信息
function loadTemplateInfo() {
  const templateId = document.getElementById('campaign-template').value;
  const multilingualOptions = document.getElementById('multilingual-options');
  
  if (templateId === '1' || templateId === '2') {
    multilingualOptions.style.display = 'block';
  } else {
    multilingualOptions.style.display = 'none';
  }
}

// 多语言配置
function configureMultilingual() {
  const languages = [
    { code: 'zh-CN', name: '中文 (简体)', checked: true },
    { code: 'en-US', name: 'English', checked: true },
    { code: 'es-ES', name: 'Español', checked: false }
  ];
  
  const container = document.getElementById('multilingual-options');
  container.innerHTML = `
    <label>多语言配置</label>
    <div class="card" style="background: var(--panel-2);">
      <div class="row">
        ${languages.map(lang => `
          <div>
            <input type="checkbox" ${lang.checked ? 'checked' : ''} 
                   onchange="updateLanguageConfig('${lang.code}', this.checked)" />
            ${lang.name}
          </div>
        `).join('')}
      </div>
      <div class="hint">系统将根据用户语言偏好自动选择对应版本</div>
    </div>
  `;
}

// 更新语言配置
function updateLanguageConfig(languageCode, enabled) {
  // 更新多语言配置
  console.log(`Language ${languageCode} ${enabled ? 'enabled' : 'disabled'}`);
}
```

## 4. 自动化旅程增强

### 4.1 功能概述

自动化旅程增强功能包括：
- 渠道和模板选择
- 多语言配置支持
- 时区处理
- 退出条件配置
- 模板库管理

### 4.2 技术架构

#### 4.2.1 数据库设计

```sql
-- 自动化旅程模板库
CREATE TABLE journey_templates (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  description       VARCHAR(512) NULL,
  journey_type      VARCHAR(32) NOT NULL,  -- welcome/onboarding/re_engagement/abandoned_cart
  template_config   JSON NOT NULL,         -- 模板配置
  steps_count       INT NOT NULL DEFAULT 0, -- 步骤数量
  validation_status VARCHAR(32) NOT NULL DEFAULT 'testing', -- testing/validated/failed
  usage_count       INT NOT NULL DEFAULT 0, -- 使用次数
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_journey_template (tenant_id, name),
  KEY idx_journey_template_type (tenant_id, journey_type),
  KEY idx_journey_template_status (tenant_id, validation_status)
);
```

#### 4.2.2 旅程执行引擎

```python
class JourneyExecutionEngine:
    def __init__(self, journey_id, contact_id):
        self.journey_id = journey_id
        self.contact_id = contact_id
        self.journey = Journey.query.get(journey_id)
        self.contact = Contact.query.get(contact_id)
    
    def execute_journey(self):
        """执行旅程"""
        # 1. 检查进入条件
        if not self.check_entry_conditions():
            return False
        
        # 2. 创建旅程实例
        instance = self.create_journey_instance()
        
        # 3. 执行第一个节点
        self.execute_node(instance, self.journey.start_node)
        
        return True
    
    def check_entry_conditions(self):
        """检查进入条件"""
        # 检查频率限制
        if not self.check_frequency_limit():
            return False
        
        # 检查受众过滤
        if not self.check_audience_filter():
            return False
        
        return True
    
    def execute_node(self, instance, node):
        """执行节点"""
        if node.type == 'send_email':
            self.execute_send_email_node(instance, node)
        elif node.type == 'wait':
            self.execute_wait_node(instance, node)
        elif node.type == 'condition':
            self.execute_condition_node(instance, node)
        elif node.type == 'ab_test':
            self.execute_ab_test_node(instance, node)
    
    def execute_send_email_node(self, instance, node):
        """执行发送邮件节点"""
        # 获取模板和渠道
        template_id = node.config.get('template_id')
        channel_id = node.config.get('channel_id')
        
        # 选择语言
        locale_selector = LocaleSelector(template_id, node.config.get('version_no'))
        locale = locale_selector.select_locale(self.contact, node.config)
        
        # 创建邮件消息
        message = EmailMessage(
            tenant_id=self.journey.tenant_id,
            journey_id=self.journey_id,
            journey_node_id=node.id,
            contact_id=self.contact_id,
            template_id=template_id,
            locale_used=locale.locale,
            channel_id=channel_id
        )
        
        # 发送邮件
        self.send_email(message)
```

### 4.3 前端实现

#### 4.3.1 旅程创建界面

```javascript
// 加载渠道信息
function loadChannelInfo() {
  const channelId = document.getElementById('journey-channel').value;
  const channelInfo = document.getElementById('journey-channel-info');
  
  if (channelId) {
    // 模拟加载渠道信息
    const channelData = {
      '1': {
        domain: 'marketing.example.com',
        quota: '50,000 (已用 32,450)',
        schedule: '时区匹配 (10:00)'
      }
    };
    
    const data = channelData[channelId];
    document.getElementById('journey-domain').textContent = data.domain;
    document.getElementById('journey-quota').textContent = data.quota;
    document.getElementById('journey-schedule').textContent = data.schedule;
    
    channelInfo.style.display = 'block';
  } else {
    channelInfo.style.display = 'none';
  }
}

// 添加旅程步骤
function addStep(stepType) {
  const stepsContainer = document.getElementById('journey-steps');
  const stepDiv = document.createElement('div');
  stepDiv.className = 'card';
  stepDiv.onclick = function() { editStep(stepsContainer.children.length + 1); };
  
  switch(stepType) {
    case 'trigger':
      stepDiv.innerHTML = '<div class="mini">触发：新步骤</div><div class="hint">点击编辑触发条件</div>';
      break;
    case 'wait':
      stepDiv.innerHTML = '<div class="mini">等待：新步骤</div><div class="hint">点击编辑等待时间</div>';
      break;
    case 'condition':
      stepDiv.innerHTML = '<div class="mini">条件：新步骤</div><div class="hint">点击编辑条件逻辑</div>';
      break;
    case 'ab-test':
      stepDiv.innerHTML = '<div class="mini">A/B 测试：新步骤</div><div class="hint">点击编辑测试配置</div>';
      break;
    case 'action':
      stepDiv.innerHTML = '<div class="mini">动作：新步骤</div><div class="hint">点击编辑动作内容</div>';
      break;
  }
  
  stepsContainer.appendChild(stepDiv);
}
```

## 5. 性能优化

### 5.1 数据库优化

- 为常用查询字段添加索引
- 使用分区表处理大量数据
- 实现读写分离
- 使用缓存减少数据库压力

### 5.2 缓存策略

```python
class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def get_channel_schedule(self, channel_id):
        """获取渠道时间表配置"""
        cache_key = f"channel_schedule:{channel_id}"
        cached = self.redis_client.get(cache_key)
        
        if cached:
            return json.loads(cached)
        
        # 从数据库加载
        schedule = ChannelSchedule.query.filter_by(channel_id=channel_id).first()
        if schedule:
            self.redis_client.setex(cache_key, 3600, json.dumps(schedule.to_dict()))
        
        return schedule
    
    def get_template_locale(self, template_id, version_no, locale):
        """获取模板语言版本"""
        cache_key = f"template_locale:{template_id}:{version_no}:{locale}"
        cached = self.redis_client.get(cache_key)
        
        if cached:
            return json.loads(cached)
        
        # 从数据库加载
        template_locale = TemplateLocale.query.filter_by(
            template_id=template_id,
            version_no=version_no,
            locale=locale
        ).first()
        
        if template_locale:
            self.redis_client.setex(cache_key, 1800, json.dumps(template_locale.to_dict()))
        
        return template_locale
```

### 5.3 异步处理

```python
class AsyncTaskProcessor:
    def __init__(self):
        self.celery_app = Celery('email_system')
    
    @celery_app.task
    def process_timezone_calculation(self, contact_id, channel_id, base_time):
        """异步处理时区计算"""
        try:
            contact = Contact.query.get(contact_id)
            channel_schedule = ChannelSchedule.query.filter_by(channel_id=channel_id).first()
            
            timezone_handler = TimezoneHandler(channel_schedule)
            send_time = timezone_handler.calculate_send_time(contact, base_time)
            
            # 更新发送时间
            self.update_send_time(contact_id, send_time)
            
        except Exception as e:
            logger.error(f"Timezone calculation failed: {e}")
    
    @celery_app.task
    def process_ab_test_allocation(self, experiment_id, contact_id):
        """异步处理A/B测试分配"""
        try:
            experiment = ABExperiment.query.get(experiment_id)
            contact = Contact.query.get(contact_id)
            
            allocator = TrafficAllocation(experiment)
            variant = allocator.allocate_variant(contact_id)
            
            # 记录分配结果
            self.record_allocation(experiment_id, contact_id, variant.id)
            
        except Exception as e:
            logger.error(f"AB test allocation failed: {e}")
```

## 6. 监控和告警

### 6.1 关键指标监控

```python
class MetricsCollector:
    def __init__(self):
        self.prometheus_client = PrometheusClient()
    
    def record_timezone_calculation_time(self, duration):
        """记录时区计算耗时"""
        self.prometheus_client.histogram(
            'timezone_calculation_duration_seconds',
            duration,
            labels={'operation': 'calculate_send_time'}
        )
    
    def record_ab_test_allocation_time(self, duration):
        """记录A/B测试分配耗时"""
        self.prometheus_client.histogram(
            'ab_test_allocation_duration_seconds',
            duration,
            labels={'operation': 'allocate_variant'}
        )
    
    def record_locale_selection_time(self, duration):
        """记录语言选择耗时"""
        self.prometheus_client.histogram(
            'locale_selection_duration_seconds',
            duration,
            labels={'operation': 'select_locale'}
        )
```

### 6.2 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: email_system_alerts
    rules:
      - alert: TimezoneCalculationSlow
        expr: histogram_quantile(0.95, timezone_calculation_duration_seconds) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "时区计算耗时过高"
          description: "时区计算95分位数耗时超过1秒"
      
      - alert: ABTestAllocationSlow
        expr: histogram_quantile(0.95, ab_test_allocation_duration_seconds) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "A/B测试分配耗时过高"
          description: "A/B测试分配95分位数耗时超过0.5秒"
      
      - alert: LocaleSelectionSlow
        expr: histogram_quantile(0.95, locale_selection_duration_seconds) > 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "语言选择耗时过高"
          description: "语言选择95分位数耗时超过0.3秒"
```

## 7. 部署和运维

### 7.1 部署配置

```yaml
# Docker Compose配置
version: '3.8'
services:
  email-system:
    image: email-system:latest
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/email_system
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/1
    depends_on:
      - mysql
      - redis
    ports:
      - "8080:8080"
  
  celery-worker:
    image: email-system:latest
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/email_system
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/1
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=email_system
      - MYSQL_USER=user
      - MYSQL_PASSWORD=pass
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:6.2
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 7.2 数据库迁移

```python
# Alembic迁移脚本
"""Add channel schedules and AB testing tables

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # 创建渠道时间表
    op.create_table(
        'channel_schedules',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('tenant_id', sa.BigInteger(), nullable=False),
        sa.Column('channel_id', sa.BigInteger(), nullable=False),
        sa.Column('schedule_type', sa.String(32), nullable=False),
        sa.Column('base_time', sa.Time(), nullable=True),
        sa.Column('timezone_strategy', sa.String(32), nullable=True),
        sa.Column('timezone_sources', sa.JSON(), nullable=True),
        sa.Column('custom_rules', sa.Text(), nullable=True),
        sa.Column('smart_optimization', sa.Boolean(), nullable=False, default=False),
        sa.Column('avoid_holidays', sa.Boolean(), nullable=False, default=True),
        sa.Column('workdays_only', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tenant_id', 'channel_id', name='uk_channel_schedule'),
        sa.Index('idx_schedule_type', 'tenant_id', 'schedule_type')
    )
    
    # 创建A/B测试实验表
    op.create_table(
        'ab_experiments',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('tenant_id', sa.BigInteger(), nullable=False),
        sa.Column('name', sa.String(128), nullable=False),
        sa.Column('target_type', sa.String(32), nullable=False),
        sa.Column('target_id', sa.BigInteger(), nullable=True),
        sa.Column('experiment_type', sa.String(32), nullable=False),
        sa.Column('sample_percentage', sa.Integer(), nullable=False, default=20),
        sa.Column('variant_count', sa.Integer(), nullable=False, default=2),
        sa.Column('traffic_allocation', sa.String(32), nullable=False, default='equal'),
        sa.Column('winning_metric', sa.String(32), nullable=False),
        sa.Column('observation_hours', sa.Integer(), nullable=False, default=6),
        sa.Column('winning_strategy', sa.String(32), nullable=False, default='auto'),
        sa.Column('statistical_significance', sa.Numeric(5,4), nullable=False, default=0.05),
        sa.Column('min_sample_size', sa.Integer(), nullable=False, default=1000),
        sa.Column('real_time_monitoring', sa.Boolean(), nullable=False, default=True),
        sa.Column('auto_stop_invalid', sa.Boolean(), nullable=False, default=True),
        sa.Column('save_as_strategy', sa.Boolean(), nullable=False, default=False),
        sa.Column('completion_notification', sa.Boolean(), nullable=False, default=True),
        sa.Column('anomaly_alert', sa.Boolean(), nullable=False, default=False),
        sa.Column('status', sa.String(32), nullable=False, default='active'),
        sa.Column('created_by', sa.BigInteger(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tenant_id', 'name', name='uk_ab_experiment'),
        sa.Index('idx_ab_experiment_target', 'tenant_id', 'target_type', 'target_id'),
        sa.Index('idx_ab_experiment_status', 'tenant_id', 'status')
    )

def downgrade():
    op.drop_table('ab_experiments')
    op.drop_table('channel_schedules')
```

## 8. 总结

本文档详细描述了邮件营销系统中新增的核心功能技术实现，包括：

1. **发件渠道时间表**：支持固定时间、时区匹配、立即发送和自定义规则，实现智能时间优化
2. **A/B测试策略管理**：提供完整的实验创建、变体管理、流量分配和统计分析功能
3. **多语言模板支持**：支持多语言模板创建、语言选择和回退机制
4. **自动化旅程增强**：集成渠道选择、模板配置和时区处理功能

这些功能通过合理的数据库设计、高效的算法实现、完善的缓存策略和全面的监控告警，确保了系统的性能、可靠性和可维护性。
