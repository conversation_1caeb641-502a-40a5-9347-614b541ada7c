# 技术方案设计｜抑制与同意

## HLD
- 组件：抑制服务、偏好中心服务、FBL/退信接入器、频控联动器
- 数据：`suppressions`、`preferences`、`consents`、`complaints`

## 时序
```mermaid
sequenceDiagram
participant MTA
participant SUP as Suppression
participant PREF as Preference
participant CAMP as Campaign
MTA->>SUP: 退信/投诉
SUP->>PREF: 更新联系人状态
CAMP->>SUP: 发送前校验
SUP-->>CAMP: 允许/拒绝
```

## LLD
- 校验缓存：`sup:{tenant}:{email}` TTL 300s；
- FBL 映射 ISP 规范；
- 偏好与频控：旅程/活动读取频率上限。

---

## 1. 目标与范围
- 目标：抑制判定 P95<5ms；投诉/硬退自动抑制；偏好中心读写一致。
- 范围：抑制清单（全局/域/主题）、偏好中心（主题/频率/语言）、DOI 流程、与频控/旅程联动。

## 2. 数据与接口
- 表：`suppression_list`、`preference_topics`、`subscription_preferences`、`consent_records`、`double_optin_tokens`；
- API：POST `/api/suppression/add|remove|search`，GET/POST `/api/preferences/get|update`，GET `/api/forms/confirm`。

## 3. 规则
- 硬退/投诉：立即全局抑制；软退按阈值升级；
- 主题退订≠全局退订；频率影响旅程/活动频控；
- DOI：令牌一次性、过期可重发。

## 4. 可靠性与幂等
- 幂等键：`(tenant_id,email,scope)`；
- 写入旁路缓存失效；
- FBL/退信接收失败重试与死信导出。

## 5. 安全与合规
- 审计同意变更；
- 偏好写入鉴权与签名；
- 退订页面防刷与水印。

## 6. 可观测与性能
- 指标：判定耗时、抑制命中、投诉/退信回流量；
- 告警：投诉率/硬退率阈值异常。

## 7. 测试与验收
- 退信/投诉/退订全链路；
- 偏好读写一致与联动旅程；
- DOI 令牌有效性。

## 8. 发布与回滚
- 新主题灰度；阈值与策略配置中心化，回滚为默认策略。

## 9. 风险与对策
- 误杀：白名单与申诉；
- FBL 不一致：ISP 差异适配与回放。

## 10. Checklist
- 统一响应；
- 审计覆盖；
- 阈值可观测可告警；
- 幂等/重试完备。
