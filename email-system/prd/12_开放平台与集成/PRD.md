# PRD｜开放平台与集成（API/Webhook/SDK/生态）

## 1. 背景与目标
- 背景：对外提供稳定 API、Webhook 与 SDK；对接主流生态（Shopify/Zapier/Segment/GA4/数仓）。
- 目标：API 可用性≥99.95%；Webhook 投递可靠性≥99.99%。

## 2. 用户画像与需求
- 开发/集成人员：鉴权、签名、防重放、速率限制；
- 业务团队：无代码/低代码连接器。

## 3. 功能模块
- REST API：仅 GET/POST，统一响应与错误码；版本管理（v1）
- Webhook：事件订阅、HMAC 签名、退避重试、死信处理
- SDK：JS/Go/Node/Python/Java；示例与测试沙盒
- 集成：Shopify/WooCommerce/Zapier/Segment/GA4、数据导出到数仓

### 接口示例
- Webhook 回调头：`X-Signature`（HMAC-SHA256）
```json
{"event":"delivered","message_id":"msg_1","ts":1730000000}
```

## 4. 非功能
- 速率限制与配额；请求签名与时效；幂等键支持。

## 5. 用户故事
1) 订阅投递/打开/点击，接收失败按退避重试；
2) 通过 SDK 写入联系人并发起活动发送；
3) Zapier 无代码集成将订单事件同步到旅程触发。

## 6. 流程图
```mermaid
flowchart LR
  Sub[订阅事件] --> Emit[平台事件]
  Emit --> Deliver[Webhook 投递]
  Deliver -->|失败| Retry[退避重试]
  Deliver -->|成功| Ack[签名校验通过]
```

## 7. 竞品与差异
- SendGrid/SES API 稳定；本产品强调“统一口径 + 强事件生态 + SDK 体验”。

## 8. 里程碑
- P1 API 与 Webhook；P2 SDK；P3 生态连接器与数仓导出。