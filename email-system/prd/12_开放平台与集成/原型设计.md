# 原型设计｜开放平台与集成

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| API 凭证 | 鉴权管理 | 创建/吊销/轮换 |
| Webhook 订阅 | 事件推送 | 新建/编辑/签名/重试策略 |
| SDK 与示例 | 开发指引 | 复制示例/下载 SDK/沙盒测试 |
| 连接器市场 | 生态连接 | 安装/配置/状态 |

## 关键原型
- Webhook：事件多选、签名秘钥展示、最近投递日志与重放按钮。

## 流程
```mermaid
flowchart LR
  S[创建订阅] --> D[事件投递]
  D -->|成功| L[日志]
  D -->|失败| R[退避重试]
```

## 组件/状态
- 凭证卡、签名展示、日志表、重放按钮。

## 校验
- 端点 URL 校验、签名开关、速率与配额提示。

## 空态/错误
- 无订阅空态引导；重放失败提示排查建议。