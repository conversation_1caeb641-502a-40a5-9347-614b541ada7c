# PRD｜表单与偏好中心

## 1. 背景与目标
- 背景：合规采集线索、双重确认、反滥用；偏好中心管理主题/频率/语言。
- 目标：假订阅率下降；机器人流量控制；偏好统一生效。

## 2. 用户画像与需求
- 增长/内容运营：灵活表单与样式；
- 合规：同意记录与撤回；
- 开发：易嵌入与回调。

## 3. 功能模块
- 表单：内嵌/弹窗/独立页；字段配置；验证码/速率/指纹；提交回执
- 双重确认：邮件确认激活联系人
- 偏好中心：主题订阅/频率/语言；一键退订；回调与审计

### 边界与异常
- 重复提交与防刷；确认链接过期与重发；偏好与旅程频控冲突解决。

## 4. 非功能
- 表单提交 P95 < 300ms；回调可靠；可定制样式。

## 5. 用户故事
1) 设置 double opt-in 并降低垃圾订阅；
2) 用户在偏好中心将频率改为“每周一次”，活动频控随之生效；
3) 欺诈流量被验证码与速率限制阻断。

## 6. 流程图
```mermaid
flowchart TD
  A[提交表单] --> B{双重确认?}
  B -- 是 --> C[发送确认邮件]
  C --> D[点击确认]
  D --> E[激活联系人]
  B -- 否 --> E
  E --> F[写同意与偏好]
```

## 7. 数据与接口
- 表单提交：POST `/api/forms/submit`
- 确认：GET `/api/forms/confirm`（token）
- 偏好：GET/POST `/api/preferences/get|update`
- 统一响应与错误码遵循平台规范。

## 8. 竞品与差异
- 标配；本产品强调“与频控/旅程联动 + 反滥用能力”。

## 9. 里程碑
- P1 表单+double opt-in；P2 偏好中心；P3 反滥用与样式增强。