# 技术方案设计｜表单与偏好中心

## HLD
- 组件：表单生成器、提交网关、反滥用引擎、确认服务、偏好存储
- 数据：`forms`、`submissions`、`preferences`、`consents`

## 时序
```mermaid
sequenceDiagram
participant User
participant FG as FormGateway
participant AA as AntiAbuse
participant CONF as Confirm
User->>FG: 提交
FG->>AA: 反滥用校验
FG->>CONF: 发送确认
CONF-->>User: 激活
```

## LLD
- Token 化确认；防重放；限速与 IP 黑名单。

---

## 1. 目标与范围
- 目标：表单提交 P95<300ms；回调可靠；可定制样式。
- 范围：表单（内嵌/弹窗/独立页）、双重确认、偏好中心、一键退订、反滥用。

## 2. 数据与接口
- 表：`forms`、`form_submissions`、`form_security_logs`、`subscription_preferences`、`double_optin_tokens`；
- API：POST `/api/forms/submit`，GET `/api/forms/confirm`；GET/POST `/api/preferences/get|update`。

## 3. 规则
- 防重放与速率限制；
- 确认链接过期与重发；
- 偏好变更写审计与影响频控。

## 4. 可靠性
- 提交幂等（指纹/邮箱+时间窗）；
- 失败补偿与回放。

## 5. 安全与合规
- 验证码/指纹/速率限制；
- 偏好/退订合规文案；
- DOI 审计。

## 6. 可观测与性能
- 指标：提交成功率、拦截率、确认到达率；
- 告警：异常拦截激增。

## 7. 测试与验收
- 防刷、防重放、DOI流程；
- 偏好中心读写一致性。

## 8. 发布与回滚
- 反滥用策略灰度；
- 确认模板回滚。

## 9. 风险与对策
- 机器人流量：多因子风控；
- 链接钓鱼：签名与过期。

## 10. Checklist
- 统一响应；
- 审计与回放；
- 指标/告警齐备。
