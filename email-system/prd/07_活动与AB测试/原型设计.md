# 原型设计｜活动 与 A/B 测试（分离）

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 发件渠道管理 | 管理发件渠道 | 新建/编辑/删除/权限设置 |
| 活动列表 | 管理活动 | 新建/复制/暂停/报告 |
| 活动编辑器 | 配置受众与内容 | 渠道选择/受众/内容/追踪/预检/测试/计划 |
| A/B 测试列表 | 管理实验 | 新建实验/暂停/查看结果/应用优胜 |
| 实验编辑器 | 配置实验 | 对象/要素/样本/流量/指标/观察期/并发 |
| 报告页 | 查看表现 | 总览/变体对比/时序/热力图 |

## 信息架构变化
- 发件渠道管理：新增独立菜单，支持渠道创建、配置、权限管理。
- 活动与 A/B 测试解耦：活动仅维护活动本身（渠道选择、受众、内容、追踪、调度与发送）。
- A/B 测试迁移为独立菜单：针对活动创建实验，支持多个实验并发运行与冲突检测。
- 活动页提供"应用优胜变体"入口，将实验结果一键回填至对应活动配置。

## 发件渠道管理（新增）
- 渠道列表：显示所有渠道，支持筛选（类型/状态）、搜索、批量操作。
- 渠道创建/编辑：基础信息（名称/代码/类型/描述）、资源分配（域名/IP池）、策略配置（限速/预热/监控）、权限设置。
- 渠道详情：配置信息、资源使用情况、投递表现、权限列表。
- 权限管理：用户角色分配、操作权限设置、数据访问控制。

## 活动编辑器（更新后）
- 步进：渠道选择 → 受众 → 内容 → 预检 → 测试发送 → 计划与发送。
- 渠道选择：必选项，影响后续的域名/IP池/限速策略选择。
- A/B 相关设置移除，改为显示当前关联实验的只读摘要与跳转入口。

## 实验编辑器（新增）
- 要素类型：主题行、发件人名称、发送时间、内容块。
- 样本：总样本比例与变体数量（A/B/C…）。
- 流量分配：平均/贝叶斯多臂赌博机/手动。
- 指标：打开率/点击率/下单率；观察期与优胜策略（自动/提前显著性/仅记录）。
- 并发：支持同一活动多个实验并发，系统进行冲突检测与受众分层避免交叉污染。

## 关键流程
```mermaid
flowchart TD
  CA[渠道配置] --> CB[渠道启用]
  CB --> AA[活动配置]
  AA --> AB[预检]
  AB --> AC[计划/发送]
  XA[创建实验] --> XB[选择对象/要素]
  XB --> XC[配置样本/流量/指标/观察期]
  XC --> XD[并发冲突检测]
  XD --> XE[运行/观察]
  XE --> XF[优胜判定]
  XF --> |应用| AA
```

## 组件/状态
- 渠道选择器、受众选择器、预检清单、测试发送抽屉。
- 实验卡片：要素、变体、样本、指标、状态、剩余观察时间、优胜。
- 渠道卡片：类型、状态、资源使用、投递表现、权限。

## 校验
- 渠道选择必填；受众为空/全抑制拦截；时区/节流配置校验。
- 实验样本比例 ∈ (0,100]；变体流量和为样本比例；并发实验要素冲突需合并或串行。
- 渠道权限校验；资源配额检查。

## 空态/错误
- 渠道：无渠道提示创建；权限不足提示申请；资源不足提示扩容。
- 活动：无模板提示新建；预检失败给出修复引导；发送失败显示批次重试入口。
- 实验：无关联活动提示选择对象；冲突时给出合并为多变体或调整并发的引导。