## 邮件营销系统｜统一数据库设计（MySQL，utf8mb4，InnoDB）

说明
- 覆盖 `prd/` 各模块的核心数据模型，按多租户隔离，关键表包含 `tenant_id`。
- 采用 MySQL 8.0，字符集 `utf8mb4`，`InnoDB`，时间使用 `TIMESTAMP`/`DATETIME` + `UTC`，审计与幂等完善。
- 命名规范：表名小写下划线，外键列名 `<entity>_id`，布尔用 `TINYINT(1)`。
- 索引：除主键外，列出关键二级索引。未列出者默认按需新增（标注 TODO）。

- 与用户系统边界：租户/用户/角色/权限由"现有用户系统"提供，本库不建立对应表；`tenant_id`/`created_by`/`reviewer_id`/`actor_user_id` 等仅保存外部系统的引用，不建立外键，权限校验在上游完成。

### 0. 全局约定与审计/用量

```sql

-- 渠道维度为主（发送均需绑定渠道），可选二级主体（如账户/域/ISP）
CREATE TABLE IF NOT EXISTS usage_counters (
  id             BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id      BIGINT NOT NULL,
  channel_id     BIGINT NOT NULL,
  subject_type   VARCHAR(32)  NULL,       -- 可选：connector_account/domain/isp/journey 等（相对于 channel 的二级主体）
  subject_key    VARCHAR(255) NULL,       -- 可选：如 account_id/domain/isp_code 等
  metric         VARCHAR(32)  NOT NULL,   -- sent_total/contacts/journey_entries 等
  period_type    VARCHAR(16)  NOT NULL,   -- second/minute/hour/day/week/month/year
  period_start   DATETIME     NOT NULL,   -- 对齐到周期起点（UTC）
  used_value     BIGINT       NOT NULL DEFAULT 0,  -- 已消耗
  reserved_value BIGINT       NOT NULL DEFAULT 0,  -- 已预留（待入队/待发送）
  updated_at     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_usage_counter (tenant_id, channel_id, subject_type, subject_key, metric, period_type, period_start),
  KEY idx_usage_lookup (tenant_id, channel_id, subject_type, subject_key, metric, period_type, period_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 渠道维度为主（发送均需绑定渠道），可选二级主体（如账户/域/ISP）
CREATE TABLE IF NOT EXISTS quota_policies (
  id             BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id      BIGINT NOT NULL,
  channel_id     BIGINT NOT NULL,
  subject_type   VARCHAR(32)  NULL,       -- 可选：connector_account/domain/isp/journey 等（相对于 channel 的二级主体）
  subject_key    VARCHAR(255) NULL,       -- 可选：如 account_id/domain/isp_code 等
  metric         VARCHAR(32)  NOT NULL,   -- 与 usage_counters.metric 对齐
  period_type    VARCHAR(16)  NOT NULL,   -- second/minute/hour/day/week/month/year
  limit_value    BIGINT       NOT NULL,   -- 限额值（例如 50 封/日/账户）
  status         VARCHAR(16)  NOT NULL DEFAULT 'active',
  created_at     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_quota_policy (tenant_id, channel_id, subject_type, subject_key, metric, period_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- （可选）统一预留明细：用于并发安全的原子预留/回收
-- 预留明细改用 Redis（热路径）：
-- - 在 Redis 中使用键空间 qresv:{reservation_key} 存储 requested/granted/status/expires 等信息（带 TTL）
-- - 周期性同步 Redis 计数到 usage_counters；本地不再持久化 usage_reservations 表
```

#### 表用途与字段说明（0. 全局约定与审计/用量）

- 审计日志 `audit_logs`
  - 用途：记录关键对象的增删改与敏感操作，支持合规追溯与导出。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识（外部系统引用）
    - `actor_user_id`：操作者用户ID（外部系统引用）
    - `action`：动作键（如 create/update/delete/publish）
    - `object_type`：对象类型（template/campaign/journey 等）
    - `object_id`：对象ID
    - `before_json`：变更前快照（JSON）
    - `after_json`：变更后快照（JSON）
    - `ip`：操作者 IP
    - `trace_id`：链路追踪 ID
    - `created_at`：创建时间（UTC）

- 统一用量计数 `usage_counters`
  - 用途：以“渠道为主维度+可选二级主体”的统一模型记录任意周期的用量（支持预留量），供配额控制与报表聚合。
  - 关键字段：`channel_id/subject_type/subject_key/metric/period_type/period_start/used_value/reserved_value`。

- 统一配额策略 `quota_policies`
  - 用途：以“渠道为主维度+可选二级主体”的统一模型配置任意周期的限额（取代各处 *_cap/*quota 字段与 `tenant_quotas`/`journey_rate_limits` 等）。
  - 关键字段：`channel_id/subject_type/subject_key/metric/period_type/limit_value/status`。

- 统一预留明细 `usage_reservations`（可选）
  - 用途：并发安全的配额分配与回收，支持过期回收与幂等。
  - 关键字段：`reservation_key/requested/granted/status/expires_at`。

### 1. 联系人中心（字段/导入/时间线）

```sql
CREATE TABLE contacts (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  email           VARCHAR(255) NOT NULL,
  status          VARCHAR(32) NOT NULL DEFAULT 'active', -- active/suppressed/unconfirmed
  attributes_json JSON NULL,  -- 可选：冗余热门字段单列化
  preferred_locale VARCHAR(16) NULL,      -- 偏好语言，用于多语言模板匹配
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_activity_at DATETIME NULL,
  UNIQUE KEY uk_contact_email (tenant_id, email),
  KEY idx_contact_tenant (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE contact_field_defs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  field_key     VARCHAR(64) NOT NULL,     -- e.g. first_name, birthday
  field_type    VARCHAR(32) NOT NULL,     -- text/number/bool/enum/date
  options_json  JSON NULL,                -- enum 可选项、校验规则、可见性
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_field (tenant_id, field_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 可选 EAV 存储（大规模稀疏字段）
CREATE TABLE contact_attributes (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  field_key     VARCHAR(64) NOT NULL,
  value_text    TEXT NULL,
  value_number  DECIMAL(20,6) NULL,
  value_bool    TINYINT(1) NULL,
  value_date    DATE NULL,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_attr (tenant_id, contact_id, field_key),
  KEY idx_attr_field (tenant_id, field_key),
  KEY idx_attr_contact (tenant_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE import_jobs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  file_url      VARCHAR(1024) NOT NULL,
  source_file_type ENUM('csv','xlsx') NOT NULL DEFAULT 'csv',
  source_bytes  BIGINT NULL,
  sheet_name    VARCHAR(128) NULL,
  header_row    INT NULL,
  mapping_json  JSON NOT NULL,
  normalized_file_url VARCHAR(1024) NULL,
  dedupe_mode   VARCHAR(32) NOT NULL DEFAULT 'merge',
  phase         ENUM('queued','normalizing','partitioning','importing','succeeded','failed','canceled') NOT NULL DEFAULT 'queued',
  status        VARCHAR(32) NOT NULL DEFAULT 'queued',
  progress      INT NOT NULL DEFAULT 0,
  shard_count   INT NOT NULL DEFAULT 0,
  total_rows    BIGINT NULL,
  error_count   INT NOT NULL DEFAULT 0,
  created_by    BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_import_tenant (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE import_job_errors (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id        BIGINT NOT NULL,
  row_number    BIGINT NOT NULL,
  raw_row       TEXT NULL,
  error_message VARCHAR(1024) NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_job (job_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 超大文件导入增强：基于数据库租约的分片（新增）
-- 说明：当源文件为 XLSX 时，先流式归一化为若干 CSV 分片对象（对象存储），并为每个分片创建一条租约记录；
--       Worker 通过租约（lease）并发处理分片，提升稳定性与可恢复性。
CREATE TABLE import_job_shards (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id BIGINT NOT NULL,
  shard_index INT NOT NULL,
  row_start BIGINT NOT NULL,
  row_end BIGINT NOT NULL,
  object_url VARCHAR(1024) NOT NULL COMMENT '分片 CSV 对象地址',
  status ENUM('pending','leased','processing','succeeded','failed','skipped') NOT NULL DEFAULT 'pending',
  progress_rows BIGINT NOT NULL DEFAULT 0,
  attempt INT NOT NULL DEFAULT 0,
  lease_owner VARCHAR(128) NULL,
  lease_until DATETIME NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_job_shard (job_id, shard_index),
  KEY idx_job_status (job_id, status),
  KEY idx_lease (status, lease_until)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入分片及租约';

CREATE TABLE contact_timeline_events (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  event_type    VARCHAR(32) NOT NULL,  -- send/deliver/open/click/bounce/complaint/unsubscribe/custom
  message_id    BIGINT NULL,           -- 关联 email_messages.id
  properties    JSON NULL,
  occurred_at   DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_timeline_contact_time (tenant_id, contact_id, occurred_at),
  KEY idx_timeline_type_time (tenant_id, event_type, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（2. 列表与标签）

- 列表 `lists`
  - 用途：联系人静态集合，便于受众管理与活动选择。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 名称（租户内唯一）；`description` 描述；`member_count` 统计数；`created_at/updated_at` 时间戳。

- 列表成员 `list_members`
  - 用途：列表与联系人关联关系表。
  - 字段：`id` 主键；`tenant_id` 租户；`list_id` 列表ID；`contact_id` 联系人ID；`added_at` 加入时间。

- 标签 `tags`
  - 用途：轻量化多维打标，支持治理与筛选。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 标签名（租户内唯一）；`color` 颜色；`usage_count` 使用次数；`created_at/updated_at` 时间。

- 联系人标签 `contact_tags`
  - 用途：联系人与标签的多对多关系。
  - 字段：`id` 主键；`tenant_id` 租户；`contact_id` 联系人；`tag_id` 标签；`added_at` 赋值时间。

#### 表用途与字段说明（1. 联系人中心）

- 联系人 `contacts`
  - 用途：保存联系人主档与基础状态。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识（外部系统引用）
    - `email`：邮箱地址（唯一键）
    - `status`：状态（active/suppressed/unconfirmed）
    - `attributes_json`：扩展属性 JSON 缓存（便于常用字段直取）
    - `preferred_locale`：偏好语言，用于多语言模板匹配
    - `created_at`：创建时间
    - `updated_at`：更新时间
    - `last_activity_at`：最近互动时间

- 自定义字段定义 `contact_field_defs`
  - 用途：定义租户级联系人自定义字段、类型与可选项。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识
    - `field_key`：字段键（如 first_name）
    - `field_type`：字段类型（text/number/bool/enum/date）
    - `options_json`：校验/枚举选项/可见性配置
    - `created_at`：创建时间
    - `updated_at`：更新时间

- 自定义字段取值 `contact_attributes`
  - 用途：EAV 方式存储稀疏的大规模自定义字段取值。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识
    - `contact_id`：联系人ID
    - `field_key`：字段键
    - `value_text/value_number/value_bool/value_date`：多类型取值之一
    - `updated_at`：更新时间

- 导入任务 `import_jobs`
  - 用途：记录联系人导入任务元信息与进度。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识
    - `file_url`：源文件地址（OSS/S3）
    - `mapping_json`：字段映射配置
    - `dedupe_mode`：去重/合并策略（merge/skip/overwrite）
    - `status`：任务状态（queued/running/done/failed）
    - `progress`：进度百分比
    - `error_count`：错误行数量
    - `created_by`：创建人（外部用户ID）
    - `created_at/updated_at`：时间戳

  - 增强字段（超大 Excel 导入支持）：
    - `source_file_type`：源文件类型 `csv|xlsx`
    - `source_bytes`：源文件字节数（可空）
    - `sheet_name/header_row`：XLSX 工作表与表头行（可空）
    - `normalized_file_url`：若整体转换为单 CSV 的对象地址（可空）
    - `shard_count`：归一化后分片数量
    - `total_rows`：总行数（估算或扫描后值）
    - `phase`：阶段 `queued|normalizing|partitioning|importing|succeeded|failed|canceled`

- 导入错误行 `import_job_errors`
  - 用途：保存导入失败的具体行与原因，支持回放修复。
  - 字段说明：
    - `id`：主键
    - `job_id`：导入任务ID
    - `row_number`：原始文件行号
    - `raw_row`：原始行内容
    - `error_message`：错误原因
    - `created_at`：记录时间

- 联系人时间线事件 `contact_timeline_events`
  - 用途：记录联系人相关的重要事件（发送/投递/打开/点击/退信/投诉/退订/自定义）。
  - 字段说明：
    - `id`：主键
    - `tenant_id`：租户标识
    - `contact_id`：联系人ID
    - `event_type`：事件类型
    - `message_id`：关联消息ID（可空）
    - `properties`：事件属性 JSON
    - `occurred_at`：事件发生时间
    - `created_at`：写入时间

### 2. 列表与标签

```sql
CREATE TABLE lists (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(128) NOT NULL,
  description   VARCHAR(255) NULL,
  member_count  BIGINT NOT NULL DEFAULT 0,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_list (tenant_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE list_members (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  list_id       BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  added_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_list_member (tenant_id, list_id, contact_id),
  KEY idx_list (tenant_id, list_id),
  KEY idx_contact (tenant_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tags (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(64) NOT NULL,
  color         VARCHAR(16) NULL,
  usage_count   BIGINT NOT NULL DEFAULT 0,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tag (tenant_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE contact_tags (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  tag_id        BIGINT NOT NULL,
  added_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_contact_tag (tenant_id, contact_id, tag_id),
  KEY idx_tag (tenant_id, tag_id),
  KEY idx_contact (tenant_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. 细分（Segments）

```sql
CREATE TABLE segments (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  name            VARCHAR(128) NOT NULL,
  rule_tree_json  JSON NOT NULL,
  type            VARCHAR(32) NOT NULL,       -- dynamic/static
  refresh_policy  VARCHAR(64) NULL,           -- schedule cron/trigger/once
  status          VARCHAR(32) NOT NULL DEFAULT 'active',
  snapshot_id     BIGINT NULL,                -- latest snapshot
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_segment (tenant_id, name),
  KEY idx_segment_tenant (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE segment_snapshots (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  segment_id    BIGINT NOT NULL,
  frozen_at     DATETIME NOT NULL,
  member_count  BIGINT NOT NULL DEFAULT 0,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_snapshot_seg (tenant_id, segment_id, frozen_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE segment_snapshot_members (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  snapshot_id   BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  UNIQUE KEY uk_snapshot_member (tenant_id, snapshot_id, contact_id),
  KEY idx_snapshot (tenant_id, snapshot_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE segment_jobs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  segment_id    BIGINT NOT NULL,
  job_type      VARCHAR(32) NOT NULL,   -- preview/rebuild
  status        VARCHAR(32) NOT NULL DEFAULT 'queued',
  progress      INT NOT NULL DEFAULT 0,
  result_json   JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_seg_job (tenant_id, segment_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 人群圈选批量操作（新增）
CREATE TABLE segment_bulk_operations (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  segment_id    BIGINT NOT NULL,
  operation_type VARCHAR(32) NOT NULL,   -- bulk_tag/bulk_add_to_list/export_snapshot
  scope         VARCHAR(16) NOT NULL,    -- all/selected
  selected_contact_ids JSON NULL,        -- 仅当 scope=selected 时使用
  params_json   JSON NOT NULL,           -- 操作参数（标签/列表/快照配置）
  status        VARCHAR(32) NOT NULL DEFAULT 'queued',
  progress      INT NOT NULL DEFAULT 0,
  result_summary JSON NULL,              -- 操作结果统计
  created_by    BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_seg_bulk_tenant (tenant_id, created_at),
  KEY idx_seg_bulk_segment (tenant_id, segment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（3. 细分）

- 细分 `segments`
  - 用途：保存细分规则与类型，支持动态/静态与快照。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 名称；`rule_tree_json` 规则树JSON；`type` 类型；`refresh_policy` 刷新策略；`status` 状态；`snapshot_id` 最新快照；`created_at/updated_at` 时间。

- 细分快照 `segment_snapshots`
  - 用途：冻结时刻的成员集合，用于投放与复现。
  - 字段：`id` 主键；`tenant_id` 租户；`segment_id` 细分；`frozen_at` 冻结时间；`member_count` 成员数；`created_at` 创建时间。

- 快照成员 `segment_snapshot_members`
  - 用途：快照与联系人映射。
  - 字段：`id` 主键；`tenant_id` 租户；`snapshot_id` 快照；`contact_id` 联系人。

- 细分任务 `segment_jobs`
  - 用途：预览/重建等异步任务。
  - 字段：`id` 主键；`tenant_id` 租户；`segment_id` 细分；`job_type` 类型；`status` 状态；`progress` 进度；`result_json` 结果；`created_at/updated_at` 时间。

- 人群圈选批量操作 `segment_bulk_operations`
  - 用途：批量操作联系人，如标签/列表添加/快照导出。
  - 字段：`id` 主键；`tenant_id` 租户；`segment_id` 细分；`operation_type` 操作类型；`scope` 范围；`selected_contact_ids` 选定联系人ID；`params_json` 操作参数；`status` 状态；`progress` 进度；`result_summary` 操作结果统计；`created_by` 创建人；`created_at/updated_at` 时间。

### 4. 投诉与退订（Complaints & Unsubscribe）

```sql
CREATE TABLE suppression_list (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  email         VARCHAR(255) NOT NULL,
  scope         VARCHAR(32) NOT NULL DEFAULT 'global',  -- global/domain/topic
  reason        VARCHAR(64) NOT NULL,                   -- unsubscribe/hard_bounce/complaint/etc.
  meta_json     JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_suppression (tenant_id, email, scope),
  KEY idx_suppress_email (tenant_id, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE preference_topics (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  key_name      VARCHAR(64) NOT NULL,
  name          VARCHAR(128) NOT NULL,
  description   VARCHAR(255) NULL,
  UNIQUE KEY uk_topic (tenant_id, key_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE subscription_preferences (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  contact_id    BIGINT NOT NULL,
  topic_id      BIGINT NOT NULL,
  frequency     VARCHAR(32) NULL,   -- immediate/daily/weekly
  language      VARCHAR(16) NULL,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_pref (tenant_id, contact_id, topic_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE consent_records (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  contact_id    BIGINT NULL,
  email         VARCHAR(255) NULL,
  action        VARCHAR(32) NOT NULL,  -- consent_granted/withdrawn
  source        VARCHAR(64) NULL,      -- form/api/import
  ip            VARCHAR(64) NULL,
  user_agent    VARCHAR(255) NULL,
  occurred_at   DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_consent_contact (tenant_id, contact_id, occurred_at),
  KEY idx_consent_email (tenant_id, email, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE subscription_confirmation_tokens (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  email         VARCHAR(255) NOT NULL,
  token         VARCHAR(128) NOT NULL,
  expires_at    DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  consumed_at   DATETIME NULL,
  UNIQUE KEY uk_sub_confirm_token (tenant_id, token),
  KEY idx_sub_confirm_email (tenant_id, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（4. 投诉与退订）

- 抑制清单 `suppression_list`
  - 用途：记录全局/域/主题维度的退订/投诉/硬退抑制记录。
  - 字段：`id` 主键；`tenant_id` 租户；`email` 邮箱；`scope` 抑制范围；`reason` 原因；`meta_json` 额外信息；`created_at` 时间。

- 偏好主题 `preference_topics`
  - 用途：偏好中心可订阅的主题清单。
  - 字段：`id` 主键；`tenant_id` 租户；`key_name` 主题键；`name` 名称；`description` 描述。

- 订阅偏好 `subscription_preferences`
  - 用途：联系人在某主题下的订阅频率/语言等偏好。
  - 字段：`id` 主键；`tenant_id` 租户；`contact_id` 联系人；`topic_id` 主题；`frequency` 频率；`language` 语言；`updated_at` 时间。

- 同意记录 `consent_records`
  - 用途：记录同意授予/撤回的历史轨迹。
  - 字段：`id` 主键；`tenant_id` 租户；`contact_id` 联系人（可空）；`email` 邮箱（可空）；`action` 动作；`source` 来源；`ip`；`user_agent`；`occurred_at` 发生时间；`created_at` 写入时间。

- 订阅确认令牌 `subscription_confirmation_tokens`
  - 用途：Double Opt-in 流程用一次性确认令牌。
  - 字段：`id` 主键；`tenant_id` 租户；`email` 邮箱；`token` 令牌；`expires_at` 过期时间；`created_at` 创建时间；`consumed_at` 使用时间。

#### 表用途与字段说明（4.1 自动验证与预热）
- 验证任务 `verification_jobs`
  - 用途：邮箱验证任务的元信息与进度统计。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 任务名称；`source_type` 来源类型；`source_path` 源路径；`config_json` 配置；`status` 状态；`progress` 进度；`total/valid/invalid/risky/unknown` 统计；`created_by` 创建人；`created_at/finished_at` 时间。

- 验证结果 `verification_results`
  - 用途：单个邮箱的验证结果与建议。
  - 字段：`id` 主键；`job_id` 任务；`email` 邮箱；`domain` 域名；`result` 结果；`reasons_json` 原因；`suggestions_json` 建议；`created_at` 时间。

- 域名预热计划 `domain_warmup_schedules`
  - 用途：域名/IP 预热的时间表与进度跟踪。
  - 字段：`id` 主键；`tenant_id` 租户；`domain_id` 域名；`curve_type` 曲线类型；`daily_targets_json` 每日目标；`isp_quotas_json` ISP 配额；`status` 状态；`current_day/current_target/actual_sent` 进度；`started_at/paused_at/completed_at` 时间；`created_at/updated_at` 时间。

- 预热阈值事件 `warmup_threshold_events`
  - 用途：预热期间命中阈值触发的事件记录。
  - 字段：`id` 主键；`tenant_id` 租户；`domain_id` 域名；`schedule_id` 计划；`threshold_type` 阈值类型；`threshold_value` 阈值；`action_taken` 采取动作；`action_params` 参数；`occurred_at/resolved_at` 时间；`created_at` 时间。

### 5. 模板与内容（模板/版本/审批）

```sql
CREATE TABLE templates (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(128) NOT NULL,
  editor_type   VARCHAR(32) NOT NULL,     -- visual/mjml/code
  variables_json JSON NULL,
  status        VARCHAR(32) NOT NULL DEFAULT 'draft', -- draft/published/archived
  current_version BIGINT NULL,
  supports_multilingual TINYINT(1) NOT NULL DEFAULT 0, -- 是否支持多语言
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_template (tenant_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE template_versions (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  template_id   BIGINT NOT NULL,
  version_no    INT NOT NULL,
  html_content  MEDIUMTEXT NOT NULL,
  partials_json JSON NULL,
  created_by    BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tpl_ver (tenant_id, template_id, version_no),
  KEY idx_tpl_ver_tpl (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 多语言模板变体（新增）
CREATE TABLE template_locales (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  template_id       BIGINT NOT NULL,
  version_no        INT NOT NULL,
  locale            VARCHAR(16) NOT NULL,  -- zh-CN/en-US/es-ES
  subject           VARCHAR(512) NOT NULL,
  preheader         VARCHAR(255) NULL,
  content_html      MEDIUMTEXT NOT NULL,
  assets_overrides  JSON NULL,             -- 资源覆盖配置
  is_default        TINYINT(1) NOT NULL DEFAULT 0, -- 是否默认语言
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tpl_locale (tenant_id, template_id, version_no, locale),
  KEY idx_tpl_locale_template (tenant_id, template_id, version_no),
  KEY idx_tpl_locale_default (tenant_id, template_id, is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE template_approvals (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  template_id   BIGINT NOT NULL,
  version_id    BIGINT NOT NULL,
  status        VARCHAR(32) NOT NULL DEFAULT 'pending', -- pending/approved/rejected
  reviewer_id   BIGINT NULL,
  comment       VARCHAR(1024) NULL,
  decided_at    DATETIME NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tpl_appr (tenant_id, version_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（5. 模板与内容）

- 模板 `templates`
  - 用途：邮件模板主信息与发布状态。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 名称；`editor_type` 编辑器类型；`variables_json` 变量定义；`status` 状态；`current_version` 当前版本ID；`created_at/updated_at` 时间。

- 模板版本 `template_versions`
  - 用途：保存模板每次变更的版本化内容。
  - 字段：`id` 主键；`tenant_id` 租户；`template_id` 模板；`version_no` 版本号；`html_content` HTML 内容；`partials_json` 片段；`created_by` 创建人；`created_at` 创建时间。

- 模板审批 `template_approvals`
  - 用途：模板发布前的审核与意见记录。
  - 字段：`id` 主键；`tenant_id` 租户；`template_id` 模板；`version_id` 版本；`status` 审批状态；`reviewer_id` 审核人；`comment` 备注；`decided_at` 决定时间；`created_at` 记录时间。

#### 表用途与字段说明（5.1 多语言模板）
- 模板语言变体 `template_locales`
  - 用途：同一模板支持多个 Locale（如 zh-CN/en-US/es-ES），包含主题、预览文本、正文与可选资产差异。
  - 字段：`id` 主键；`tenant_id` 租户；`template_id` 模板；`version_no` 版本号；`locale` 语言代码；`subject` 主题；`preheader` 预览文本；`content_html` HTML 内容；`assets_overrides` 资源覆盖；`is_default` 是否默认语言；`created_by` 创建人；`created_at/updated_at` 时间。

---

后续部分（活动与 A/B、自动化旅程、投递与可达性、追踪与报表、表单与偏好中心、开放平台与集成、运维与管理、扩展能力、计费与额度、安全与合规、发送与队列化模型）见本文件下续节。

### 6. 活动与 A/B 测试（Campaigns & AB）

```sql
CREATE TABLE campaigns (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  name            VARCHAR(128) NOT NULL,
  type            VARCHAR(32)  NOT NULL DEFAULT 'scheduled',  -- scheduled/triggered/recurring
  channel_id      BIGINT NOT NULL,                           -- 发件渠道ID
  template_id     BIGINT NOT NULL,                           -- 模板ID
  audience_json   JSON NOT NULL,  -- include_lists/segments, exclude_lists/segments
  settings_json   JSON NOT NULL,  -- subject/from/utm/track/headers
  locale_strategy VARCHAR(32) NOT NULL DEFAULT 'auto',       -- auto/force_locale
  force_locale     VARCHAR(16) NULL,                         -- 强制语言（当 strategy=force_locale）
  multilingual_config JSON NULL,                             -- 多语言配置
  schedule_time   DATETIME NULL,
  timezone        VARCHAR(64) NULL,
  ab_enabled      TINYINT(1) NOT NULL DEFAULT 0,
  ab_experiment_id BIGINT NULL,                              -- A/B测试实验ID
  status          VARCHAR(32) NOT NULL DEFAULT 'draft', -- draft/scheduled/running/paused/canceled/completed
  created_by      BIGINT NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_campaign (tenant_id, name),
  KEY idx_campaign_status (tenant_id, status, schedule_time),
  KEY idx_campaign_channel (tenant_id, channel_id),
  KEY idx_campaign_template (tenant_id, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- A/B测试实验（新增）
CREATE TABLE ab_experiments (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  target_type       VARCHAR(32) NOT NULL,  -- campaign/template/strategy
  target_id         BIGINT NULL,           -- 目标对象ID
  experiment_type   VARCHAR(32) NOT NULL,  -- subject/sender/send_time/content/template
  sample_percentage  INT NOT NULL DEFAULT 20, -- 总样本比例
  variant_count     INT NOT NULL DEFAULT 2,   -- 变体数量
  traffic_allocation VARCHAR(32) NOT NULL DEFAULT 'equal', -- equal/weighted/bayesian/manual
  winning_metric    VARCHAR(32) NOT NULL,  -- open_rate/click_rate/conversion_rate/revenue
  observation_hours INT NOT NULL DEFAULT 6, -- 观察期（小时）
  winning_strategy  VARCHAR(32) NOT NULL DEFAULT 'auto', -- auto/significance/manual
  statistical_significance DECIMAL(5,4) NOT NULL DEFAULT 0.05, -- 统计显著性
  min_sample_size   INT NOT NULL DEFAULT 1000, -- 最小样本量
  real_time_monitoring TINYINT(1) NOT NULL DEFAULT 1, -- 启用实时监控
  auto_stop_invalid TINYINT(1) NOT NULL DEFAULT 1, -- 自动停止无效实验
  save_as_strategy  TINYINT(1) NOT NULL DEFAULT 0, -- 保存为策略模板
  completion_notification TINYINT(1) NOT NULL DEFAULT 1, -- 实验完成通知
  anomaly_alert     TINYINT(1) NOT NULL DEFAULT 0, -- 异常情况告警
  status            VARCHAR(32) NOT NULL DEFAULT 'active', -- active/paused/completed/stopped
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_experiment (tenant_id, name),
  KEY idx_ab_experiment_target (tenant_id, target_type, target_id),
  KEY idx_ab_experiment_status (tenant_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- A/B测试变体（新增）
CREATE TABLE ab_experiment_variants (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  experiment_id     BIGINT NOT NULL,
  variant_key       VARCHAR(16) NOT NULL,  -- A/B/C/D/E
  variant_name      VARCHAR(64) NOT NULL,
  variant_value     TEXT NOT NULL,         -- 变体内容
  variant_description VARCHAR(255) NULL,   -- 变体描述
  traffic_percentage INT NOT NULL DEFAULT 50, -- 流量分配比例
  status            VARCHAR(32) NOT NULL DEFAULT 'active', -- active/inactive
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_variant (tenant_id, experiment_id, variant_key),
  KEY idx_ab_variant_experiment (tenant_id, experiment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- A/B测试策略库（新增）
CREATE TABLE ab_strategies (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  description       VARCHAR(512) NULL,
  strategy_type     VARCHAR(32) NOT NULL,  -- subject/sender/send_time/content/template
  config_json       JSON NOT NULL,         -- 策略配置
  validation_status VARCHAR(32) NOT NULL DEFAULT 'testing', -- testing/validated/failed
  usage_count       INT NOT NULL DEFAULT 0, -- 使用次数
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_ab_strategy (tenant_id, name),
  KEY idx_ab_strategy_type (tenant_id, strategy_type),
  KEY idx_ab_strategy_status (tenant_id, validation_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE campaign_variants (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  campaign_id     BIGINT NOT NULL,
  variant_key     VARCHAR(16) NOT NULL, -- A/B/C
  traffic_pct     INT NOT NULL DEFAULT 50,
  winner_metric   VARCHAR(32) NULL,     -- open/click/conversion
  observe_minutes INT NULL,
  status          VARCHAR(32) NOT NULL DEFAULT 'active',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_variant (tenant_id, campaign_id, variant_key),
  KEY idx_variant_campaign (campaign_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE campaign_prechecks (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  campaign_id     BIGINT NOT NULL,
  result          VARCHAR(32) NOT NULL, -- pass/warn/fail
  details_json    JSON NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_precheck_campaign (tenant_id, campaign_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE campaign_batches (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  campaign_id     BIGINT NOT NULL,
  variant_id      BIGINT NULL,
  batch_no        INT NOT NULL,
  size_planned    INT NOT NULL DEFAULT 0,
  size_sent       INT NOT NULL DEFAULT 0,
  status          VARCHAR(32) NOT NULL DEFAULT 'queued', -- queued/running/done/failed
  scheduled_at    DATETIME NULL,
  started_at      DATETIME NULL,
  finished_at     DATETIME NULL,
  error_message   VARCHAR(1024) NULL,
  UNIQUE KEY uk_campaign_batch (tenant_id, campaign_id, batch_no),
  KEY idx_campaign_batches (tenant_id, campaign_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（6. 活动与 A/B）

- 活动 `campaigns`
  - 用途：一次群发/定时/触发型发送的配置入口。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 名称；`type` 类型；`audience_json` 受众配置；`settings_json` 内容与追踪配置；`locale_strategy` 语言策略；`force_locale` 强制语言；`schedule_time` 计划时间；`timezone` 时区；`ab_enabled` 是否AB；`status` 状态；`created_by` 创建人；`created_at/updated_at` 时间。

- 活动变体 `campaign_variants`
  - 用途：A/B/C 变体配置与观察窗口。
  - 字段：`id` 主键；`tenant_id` 租户；`campaign_id` 活动；`variant_key` 变体键；`traffic_pct` 流量比例；`winner_metric` 优胜指标；`observe_minutes` 观察期；`status` 状态；`created_at` 时间。

- 活动预检 `campaign_prechecks`
  - 用途：发送前的抑制/域名/额度等预检结果记录。
  - 字段：`id` 主键；`tenant_id` 租户；`campaign_id` 活动；`result` 结果；`details_json` 详情；`created_at` 时间。

- 活动批次 `campaign_batches`
  - 用途：投递批次化执行单元与结果统计。
  - 字段：`id` 主键；`tenant_id` 租户；`campaign_id` 活动；`variant_id` 变体；`batch_no` 批次号；`size_planned/size_sent` 计划/实际；`status` 状态；`scheduled_at/started_at/finished_at` 时间；`error_message` 错误。

### 6.1 发送与执行核心（无外部消息队列，基于表驱动）

符合"使用数据库的 `email_messages` 表进行发送请求与执行隔离"的约定。

```sql
CREATE TABLE email_messages (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  campaign_id       BIGINT NULL,
  campaign_variant_id BIGINT NULL,
  journey_id        BIGINT NULL,
  journey_node_id   BIGINT NULL,
  message_type      VARCHAR(16) NOT NULL DEFAULT 'email',
  contact_id        BIGINT NULL,
  to_email          VARCHAR(255) NOT NULL,
  from_email        VARCHAR(255) NOT NULL,
  from_name         VARCHAR(128) NULL,
  subject           VARCHAR(512) NOT NULL,
  body_html         MEDIUMTEXT NULL,
  body_text         MEDIUMTEXT NULL,
  headers_json      JSON NULL,
  personalization_json JSON NULL,
  tags_json         JSON NULL,
  locale_used       VARCHAR(16) NULL,                       -- 实际使用的语言
  locale_fallback   TINYINT(1) NOT NULL DEFAULT 0,          -- 是否使用了回退语言
  track_open        TINYINT(1) NOT NULL DEFAULT 1,
  track_click       TINYINT(1) NOT NULL DEFAULT 1,
  priority          INT NOT NULL DEFAULT 100,
  scheduled_at      DATETIME NULL,
  status            VARCHAR(32) NOT NULL DEFAULT 'queued', -- queued/sending/sent/failed/skipped/suppressed
  last_error        VARCHAR(1024) NULL,
  provider_message_id VARCHAR(255) NULL,
  dedupe_key        VARCHAR(128) NULL,
  lock_token        VARCHAR(64) NULL,
  locked_at         DATETIME NULL,
  attempts          INT NOT NULL DEFAULT 0,
  next_attempt_at   DATETIME NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_msg_dedupe (tenant_id, dedupe_key),
  KEY idx_msg_pick (tenant_id, status, priority, scheduled_at),
  KEY idx_msg_contact (tenant_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE email_delivery_events (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  message_id      BIGINT NOT NULL,
  event_type      VARCHAR(32) NOT NULL,   -- queued/sent/delivered/open/click/bounce/complaint/unsubscribe
  occurred_at     DATETIME NOT NULL,
  payload_json    JSON NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_msg_event_time (tenant_id, message_id, occurred_at),
  KEY idx_event_type_time (tenant_id, event_type, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 执行隔离/并发控制（可选）
CREATE TABLE send_execution_locks (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  resource_type   VARCHAR(32) NOT NULL,  -- tenant/domain/isp
  resource_id     VARCHAR(128) NOT NULL, -- e.g. domain name or isp code
  acquired_by     VARCHAR(64) NOT NULL,  -- worker id
  acquired_at     DATETIME NOT NULL,
  expire_at       DATETIME NOT NULL,
  UNIQUE KEY uk_send_lock (tenant_id, resource_type, resource_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（6.1 发送与执行核心）

- 邮件消息 `email_messages`
  - 用途：作为发送请求与执行隔离的队列表，承载邮件内容与状态机。
  - 字段：`id` 主键；`tenant_id` 租户；`campaign_id/campaign_variant_id` 活动/变体；`journey_id/journey_node_id` 旅程关联；`message_type` 消息类型；`contact_id` 联系人；`to_email/from_email/from_name` 收发人；`subject` 主题；`body_html/body_text` 正文；`headers_json` 头部；`personalization_json` 个性化变量；`tags_json` 标签；`locale_used/locale_fallback` 语言；`track_open/track_click` 追踪开关；`priority` 优先级；`scheduled_at` 计划时间；`status` 状态；`last_error` 错误；`provider_message_id` ESP 消息ID；`dedupe_key` 幂等键；`lock_token/locked_at` 执行锁；`attempts/next_attempt_at` 重试；`created_at/updated_at` 时间。

- 投递事件 `email_delivery_events`
  - 用途：记录每封邮件的生命周期事件，驱动报表与可达性治理。
  - 字段：`id` 主键；`tenant_id` 租户；`message_id` 邮件ID；`event_type` 事件；`occurred_at` 发生时间；`payload_json` 原始载荷；`created_at` 写入时间。

- 执行锁 `send_execution_locks`
  - 用途：对域名/ISP/租户级资源做并发互斥控制（限速/预热）。
  - 字段：`id` 主键；`tenant_id` 租户；`resource_type` 资源类型；`resource_id` 资源键；`acquired_by` 工作器ID；`acquired_at/expire_at` 锁时间。

### 7. 自动化旅程（Journeys）

```sql
CREATE TABLE journeys (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  name            VARCHAR(128) NOT NULL,
  journey_type    VARCHAR(32) NOT NULL,  -- welcome/onboarding/re_engagement/abandoned_cart/custom
  channel_id      BIGINT NOT NULL,       -- 发件渠道ID
  template_id     BIGINT NULL,           -- 默认模板ID
  multilingual_config JSON NULL,         -- 多语言配置
  status          VARCHAR(32) NOT NULL DEFAULT 'draft', -- draft/published/paused/stopped
  current_version BIGINT NULL,
  entry_frequency VARCHAR(32) NOT NULL DEFAULT 'once', -- once/repeat/always
  rate_limit_config JSON NULL,           -- 上限控制配置
  target_audience_filter JSON NULL,      -- 目标受众过滤
  timezone_handling VARCHAR(32) NOT NULL DEFAULT 'channel_default', -- channel_default/user_timezone/business_hours
  exit_conditions JSON NULL,             -- 退出条件配置
  created_by      BIGINT NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_journey (tenant_id, name),
  KEY idx_journey_channel (tenant_id, channel_id),
  KEY idx_journey_template (tenant_id, template_id),
  KEY idx_journey_type (tenant_id, journey_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 自动化旅程模板库（新增）
CREATE TABLE journey_templates (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(128) NOT NULL,
  description       VARCHAR(512) NULL,
  journey_type      VARCHAR(32) NOT NULL,  -- welcome/onboarding/re_engagement/abandoned_cart
  template_config   JSON NOT NULL,         -- 模板配置
  steps_count       INT NOT NULL DEFAULT 0, -- 步骤数量
  validation_status VARCHAR(32) NOT NULL DEFAULT 'testing', -- testing/validated/failed
  usage_count       INT NOT NULL DEFAULT 0, -- 使用次数
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_journey_template (tenant_id, name),
  KEY idx_journey_template_type (tenant_id, journey_type),
  KEY idx_journey_template_status (tenant_id, validation_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE journey_versions (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  journey_id      BIGINT NOT NULL,
  version_no      INT NOT NULL,
  graph_json      JSON NOT NULL,       -- nodes/edges with config
  settings_json   JSON NULL,           -- concurrency/entry/exit
  published_at    DATETIME NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_jver (tenant_id, journey_id, version_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE journey_instances (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  journey_id      BIGINT NOT NULL,
  version_id      BIGINT NOT NULL,
  contact_id      BIGINT NOT NULL,
  status          VARCHAR(32) NOT NULL DEFAULT 'active', -- active/completed/exited
  started_at      DATETIME NOT NULL,
  finished_at     DATETIME NULL,
  last_node_id    VARCHAR(64) NULL,
  last_activity_at DATETIME NULL,
  UNIQUE KEY uk_jinst (tenant_id, journey_id, contact_id, status),
  KEY idx_jinst_contact (tenant_id, contact_id, started_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE journey_instance_nodes (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  instance_id     BIGINT NOT NULL,
  node_id         VARCHAR(64) NOT NULL,
  status          VARCHAR(32) NOT NULL DEFAULT 'pending', -- pending/running/done/failed
  retry_count     INT NOT NULL DEFAULT 0,
  last_error      VARCHAR(1024) NULL,
  started_at      DATETIME NULL,
  finished_at     DATETIME NULL,
  UNIQUE KEY uk_jnode (tenant_id, instance_id, node_id),
  KEY idx_jnode_status (tenant_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE journey_ingest_events (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  source          VARCHAR(32) NOT NULL,  -- api/webhook/segment
  contact_id      BIGINT NULL,
  email           VARCHAR(255) NULL,
  event_type      VARCHAR(64) NOT NULL,
  payload_json    JSON NOT NULL,
  received_at     DATETIME NOT NULL,
  dedupe_key      VARCHAR(128) NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_jingest_dedupe (tenant_id, dedupe_key),
  KEY idx_jingest_time (tenant_id, received_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 旅程频控（可选，DEPRECATED：统一由 `quota_policies` 承载，resource_type='journey'）
CREATE TABLE journey_rate_limits (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  journey_id      BIGINT NOT NULL,
  window_seconds  INT NOT NULL,
  max_in_window   INT NOT NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_jrate (tenant_id, journey_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 8. 投递与可达性（Deliverability）

```sql
CREATE TABLE sending_channels (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  name              VARCHAR(64) NOT NULL,
  code              VARCHAR(32) NOT NULL,
  type              VARCHAR(16) NOT NULL, -- marketing/transactional/system
  description       VARCHAR(255) NULL,
  status            VARCHAR(16) NOT NULL DEFAULT 'active', -- active/inactive/suspended
  default_domain_id BIGINT NULL,
  default_ip_pool_id BIGINT NULL,
  -- 限额由 `quota_policies` 统一配置（resource_type='channel'）
  settings_json     JSON NULL,
  created_by        BIGINT NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_channel_code (tenant_id, code),
  KEY idx_channel_type (tenant_id, type),
  KEY idx_channel_status (tenant_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 发件渠道时间表配置（新增）
CREATE TABLE channel_schedules (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  channel_id        BIGINT NOT NULL,
  schedule_type     VARCHAR(32) NOT NULL, -- fixed/timezone/immediate/custom
  base_time         TIME NULL,            -- 基准时间（如 10:00）
  timezone_strategy VARCHAR(32) NULL,     -- user_timezone/business_hours/optimal_hours
  timezone_sources  JSON NULL,            -- 时区数据来源配置
  custom_rules      TEXT NULL,            -- 自定义规则描述
  smart_optimization TINYINT(1) NOT NULL DEFAULT 0, -- 启用智能时间优化
  avoid_holidays    TINYINT(1) NOT NULL DEFAULT 1,  -- 避免节假日发送
  workdays_only     TINYINT(1) NOT NULL DEFAULT 0,  -- 工作日限制
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_channel_schedule (tenant_id, channel_id),
  KEY idx_schedule_type (tenant_id, schedule_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 国家/地区特定时间配置（新增）
CREATE TABLE channel_country_times (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  channel_id        BIGINT NOT NULL,
  country_code      VARCHAR(8) NOT NULL,  -- CN/US/EU/JP
  country_name      VARCHAR(64) NOT NULL,
  send_time         TIME NOT NULL,        -- 该国家的发送时间
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_country_time (tenant_id, channel_id, country_code),
  KEY idx_country_time (tenant_id, country_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE channel_permissions (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  channel_id        BIGINT NOT NULL,
  user_id           BIGINT NOT NULL,
  role              VARCHAR(32) NOT NULL, -- owner/admin/operator/viewer
  permissions_json  JSON NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_channel_user (channel_id, user_id),
  KEY idx_channel_permissions (channel_id, role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE bounce_logs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  message_id    BIGINT NOT NULL,
  email         VARCHAR(255) NOT NULL,
  bounce_type   VARCHAR(16) NOT NULL,
  reason_code   VARCHAR(64) NULL,
  isp           VARCHAR(64) NULL,
  diagnostic    VARCHAR(1024) NULL,
  occurred_at   DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_bounce_msg (tenant_id, message_id),
  KEY idx_bounce_time (tenant_id, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE fbl_complaints (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  message_id    BIGINT NOT NULL,
  email         VARCHAR(255) NOT NULL,
  isp           VARCHAR(64) NULL,
  feedback_id   VARCHAR(128) NULL,
  complaint_type VARCHAR(64) NULL,
  occurred_at   DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_fbl_msg (tenant_id, message_id),
  KEY idx_fbl_time (tenant_id, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE deliverability_metrics_hourly (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  date_hour       DATETIME NOT NULL,
  dimension_type  VARCHAR(16) NOT NULL,
  dimension_id    VARCHAR(255) NULL,
  sent            BIGINT NOT NULL DEFAULT 0,
  delivered       BIGINT NOT NULL DEFAULT 0,
  opens           BIGINT NOT NULL DEFAULT 0,
  unique_opens    BIGINT NOT NULL DEFAULT 0,
  clicks          BIGINT NOT NULL DEFAULT 0,
  unique_clicks   BIGINT NOT NULL DEFAULT 0,
  hard_bounces    BIGINT NOT NULL DEFAULT 0,
  soft_bounces    BIGINT NOT NULL DEFAULT 0,
  complaints      BIGINT NOT NULL DEFAULT 0,
  unsubscribes    BIGINT NOT NULL DEFAULT 0,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_deliv_hour (tenant_id, date_hour, dimension_type, dimension_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 8.3 架构复审与修正项（多轮分析结论）
- 账户用量与配额执行：仅在 `connector_accounts.daily_cap` 中保存上限，不足以原子地限制日用量。需引入“账户-日期”用量表，支持原子更新与审计。
- ISP 限流策略唯一性：`throttle_policies` 需唯一化 (tenant_id, isp, channel_id) 以避免冲突策略。
- 预热可观测性：仅有日度聚合，缺少阈值命中与动作日志。需补充 `warmup_events`。
- 路由与黑名单：DNSBL 扫描与出口 IP 管理未关联账户/渠道。需补充 `outbound_ips` 与账户映射表。
- 外部声誉：Gmail Postmaster / SNDS / QQ 开放平台等外部声誉数据缺乏落库。需补充 `reputation_external_snapshots`。
- 查询热点：`email_messages` 缺少按收件人邮箱检索索引，不利于客服/合规回溯。需新增 `(tenant_id, to_email, created_at)` 索引。

#### 8.4 结构修正 SQL（新增/变更）

```sql
-- 8.4.1 通用用量计数（任意维度/任意周期）
-- 说明：支持对任意资源维度（账户/渠道/域名/ISP/租户等）、任意周期（hour/day/week/month/year）的用量累计，
--       用于配额控制与报表聚合。避免“日”级别的刚性限制。
-- 注意：统一模型已在“0. 全局约定与审计/用量”中定义，此处提供迁移与清理示例。

-- 8.4.1A 迁移 deliverability_usage_counters → usage_counters
-- INSERT INTO usage_counters (tenant_id, resource_type, resource_key, metric, period_type, period_start, used_value, reserved_value, updated_at)
-- SELECT tenant_id, resource_type, resource_key, metric, period_type, period_start, used_value, reserved_value, updated_at
-- FROM deliverability_usage_counters;

-- 8.4.1B 迁移 deliverability_usage_reservations → usage_reservations
-- INSERT INTO usage_reservations (tenant_id, resource_type, resource_key, metric, period_type, period_start, reservation_key, requested, granted, status, expires_at, created_at, updated_at)
-- SELECT tenant_id, resource_type, resource_key, metric, period_type, period_start, reservation_key, requested, granted, status, expires_at, created_at, updated_at
-- FROM deliverability_usage_reservations;

-- 8.4.1C 迁移 deliverability_quota_policies → quota_policies
-- INSERT INTO quota_policies (tenant_id, resource_type, resource_key, metric, period_type, limit_value, status, created_at, updated_at)
-- SELECT tenant_id, resource_type, resource_key, metric, period_type, limit_value, status, created_at, updated_at
-- FROM deliverability_quota_policies;

-- 8.4.1D 清理废弃表
-- DROP TABLE IF EXISTS deliverability_usage_counters;
-- DROP TABLE IF EXISTS deliverability_usage_reservations;
-- DROP TABLE IF EXISTS deliverability_quota_policies;

-- 8.4.2 ISP 限流策略唯一化
ALTER TABLE throttle_policies
  ADD UNIQUE KEY uk_throttle_policy (tenant_id, isp, channel_id);

-- 8.4.3 预热事件流水（阈值命中与动作）
CREATE TABLE IF NOT EXISTS warmup_events (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  warmup_job_id     BIGINT NOT NULL,
  event_type        VARCHAR(32) NOT NULL,  -- threshold_hit/action/applied/backoff/resume
  event_key         VARCHAR(64) NOT NULL,  -- complaint_rate/hard_bounce/deferral/spam_trap 等
  event_value       VARCHAR(64) NULL,
  action_taken      VARCHAR(64) NULL,      -- pause/decrease_30/restart_linear 等
  details_json      JSON NULL,
  occurred_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_warmup_evt_job_time (tenant_id, warmup_job_id, occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8.4.4 出口 IP 管理与账户映射（便于 DNSBL 与智能路由）
CREATE TABLE IF NOT EXISTS outbound_ips (
  id             BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id      BIGINT NULL,              -- 共享池为 NULL，专属为具体租户
  ip_address     VARCHAR(64) NOT NULL,
  region         VARCHAR(32) NULL,         -- us-east-1/cn-shanghai 等
  provider       VARCHAR(32) NULL,         -- aws/alicloud/custom
  status         VARCHAR(16) NOT NULL DEFAULT 'active', -- active/retired/blocked
  created_at     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_out_ip (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS connector_account_ips (
  id                     BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id              BIGINT NOT NULL,
  connector_account_id   BIGINT NOT NULL,
  outbound_ip_id         BIGINT NOT NULL,
  priority               INT NOT NULL DEFAULT 100,
  created_at             DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_acc_ip (connector_account_id, outbound_ip_id),
  KEY idx_acc_ip_tenant (tenant_id, connector_account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8.4.5 外部声誉快照（Postmaster/SNDS/QQ 等）
CREATE TABLE IF NOT EXISTS reputation_external_snapshots (
  id             BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id      BIGINT NULL,
  source         VARCHAR(32) NOT NULL,     -- gmail_postmaster/snds/qq_open
  entity_type    VARCHAR(16) NOT NULL,     -- domain/ip
  entity_value   VARCHAR(255) NOT NULL,    -- 域名或 IP
  metric_key     VARCHAR(64) NOT NULL,     -- spam_rate/domain_rep/ip_rep/fbl 等
  metric_value   VARCHAR(64) NOT NULL,
  captured_at    DATETIME NOT NULL,
  created_at     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_rep_snap (source, entity_type, entity_value, captured_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8.4.6 email_messages 热点索引（按收件人回溯）
ALTER TABLE email_messages
  ADD KEY idx_msg_to_email (tenant_id, to_email, created_at);
```

#### 8.1 新增表（域名/DNS 健康、账户轮换、预热、节流、验证、自然性、DNSBL、合规 BCC）

```sql
-- 发信域名与信誉
CREATE TABLE sending_domains (
  id               BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id        BIGINT NOT NULL,
  domain           VARCHAR(255) NOT NULL,
  tracking_domain  VARCHAR(255) NULL,
  spf_status       VARCHAR(16) NOT NULL DEFAULT 'unknown', -- unknown/pass/fail
  dkim_status      VARCHAR(16) NOT NULL DEFAULT 'unknown',
  dmarc_status     VARCHAR(16) NOT NULL DEFAULT 'unknown',
  bimi_status      VARCHAR(16) NOT NULL DEFAULT 'unknown',
  ptr_status       VARCHAR(16) NOT NULL DEFAULT 'unknown',
  warmup_status    VARCHAR(16) NOT NULL DEFAULT 'idle',     -- idle/running/paused/completed/failed
  warmup_progress  INT NOT NULL DEFAULT 0,
  reputation_score DECIMAL(6,2) NOT NULL DEFAULT 0.00,
  created_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_domain (tenant_id, domain)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- DNS 健康检查历史
CREATE TABLE dns_health_checks (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  sending_domain_id BIGINT NOT NULL,
  check_type        VARCHAR(32) NOT NULL, -- spf/dkim/dmarc/bimi/ptr/tracking_domain
  status            VARCHAR(16) NOT NULL, -- pass/fail/warn/unknown
  details_json      JSON NULL,
  checked_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_dns_health (sending_domain_id, checked_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 连接账户（邮箱/ESP 账号），支持按账户限额与轮换（结合 Geeksend 建议：单账户日上限≈50，营销:预热起步 50/50）
CREATE TABLE connector_accounts (
  id               BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id        BIGINT NOT NULL,
  channel_id       BIGINT NOT NULL,
  account_key      VARCHAR(64) NOT NULL,
  auth_type        VARCHAR(32) NOT NULL,  -- oauth/app_password/api_key/smtp_login
  auth_payload_enc VARBINARY(4096) NULL,  -- 密文存储
  from_address     VARCHAR(255) NOT NULL,
  reply_to         VARCHAR(255) NULL,
  envelope_from    VARCHAR(255) NULL,
  -- 限额由 `quota_policies` 统一配置（resource_type='connector_account'）
  ratio_marketing  INT NOT NULL DEFAULT 50,
  health_score     DECIMAL(5,2) NOT NULL DEFAULT 100.00,
  status           VARCHAR(16) NOT NULL DEFAULT 'ready', -- ready/warming/limited/disabled
  created_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_connector (tenant_id, channel_id, account_key),
  KEY idx_connector_channel (tenant_id, channel_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ISP 节流策略（令牌桶/AIMD 阈值）
CREATE TABLE throttle_policies (
  id               BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id        BIGINT NOT NULL,
  channel_id       BIGINT NULL,
  isp              VARCHAR(64) NOT NULL,  -- gmail/outlook/yahoo/qq/enterprise
  rate_per_sec     INT NOT NULL,
  burst            INT NOT NULL DEFAULT 0,
  max_connections  INT NOT NULL DEFAULT 0,
  thresholds_json  JSON NULL,             -- complaint/hard/deferral 阈值、AIMD 参数
  safe_cap_isp     INT NULL,
  enabled          TINYINT(1) NOT NULL DEFAULT 1,
  created_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_throttle (tenant_id, channel_id, isp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 预热计划与日度进展
CREATE TABLE warmup_jobs (
  id                BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id         BIGINT NOT NULL,
  sending_domain_id BIGINT NOT NULL,
  curve_type        VARCHAR(16) NOT NULL,  -- conservative/standard/aggressive
  plan_json         JSON NOT NULL,         -- 每日目标、ISP 分配、阈值
  status            VARCHAR(16) NOT NULL DEFAULT 'idle',
  started_at        DATETIME NULL,
  paused_at         DATETIME NULL,
  completed_at      DATETIME NULL,
  last_metrics_json JSON NULL,
  created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_warmup_domain (tenant_id, sending_domain_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE warmup_daily_progress (
  id               BIGINT PRIMARY KEY AUTO_INCREMENT,
  warmup_job_id    BIGINT NOT NULL,
  day_number       INT NOT NULL,
  planned_total    INT NOT NULL,
  actual_total     INT NOT NULL DEFAULT 0,
  metrics_json     JSON NULL,
  created_at       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_warmup_day (warmup_job_id, day_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 邮箱验证
CREATE TABLE verification_jobs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(128) NOT NULL,
  source_type   VARCHAR(16) NOT NULL, -- csv/url/api
  source_path   VARCHAR(512) NULL,
  config_json   JSON NULL,
  status        VARCHAR(16) NOT NULL, -- queued/running/paused/completed/failed
  progress      INT NOT NULL DEFAULT 0,
  total         INT NOT NULL DEFAULT 0,
  valid         INT NOT NULL DEFAULT 0,
  invalid       INT NOT NULL DEFAULT 0,
  risky         INT NOT NULL DEFAULT 0,
  unknown       INT NOT NULL DEFAULT 0,
  created_by    BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  finished_at   DATETIME NULL,
  KEY idx_ver_job (tenant_id, status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE verification_results (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id        BIGINT NOT NULL,
  email         VARCHAR(320) NOT NULL,
  domain        VARCHAR(255) NOT NULL,
  result        VARCHAR(16) NOT NULL, -- valid/invalid/risky/unknown
  reasons_json  JSON NULL,
  suggestions_json JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_ver_result_job (job_id),
  KEY idx_ver_result_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 自然性策略（平滑曲线、非营销日用预热回填）
CREATE TABLE naturalness_policies (
  id                     BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id              BIGINT NOT NULL,
  channel_id             BIGINT NULL,
  hourly_growth_max_pct  INT NOT NULL DEFAULT 50,
  backfill_with_warmup   TINYINT(1) NOT NULL DEFAULT 1,
  created_at             DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at             DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_naturalness (tenant_id, channel_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- DNSBL 实时扫描
CREATE TABLE dnsbl_scan_results (
  id           BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id    BIGINT NULL,
  ip_address   VARCHAR(64) NOT NULL,
  list_name    VARCHAR(128) NOT NULL,
  status       VARCHAR(16) NOT NULL, -- listed/clear
  details_json JSON NULL,
  scanned_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_dnsbl_ip (ip_address, scanned_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 合规 BCC 设置（可选）
CREATE TABLE bcc_audit_settings (
  id           BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id    BIGINT NOT NULL,
  channel_id   BIGINT NULL,
  enabled      TINYINT(1) NOT NULL DEFAULT 0,
  bcc_address  VARCHAR(255) NULL,
  redaction    VARCHAR(16) NOT NULL DEFAULT 'partial', -- none/partial/hash
  created_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_bcc (tenant_id, channel_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 8.2 现有表结构调整（ALTER）

为支持按渠道/账户/域名的智能路由与限额，将在 `6.1 发送与执行核心` 的 `email_messages` 表中增加可选外键列，并建立必要索引：

```sql
ALTER TABLE email_messages
  ADD COLUMN channel_id BIGINT NULL AFTER tenant_id,
  ADD COLUMN connector_account_id BIGINT NULL AFTER channel_id,
  ADD COLUMN sending_domain_id BIGINT NULL AFTER connector_account_id;

CREATE INDEX idx_msg_channel ON email_messages (tenant_id, channel_id, scheduled_at);
CREATE INDEX idx_msg_account ON email_messages (tenant_id, connector_account_id, scheduled_at);
```

### 9. 追踪与分析与报表（Tracking/Analytics/Reports）

```sql
CREATE TABLE events (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  type          VARCHAR(64) NOT NULL,
  properties    JSON NULL,
  contact_id    BIGINT NULL,
  message_id    BIGINT NULL,
  occurred_at   DATETIME NOT NULL,
  source        VARCHAR(32) NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_event_time (tenant_id, occurred_at),
  KEY idx_event_type (tenant_id, type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE message_links (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  message_id      BIGINT NOT NULL,
  url_original    TEXT NOT NULL,
  url_token       VARCHAR(64) NOT NULL,
  url_rewritten   TEXT NOT NULL,
  click_count     BIGINT NOT NULL DEFAULT 0,
  first_clicked_at DATETIME NULL,
  last_clicked_at DATETIME NULL,
  UNIQUE KEY uk_msg_link (tenant_id, message_id, url_token),
  KEY idx_msg_links (tenant_id, message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE metrics_hourly (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  date_hour       DATETIME NOT NULL,
  dimension_type  VARCHAR(16) NOT NULL,
  dimension_id    VARCHAR(255) NULL,
  sent            BIGINT NOT NULL DEFAULT 0,
  delivered       BIGINT NOT NULL DEFAULT 0,
  opens           BIGINT NOT NULL DEFAULT 0,
  unique_opens    BIGINT NOT NULL DEFAULT 0,
  clicks          BIGINT NOT NULL DEFAULT 0,
  unique_clicks   BIGINT NOT NULL DEFAULT 0,
  unsubscribes    BIGINT NOT NULL DEFAULT 0,
  conversions     BIGINT NOT NULL DEFAULT 0,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_metrics_hour (tenant_id, date_hour, dimension_type, dimension_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 10. 表单与偏好中心（Forms/Preference Center）

```sql
CREATE TABLE forms (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(128) NOT NULL,
  form_key      VARCHAR(64) NOT NULL,
  schema_json   JSON NOT NULL,
  doi_enabled   TINYINT(1) NOT NULL DEFAULT 0,
  status        VARCHAR(32) NOT NULL DEFAULT 'active',
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_form (tenant_id, form_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE form_submissions (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  form_id       BIGINT NOT NULL,
  contact_id    BIGINT NULL,
  email         VARCHAR(255) NULL,
  payload_json  JSON NOT NULL,
  ip            VARCHAR(64) NULL,
  user_agent    VARCHAR(255) NULL,
  status        VARCHAR(32) NOT NULL DEFAULT 'submitted',
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_form_submit (tenant_id, form_id, email, created_at),
  KEY idx_form_submit_time (tenant_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE form_security_logs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  form_id       BIGINT NOT NULL,
  fingerprint   VARCHAR(128) NULL,
  ip            VARCHAR(64) NULL,
  action        VARCHAR(32) NOT NULL,
  detail        VARCHAR(512) NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_form_sec (tenant_id, form_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 11. 开放平台与集成（API/Webhook/SDK/生态）

```sql
CREATE TABLE api_keys (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(64) NOT NULL,
  key_id        VARCHAR(32) NOT NULL,
  key_hash      VARCHAR(128) NOT NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'active',
  expires_at    DATETIME NULL,
  last_used_at  DATETIME NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_api_key (tenant_id, key_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE webhook_subscriptions (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  name          VARCHAR(128) NOT NULL,
  target_url    VARCHAR(1024) NOT NULL,
  secret        VARCHAR(128) NOT NULL,
  events_json   JSON NOT NULL,
  retry_policy  JSON NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'active',
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE webhook_deliveries (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  subscription_id BIGINT NOT NULL,
  event_type      VARCHAR(64) NOT NULL,
  event_ref_id    BIGINT NULL,
  payload_json    JSON NOT NULL,
  signature       VARCHAR(128) NULL,
  status          VARCHAR(16) NOT NULL DEFAULT 'pending',
  attempt_count   INT NOT NULL DEFAULT 0,
  next_attempt_at DATETIME NULL,
  last_error      VARCHAR(1024) NULL,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  delivered_at    DATETIME NULL,
  KEY idx_wh_deliv_status (tenant_id, status, next_attempt_at),
  KEY idx_wh_deliv_sub (tenant_id, subscription_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE integrations (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  type          VARCHAR(32) NOT NULL,
  name          VARCHAR(128) NOT NULL,
  config_json   JSON NOT NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'active',
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_integ_type (tenant_id, type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 12. 运维与管理（看板/告警/任务）

```sql
CREATE TABLE ops_tasks (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NULL,
  task_type     VARCHAR(64) NOT NULL,
  payload_json  JSON NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'queued',
  scheduled_at  DATETIME NULL,
  started_at    DATETIME NULL,
  finished_at   DATETIME NULL,
  attempts      INT NOT NULL DEFAULT 0,
  last_error    VARCHAR(1024) NULL,
  created_by    BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_ops_task_status (status, scheduled_at),
  KEY idx_ops_task_tenant (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE alert_rules (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NULL,
  name          VARCHAR(128) NOT NULL,
  condition_json JSON NOT NULL,
  channels_json JSON NOT NULL,
  enabled       TINYINT(1) NOT NULL DEFAULT 1,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE alert_events (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NULL,
  rule_id       BIGINT NULL,
  severity      VARCHAR(16) NOT NULL,
  message       VARCHAR(1024) NOT NULL,
  context_json  JSON NULL,
  triggered_at  DATETIME NOT NULL,
  sent_channels_json JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_alert_time (tenant_id, triggered_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 13. 扩展功能（Bandit/选时/个性化/预览/数仓）

```sql
CREATE TABLE experiments (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  name            VARCHAR(128) NOT NULL,
  goal            VARCHAR(32) NOT NULL,
  status          VARCHAR(16) NOT NULL DEFAULT 'active',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_experiment (tenant_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE experiment_variants (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  experiment_id   BIGINT NOT NULL,
  variant_key     VARCHAR(16) NOT NULL,
  template_version_id BIGINT NULL,
  traffic_allocation INT NOT NULL,
  status          VARCHAR(16) NOT NULL DEFAULT 'active',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_exp_variant (tenant_id, experiment_id, variant_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE experiment_allocations (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  experiment_id   BIGINT NOT NULL,
  contact_id      BIGINT NOT NULL,
  variant_id      BIGINT NOT NULL,
  allocated_at    DATETIME NOT NULL,
  UNIQUE KEY uk_exp_alloc (tenant_id, experiment_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE personalization_decisions (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  source          VARCHAR(32) NOT NULL,
  params_json     JSON NULL,
  decided_value   TEXT NULL,
  fallback_used   TINYINT(1) NOT NULL DEFAULT 0,
  decided_at      DATETIME NOT NULL,
  KEY idx_p13n_time (tenant_id, decided_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE smart_send_times (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id       BIGINT NOT NULL,
  contact_id      BIGINT NOT NULL,
  best_hour       TINYINT NOT NULL,
  model_version   VARCHAR(32) NULL,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_sst (tenant_id, contact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 14. 计费与额度（Plans/Billing/Overage）

```sql
CREATE TABLE billing_plans (
  id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
  code                VARCHAR(64) NOT NULL,
  name                VARCHAR(128) NOT NULL,
  monthly_price_cents INT NOT NULL,
  currency            VARCHAR(8) NOT NULL DEFAULT 'USD',
  -- 配额与能力（套餐模板级默认值）
  max_contacts                BIGINT NOT NULL DEFAULT 100000,
  max_contact_custom_fields   INT    NOT NULL DEFAULT 200,
  import_max_rows_per_job     BIGINT NOT NULL DEFAULT 1000000,
  max_segment_jobs_concurrent INT    NOT NULL DEFAULT 2,
  enable_open_tracking        TINYINT(1) NOT NULL DEFAULT 1,
  max_templates               INT    NOT NULL DEFAULT 200,
  max_experiments             INT    NOT NULL DEFAULT 10,
  monthly_email_quota         BIGINT NOT NULL DEFAULT 100000,
  max_active_journeys         INT    NOT NULL DEFAULT 10,
  monthly_email_verification_quota BIGINT NOT NULL DEFAULT 100000,
  features_json       JSON NULL,
  created_at          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_plan_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tenant_subscriptions (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  plan_id       BIGINT NOT NULL,
  start_date    DATE NOT NULL,
  end_date      DATE NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'active',
  -- 可选：套餐覆盖（租户级自定义上限，如企业版定制）
  override_max_contacts                BIGINT NULL,
  override_max_contact_custom_fields   INT    NULL,
  override_import_max_rows_per_job     BIGINT NULL,
  override_max_segment_jobs_concurrent INT    NULL,
  override_enable_open_tracking        TINYINT(1) NULL,
  override_max_templates               INT    NULL,
  override_max_experiments             INT    NULL,
  override_monthly_email_quota         BIGINT NULL,
  override_max_active_journeys         INT    NULL,
  override_monthly_email_verification_quota BIGINT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_subscription (tenant_id, plan_id, start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE billing_invoices (
  id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id           BIGINT NOT NULL,
  period_start        DATE NOT NULL,
  period_end          DATE NOT NULL,
  total_amount_cents  BIGINT NOT NULL DEFAULT 0,
  currency            VARCHAR(8) NOT NULL DEFAULT 'USD',
  status              VARCHAR(16) NOT NULL DEFAULT 'open',
  created_at          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  paid_at             DATETIME NULL,
  UNIQUE KEY uk_invoice_period (tenant_id, period_start, period_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE billing_invoice_items (
  id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
  invoice_id          BIGINT NOT NULL,
  item_type           VARCHAR(32) NOT NULL,
  quantity            BIGINT NOT NULL,
  unit_price_cents    BIGINT NOT NULL,
  amount_cents        BIGINT NOT NULL,
  meta_json           JSON NULL,
  KEY idx_invoice_item (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE quota_overage_policies (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  policy        VARCHAR(16) NOT NULL,
  whitelist_json JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_overage_policy (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 视图（可选）：计算租户当前有效配额（考虑套餐与 override）
-- CREATE VIEW v_effective_quota AS
-- SELECT ts.tenant_id,
--        COALESCE(ts.override_max_contacts, bp.max_contacts) AS max_contacts,
--        COALESCE(ts.override_max_contact_custom_fields, bp.max_contact_custom_fields) AS max_contact_custom_fields,
--        COALESCE(ts.override_import_max_rows_per_job, bp.import_max_rows_per_job) AS import_max_rows_per_job,
--        COALESCE(ts.override_max_segment_jobs_concurrent, bp.max_segment_jobs_concurrent) AS max_segment_jobs_concurrent,
--        COALESCE(ts.override_enable_open_tracking, bp.enable_open_tracking) AS enable_open_tracking,
--        COALESCE(ts.override_max_templates, bp.max_templates) AS max_templates,
--        COALESCE(ts.override_max_experiments, bp.max_experiments) AS max_experiments,
--        COALESCE(ts.override_monthly_email_quota, bp.monthly_email_quota) AS monthly_email_quota,
--        COALESCE(ts.override_max_active_journeys, bp.max_active_journeys) AS max_active_journeys,
--        COALESCE(ts.override_monthly_email_verification_quota, bp.monthly_email_verification_quota) AS monthly_email_verification_quota
-- FROM tenant_subscriptions ts
-- JOIN billing_plans bp ON ts.plan_id = bp.id
-- WHERE ts.status='active' AND (ts.end_date IS NULL OR ts.end_date >= CURRENT_DATE());
```

### 15. 安全与合规（隐私/内容/保留/驻留）

```sql
CREATE TABLE privacy_dsr_requests (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  request_type  VARCHAR(16) NOT NULL,
  subject_contact_id BIGINT NULL,
  subject_email VARCHAR(255) NULL,
  status        VARCHAR(16) NOT NULL DEFAULT 'pending',
  requested_at  DATETIME NOT NULL,
  approved_by   BIGINT NULL,
  approved_at   DATETIME NULL,
  processed_at  DATETIME NULL,
  result_url    VARCHAR(1024) NULL,
  audit_json    JSON NULL,
  KEY idx_dsr_time (tenant_id, requested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE data_retention_policies (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  entity        VARCHAR(64) NOT NULL,
  retention_days INT NOT NULL,
  effective_from DATETIME NOT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_retention (tenant_id, entity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE field_encryption_policies (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  entity        VARCHAR(64) NOT NULL,
  field_name    VARCHAR(64) NOT NULL,
  algorithm     VARCHAR(32) NOT NULL,
  key_alias     VARCHAR(64) NOT NULL,
  rotation_days INT NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_encrypt_field (tenant_id, entity, field_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE compliance_validation_logs (
  id            BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id     BIGINT NOT NULL,
  object_type   VARCHAR(32) NOT NULL,
  object_id     BIGINT NOT NULL,
  rule_key      VARCHAR(64) NOT NULL,
  status        VARCHAR(16) NOT NULL,
  details_json  JSON NULL,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_comp_val (tenant_id, object_type, object_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 表用途与字段说明（15. 安全与合规）

- 隐私请求 `privacy_dsr_requests`
  - 用途：记录数据导出/删除的 DSR 工单全流程。
  - 字段：`request_type` 类型；`subject_contact_id/subject_email` 主体；`status` 状态；`requested_at/approved_at/processed_at` 时间；`approved_by` 审批人；`result_url` 结果链接；`audit_json` 审计。

- 数据保留策略 `data_retention_policies`
  - 用途：为不同实体设置数据保留周期与生效时间。
  - 字段：`entity` 实体键；`retention_days` 天数；`effective_from` 生效时间；`created_at` 时间。

- 字段加密策略 `field_encryption_policies`
  - 用途：对敏感字段配置加密算法、密钥别名与轮换周期。
  - 字段：`entity/field_name` 实体/字段；`algorithm` 算法；`key_alias` 密钥别名；`rotation_days` 轮换天数；`created_at` 时间。

- 合规模型校验日志 `compliance_validation_logs`
  - 用途：模板/活动发布前的合规校验结果记录。
  - 字段：`object_type/object_id` 对象；`rule_key` 规则键；`status` 结果；`details_json` 详情；`created_at` 时间。

---

说明补充
- 本设计以"用途+字段说明"覆盖所有表，`tenant_id` 等为外部用户系统引用，不设外键；权限在上游校验。
- 如需生成可执行 SQL 文件或添加外键/分区/触发器示例，可按运行环境单独补充。

#### 表用途与字段说明（14. 计费与额度）

- 套餐 `billing_plans`
  - 用途：定义套餐模板的默认配额与功能开关。
  - 字段：
    - `code/name`：代码/名称；`monthly_price_cents/currency`：价格/币种；
    - 配额与能力：
      - `max_contacts`：联系人上限
      - `max_contact_custom_fields`：联系人扩展字段数量上限
      - `import_max_rows_per_job`：单次导入最大行数
      - `max_segment_jobs_concurrent`：圈选任务并发数
      - `enable_open_tracking`：是否支持邮件打开率追踪
      - `max_templates`：邮件模板数量上限
      - `max_experiments`：实验数量上限
      - `monthly_email_quota`：月度发送数量上限
      - `max_active_journeys`：自动化（旅程）激活数量上限
      - `monthly_email_verification_quota`：月度邮箱验证数量上限
    - 其余：`features_json` 可选功能集合；`created_at` 时间。

- 订阅 `tenant_subscriptions`
  - 用途：绑定租户与套餐，可选覆盖默认配额。
  - 字段：`tenant_id/plan_id/start_date/end_date/status/created_at` 基础信息；可选覆盖：
    - `override_max_contacts`、`override_max_contact_custom_fields`、`override_import_max_rows_per_job`、`override_max_segment_jobs_concurrent`、`override_enable_open_tracking`、`override_max_templates`、`override_max_experiments`、`override_monthly_email_quota`、`override_max_active_journeys`、`override_monthly_email_verification_quota`。

- 账单 `billing_invoices`
  - 用途：账单头，记录周期与金额。
  - 字段：`period_start/period_end` 周期；`total_amount_cents/currency` 金额币种；`status` 状态；`created_at/paid_at` 时间。

- 账单明细 `billing_invoice_items`
  - 用途：账单内的计费项（套餐/超量/附加）。
  - 字段：`invoice_id` 账单；`item_type` 类型；`quantity/unit_price_cents/amount_cents` 数量与金额；`meta_json` 备注。

- 超量策略 `quota_overage_policies`
  - 用途：达到额度后的系统行为配置。
  - 字段：`tenant_id` 租户；`policy` 策略（throttle/stop/notify）；`whitelist_json` 白名单；`created_at` 时间。

#### 表用途与字段说明（13. 扩展功能）

- 实验 `experiments`
  - 用途：定义 Bandit/个性化等在线实验。
  - 字段：`name` 名称；`goal` 目标指标；`status` 状态；`created_at` 时间。

- 实验变体 `experiment_variants`
  - 用途：实验的各变体及其流量分配与模板版本绑定。
  - 字段：`experiment_id` 实验；`variant_key` 变体键；`template_version_id`

- 细分任务 `segment_jobs`
  - 用途：预览/重建等异步任务。
  - 字段：`id` 主键；`tenant_id` 租户；`segment_id` 细分；`job_type` 类型；`status` 状态；`progress` 进度；`result_json` 结果；`created_at/updated_at` 时间。

#### 表用途与字段说明（3.1 人群圈选批量操作）
- 批量操作 `segment_bulk_operations`
  - 用途：对人群圈选结果执行批量打标签、加入列表、导出快照等操作。
  - 字段：`id` 主键；`tenant_id` 租户；`segment_id` 细分；`operation_type` 操作类型；`scope` 作用范围；`selected_contact_ids` 选中联系人；`params_json` 操作参数；`status` 状态；`progress` 进度；`result_summary` 结果统计；`created_by` 创建人；`created_at/updated_at` 时间。

- 发送域 `sending_domains`
  - 用途：管理发件域的接入、校验与信誉。
  - 字段：`tenant_id` 租户；`domain` 域名；`tracking_domain` 追踪域；`spf_status/dkim_status/dmarc_status/bimi_status` 身份状态；`dkim_selector/public_key` DKIM 信息；`verification_*` 校验字段；`reputation_score` 信誉分；`ip_pool_id` 绑定IP池；`prewarm_status` 预热状态；`warmup_status` 预热状态；`warmup_progress` 预热进度；`created_at/updated_at` 时间。

#### 表用途与字段说明（8.1 发件渠道时间表）

- 渠道时间表 `channel_schedules`
  - 用途：配置发件渠道的发送时间表，支持固定时间、时区匹配、立即发送和自定义规则。
  - 字段：`id` 主键；`tenant_id` 租户；`channel_id` 渠道；`schedule_type` 时间表类型；`base_time` 基准时间；`timezone_strategy` 时区策略；`timezone_sources` 时区数据来源；`custom_rules` 自定义规则；`smart_optimization` 智能优化；`avoid_holidays` 避免节假日；`workdays_only` 工作日限制；`created_at/updated_at` 时间。

- 国家/地区时间 `channel_country_times`
  - 用途：为不同国家和地区的用户配置特定的发送时间。
  - 字段：`id` 主键；`tenant_id` 租户；`channel_id` 渠道；`country_code/country_name` 国家代码/名称；`send_time` 发送时间；`created_at/updated_at` 时间。

#### 表用途与字段说明（5.1 多语言模板）

- 模板语言变体 `template_locales`
  - 用途：同一模板支持多个 Locale（如 zh-CN/en-US/es-ES），包含主题、预览文本、正文与可选资产差异。
  - 字段：`id` 主键；`tenant_id` 租户；`template_id` 模板；`version_no` 版本号；`locale` 语言代码；`subject` 主题；`preheader` 预览文本；`content_html` HTML 内容；`assets_overrides` 资源覆盖；`is_default` 是否默认语言；`created_by` 创建人；`created_at/updated_at` 时间。

#### 表用途与字段说明（6.1 A/B测试策略）

- A/B测试实验 `ab_experiments`
  - 用途：定义和管理A/B测试实验，支持多种测试类型和策略。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 实验名称；`target_type/target_id` 目标对象；`experiment_type` 实验类型；`sample_percentage` 样本比例；`variant_count` 变体数量；`traffic_allocation` 流量分配；`winning_metric` 优胜指标；`observation_hours` 观察期；`winning_strategy` 优胜策略；`statistical_significance` 统计显著性；`min_sample_size` 最小样本量；`real_time_monitoring` 实时监控；`auto_stop_invalid` 自动停止无效；`save_as_strategy` 保存为策略；`completion_notification` 完成通知；`anomaly_alert` 异常告警；`status` 状态；`created_by` 创建人；`created_at/updated_at` 时间。

- A/B测试变体 `ab_experiment_variants`
  - 用途：定义实验的各个变体及其配置。
  - 字段：`id` 主键；`tenant_id` 租户；`experiment_id` 实验；`variant_key` 变体键；`variant_name` 变体名称；`variant_value` 变体内容；`variant_description` 变体描述；`traffic_percentage` 流量比例；`status` 状态；`created_at/updated_at` 时间。

- A/B测试策略库 `ab_strategies`
  - 用途：保存已验证的A/B测试策略，供重复使用。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 策略名称；`description` 描述；`strategy_type` 策略类型；`config_json` 策略配置；`validation_status` 验证状态；`usage_count` 使用次数；`created_by` 创建人；`created_at/updated_at` 时间。

#### 表用途与字段说明（7.1 自动化旅程模板）

- 自动化旅程模板库 `journey_templates`
  - 用途：保存已验证的自动化旅程模板，支持快速创建常用旅程。
  - 字段：`id` 主键；`tenant_id` 租户；`name` 模板名称；`description` 描述；`journey_type` 旅程类型；`template_config` 模板配置；`steps_count` 步骤数量；`validation_status` 验证状态；`usage_count` 使用次数；`created_by` 创建人；`created_at/updated_at` 时间。