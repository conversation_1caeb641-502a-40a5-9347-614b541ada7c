# 技术方案设计｜计费与额度

## HLD
- 组件：用量采集器、配额计算器、策略执行器、账单导出器
- 数据：`usage_counters`、`quotas`、`billing_items`

## 时序
```mermaid
sequenceDiagram
participant Q as QuotaSvc
participant S as Sender
S->>Q: 发送前配额校验
Q-->>S: allow/deny/limit
```

## LLD
- 计数器：原子自增；周期重置；跨区域一致性（CRDT 可选）
- 导出：账单周期聚合与对账。

---

## 1. 目标与范围
- 目标：提前预警、体验不中断、成本可核算。
- 范围：额度与用量、超量策略（限速/停发/提醒/白名单）、账单与导出。

## 2. 数据与接口
- 表：`tenant_quotas`、`usage_counters`、`billing_plans`、`tenant_subscriptions`、`billing_invoices`、`billing_invoice_items`、`quota_overage_policies`；
- API：`/api/tenants/quotas`、`/api/billing/export`。

## 3. 规则
- 用量统计与预测；
- 超量策略执行优先级与白名单例外；
- 账单周期聚合与税率/币种显示。

## 4. 可靠性
- 计数原子性保障；
- 账单导出断点续传。

## 5. 安全与合规
- 账务审计；
- 发票导出权限控制。

## 6. 可观测与性能
- 指标：使用趋势、预测误差、策略命中；
- 告警：超量阈值/用量异常。

## 7. 测试与验收
- 用量统计准确性；
- 策略执行正确性；
- 导出稳定性。

## 8. 发布与回滚
- 策略参数灰度；
- 计费项版本化。

## 9. 风险与对策
- 统计口径误差：对齐口径并回测；
- 策略误杀：白名单与人工审批。

## 10. Checklist
- 统一响应；
- 计数与账单可追溯；
- 看板与告警齐备。
