# 原型设计｜扩展功能（智能化）

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 实验管理 | Bandit 与多变体 | 新建实验/目标/流量/监控 |
| 智能选时 | 策略配置 | 开关/约束/回退策略 |
| 个性化 | 推荐源 | 规则/模型/兜底 |
| 客户端预览 | 渲染测试 | 选择客户端/查看截图/垃圾箱测试 |

## 关键原型
- 实验监控：变体实时曲线与显著性提示；
- 选时：用户分布直方图与建议时段。

## 流程
```mermaid
flowchart LR
  C[配置能力] --> D[决策]
  D --> O[输出结果]
  O --> M[指标回流]
```

## 组件/状态
- 指标卡、曲线图、切换开关、兜底提示。

## 校验
- 探索比例下限；模型健康检测；失败回退。

## 空态/错误
- 模型不可用时展示回退说明与开关。