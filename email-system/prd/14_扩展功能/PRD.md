# PRD｜扩展功能（Bandit/选时/个性化/预览/数仓）

## 1. 背景与目标
- 背景：在基础能力之上提供智能化提效功能与生态对接。
- 目标：提高点击/转化；降低人工配置；可回退与可解释。

## 2. 用户画像与需求
- 增长团队/数据科学：实验与个性化工具；
- 运维：可回退与监控。

## 3. 功能模块
- 多臂 Bandit：多变体在线优化，约束探索比例，目标（点击/转化）
- 智能选时：基于历史互动与时区预测最佳小时段
- 个性化推荐：规则/模型驱动，失败兜底为静态区块
- 客户端预览：常见邮箱客户端渲染与垃圾箱测试（第三方）
- 数仓直连：导出到 Snowflake/BigQuery，增量同步

### 数据结构
- Experiment：id、variants、goal、traffic_allocation、status
- Recommendation：source、params、fallback

### 异常
- 模型不可用 → 回退静态；探索上限保护；数据漂移告警。

## 4. 非功能
- 在线决策 P95<30ms；可解释日志；开关化控制。

## 5. 用户故事
1) 开启 Bandit 在 24 小时内将流量收敛到优胜变体；
2) 选时在用户常用设备时段发送；
3) 推荐失败回退静态模板，不影响发送。

## 6. 流程图
```mermaid
flowchart LR
  C[配置扩展能力] --> D[决策引擎]
  D --> R[返回变体/时段/内容]
  R --> S[发送执行]
  S --> M[指标回流]
  M --> D
```

## 7. 接口
- `/api/experiments/*`、`/api/personalize/*`、`/api/export/warehouse`

## 8. 竞品与差异
- 高阶能力分散；本产品提供“可回退、可解释、可观测”的一体化。

## 9. 里程碑
- P1 选时/预览；P2 Bandit/个性化；P3 数仓导出。