# 技术方案设计｜扩展功能

## HLD
- 组件：决策引擎（在线）、模型服务、特征服务、可解释服务、开关与回退
- 数据：`experiments`、`decisions`、`features`

## 时序
```mermaid
sequenceDiagram
participant S as Sender
participant DEC as DecisionSvc
participant FE as FeatureStore
S->>DEC: 决策请求
DEC->>FE: 拉取特征
DEC-->>S: 变体/时段/推荐
```

## LLD
- P95 30ms 预算；特征缓存；熔断与回退；记录 Shapley/特征重要性。

---

## 1. 目标与范围
- 目标：在线决策 P95<30ms；可解释与可回退；业务无感降级。
- 范围：Bandit/选时/个性化/预览/数仓导出。

## 2. 数据与接口
- 表：`experiments`、`experiment_variants`、`experiment_allocations`、`personalization_decisions`、`smart_send_times`；
- API：`/api/experiments/*`、`/api/personalize/*`、`/api/export/warehouse`。

## 3. 规则与算法
- Bandit：探索比例上限、冷启动、收敛与停止；
- 选时：小时段预测与时区对齐；
- 推荐：兜底静态模板与置信阈值。

## 4. 可靠性
- 判定超时兜底；
- 远端模型不可用自动回退静态。

## 5. 安全与合规
- 模型与特征访问控制；
- 记录决策日志用于审计与解释。

## 6. 可观测与性能
- 指标：决策延迟、回退率、收益指标提升；
- 告警：延迟/失败/回退异常。

## 7. 测试与验收
- 线上/离线一致性；
- 回退与兜底路径验证；
- 收益评估。

## 8. 发布与回滚
- 功能开关；
- 变体配置灰度。

## 9. 风险与对策
- 数据漂移：监控与再训练；
- 黑盒不可解释：可解释日志与可视报告。

## 10. Checklist
- 统一响应；
- 兜底与回退完备；
- 指标/告警齐备。
