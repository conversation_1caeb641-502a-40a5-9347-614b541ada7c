# 技术方案设计｜列表与标签

## HLD
- 组件：批处理器、关系维护器、合并器、统计器
- 数据：`lists`、`list_members`、`tags`、`contact_tags`

## 时序
```mermaid
sequenceDiagram
participant UI
participant S as BatchService
participant DB
UI->>S: 批量打标请求
S->>DB: 批次入队
S->>DB: 批处理分片执行
S-->>UI: 进度与结果
```

## LLD
- 分片大小 5k；失败部分重试；合并保持引用一致性

## 索引
- `list_members(list_id,contact_id)`；`contact_tags(contact_id,tag_id)`

## 可观测
- 批处理吞吐、失败率、重试次数

---

## 1. 目标与范围
- 目标：批量赋值成功率≥99.9%，批处理 P95<1s/千条；列表/标签查询 P95<100ms。
- 范围：列表 CRUD、成员增删/导入；标签 CRUD、赋值/重命名/合并；治理（命名规范、使用热度）。

## 2. 系统边界与上下文
- 依赖：外部用户系统鉴权与租户上下文；联系人模块；审计日志；导入任务中心（可复用）。
- 仅使用 GET/POST 与统一响应；权限判定由上游完成。

## 3. 数据设计（与 prd/数据库设计.md 对齐）
- 表：`lists`、`list_members`、`tags`、`contact_tags`；字段说明已在统一设计中给出。
- 约束：租户内 `lists.name`、`tags.name` 唯一；成员/打标唯一键保护多次写入。
- 迁移：新增/变更 DDL 需附回滚脚本；大规模重命名/合并采用影子映射表（TODO）。

## 4. API 契约（仅 GET/POST）
- 列表：
  - POST `/api/lists/create|update|delete`，GET `/api/lists/get`，POST `/api/lists/members`
- 标签：
  - POST `/api/tags/create|update|delete|assign|merge|rename`，GET `/api/tags/get`
- 响应以 `code` 判断成功，错误码对齐平台规范；分页使用 page/size。

## 5. 业务规则
- 标签命名规范：小写-中划线、≤40 字、禁止表情；重名冲突返回 409。
- 合并与重命名需保持历史引用一致（建立 `old_name → new_id` 映射期间兼容，TODO）。
- 列表导入去重：同一联系人仅保留一次成员关系。

## 6. 可靠性与幂等
- 批处理分片（默认 5k），失败行重试 3 次，退避抖动；
- 幂等键：`(tenant_id,list_id,contact_id)`、`(tenant_id,contact_id,tag_id)`；
- 大批量操作采用任务化 + 进度查询，错误项可导出回放（TODO：与 import_jobs 复用）。

## 7. 安全与合规
- 上游鉴权/授权；敏感操作记录 `audit_logs`（重命名/合并/批量删除）。

## 8. 可观测性
- 指标：批处理吞吐、失败率、重试次数、热点标签命中、操作耗时；
- 告警：失败率＞1% 或重试>3 次占比异常。

## 9. 性能与容量
- 高基数：`contact_tags`、`list_members` 百万至千万；索引与批写入优化；
- 读场景以 `(tenant_id,contact_id)`、`(tenant_id,list_id)` 为主；必要时冷热分层（TODO）。

## 10. 测试与验收
- 功能：创建/重命名/合并/赋值/撤销/导入；
- 负例：重名/循环合并/超额批次；
- 性能：10M 关系导入与 1k QPS 查询基线；
- 验收：看板与告警上线，变更可审计。

## 11. 发布与回滚
- 重命名/合并灰度到小流量；提供回滚映射与冲突恢复；
- 大表变更在线 DDL，备份与回放脚本。

## 12. 风险与对策
- 热点标签写放大：分片 + 批写 + 合并写；
- 冲突/死锁：排序写入与有限重试；
- 命名治理落地：后端硬校验 + 前端提示。

## 13. Checklist
- 仅 GET/POST 与统一响应；
- DDL 与回滚脚本具备；
- 指标/日志/告警齐备；
- 审计覆盖重命名/合并/批删；
- 大批量任务可观测、可暂停/重试。
