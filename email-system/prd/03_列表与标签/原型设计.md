# 原型设计｜列表与标签（聚焦标签维护）

## 页面地图（更新）
| 页面 | 目标 | 操作 |
|---|---|---|
| 标签管理 | 管理标签 | 新建/重命名/合并/删除 |
| 批量打标向导 | 批处理 | 选择对象→选择/新建标签→确认→执行 |

说明：移除“新建列表”入口，页面聚焦标签维护；列表能力保留在后台数据结构与批量操作中，不在该页面新建。

## 新建标签弹窗（新增）
- 类型：支持“静态 / 动态”。
- 静态：
  - 可选择“不圈选”（稍后在联系人或批处理页打标）。
  - 或“使用条件圈选并立即打标”，复用条件构建器后发起批量赋值任务。
- 动态：
  - 必须配置条件（可视化规则：AND/OR、事件窗口、计数、属性比较、关系包含）。
  - 提供“预估人数”和“示例”页签（与细分模块口径一致）。

## 条件设定建议（与系统设计对齐）
- 行为事件窗口：
  - 最近N天有购买：事件=购买，窗口=N天，计数≥1（示例：最近7天有购买）。
  - 近30天打开≥1或点击≥1：事件=打开/点击，窗口=30天，计数≥1（支持 OR 组合）。
- 属性过滤：
  - 订阅状态=active；地区=CN/US 等。
- 关系包含：
  - 已包含某标签/列表；或不包含黑名单标签。
- 组合与分组：
  - 支持 AND/OR 与括号，避免歧义；限制最大深度与项数以保证性能（参考细分 DSL 约束）。

以上口径与 `prd/04_细分` 模块保持一致，动态标签底层可复用细分引擎的规则 DSL 与预估接口。

## 流程
```mermaid
flowchart LR
  NT[新建标签] --> CHK{类型}
  CHK -- 静态 --> OPT{是否条件圈选}
  OPT -- 否 --> SAVE[保存]
  OPT -- 是 --> RULES[配置条件并预估] --> SAVE
  CHK -- 动态 --> RULES
```

## 组件与状态
- 标签表格：名称/类型（静态/动态）/人数/说明。
- 条件构建器：字段（行为/属性/关系）+ 运算符 + 值；可添加/删除行。
- 预估与示例：返回规模与样本（演示级）。

## 校验
- 标签名唯一、1-40 字、命名规范（小写-中划线、禁止表情）。

## 空态/错误
- 无标签时给出创建引导；重名冲突提示修复方案。
