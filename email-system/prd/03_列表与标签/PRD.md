# PRD｜列表与标签

## 1. 背景与目标
- 背景：提供静态列表与标签维度，支撑受众组织与筛选。
- 目标：批量操作成功率>99.9%，活动侧检索 P95<100ms。

## 2. 用户画像与需求
- 运营：批量打标、列表成员管理；
- 分析：按标签/列表过滤查看表现。

## 3. 功能模块
- 列表 CRUD、成员增删导入；
- 标签 CRUD、批量赋值、重命名与合并；
- 搜索与治理（命名规范校验、使用热度）。

### 界面/流程
- 批量打标：选择联系人 → 选择/新建标签 → 确认 → 回执。

### 数据结构
- List：id、name、description、member_count
- Tag：id、name、color、usage_count

### 边界/异常
- 标签重名校验；列表导入去重。

## 4. 非功能
- 高并发批处理，任务异步队列。

## 5. 用户故事（3）
1) 批量为 1000 人赋值“VIP”；
2) 合并“大促邀请”“大促首发”为“11.11大促”；
3) 从 CSV 导入到“新品发布”列表。

## 6. 流程图
```mermaid
flowchart LR
  S[选择对象] --> T[选择/创建标签]
  T --> C[确认]
  C --> Q[入队执行]
  Q --> R[结果回执]
```

## 7. 数据与接口
- `/api/lists/create|update|delete|members`，`/api/tags/create|update|delete|assign`
- 响应遵循统一格式。

### 条件构建（默认字段与运算符建议）
- 字段：
  - 行为：购买/打开/点击（带时间窗口）
  - 属性：地区/语言/时区/自定义字段
  - 关系：标签包含/不包含、列表包含/不包含
- 运算符：=、!=、>=、<=、包含、不包含、为空/不为空
- 组合：AND/OR，限制最大层级与项数（对齐 `prd/04_细分`）

## 8. 竞品与差异
- 普遍具备；本产品加强治理（重命名保留引用、使用热度、规范校验）。

## 9. 里程碑
- P1 列表/标签基础；P2 合并/重命名与治理；P3 统计与报表联动。

## 附：缺失分析与补充（并入）
- 错误码：400301 invalid_tag_name、409302 tag_conflict、429301 batch_rate_limited。
- 并发与限额：单次≤100k、并发≤3、低优先级队列；支持进度与暂停。
- 治理规范：标签命名小写-中划线、禁止表情、≤40 字；提供使用热度与搜索。
