# 原型设计｜模板与内容

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 模板列表 | 模板资产管理 | 新建/复制/归档/版本对比/语言概览 |
| 编辑器 | 设计内容 | 拖拽组件/变量插入/片段/预览/校验/语言切换 |
| 审批流 | 管控发布 | 提交/通过/驳回/备注 |

## 关键原型
- 编辑器：左组件与片段，中画布，右属性与变量面板；顶部预览/校验/提交审批；右上角提供语言切换器与“新增语言”。
- 多语言状态：
  - 语言切换器（Locale 下拉）
  - 新增语言：从默认语言拷贝全部字段，进入差异编辑
  - 缺失字段提示与完成度进度条
- 变量校验气泡提示：显示缺失/类型错误，点击定位变量位置。

## 流程
```mermaid
flowchart LR
  N[新建] --> E[编辑]
  E --> L[语言切换/新增语言]
  L --> V[校验]
  V -->|通过| A[提交审批]
  A --> P[发布]
```

## 组件/状态
- 组件库、片段库、变量面板、Diff 视图、审批时间线。

## 校验
- 退订与公司信息必填；变量缺失阻止发布；链接重写预检查；多语言缺失必填阻止发布并给出修复入口。

## 空态/错误
- 模板空态：提供示例模板与导入入口；审批失败显示理由与修复建议。