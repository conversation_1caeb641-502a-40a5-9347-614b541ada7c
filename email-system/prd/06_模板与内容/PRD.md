# PRD | 模板与内容管理

- 版本/状态：v2.0（Draft）
- 作者/评审：产品团队/设计团队
- 更新时间：2025-01-15
- 关联链接：模板编辑器原型/品牌组件库设计稿

## 1. 概述

### 1.1 背景与问题
- **现状痛点**：邮件模板制作效率低下，品牌一致性难以保证，变量使用错误率高，缺乏有效的审批流程
- **业务影响**：模板制作周期长（平均2-3天），品牌违规率15%，变量错误导致的发送失败率8%
- **解决价值**：提升模板制作效率70%，确保品牌一致性，降低变量错误率至1%以下

### 1.2 目标与成功指标
- **业务目标**：建立高效、可靠、可审计的模板内容管理体系
- **用户体验目标**：10分钟内完成专业模板制作，所见即所得的编辑体验
- **技术性能目标**：模板渲染预览<1s，支持10万+模板并发编辑
- **成功指标**：
  - 模板制作效率提升70%（从3天降至1天内）
  - 品牌合规率达到98%+
  - 变量错误率<1%
  - 模板复用率>30%
  - 审批通过率>85%

### 1.3 目标用户与使用场景
- **品牌设计师**：创建品牌组件库、设计模板主题、维护视觉规范
- **内容运营**：使用组件快速搭建模板、配置变量、提交审批
- **营销经理**：审核模板内容、管理模板版本、监控使用情况
- **开发者**：通过API集成模板、自定义变量、扩展组件功能

## 2. 范围与假设

### 2.1 功能范围
**In Scope：**
- 可视化拖拽编辑器 + 代码编辑器双模式
- 响应式模板预览（桌面/移动/暗色模式）
- 品牌组件库与主题管理
- 变量系统与智能校验
- 模板版本管理与审批流程
- 素材库管理与CDN集成
- 模板性能优化与兼容性检测
- 多语言模板（Locale 变体：主题/预览文本/正文/资产按语言切换，支持回退策略与发送侧匹配）

**Out of Scope：**
- 视频编辑功能
- 复杂动画效果
- 第三方设计工具深度集成（如Figma插件）

**Future Scope：**
- AI辅助内容生成
- 多语言自动翻译与术语库/翻译记忆集成
- 高级动画效果支持

### 2.2 约束与假设
- **技术约束**：支持HTML/MJML格式，兼容主流邮件客户端
- **业务约束**：必须符合CAN-SPAM/GDPR合规要求
- **资源约束**：单个模板大小<2MB，素材库总容量<100GB/租户
- **依赖项**：CDN服务、图片压缩服务、邮件客户端测试服务

## 3. 用户故事与验收标准

### 3.1 核心用户故事

**故事1：快速模板创建**
- 作为内容运营，我想要使用预制组件快速搭建邮件模板，以便在30分钟内完成专业模板制作

**故事2：品牌一致性保证**
- 作为品牌设计师，我想要创建统一的组件库和主题，以便确保所有邮件模板符合品牌规范

**故事3：变量管理与校验**
- 作为内容运营，我想要可视化配置变量并获得实时校验，以便避免发送时的变量错误

**故事4：协作审批流程**
- 作为营销经理，我想要审核团队创建的模板并提供反馈，以便确保内容质量和合规性

**故事5：版本管理与回滚**
- 作为内容运营，我想要管理模板版本并支持快速回滚，以便应对紧急修改需求

### 3.2 验收标准（Gherkin格式）

```gherkin
# 模板创建与编辑
Given 用户具有模板编辑权限
When 用户创建新模板并拖拽组件到画布
Then 模板应实时渲染预览
And 组件应自动应用品牌主题样式
And 预览响应时间应<1秒

# 变量校验
Given 模板包含必填变量{{contact.name}}
When 用户尝试发布模板但未配置变量默认值
Then 系统应阻止发布并显示错误提示
And 错误提示应包含具体的缺失变量名称
And 提供一键修复建议

# 审批流程
Given 模板已完成编辑并提交审批
When 审批人查看模板差异对比
Then 应显示与上一版本的可视化差异
And 审批人可以添加评论和修改建议
And 审批结果应实时通知创建者

# 兼容性检测
Given 模板包含复杂CSS样式
When 用户触发兼容性检测
Then 系统应检测主流邮件客户端兼容性
And 提供不兼容项的修复建议
And 生成兼容性报告
```

### 3.3 边界与异常场景
- **模板过大**：超过2MB时自动压缩图片并提示优化建议
- **变量冲突**：检测变量命名冲突并提供重命名建议
- **权限不足**：无编辑权限时显示只读模式
- **网络异常**：自动保存草稿，网络恢复后同步
- **并发编辑**：多人编辑时显示冲突提示并支持合并

## 4. 功能详细设计

### 4.0 多语言与区域化（新增）
**目标**：同一模板支持多个 Locale（如 zh-CN/en-US/es-ES），包含主题、预览文本、正文与可选资产差异，发送时按收件人语言或活动语言策略匹配，未命中时回退到默认语言。

**能力**：
- 语言变体管理：新增/删除语言；从默认语言一键拷贝；差异标记
- 字段覆盖：主题、预览文本、正文（HTML/MJML）、资源链接可按语言覆盖
- 预览与校验：切换语言实时预览；校验缺失语言的必填字段
- 回退策略：Locale → 语言（en-US→en）→ 默认语言
- 发送侧匹配：优先使用联系人 `preferred_locale`，否则用活动配置语言；支持强制覆盖

**发送前检查（扩展）**：
- 若活动配置了多语言发送且某语言缺失必填字段，则阻止发布并提示
- 显示已覆盖/继承状态；统计不同语言的完成度

### 4.1 模板编辑器
**可视化编辑器**
- 拖拽式组件库：文本、图片、按钮、分割线、社交媒体、商品卡片等
- 实时预览：桌面/移动/暗色模式三种视图
- 样式面板：字体、颜色、间距、对齐等可视化配置
- 响应式设计：自动适配不同屏幕尺寸

**代码编辑器**
- HTML/MJML双格式支持
- 语法高亮与智能提示
- 实时错误检测与修复建议
- 代码与可视化模式无缝切换

### 4.2 品牌组件库
**组件管理**
- 预制组件：页眉、页脚、导航、产品展示、优惠券等
- 自定义组件：支持HTML/CSS自定义组件创建
- 组件版本：支持组件版本管理与更新推送
- 组件权限：按团队/角色控制组件使用权限

**主题系统**
- 品牌主题：色彩、字体、Logo、间距等统一配置
- 主题继承：子主题可继承并覆盖父主题样式
- 主题预览：一键切换查看不同主题效果
- 主题导出：支持主题配置导出与导入

### 4.3 变量系统
**变量类型**
- 联系人变量：{{contact.name}}、{{contact.email}}等
- 系统变量：{{system.date}}、{{system.unsubscribe_url}}等
- 自定义变量：支持文本、数字、日期、布尔等类型
- 条件变量：支持if/else逻辑渲染

**变量校验**
- 语法检查：检测变量语法正确性
- 必填校验：检测必填变量是否配置默认值
- 类型校验：检测变量类型与使用场景匹配性
- 预览测试：使用测试数据预览变量渲染效果

### 4.4 版本管理与审批
**版本控制**
- 自动版本：每次保存自动创建版本快照
- 版本对比：可视化显示版本间差异
- 版本回滚：一键回滚到历史版本
- 版本标签：支持为重要版本添加标签说明

**审批流程**
- 审批配置：可配置审批人员与审批规则
- 审批通知：邮件/站内信通知审批人员
- 审批记录：完整记录审批过程与意见
- 批量审批：支持批量审批多个模板

### 4.5 多语言与区域化 UI（新增）
- 语言切换器：顶部下拉选择 Locale，支持新增语言（基于默认语言拷贝）
- 语言状态：当前语言差异标记，缺失字段提示
- 预览联动：切换语言同步更新预览与校验

## 5. 数据设计

### 5.1 核心实体
**Template（模板）**
- id、tenant_id、name、description
- editor_type（visual/code）、content（HTML/MJML）
- variables（变量定义）、theme_id（主题ID）
- status（draft/pending/published/archived）
- created_by、updated_by、created_at、updated_at

**TemplateVersion（模板版本）**
**TemplateLocale（模板语言变体）**（新增）
- id、template_id、version_number、locale（如 zh-CN/en-US）
- subject、preheader、content_html、assets_overrides_json
- is_default（模板层面仅一个默认语言）
- created_by、created_at、updated_at
- id、template_id、version_number、content
- variables、changelog、created_by、created_at

**Component（组件）**
- id、tenant_id、name、type、html_content
- css_styles、variables、preview_image
- category、tags、is_public、created_by

**Theme（主题）**
- id、tenant_id、name、colors、fonts
- spacing、logo_url、parent_theme_id
- is_default、created_by、updated_at

### 5.2 数据流设计
**编辑流程**：用户编辑 → 自动保存草稿 → 变量校验 → 提交审批 → 发布
**版本管理**：每次保存创建版本 → 版本对比 → 选择回滚版本
**组件同步**：组件更新 → 通知使用该组件的模板 → 用户选择是否更新

## 6. 接口设计

### 6.1 API列表（仅GET/POST）
**模板管理**
- POST `/api/templates/create` - 创建模板
- POST `/api/templates/update` - 更新模板
- GET `/api/templates/get` - 获取模板详情
- POST `/api/templates/list` - 获取模板列表
- POST `/api/templates/publish` - 发布模板
- POST `/api/templates/archive` - 归档模板

**版本管理**
- GET `/api/templates/versions/get` - 获取版本列表
- POST `/api/templates/versions/compare` - 版本对比
- POST `/api/templates/versions/rollback` - 版本回滚

**组件管理**
- POST `/api/components/create` - 创建组件
- POST `/api/components/list` - 获取组件列表
- POST `/api/components/update` - 更新组件

**校验与预览**
**多语言（新增）**
- POST `/api/templates/locales/add` - 为模板新增语言（可从默认语言拷贝）
- POST `/api/templates/locales/update` - 更新指定语言字段
- POST `/api/templates/locales/delete` - 删除语言
- GET `/api/templates/locales/list` - 获取模板语言列表与完成度

- POST `/api/templates/validate` - 模板校验
- POST `/api/templates/preview` - 模板预览
- POST `/api/templates/test-render` - 测试渲染

### 6.2 统一响应格式
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "template": {
      "id": "tpl_123",
      "name": "Welcome Email",
      "status": "published"
    }
  },
  "errors": null,
  "meta": {
    "request_id": "req_456",
    "trace_id": "trace_789"
  }
}
```

### 6.3 错误码定义
| Code | Message | 说明 |
|------|---------|------|
| 400601 | missing_required_variable | 必填变量缺失 |
| 400602 | invalid_template_syntax | 模板语法错误 |
| 400603 | template_too_large | 模板文件过大 |
| 403601 | template_edit_forbidden | 无模板编辑权限 |
| 409601 | template_name_conflict | 模板名称冲突 |
| 409602 | concurrent_edit_conflict | 并发编辑冲突 |

## 7. 非功能性需求

### 7.1 性能要求
- **响应时间**：模板预览P95<1s，保存操作P95<500ms
- **并发支持**：支持1000+用户同时编辑模板
- **存储容量**：单租户模板总数<10万，素材库<100GB

### 7.2 安全与合规
- **数据安全**：模板内容加密存储，敏感变量脱敏显示
- **访问控制**：基于角色的模板访问权限控制
- **合规要求**：自动检测并提示CAN-SPAM/GDPR合规项

### 7.3 可用性要求
- **系统可用性**：99.9%可用性目标
- **数据备份**：模板数据实时备份，支持灾难恢复
- **降级策略**：编辑器故障时提供基础HTML编辑功能

## 8. 测试策略

### 8.1 功能测试
- 模板创建、编辑、预览、发布全流程测试
- 变量系统各种类型变量渲染测试
- 审批流程多角色协作测试
- 兼容性测试覆盖主流邮件客户端

### 8.2 性能测试
- 大模板（接近2MB）编辑性能测试
- 1000+并发用户编辑压力测试
- 素材库大文件上传下载性能测试

### 8.3 安全测试
- XSS/CSRF等Web安全漏洞测试
- 权限控制边界测试
- 敏感数据泄露测试

## 9. 风险评估与预案

### 9.1 技术风险
**风险**：富文本编辑器兼容性问题
**应对**：采用成熟的开源编辑器，建立兼容性测试矩阵

**风险**：大文件上传影响系统性能
**应对**：实施文件大小限制，使用CDN加速，异步处理

### 9.2 业务风险
**风险**：用户学习成本高，接受度低
**应对**：提供详细的使用教程，设计直观的用户界面

**风险**：模板审批流程过于复杂
**应对**：提供灵活的审批配置，支持快速审批通道

## 10. 发布计划

### 10.1 里程碑定义
**P1（MVP）**：基础编辑器、组件库、变量系统、简单审批
**P2（增强）**：高级编辑功能、完整审批流程、版本管理
**P3（优化）**：性能优化、高级组件、AI辅助功能

### 10.2 验收标准
- 功能完整性：所有核心功能正常运行
- 性能基准：满足响应时间和并发要求
- 用户体验：用户满意度>85%，任务完成率>90%

## 附录

### A. 术语表
- **MJML**：邮件标记语言，用于创建响应式邮件模板
- **变量渲染**：将模板中的变量占位符替换为实际数据
- **组件库**：可复用的模板组件集合
- **主题系统**：统一的品牌样式配置体系

### B. 参考资料
- 邮件客户端兼容性指南
- CAN-SPAM法规要求
- GDPR数据保护条例
- 无障碍设计标准（WCAG 2.1）

### C. 变更记录
- v2.0：完整重写PRD，增加详细的功能设计和技术要求
- v1.0：初始版本，基础功能描述
