# 技术方案设计｜模板与内容

## HLD
- 组件：编辑器前端、渲染与校验服务、资产存储、审批流服务、Diff 服务
- 数据：`templates`、`template_versions`、`assets`、`approvals`

## 时序
```mermaid
sequenceDiagram
participant UI
participant VAL as Validator
participant APP as Approval
participant DB
UI->>VAL: 校验模板
VAL-->>UI: 校验报告
UI->>APP: 提交审批
APP->>DB: 状态变更
```

## LLD
- 语法：Handlebars 子集 + 片段；
- 校验：变量必填、退订/公司信息、链接追踪；
- 渲染：MJML→HTML 预编译缓存。

### 多语言渲染与回退（新增）
- 数据模型：`template_locales` 存储语言变体，链接 `template_versions`
- 选择策略：`preferred_locale` → 语言泛化（en-US→en）→ 默认语言
- 渲染缓存键：`template_id:version:locale`
- 校验扩展：各语言的 subject/preheader/content_html 必填校验；缺失提示
- 预览：前端切换 locale 参数传给预览 API，返回对应内容

---

## 1. 目标与范围
- 目标：渲染预览 <1s；发布流程可审计；变量缺失阻止发布。
- 范围：编辑/预览、变量/片段、校验、版本/审批、发布与归档。

## 2. 数据与接口
- 表：`templates`、`template_versions`、`template_approvals`、`template_locales`；
- API：POST `/api/templates/create|update|publish|archive|validate|get|list`；
  - 多语言：POST `/api/templates/locales/add|update|delete`，GET `/api/templates/locales/list`。

## 3. 规则与算法
- 变量治理：必填校验、默认值、类型检查；
- 合规模型：退订/公司信息/垃圾词；
- 链接重写与 UTM 自动注入（可选开关）。
 - 多语言回退：语言-地区拆解回退，按 `zh-Hans-CN→zh-CN→zh→默认`。

## 4. 可靠性
- 审批版本不可变；
- 渲染与校验服务熔断与兜底提示。

## 5. 安全与合规
- 模板内容存储与访问鉴权；
- 发布前执行 `compliance_validation_logs` 校验并存档结果。

## 6. 可观测与性能
- 指标：渲染/校验耗时、发布通过率、回滚率；
- 告警：渲染 P95 异常、校验失败突增。

## 7. 测试与验收
- 变量缺失、退订校验、版本 diff、审批流、回滚；
- 性能：批量预览与大模板渲染。

## 8. 发布与回滚
- 版本化发布；审批策略可配置，回滚到上一个发布版本。

## 9. 风险与对策
- 大模板/外链资源导致渲染慢：缓存与占位提示；
- 变量与数据口径不一致：强校验 + 示例渲染。

## 10. Checklist
- 统一响应；
- 审批/审计闭环；
- 校验与发布门禁启用。
