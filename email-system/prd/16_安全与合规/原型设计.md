# 原型设计｜安全与合规

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 合规校验 | 内容合规 | 校验退订与公司信息/频率 |
| 隐私请求（DSR） | 流程处理 | 创建/审批/执行/结果与审计 |
| 数据保留与加密 | 策略配置 | 保留周期/字段加密/密钥轮换 |
| 区域化与驻留 | 区域设置 | 选择区域/迁移计划 |

## 关键原型
- DSR 工单：状态流转（创建/审批/处理中/完成），每步审计与通知。
- 合规校验：模板/活动发布前的阻断提示与修复链接。

## 流程
```mermaid
flowchart LR
  C[创建 DSR] --> R[审批]
  R --> P[处理]
  P --> A[审计记录]
  P --> N[结果通知]
```

## 组件/状态
- 工单表、状态标签、审计时间线、加密策略表单。

## 校验
- 删除冷静期确认；加密字段不可逆提示；区域切换风险提示。

## 空态/错误
- 无 DSR 工单空态；密钥轮换失败的回滚提示。