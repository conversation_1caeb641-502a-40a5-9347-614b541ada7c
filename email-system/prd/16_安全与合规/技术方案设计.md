# 技术方案设计｜安全与合规

## HLD
- 组件：合规校验器、DSR 工作流、数据保留与加密、区域驻留控制器
- 数据：`dsr_requests`、`retention_policies`、`encryption_keys`

## 时序
```mermaid
sequenceDiagram
participant U as User
participant DSR as DSRFlow
participant ENC as Encryption
U->>DSR: 提交请求
DSR->>ENC: 数据导出/删除
DSR-->>U: 结果与审计
```

## LLD
- 审计不可篡改（WORM/Append-only）；
- 密钥轮换；
- 区域化通过数据路由与分区策略实现。

---

## 1. 目标与范围
- 目标：退订可达；DSR 可追踪；保留与加密可配置；区域化可选。
- 范围：DSR（导出/删除、自助入口、审批流、审计）、内容合规、保留与加密、区域化与驻留。

## 2. 数据与接口
- 表：`privacy_dsr_requests`、`data_retention_policies`、`field_encryption_policies`、`compliance_validation_logs`；
- API：`/api/privacy/dsr/*`、`/api/compliance/validate`、`/api/tenants/data_retention`。

## 3. 规则
- 删除冷静期与法定保留豁免；
- 模板/活动发布前合规校验（必填退订与公司信息）；
- 字段级加密与密钥轮换策略。

## 4. 可靠性
- DSR 队列化处理；
- 导出大文件分片与签名 URL 过期控制。

## 5. 安全与合规
- 审计不可篡改；
- 密钥安全存储；
- 区域路由与驻留策略（企业版）。

## 6. 可观测与性能
- 指标：DSR SLA、校验失败率、保留清理耗时；
- 告警：DSR 超时与失败。

## 7. 测试与验收
- DSR 全流程；
- 校验前置；
- 数据保留清理任务。

## 8. 发布与回滚
- 合规规则灰度；
- 密钥轮换演练与回滚。

## 9. 风险与对策
- 删除不可逆：冷静期与审批；
- 驻留/跨境：分区与访问控制。

## 10. Checklist
- 统一响应；
- 审计全覆盖；
- SLA/告警齐备。
