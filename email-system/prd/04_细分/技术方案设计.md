# 技术方案设计｜人群圈选（原名：细分 / Segments）

## HLD
- 组件：规则编译器（DSL→SQL/Plan）、计算引擎（批/流）、快照管理、缓存
- 数据：`segments`、`segment_snapshots`、`segment_metrics`

## 时序
```mermaid
sequenceDiagram
participant UI
participant COM as Compiler
participant ENG as Engine
participant DB
UI->>COM: 提交规则树
COM-->>UI: 预估SQL/Plan
UI->>ENG: 计算请求
ENG->>DB: 执行/物化快照
UI-->>UI: 返回预估与示例
```

## LLD
- 增量重算：基于事件更新窗口索引；
- 物化：快照表 + 版本号；
- 缓存：热门细分结果 TTL 10min。

## 索引
- 事件表 `(contact_id, event_type, ts)`；
- 快照 `(segment_id, contact_id)`

## 可观测
- 计算耗时、物化大小、命中率

---

## 1. 目标与范围
- 目标：百万联系人分钟级计算完成；预估 P95<2s；快照用于投放口径一致。
- 范围：规则 DSL、动态/静态细分、快照冻结、重建计划、预览与示例。

## 2. 上下文与依赖
- 依赖：联系人与时间线事件；活动/旅程引用快照；审计与任务中心。

## 3. 数据设计（对齐数据库设计）
- 表：`segments`、`segment_snapshots`、`segment_snapshot_members`、`segment_jobs`；
- 规则存储：`rule_tree_json`；快照与版本；成员计数与索引。

## 4. API（仅 GET/POST）
- POST `/api/segments/create|update|delete|rebuild|get|preview`；
- 预估返回规模与示例，预览限制上限（默认 100 条）。
- 批量能力（新增）：
  - POST `/api/segments/bulk/tag`：参数 { segment_id 或 rule_tree, scope: all/selected, selected_contact_ids?, tags: [..], mode: add/remove }
  - POST `/api/segments/bulk/add_to_list`：参数 { list_id 或 new_list_name, scope, selected_contact_ids? }
  - POST `/api/segments/export_snapshot`：参数 { segment_id 或 rule_tree, note?, expire_at? }

## 5. 规则与算法
- 规则 DSL：AND/OR/括号、事件窗口/计数、属性比较、列表/标签包含；
- 预估：抽样 + 近似统计算法；
- 重建：全量/增量（基于事件时间窗口索引）。
 - 批量：对“当前预估人群”采用规则执行器拉取 contact_id 列表并分页处理；对“已选联系人”直接按 ID 集合处理；公共去重与幂等。

## 6. 可靠性
- 作业 `segment_jobs`：状态机、进度、错误；
- 幂等：`(tenant_id, segment_id, version)`；
- 快照引用不可变；动态口径与快照口径一致（带版本）。
 - 批量作业：写入 `segment_jobs` 子类型 bulk_tag/bulk_add_to_list/export_snapshot；支持断点续跑与部分失败重试。

## 7. 安全与合规
- 上游鉴权；敏感字段不出界；审计规则变更与投放引用。
 - 批量权限：打标签/加入列表/导出需具备相应角色；审计记录操作人、作用范围、样本规模、参数。

## 8. 可观测与性能
- 指标：计算耗时、增量窗口命中率、快照大小；
- 性能：分片并行、物化视图/临时表、必要时行列混存策略评估（TODO）。

## 9. 测试与验收
- 规则边界/空值/跨时区；
- 预估准确度与误差范围；
- 增量重算与快照一致性；
- 验收：看板/告警、审计可导出。

## 10. 发布与回滚
- DSL 版本化；编译器灰度；快照与动态一致性校验器开关。

## 11. 风险与对策
- 规则复杂导致超时：限制算子深度与数据量、分段计算；
- 动态与快照口径偏差：单测+集成对齐测试。

## 12. Checklist
- 仅 GET/POST；
- DDL 与回滚；
- 作业状态机与告警；
- 规则与口径文档化。
