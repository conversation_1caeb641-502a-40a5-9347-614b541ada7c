# PRD｜人群圈选（原名：细分 / Segments）

## 1. 背景与目标
- 背景：支持属性/行为/时间窗口/频率/地理设备等复杂条件；动态/静态与快照。
- 目标：百万级分钟级出结果；规则易用可视化；口径一致。

## 2. 用户画像与需求
- 运营/分析：构建高意向人群；预估规模与示例。

## 3. 功能模块
- 可视化规则树（AND/OR/括号、权重）
- 预览与预估；动态/静态与快照冻结
- 重建策略与计划

### 3.1 人群操作与批量能力（新增）
- 批量打标签：对当前预估人群或手动选中样本进行标签追加/移除
- 批量加入列表：将人群加入既有或新建列表
- 导出静态人群快照：生成可复用的快照，支持作为活动/旅程目标
- 发送测试活动：对小样本发送校验邮件（仅管理员可用）

### 界面/流程
- 规则编辑 → 预览与预估 → 批量操作（可选）→ 保存（动态/静态）→ 冻结快照投放

### 数据结构
- Segment：id、name、rule_tree、type、refresh_policy、snapshot_id

### 边界/异常
- 空值策略；多语言大小写；事件窗口跨时区。

## 4. 非功能
- 并行分片、增量重算、缓存。

## 5. 用户故事
1) “30天下单且近3封未打开”——预估 12,345 人；一键给此人群打上“黑五预热”标签；
2) 冻结快照用于 A/B 活动；
3) 每天 3:00 自动重建动态人群。

## 6. 流程图
```mermaid
flowchart LR
  E[编辑规则] --> P[预览&预估]
  P --> B[批量操作]
  B --> S[保存类型]
  S --> F[冻结快照]
  F --> U[投放引用]
```

## 7. 数据与接口
- `/api/segments/create|update|delete|rebuild|get|preview`
- 请求体含 rule_tree，返回预估与示例。
- 批量接口（新增）：
  - POST `/api/segments/bulk/tag`（scope: all/selected, tags: [..], mode: add/remove）
  - POST `/api/segments/bulk/add_to_list`（list_id/new_list_name, scope）
  - POST `/api/segments/export_snapshot`（segment_id or rule_tree，备注与过期策略）

## 8. 竞品与差异
- Klaviyo 实时性强；本产品强调“口径透明 + 快照冻结”。

## 9. 里程碑
- P1 规则与预览；P2 动态与快照；P3 重建与缓存优化。

## 附：缺失分析与补充（并入）
- 规则 DSL：提供 AND/OR/括号、事件窗口与计数的 DSL 说明（示例见补充文档）。
- 错误码：400401 invalid_rule_tree、409402 field_missing、503401 segment_engine_busy。
- 一致性：快照冻结用于投放，动态计算用于实时预估；二者口径对齐且带版本号。
- 性能口径：百万联系人分钟级；增量重算与物化视图策略明确。
