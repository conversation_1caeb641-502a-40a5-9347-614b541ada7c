# 预热算法设计

本文档定义“发件渠道/专用IP预热（IP/域名信誉构建）”的策略与实现。用于 `投递与可达性` 模块中的“IP 池与预热”。

## 1. 目标与约束
- 目标：
  - 快速、稳定地建立发件人信誉（IP/域名），达到目标发送规模且不触发大规模封锁
  - 最大化投递成功率与收件箱率，最小化投诉/退回
  - 自适应不同 ISP（Gmail/Outlook/Yahoo/QQ/企业域等）策略
- 约束：
  - 严格遵循各 ISP 的批量发送与信誉构建指南
  - 合规：尊重退订与同意；不得发送违法/高风险列表
  - 平台限额：配合账户额度/配额、RPM/QPS 限流

## 2. 预热阶段与静态曲线（Baseline）
- 预热周期：默认 28 天，可根据历史信誉缩短/延长（14~42 天）
- 初始量：从极小每日量（例：每 ISP 500~2,000）起步
- 典型基线曲线（以单 ISP 为例）：
  - D1: 500
  - D2: 1,000
  - D3: 2,000
  - D4: 3,000
  - D5: 5,000
  - D6~D7: 8,000
  - W2: 12,000 → 16,000
  - W3: 20,000 → 32,000
  - W4: 40,000 → 60,000（依据表现上/下限自适应）
- 内容与对象：仅向“高参与度与明确同意”的活跃人群发送（开/点活跃≥最近90天，零/低退订投诉来源）

## 3. 动态自适应控制（核心）
- 指标：按 ISP 维度滚动窗口（过去 N 批/24h）
  - 投递成功率（delivered rate）
  - 打开率（open rate）、点击率（click rate）
  - 退回率（bounce, hard vs soft）
  - 投诉率（complaint/spam）
  - 延迟/拒收（deferral/block）与 4xx/5xx 比例
- 自适应策略：
  - 正常区（绿）：上调 30%~100%
  - 关注区（黄）：保持或小幅上调 ≤10%
  - 风险区（橙）：降档 20%~50%，并切换更保守 ISP 配置
  - 触发区（红）：立即暂停该 ISP 预热 ≥24h，进入诊断
- 动态上限：受账号总额度、IP 池可用并发、模板/域名信誉共同限制

## 4. ISP 分配与速率限制
- 目标：避免单 ISP 短时过载与触发频控
- 分配：基于历史人群 ISP 占比 × 安全权重（ISP_weight）与上文动态上限
- 限流：
  - 全局 RPM/QPS（平台）
  - ISP 级 RPM（如 Gmail ≤ X/min, Outlook ≤ Y/min）
  - 连接并发与管道并发（SMTP 并发/每连接批量）

## 5. 异常阈值与回退/暂停
- 建议阈值（ISP 级，单日/滚动 1k+ 样本）：
  - 硬退回（hard bounce）> 2%：降档 30%，并强制列表清洗
  - 投诉率 > 0.08%：暂停 24h，触发审计与模板/人群调整
  - deferral/block > 3%：降档 30%，调整时段与速率
  - 打开率 < 10% 且点击率 < 1%：保持或降档，并收紧人群质量
- 回退策略：
  - 单 ISP 触发：仅该 ISP 回退
  - 多 ISP 同时恶化：暂停全局预热，全面体检（认证、内容、列表、发送时间）

## 6. 发送次序与对象选择
- 优先级：高参与度分层 → 热度衰减人群 → 新增同意人群
- 排除：退订/投诉/硬退回域/风险域；灰名单/冷名单延后
- 模板：首周减少强促销元素，提升可读价值与互动引导

## 7. 实现与伪代码
```pseudo
for each day in warmup_period:
  for each isp in ISP_LIST:
    metrics = get_isp_metrics(isp, window=24h)
    state = classify_state(metrics, thresholds)
    planned = baseline_plan(day, isp)
    limit   = dynamic_cap(isp, account_caps, ip_pool_caps)

    send_quota = adjust_by_state(planned, state)
    send_quota = min(send_quota, limit)

    cohort = pick_audience(
      consented=true,
      engagement_tier in [high, mid],
      exclude=[unsub, complaint, hard_bounce, risky_domains]
    )
    batch_send(isp, cohort.take(send_quota), rate_limits[isp])

    observe = collect_outcomes(isp)
    push_metrics(isp, observe)

    if breached(observe, thresholds):
      apply_backoff(isp)
      create_incident(isp, observe)
```

## 8. 监控、SLO 与可观测性
- 实时：投递事件（delivered/deferral/bounce/complaint）流式聚合到分钟级
- 仪表盘：ISP × 指标（成功/退回/投诉/打开/点击/延迟），曲线+阈值线
- SLO 例：
  - 预热阶段投递成功率 ≥ 97.5%
  - 投诉率 ≤ 0.05%（警戒 0.08%）
  - 硬退回 ≤ 1.5%（警戒 2%）

## 9. 运营 SOP（异常处置）
- 投诉升高：暂停 → 模板审查（主题、频次、发件名）→ 人群缩紧 → A/B 降噪
- 硬退回升高：立即进行列表清洗与验证 → 抽检退回代码
- 某 ISP 拒收：调低该 ISP 速率与并发，切换发送时段，检查认证/逆解析

## 10. A/B 与灰度
- 基线 vs 自适应策略 AB：对比 ramp-up 速度与投诉/退回
- 模板 AB：主题行与 CTA 强度
- 灰度：按 IP 池、域名、ISP 分层灰度发布新策略

## 11. 风险与对策
- 批量误发高风险人群 → 双重过滤 + 发送前快照审查
- 监控盲区 → 强制最小采样量，低基数启用贝叶斯平滑
- 昼夜节律 → 避开 ISP 高拥时段，分散时区

## 12. TODO（落地项）
- ISP 画像与速率库（可配置）
- 列表健康度模型（活跃/风险评分）与自动清洗流程
- 动态状态机与策略引擎（规则+阈值+ML 评分，可热更新）
- 预热任务编排：失败回退、断点续跑、进度可视化
- 事件数据模型与预警策略（阈值、环比、同比）
```
接口与实现细节将在《自动验证与预热_技术细节.md》内进一步展开。
```

---

## 附录A｜行业账户预热与发送策略（精炼版，保留全部细节）

> 目的：将主流邮件服务商（Zoho Campaigns、Sendinblue、Mailchimp、Constant Contact、SendGrid、Mailgun、U-Mail 等）在预热与发送策略上的共性做法沉淀为可执行模板，便于评估与落地。

### 1) 域名 & IP 预热（Warm-up）
- 目标：让 ESP（Gmail、Outlook/Hotmail、QQ/163、企业域等）逐步认可发件信誉，避免“冷启动”触发限流/垃圾箱。
- 分阶段基线（可按规模等比缩放）：

| 阶段 | 日发信量 | 典型周期 | 关键动作 |
|---|---:|---|---|
| 冷启动 | 20–100 封 | 第 1–3 天 | 使用高互动种子名单（内部/老客/合作方），确保 ≥30% 打开率 |
| 线性爬坡 | 日增 30–50% | 第 4–14 天 | 按收件域分流（Gmail 单独一条线），观察 Spam Rate < 0.1% |
| 指数爬坡 | 日增 50–100% | 第 15–30 天 | 自动规则：若当日硬退/总退信率 >2% 或 投诉率 >0.08%，次日降量 30% |

- 服务商差异（概览）：
  - SendGrid / Mailgun：提供 Automated IP Warmup（可按曲线自动放量）。
  - Amazon SES：共享池默认手动控制；专用 IP 的自动预热与关闭方式详见“技术细节｜AWS SES”章节。
  - 国内 U-Mail：提供国内四大邮箱（QQ/163/139/Sina）专用预热模板与分域限速。

### 2) 域名声誉监控（SNDS、Postmaster Tools）
- 实时拉取并监控：Gmail Postmaster、Microsoft SNDS、QQ 开放平台等的 Spam Rate、IP/Domain Reputation、FBL 投诉量。
- 预警阈值：当 Gmail Domain Reputation < 中/高 或 Spam Rate > 0.3% 时，自动暂停该域名流量。

### 3) 分域 & 分 IP 策略
- 共享池 vs 专属 IP：
  - 日发 < 5 万：可用共享池；
  - ≥ 5 万或 B2B（高价值低频）：建议专属 IP，配置 PTR、MX、SPF、DKIM、DMARC。
- 分域：
  - 事务类（订单/验证码）使用 `trans.company.com`；
  - 营销类使用 `promo.company.com`；
  - 避免事务域名因营销投诉被拖垮。

### 4) 发送节奏 & 节流（Throttling）
- 动态 QPS/RPM（经验基线，依热度自适应）：
  - Gmail：冷域名 1–2 封/秒；热域名最高约 30 封/秒。
  - QQ/163：冷域名 10–20 封/分钟；热域名 200–300 封/分钟。
- 退信/投诉触发：
  - 若 5xx 退信率 >5%：立即将速率减半；
  - 若 FBL 投诉率 >0.1%：暂停该批次发送。

### 5) 智能路由（Smart Routing）
- 按收件域/历史互动/地理位置选择：
  - IP：欧美客户走美国东部 IP；国内客户走华东/上海出口；
  - 通道：高互动名单用“速达通道”（高 QPS），低互动名单用“稳健通道”（低 QPS，高隔离）。

### 6) 种子名单 & 互动加权
- 发送中固定加入 5–10% 种子邮箱（内部、活跃客户、合作伙伴）。
- 互动加权：
  - 最近 30 天有开/点的优先发送；
  - 近 90 天无互动的延迟 24 小时发送，降低被判定为垃圾群发的风险。

### 7) 主题/内容 A/B 与预检
- 预热阶段做小流量 A/B（约 10% 流量）：对比主题行与版本，选择 Spam Score 低、打开率高的版本全量。
- ESP 预检：Gmail Promo Tab 检测工具（如 Mailchimp、Litmus 集成）、国内“腾讯易企发”等。

### 8) 黑名单 & DNSBL 扫描
- 发送前自动扫描 Spamhaus、Barracuda、CBL、SURBL 等；命中即切换 IP。
- 若 24 小时内同一 IP 被 ≥2 个黑名单收录：自动退役并启用新 IP。

### 9) 一键冷却与重暖
- 触发阈值后平台支持：暂停该域名/IP、自动降温（回到 100 封/日起点）、7 天后重新进入线性爬坡。
- 日志回溯：近 30 天每 10 分钟的发送量/退信/投诉曲线可追溯，定位问题活动。

### 常见服务商的预热/策略功能对照

| 功能/平台 | Mailchimp | SendGrid | Klaviyo | U-Mail（国内） | Zoho Campaigns |
|---|---|---|---|---|---|
| 自动 IP Warm-up | ❌（共享池为主） | ✅（API 控制） | ❌ | ✅（国内域模板） | ✅ |
| 分通道节流 | 基础 | 高级规则引擎 | 基础 | 国内域专用 | 中 |
| 黑名单扫描 | 第三方插件 | 内置 | 第三方 | 内置 | 内置 |
| Postmaster 集成 | Gmail、Yahoo | Gmail、Yahoo、SNDS | Gmail、Yahoo | QQ、163 开放平台 | Gmail、Yahoo |
| 一键冷却 | ❌ | ✅ | ❌ | ✅ | ✅ |

### 实操清单（可直接落地）
- 第 0 天：注册 `promo.company.com`，配置 SPF、DKIM、DMARC、MX、PTR。
- 第 1–3 天：导入 100 个高互动种子用户，发送欢迎邮件，目标打开率 ≥ 40%。
- 第 4–10 天：每天按 50% 递增，名单拆成 Gmail / 非 Gmail 两条线，分别观察 Postmaster。
- 第 11–30 天：开启自动化营销（欢迎流程、生日优惠），每日抽检退信率 <2%、投诉率 <0.08%。
- 第 30 天以后：
  - 事务域名 `trans.company.com` 用独立 IP；
  - 日常营销用 `promo.company.com`（共享池或专属 IP）；
  - 每月一次“健康日”：停发营销，只发事务邮件，让域名休息 24 小时。

> 一句话总结：预热不是一次性动作，而是“监控—调速—冷却”的持续循环机制。

