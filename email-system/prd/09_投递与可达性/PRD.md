# PRD | 投递与可达性管理

- 版本/状态：v2.2（Draft）
- 作者/评审：产品团队/技术团队
- 更新时间：2025-08-11
- 关联链接：域名配置向导原型/投递监控看板设计/发件渠道管理
- 技术细节补充：`prd/09_投递与可达性/自动验证与预热_技术细节.md`

## 1. 概述

### 1.1 背景与问题
- **现状痛点**：邮件投递率低，域名配置复杂，缺乏有效的信誉管理，投诉和退信处理不及时；缺少批量邮箱验证能力，名单质量不可控，导致预热与投递阶段退信率高；不同业务场景（营销/交易/系统）混用同一发送资源，导致信誉互相影响。
- **业务影响**：平均投递率仅85%，域名配置错误率20%，投诉率超标导致IP被封；名单无验证导致硬退率高、信誉下降；营销邮件投诉影响交易通知投递。
- **解决价值**：提升邮件投递率至98%+，自动化域名预热，提供高性能邮箱验证能力，建立完善的信誉监控体系，通过发件渠道隔离实现业务场景独立管理。

### 1.2 目标与成功指标
- **业务目标**：建立高可达性的邮件投递基础设施、自动化预热、批量邮箱验证能力、多场景发件渠道管理。
- **用户体验目标**：10分钟内完成域名配置，一键启动预热流程；支持一键创建邮箱验证任务并快速查看结果；5分钟内完成发件渠道配置。
- **技术性能目标**：投递率≥98%，限速判定P95<5ms，FBL处理可靠性≥99.99%，邮箱验证吞吐≥2k地址/秒，渠道间资源隔离率100%。
- **成功指标**：
  - 投递率≥98%（从85%提升）
  - 投诉率≤0.08%
  - 硬退率≤0.6%
  - 域名配置成功率≥95%
  - 预热成功率≥90%
  - 邮箱验证覆盖率≥99%，平均验证时延≤1.5s/地址（含SMTP探测并发下）
  - 渠道间信誉隔离率≥99.9%

### 1.3 目标用户与使用场景
- **域名管理员**：配置和管理发送域名、监控域名健康状态、处理DNS问题
- **投递工程师**：配置限速策略、优化投递路由、处理投递异常、预热管理
- **运营人员**：监控投递表现、处理投诉和退信、制定发送策略、在发送前验证名单质量
- **系统管理员**：管理IP池、配置安全策略、监控系统健康、管理发件渠道
- **业务管理员**：配置和管理发件渠道、分配渠道权限、监控渠道表现

## 2. 范围与假设

### 2.1 功能范围
**In Scope：**
- 发件渠道管理（营销/交易/系统渠道、渠道配置、权限控制、资源隔离）
- 域名身份验证（SPF/DKIM/DMARC/BIMI）
- 域名/IP 预热系统（计划曲线、ISP差异化、暂停/恢复、进度与告警）
- 多级限速与流量控制（渠道维度）
- 投诉反馈处理（FBL）
- 退信分析与处理
- 信誉监控与告警（渠道维度）
- IP池管理与路由（渠道隔离）
- 邮箱验证（Syntax/MX/SMTP/风险评分、批量任务、结果下载、API）

**Out of Scope：**
- 第三方ESP集成
- 复杂的机器学习预测模型
- 跨平台统一身份认证
- 渠道间的智能路由切换

**Future Scope：**
- AI驱动的投递优化
- 高级信誉预测模型
- 邮箱验证的历史缓存共享与跨租户风险黑名单
- 渠道间的智能负载均衡

### 2.2 约束与假设
- **技术约束**：DNS传播时间24-48小时，部分ISP的FBL覆盖有限；部分域的SMTP防探测策略导致验证为unknown；渠道间资源隔离需要额外的IP和域名资源。
- **业务约束**：必须符合各ISP的发送政策和反垃圾邮件法规；验证结果仅作风险参考，不作为绝对真值；不同渠道需要不同的合规要求。
- **资源约束**：IP池数量有限，预热周期需要2-4周；SMTP验证需要控制并发与速率，避免被拉黑；渠道隔离需要更多基础设施资源。
- **依赖项**：DNS服务、第三方信誉监控服务、ISP反馈循环、权限管理系统。

## 3. 用户故事与验收标准

### 3.1 核心用户故事
- **故事1：发件渠道快速配置**
  - 作为业务管理员，我想要快速配置发件渠道，以便为不同业务场景提供独立的发送能力
- **故事2：域名快速接入**
  - 作为域名管理员，我想要快速配置发送域名，以便开始邮件发送
- **故事3：自动化预热管理**
  - 作为投递工程师，我想要自动执行域名/IP预热流程，以便建立良好的发送信誉
- **故事4：智能限速控制**
  - 作为投递工程师，我想要设置智能限速策略，以便避免触发ISP限制
- **故事5：实时投递监控**
  - 作为运营人员，我想要实时监控投递表现，以便及时发现和处理问题
- **故事6：自动化抑制管理**
  - 作为系统管理员，我想要自动处理投诉和退信，以便维护发送信誉
- **故事7：批量邮箱验证**
  - 作为运营人员，我想在发送前批量验证邮箱地址，过滤无效/高风险地址，降低退信与投诉
- **故事8：渠道权限管理**
  - 作为系统管理员，我想要为不同团队分配渠道权限，以便实现业务隔离和安全管理

### 3.2 验收标准（Gherkin格式）
```gherkin
# 发件渠道配置
Given 用户创建新的发件渠道
When 系统配置渠道类型和资源
Then 系统应分配独立的域名和IP池
And 系统应设置渠道特定的限速策略
And 系统应配置渠道权限

# 域名配置
Given 用户添加新的发送域名
When 系统生成DNS配置记录
Then 用户应收到详细的DNS配置指南
And 系统应在24小时内自动验证DNS配置
And 验证通过后自动启动预热流程

# 预热管理
Given 新域名通过DNS验证
When 系统启动自动预热流程
Then 系统应按预设曲线逐步增加发送量
And 系统应监控预热期间的投递指标
And 异常情况下应自动暂停预热

# 限速控制
Given 系统检测到投诉率上升
When 触发自动限速机制
Then 系统应立即降低发送速率
And 系统应通知相关人员
And 系统应记录限速原因和持续时间

# 投递监控
Given 系统正在执行邮件发送
When 投递指标出现异常波动
Then 系统应立即触发告警
And 系统应提供详细的异常分析
And 系统应建议相应的处理措施

# 邮箱验证
Given 用户创建邮箱验证任务并上传地址清单
When 系统执行Syntax/MX/SMTP多级验证
Then 系统应给出 valid/invalid/risky/unknown 分类
And 提供下载结果与过滤建议
And 对验证失败的域名提供原因与重试建议

# 渠道隔离
Given 营销渠道出现投诉率超标
When 系统检测到信誉风险
Then 系统应自动隔离该渠道
And 其他渠道应继续正常发送
And 系统应提供渠道恢复建议
```

### 3.3 边界与异常场景
- **DNS传播延迟**：提供手动验证选项和状态追踪
- **BIMI证书依赖**：提供证书验证和管理工具
- **ISP政策变化**：及时更新限速策略和处理规则
- **预热中断**：支持预热流程的暂停和恢复
- **批量域名操作**：支持批量配置和验证
- **邮箱验证Unknown**：部分域拒绝SMTP探测，标记为unknown并提供重试/域策略说明
- **渠道资源不足**：提供资源扩容建议和临时共享策略
- **渠道权限冲突**：提供权限继承和覆盖规则

## 4. 功能详细设计

### 4.1 发件渠道管理
**渠道类型定义**
- 营销渠道（Marketing）：促销、新闻、活动通知，批量发送，需要预热，严格内容合规
- 交易渠道（Transactional）：订单确认、密码重置、验证码，实时发送，高优先级
- 系统渠道（System）：系统告警、运维通知，内部使用，高可靠性要求

**渠道配置**
- 基础信息：名称、代码、描述、状态
- 资源分配：默认域名、默认IP池、配额设置
- 策略配置：限速策略、预热策略、监控阈值
- 权限设置：用户角色、操作权限、数据访问权限

**渠道隔离**
- 域名隔离：不同渠道使用独立域名或子域名
- IP池隔离：不同渠道使用独立IP池
- 限速隔离：渠道级别的限速策略
- 监控隔离：渠道级别的投递监控和告警

### 4.2 域名身份验证
**DNS记录管理**
- SPF记录：配置授权发送IP范围
- DKIM签名：生成和管理域名密钥对
- DMARC策略：配置域名对齐和处理策略
- BIMI记录：品牌标识显示配置

**验证流程**
- 自动DNS查询和验证
- 配置错误检测和修复建议
- 多子域名统一管理
- 验证状态实时更新

### 4.3 域名/IP 自动化预热系统
**预热策略**
- 渐进式发送量增长曲线（保守/标准/激进）
- 基于ISP的差异化预热策略（Gmail/Outlook/Yahoo自适应）
- 预热期间的质量监控（退信、投诉、退订、陷阱命中）
- 异常情况的自动暂停/降速机制（阈值可配）

**预热监控**
- 发送量与成功率追踪（按日/ISP分布）
- 投诉与退信率监控（阈值告警）
- 信誉分数变化趋势与预热进度可视化
- 支持手动暂停/恢复与曲线切换（需二次确认）

### 4.4 多级限速系统
**限速维度**
- 租户、渠道、域名、ISP、IP
**动态调优**
- 基于实时反馈的速率调整；阈值触发降速；信誉恢复后的提升；特殊时期策略

### 4.5 反馈处理系统
**投诉反馈循环（FBL）** 与 **退信处理**（硬退即时抑制、软退重试、原因码分析、异常检测）

### 4.6 信誉监控系统
**监控指标** 与 **告警机制**（阈值、波动、通知、处置流转）

### 4.7 邮箱验证系统（Email Verification）
**验证层级**
- Syntax：格式与规则校验（RFC、常见别名、一次性邮箱检测）
- Domain：MX/NS/DNS健康校验（临时域、弃用域识别）
- SMTP：可达性探测（VRFY/RCPT，超时/灰度策略，安全退避）
- 风险评分：基于历史结果、域信誉、一次性邮箱库、投诉/退信相关性

**任务管理**
- 支持上传CSV/URL、API创建任务；可配置验证深度与并发/速率；失败重试与截止时长
- 任务状态：queued/running/paused/completed/failed；提供下载与筛选视图
- 结果分类：valid/invalid/risky/unknown；提供去重与抑制建议（可一键同步到抑制列表）

**可视化与操作**
- 验证任务列表、任务详情（分布饼图/趋势）、失败域名Top、原因码分布
- 一键导出CSV；支持只导出invalid/risky

## 5. 数据设计

### 5.1 核心实体
**SendingChannel（发件渠道）**
- id、tenant_id、name、code、type、description、status
- default_domain_id、default_ip_pool_id、quota_daily、quota_monthly
- settings_json、created_by、created_at、updated_at

**ChannelPermission（渠道权限）**
- id、channel_id、user_id、role、permissions_json、created_at

**Domain（域名）**
- id、tenant_id、channel_id、domain、subdomain、status
- spf_status、dkim_status、dmarc_status、bimi_status
- tracking_domain、ip_pool_id、reputation_score
- warmup_status、warmup_progress、created_at、updated_at

**IPPool（IP池）**
- id、tenant_id、channel_id、name、ip_addresses、status
- reputation_score、warmup_status、usage_stats
- created_at、updated_at

**ThrottlePolicy（限速策略）**
- id、tenant_id、channel_id、scope（tenant/channel/domain/isp）、target
- rate_per_minute、burst_limit、priority
- is_active、created_by、updated_at

**DeliveryLog（投递日志）**
- id、tenant_id、channel_id、domain_id、ip_address、recipient_domain
- message_id、status、bounce_type、bounce_reason
- complaint_type、occurred_at

**VerificationJob（验证任务）**
- id、tenant_id、channel_id、name、source_type（csv/url/api）、config（depth、timeout、concurrency、rpm）
- status、progress、total、valid、invalid、risky、unknown、created_by、created_at、finished_at

**VerificationResult（验证结果）**
- id、job_id、email、domain、result（valid/invalid/risky/unknown）
- reasons（数组，如 syntax_error/mx_missing/smtp_timeout/role_account/temporary_domain）
- suggestions（suppress/retry/lower_priority）

### 5.2 数据流设计
- 渠道配置流程：创建渠道 → 分配资源 → 配置策略 → 设置权限 → 启用渠道
- 域名配置流程：添加域名 → 生成DNS记录 → 验证配置 → 启动预热 → 监控状态
- 投递执行流程：检查限速 → 选择IP → 发送邮件 → 记录结果 → 更新指标
- 反馈处理流程：接收反馈 → 解析内容 → 更新抑制 → 调整策略 → 通知告警
- 验证流程：导入地址 → 分片并发验证（Syntax→Domain→SMTP）→ 聚合统计 → 存储结果 → 导出/同步抑制

## 6. 接口设计

### 6.1 API列表
**发件渠道管理**
- POST `/api/channels/create` - 创建渠道
- POST `/api/channels/update` - 更新渠道
- GET `/api/channels/get` - 获取渠道详情
- POST `/api/channels/list` - 获取渠道列表
- POST `/api/channels/delete` - 删除渠道
- POST `/api/channels/permissions/set` - 设置渠道权限
- GET `/api/channels/permissions/list` - 获取渠道权限列表

**域名管理**
- POST `/api/domains/create` - 添加域名
- POST `/api/domains/verify` - 验证域名配置
- GET `/api/domains/get` - 获取域名详情
- POST `/api/domains/list` - 获取域名列表
- POST `/api/domains/delete` - 删除域名

**预热管理**
- POST `/api/domains/warmup/start` - 启动预热
- POST `/api/domains/warmup/pause` - 暂停预热
- POST `/api/domains/warmup/resume` - 恢复预热
- GET `/api/domains/warmup/status` - 获取预热状态

**限速管理**
- POST `/api/throttle/create` - 创建限速策略
- POST `/api/throttle/update` - 更新限速策略
- GET `/api/throttle/list` - 获取限速策略列表
- POST `/api/throttle/delete` - 删除限速策略

**邮箱验证**
- POST `/api/verification/jobs/create` - 创建验证任务（上传CSV或URL）
- GET `/api/verification/jobs/list` - 查询验证任务列表
- GET `/api/verification/jobs/status` - 查询单任务状态与统计
- GET `/api/verification/jobs/results` - 分页获取任务结果
- GET `/api/verification/jobs/download` - 下载结果CSV
- POST `/api/verification/jobs/pause` - 暂停任务
- POST `/api/verification/jobs/resume` - 恢复任务

### 6.2 统一响应格式
```json
{
  "code": 0,
  "message": "ok",
  "data": {},
  "errors": null,
  "meta": {"request_id": "req_456", "timestamp": "2025-08-11T10:30:00Z"}
}
```

### 6.3 错误码定义
| Code | Message | 说明 |
|------|---------|------|
| 400901 | invalid_domain_format | 域名格式错误 |
| 400902 | dns_verification_failed | DNS验证失败 |
| 400903 | warmup_already_started | 预热已启动 |
| 400904 | invalid_channel_type | 渠道类型错误 |
| 400905 | channel_code_exists | 渠道代码已存在 |
| 403901 | domain_access_forbidden | 无域名访问权限 |
| 403902 | channel_access_forbidden | 无渠道访问权限 |
| 409901 | domain_already_exists | 域名已存在 |
| 429901 | rate_limit_exceeded | 超出速率限制 |
| 400951 | invalid_email_format | 邮箱格式错误 |
| 400952 | mx_not_found | MX记录不存在 |
| 408953 | smtp_timeout | SMTP验证超时 |
| 409954 | verification_job_conflict | 任务冲突（同名或重复源） |

## 7. 非功能性需求

### 7.1 性能要求
- 限速判定：P95<5ms，支持10万+QPS限速查询
- DNS验证：自动验证周期≤5分钟，手动验证≤30秒
- 监控更新：指标更新延迟≤1分钟
- 邮箱验证：吞吐≥2k地址/秒（集群），SMTP探测平均时延≤1.5s/地址（并发受控）
- 渠道切换：渠道选择判定≤10ms

### 7.2 安全与合规
- 密钥安全：DKIM私钥加密存储，定期轮换
- 访问控制：基于角色的域名/验证任务/渠道操作权限控制
- 审计日志：记录所有配置与批量验证操作
- 渠道隔离：渠道间数据完全隔离，无交叉访问

### 7.3 可用性要求
- 系统可用性：99.99%可用性目标
- 故障恢复：限速服务故障时自动降级；验证集群支持任务断点续跑；渠道故障不影响其他渠道
- 数据备份：配置与验证结果实时备份，支持快速恢复

## 8. 业务流程图
```mermaid
flowchart TD
    A[创建发件渠道] --> B[分配资源]
    B --> C[配置策略]
    C --> D[设置权限]
    D --> E[启用渠道]
    E --> F[添加域名]
    F --> G[生成DNS记录]
    G --> H[用户配置DNS]
    H --> I[自动验证]
    I --> J{验证通过?}
    J -->|是| K[启动预热]
    J -->|否| L[提供修复建议]
    K --> M[监控预热进度]
    M --> N[正常投递]
    N --> O[处理反馈]
    O --> P[更新信誉]
    P --> Q[调整策略]
    L --> H
```

```mermaid
flowchart TD
    V1[创建验证任务] --> V2[分片排队]
    V2 --> V3[Syntax/MX校验]
    V3 --> V4[SMTP并发探测]
    V4 --> V5[聚合统计]
    V5 --> V6[导出/同步抑制]
```

## 9. 风险评估与预案
- DNS传播延迟：多DNS服务器查询，支持手动验证
- ISP政策变化：建立ISP政策监控机制，及时更新策略
- 预热期间投递率低：提供预期说明与阈值保护
- SMTP探测受限：尊重对方策略，限速与退避，标记unknown并支持重试
- 渠道资源不足：提供资源扩容建议和临时共享策略
- 渠道权限冲突：提供权限继承和覆盖规则

## 10. 发布计划
- P1（MVP）：发件渠道基础功能、域名接入、DNS验证、基础预热、邮箱验证（Syntax/MX）
- P2（增强）：SMTP深度验证、智能限速、FBL处理、基础监控、渠道权限管理
- P3（完善）：高级监控、自动化优化、告警系统、验证结果与发送自动联动、渠道间智能路由

## 附：端到端流程与解锁条件（账户→渠道→域名→验证→实验→预热→发送→治理）

### A. 端到端流程（推荐）
- 阶段 0｜账户接入与合规：组织信息/KYC/用途申报，开启必要权限；Gate：账户资料完整
- 阶段 1｜发件渠道配置：创建渠道、分配资源、配置策略、设置权限；Gate：渠道配置完成
- 阶段 2｜自定义域名与跟踪域：配置并通过 SPF/DKIM/DMARC（BIMI 可选），设置 tracking domain；Gate：SPF+DKIM 通过
- 阶段 3｜数据与同意：导入/同步联系人与同意状态，启用抑制名单与事件追踪；Gate：同意与事件可用
- 阶段 4｜名单卫生（邮箱验证）：Syntax/MX/SMTP 多级验证，Invalid 同步到抑制；Gate：预计硬退≤0.6%
- 阶段 5｜小样本实验（Seed）：构建高活跃多 ISP 种子人群，进行模板渲染/内容合规/主题预头 A/B 微实验；Gate：投诉≈0、硬退<0.4%
- 阶段 6｜预热（域名/IP）：选择曲线（保守/标准/激进），按 ISP 配额发量，监控投诉/退信/陷阱，阈值命中自适应降速或暂停；Gate：连续多日稳定
- 阶段 7｜规模化发送与常态 A/B：Preflight 体检通过后提量；持续 A/B（主题/版式/时段），动态 ISP 配额
- 阶段 8｜发送中监控与自动治理：实时监控指标，命中阈值自动降速/暂停并联动抑制与内容回退
- 阶段 9｜发送后复盘与持续优化：汇总指标，risky/unknown 再验证或再激活，沉默人群治理，预热完成后定期健康检查

### B. 发送前 Preflight（体检）清单
- 渠道健康：渠道配置完整，资源分配合理，权限设置正确
- 域名健康：SPF/DKIM 通过，DMARC 对齐（建议 p=none 起步），跟踪域连通
- 名单健康：Invalid 全抑制，预计硬退≤0.6%，采集与来源合规
- 节流策略：租户/渠道/域/ISP/IP 限速到位，黑名单/例外规则校验
- 内容与追踪：渲染无错误，Unsubscribe/偏好中心入口，UTM/像素开关依配置
- Seed 检查：小样本实验反馈正常（投诉≈0、硬退<0.4%）

### C. 解锁与退出阈值（建议，详见《自动验证与预热_技术细节》）
- 渠道启用：渠道配置完成 + 资源分配到位 + 权限设置正确
- 预热启动：域名校验通过 + 名单卫生完成 + Seed 正常
- 预热自适应：投诉/硬退/软退/陷阱命中阈值 → 自动降速或暂停
- 预热退出（进入常态发送）：
  - 连续≥3日稳定：投诉<0.05%，硬退<0.4%，软退<2%
  - 送达率与互动上升，ISP 指标无异常

### D. 流程图
```mermaid
flowchart LR
  A[账户接入] --> B[发件渠道配置]
  B --> C[自定义域名校验 SPF/DKIM/DMARC]
  C --> D[数据接入 与 同意/抑制就绪]
  D --> E[邮箱验证 清洗名单]
  E --> F[小样本实验 Seed A/B/渲染检查]
  F --> G[预热 曲线+ISP配额+阈值自适应]
  G --> H[Preflight 发送前体检]
  H --> I[规模化发送 + 常态A/B]
  I --> J[实时监控与自动治理]
  J --> K[复盘与持续优化/再验证/人群治理]
```

## 附录
- 术语：SPF/DKIM/DMARC/FBL/IP预热/SMTP探测/发件渠道
- 参考：RFC 7208/6376/7489，主要ISP投递最佳实践，邮件信誉管理标准，反垃圾法规
- 变更记录：v2.2 增加发件渠道管理；v2.1 增加邮箱验证系统；v2.0 完整重写；v1.0 初版
