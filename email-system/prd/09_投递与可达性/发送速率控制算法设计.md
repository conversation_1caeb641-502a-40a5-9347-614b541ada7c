# 发送速率控制算法设计（非预热阶段）

本文档定义在“非预热阶段”的大规模邮件发送时的速率控制策略与实现，适用于 `投递与可达性` 模块的稳定期发送。

> 结论：不建议无控制发送。应采用分层限流 + 自适应闭环，避免 ISP 临时限流/灰名单和质量恶化。

---

## 1. 场景与目标
- 场景：营销活动/系统批量通知/周报等高峰发送，已完成预热且信誉稳定。
- 目标：
  - 在保持高投递成功率/收件箱率的前提下，最大吞吐与稳定性。
  - 快速感知异常（4xx/deferral、投诉、硬退回），并自动降档/退避。
  - 支持 ISP 差异化策略与队列公平调度。

## 2. 分层限流架构
- 全局层（Account/Business）：总 RPM/QPS、全局并发连接上限、任务并发。
- ISP/域名层：每 ISP 独立的速率/并发/重试策略（Gmail/Outlook/Yahoo/QQ/企业域等）。
- IP 池/连接层：每 IP 并发连接、每连接批量大小、连接存活与节奏。
- 队列调度层：按 ISP 分队列 + 优先级（事务类 > 营销类），避免头阻塞与“坏邻居”影响。

## 3. 核心算法组合
### 3.1 基线限流（必须）
- 令牌桶（Token Bucket）
  - 适用：允许短暂突发 + 平滑平均速率。
  - 参数：`rate`（令牌生成速率，msg/s）、`burst`（桶容量，支持瞬时峰值）。
- 漏桶（Leaky Bucket）（可选）
  - 适用：需要更强的稳定输出速率（如单 ISP 出口）。

### 3.2 自适应控制（强烈建议）
- AIMD（加性增/乘性减，类 TCP）：
  - 健康时缓慢上调（每轮 +10%~20%）；异常时成倍下调（×0.5 或 ×0.7）。
- 指数退避（Exponential Backoff）+ 抖动（Jitter）：
  - 对 4xx/deferral 即时退避，避免同步重试雪崩。
- 健康窗口（5~15 分钟滚动）：
  - 指标：投递成功率、deferral/4xx 占比、硬退回、投诉、延迟分布、打开/点击（可选）。

### 3.3 阈值与动作（建议 ISP 维度配置）
- 投诉率 > 0.08%：该 ISP 速率立即减半，并冷却 5~15 分钟。
- 硬退回 > 1.5%（警戒 2%）：降档 30% + 强制列表清洗（最近样本）。
- deferral/4xx > 3%：降档 30%，延长重试间隔，降低并发。
- 健康窗口满足 SLO 且稳定 ≥ 2 个评估周期，才允许上调（单次 ≤ 10~20%）。

## 4. SMTP 连接/并发策略
- 每 ISP 并发连接上限（如 10~50），每连接批量（20~100），保持管道化但避免过载。
- 严格区分 4xx（可重试）与 5xx（不可重试）。
- 监控 221/421/451/550 等返回码分布，动态调整连接与批量大小。

## 5. 默认起点与自学习
- 初始 `safe_cap_isp`：取历史稳定高水位的 70~80% 作为初始上限。
- 运行期：AIMD 慢涨，质量触发则快速降档；周期性固化“安全水位”。

## 6. 伪代码
```pseudo
# 每 ISP 周期调度（1 分钟）
for each isp every 1 min:
  health = read_metrics(isp, window=5-15m)
  if health.complaint > 0.0008 or health.hard_bounce > 0.015 or health.deferral > 0.03:
    rate_isp *= 0.5                      # 乘性减
    cooldown(isp, 5-15m)                 # 暂缓新流入，保留在途与计划重试
  else:
    rate_isp = min(rate_isp * 1.1, safe_cap_isp)  # 加性增（或乘性小增）

# 令牌桶（每 ISP，一个简化实现）
tokens[isp] += rate_isp * dt
while tokens[isp] >= cost and queue[isp].not_empty() and not cooling(isp):
  batch = batch_from(queue[isp], size=ideal_batch(isp))
  send(batch, isp)
  tokens[isp] -= cost_of(batch)

# 4xx/deferral 重试（指数退避 + 抖动）
on_4xx_or_deferral(msg, isp, code):
  backoff = base * 2^retries(msg) * (1 + random_jitter())
  schedule_retry(msg, now + clamp(backoff, min, max))

# 5xx 不重试，写入失败与健康指标
```

## 7. 可观测性与 SLO
- 实时汇聚：投递事件流（delivered/deferral/bounce/complaint），分钟级聚合。
- 仪表盘：ISP ×（成功/退回/投诉/deferral/延迟），配阈值线与告警。
- SLO 示例（稳定期）：
  - 投递成功率 ≥ 98.0%
  - 投诉率 ≤ 0.05%（警戒 0.08%）
  - 硬退回 ≤ 1.5%（警戒 2%）

## 8. 配置清单（示例）
```yaml
rate_limit:
  global:
    qps: 2000
    connections: 200
  isp:
    gmail:
      rate: 400 qps
      burst: 800
      max_connections: 40
      thresholds:
        complaint: 0.0008
        hard_bounce: 0.015
        deferral: 0.03
    outlook:
      rate: 300 qps
      burst: 600
      max_connections: 30
    yahoo:
      rate: 150 qps
      burst: 300
```

## 9. 风险与降级
- 批量同步重试雪崩 → 指数退避 + 抖动；重试队列与主队列隔离。
- 单 ISP 大面积 deferral → 立即降档并错峰；优先发送其他 ISP。
- 质量波动 → 自动切换更保守的模板/人群策略。

## 10. 与预热策略的衔接
- 预热完成后，将预热“安全水位”固化为各 ISP `safe_cap_isp` 初值。
- 参考：《预热算法设计.md》中的阈值与 ISP 画像库，保持一致的异常动作定义。

## 11. TODO（落地项）
- 速率控制器：
  - 令牌桶中枢（多租户/多 ISP 多桶）与持久化（崩溃恢复）。
  - AIMD 与阈值状态机（可热更新）。
- 监控与告警：指标埋点、分钟聚合、告警策略（阈值/环比/同比）。
- 配置中心：ISP 画像、速率/并发上限、退避参数与模板策略。
- 重试与死信：指数退避、最大重试、死信路由与报表。
```
接口与实现细节将在《自动验证与预热_技术细节.md》与后端任务编排文档中展开。
```

---

## 12. 行业节奏建议与触发规则（补充）

- 动态 QPS/RPM（经验基线，按“冷/热”状态与 ISP 策略自适应）：
  - Gmail：冷域名 1–2 封/秒；热域名最高约 30 封/秒。
  - QQ/163：冷域名 10–20 封/分钟；热域名 200–300 封/分钟。

- 批次级降速/暂停触发（在 3.3 阈值之上，适用于预热或敏感批次的更保守控制）：
  - 若 5xx 退信率 >5%：立即将该 ISP/批次速率减半；
  - 若 FBL 投诉率 >0.1%：暂停该批次（保留在途与重试队列）。

- 说明：上述阈值来源于主流 ESP 运营经验，与本文件 3.x 自适应控制组合使用时，应取更严格者优先执行；热域名定义参考《预热算法设计.md》预热退出标准。

