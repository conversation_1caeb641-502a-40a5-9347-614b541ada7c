# 技术细节｜自动验证（Email Verification）与预热（Warmup)

- 版本：v1.0
- 更新时间：2025-08-11
- 状态：Draft
- 关联文档：`prd/09_投递与可达性/PRD.md`，`prd/09_投递与可达性/技术方案设计.md`

## 1. 概述与目标
- 目标：在不打扰对端服务器的前提下，提供高吞吐、可观测、可控的邮箱地址自动验证与域名/IP 预热能力，降低退信与投诉，提升可达性。
- 成功标准：验证吞吐≥2k地址/秒（集群）；平均 SMTP 探测时延≤1.5s/地址；预热成功率≥90%；异常场景自动降级，数据与操作全量审计。

## 2. 体系架构
- 组件：任务编排器（Scheduler）、工作队列（Queue）、验证器（Verifier Workers）、DNS服务（Resolver）、速率限制器（RateLimiter）、预热管理器（Warmup Manager）、观测与告警（Metrics/Alerts）、存储（DB/对象存储）。

```mermaid
flowchart LR
  U[触发: 导入/API/定时] --> S[任务编排器]
  S --> Q[分片队列]
  Q --> V1[Verifier Workers]
  V1 -->|Syntax| V2[Domain/MX]
  V2 -->|可达性| V3[SMTP 探测]
  V3 --> R[(DB: jobs/results)]
  S -.-> W[Warmup Manager]
  W --> RL[RateLimiter]
  RL --> MTA[发送执行]
  subgraph Observability
    R --> M[Metrics]
    R --> L[Logs]
  end
```

## 3. 工作流
### 3.1 自动验证流水线
1) 任务创建：导入完成/API/定时触发 → 生成 `VerificationJob`，拆分为分片任务入队。
2) 并发控制：按全局/租户/域/MX 四级限流；幂等（tenant+email）。
3) 验证阶段：Syntax → Domain/MX → SMTP（仅到 RCPT TO）。
4) 分类与评分：valid/invalid/risky/unknown + 风险评分。
5) 结果落库：`verification_results`、任务统计聚合；导出与抑制联动。

```mermaid
sequenceDiagram
  participant UI
  participant S as Scheduler
  participant Q as Queue
  participant W as Worker
  participant DNS
  participant SMTP
  UI->>S: 创建验证任务
  S->>Q: 分片入队
  loop 并发执行
    Q-->>W: 领取分片
    W->>DNS: MX 查询
    alt MX 存在
      W->>SMTP: EHLO/MAIL FROM/RCPT TO
      SMTP-->>W: 2xx/4xx/5xx/超时
    else MX 缺失
      W-->>W: 标记 mx_missing
    end
    W-->>S: 上报进度与统计
  end
  S-->>UI: 状态/下载链接
```

### 3.2 预热流水线
1) 前置条件：域名通过 SPF/DKIM/DMARC（至少 SPF+DKIM）。
2) 策略选择：保守/标准/激进曲线 + ISP 差异化；每日目标量与阈值配置。
3) 执行与监控：按曲线发量；监控退信/投诉/退订/陷阱；
4) 自适应：阈值命中自动降速/暂停；人工可恢复或切换曲线。

```mermaid
stateDiagram-v2
  [*] --> Idle
  Idle --> Running: start
  Running --> Paused: threshold_hit/手动暂停
  Paused --> Running: resume
  Running --> Completed: 达到预热目标
  Running --> Failed: 连续异常/管控触发
```

## 4. 状态机
- 验证任务 `VerificationJob.status`：`queued|running|paused|completed|failed`
- 邮箱结果 `VerificationResult.result`：`valid|invalid|risky|unknown`
- 预热状态 `sending_domains.warmup_status`：`idle|running|paused|completed|failed`

## 5. 核心算法与策略
### 5.1 Syntax 校验
- RFC 5321/5322 解析；IDN/Punycode 归一化；本地段规则；加号别名规整；
- 角色邮箱识别（admin/support/noreply 等）；一次性邮箱/临时域库匹配；
- 产出原因码：`syntax_error|disposable|role_account`。

### 5.2 Domain/MX 校验
- DNS A/AAAA/MX 查询；无 MX 则按 RFC 5321 退回到 A 记录主机；
- MX 优先级排序与可达性快速探测；响应缓存与 TTL；
- 产出原因码：`mx_missing|domain_nxdomain|mx_temp_failure`。

### 5.3 SMTP 探测（不发送 DATA）
- 流程：`EHLO -> (STARTTLS 可选) -> MAIL FROM -> RCPT TO`；
- 仅到 RCPT，读取 250/550/450/421 等；4xx 视为临时失败；
- Catch-all 识别：对随机地址 RCPT 探测，多次 250 判定 `catch_all`；
- 产出原因码：`smtp_accept|smtp_user_not_found|smtp_timeout|catch_all`。

### 5.4 分类与评分
- 规则映射：
  - valid：Syntax OK + MX OK + RCPT 2xx + 非 catch_all + 非高风险；
  - invalid：5xx 明确拒绝/域不存在；
  - risky：catch_all/角色邮箱/一次性域/异常比例高；
  - unknown：超时/策略拒绝/对端防探测；
- 风险评分：域信誉、历史退信/投诉、一次性域库、来源标签（手动/导入）。

### 5.5 预热曲线与自适应
- 曲线模板：保守/标准/激进，按日目标量；ISP 配额系数自动分配；
- 自适应降速：当 `complaint_rate>阈值` 或 `hard_bounce_rate>阈值` 或 `spam_trap_detected` → 降速 x% 或暂停；
- 恢复策略：指标回落至安全区间后，按阶梯式恢复；
- 人工干预：强制暂停/恢复、曲线切换，记录审计日志。

## 6. 并发与限流
- 维度：全局、租户、域、MX 主机；
- 示例默认：
  - 全局：≤2000 并发连接；
  - 租户：≤200 并发；
  - 域：≤20 并发；
  - MX：≤5 并发，RPM≤60；
- 退避：指数退避+Jitter，最大重试 N=2（仅 4xx/网络错误）。

## 7. 反探测与“卫生”策略
- 仅 RCPT 探测，不发送 DATA；
- 合法可解析的 `MAIL FROM`，随机化 EHLO 标识；
- 控制每 MX 的 QPS/RPM；域级冷却时间；
- 频繁 421/451 → 熔断该 MX 一段时间，结果记为 `unknown`；
- 使用真实 rDNS/PTR，避免被误判为垃圾流量。

## 8. 数据结构
- `verification_jobs`
  - `id, tenant_id, name, source_type(csv/url/api), source_path, config_json(depth, timeout, concurrency, rpm, retry), status, progress, total, valid, invalid, risky, unknown, created_by, created_at, finished_at`
- `verification_results`
  - `id, job_id, email, domain, result, reasons_json, suggestions_json`
- `sending_domains`
  - `id, tenant_id, domain, spf_status, dkim_status, dmarc_status, warmup_status, warmup_progress, reputation_score, created_at, updated_at`
- 指标表（可选聚合）：`deliverability_metrics_hourly`

## 9. API（概要）
- 验证：`POST /api/verification/jobs/create|pause|resume`，`GET /api/verification/jobs/list|status|results|download`
- 预热：`POST /api/domains/warmup/start|pause|resume`，`GET /api/domains/warmup/status`

## 10. 监控与告警
- 验证链路指标：吞吐、成功率、`unknown` 占比、超时率、域/MX TopN；
- 预热指标：每日计划量/实际量、投诉/退信率、暂停与恢复事件；
- 告警：`unknown`、`timeout`、4xx 比例异常；预热阈值命中；熔断次数异常。

## 11. 容错与降级
- 无 25 端口：自动降级为 Syntax+MX；
- `unknown` 比例过高：自动下调并发与 RPM；
- DNS 异常：切换备用解析器；
- 队列堆积：限流前移，任务分片动态调度；
- 断点恢复：任务状态持久化，Worker 再平衡。

## 12. 安全与合规
- 不发送邮件内容，仅到 RCPT；
- 数据最小化，敏感字段加密/Hash；
- RBAC 权限与全链路审计；
- 遵循各 ISP 与法律法规（含隐私与反垃圾）。

## 13. 部署与容量规划
- Worker 横向扩展；按域/MX 维度做亲和性或粘性分片以减少抖动；
- 预估：2k 并发验证需 ~N 核 CPU + M GiB 内存（与网络带宽相关）；
- 出口链路与 IP 信誉隔离（多出口/专线）。

## 14. 变更记录
- v1.0（2025-08-11）：初版，覆盖实现原理、策略、数据与接口、观测与SRE要点。

## 15. 默认配置与建议

### 15.1 预热曲线模板（示例）
- 单位：每日计划量（相对首日基数 = 100）。可按实际量级缩放。

| 天数 | 保守 | 标准 | 激进 |
|---|---:|---:|---:|
| D1 | 100 | 100 | 100 |
| D2 | 120 | 150 | 200 |
| D3 | 150 | 220 | 350 |
| D4 | 180 | 300 | 500 |
| D5 | 220 | 400 | 700 |
| D6 | 260 | 520 | 950 |
| D7 | 300 | 650 | 1200 |
| D8-D14 | 每日+8-12% | 每日+15-20% | 每日+20-30% |

- 降速/暂停条件（默认）：
  - 投诉率（24h）> 0.08% → 立即暂停
  - 硬退率（24h）> 0.6% → 降速 50%
  - 软退率（连续两天）> 4% → 降速 30%
  - 垃圾陷阱命中（任一）→ 立即暂停并通知

- 恢复条件（默认）：
  - 投诉率回落 < 0.05%，硬退率 < 0.4%，软退率 < 2%，连续两日稳定 → 恢复至降速前的 70%，随后每日+10% 逐步恢复。

### 15.2 ISP 分配系数（示例）
- 依据历史表现与ISP策略差异动态调整，初始建议：

| ISP | 系数（标准曲线） |
|---|---:|
| Gmail | 0.40 |
| Outlook/Hotmail | 0.30 |
| Yahoo/AOL | 0.15 |
| 其他（本地/企业域） | 0.15 |

- 当某 ISP 指标偏离：
  - 投诉/硬退上升 → 下调该 ISP 系数 20-50%
  - 表现优异 → 上调 10-20%

### 15.3 验证并发与速率建议
- 全局并发/速率上限需结合出口带宽与对端容忍度，初始建议：

| 维度 | 并发上限 | RPM 上限 | 备注 |
|---|---:|---:|---|
| 全局 | 2000 | 120000 | 集群层面，可线性扩展 |
| 租户 | 200 | 12000 | 与套餐/配额关联 |
| 域 | 20 | 1200 | 避免对单域过度探测 |
| MX主机 | 5 | 60 | 最关键的防探测控制 |

- 超时（建议）：连接 5s，读 10s；
- 重试：对 4xx/网络错误最多 2 次，指数退避（1s, 4s, 9s + 抖动）。

### 15.4 验证结果联动策略（建议）
- invalid：默认同步到抑制名单（全租户级）；
- risky：默认不抑制，但在活动发送时提供“排除 risky”选项；
- unknown：进入 24-48 小时延迟队列自动二次验证；
- valid：可选做抽样追踪以监控名单质量漂移。

### 15.5 审计与访问控制
- 关键操作（启动/暂停预热、曲线切换、阈值变更、任务创建/暂停/恢复/删除）必须写审计；
- RBAC：预热仅域名管理员与投递工程师可操作；验证任务运营与管理员可操作；导出结果需要数据权限。

## 16. 发送前 Preflight 检查清单（推荐）
- 域名健康：SPF/DKIM 必须通过；DMARC 对齐（建议 p=none 起步）；跟踪域可用
- 名单健康：invalid 全抑制；预计硬退≤0.6%；来源与同意合规；高风险来源单独标记
- 节流策略：租户/域/ISP/IP 限速策略生效；例外与白名单审核；黑名单同步
- 内容与追踪：模板渲染通过；Unsubscribe/偏好中心合规；UTM/像素配置按需
- Seed 验证：小样本实验投诉≈0、硬退<0.4%，指标正常

## 17. 解锁阈值与退出条件（建议）

### 17.1 启动与自适应
- 预热启动：域名校验通过 + 名单卫生完成 + Seed 小样本正常
- 自适应降速/暂停条件（默认）：
  - 投诉率（24h）> 0.08% → 立即暂停
  - 硬退率（24h）> 0.6% → 降速 50%
  - 软退率（连续两天）> 4% → 降速 30%
  - 垃圾陷阱命中（任一）→ 立即暂停并通知

### 17.2 预热退出（进入常态发送）
- 连续≥3天满足：
  - 投诉率 < 0.05%
  - 硬退率 < 0.4%
  - 软退率 < 2%
  - 主要 ISP 指标无异常，送达率与互动稳定回升

## 18. 供应商适配｜AWS SES 预热与配额（精炼，不丢细节）

- 一句话结论：用 AWS SES 是否需要预热，取决于是否使用共享 IP 还是专用 IP，以及你的每日/每秒配额是否已足够高。

### 18.1 共享 IP（默认）
- 新账户默认在共享池：理论上无需你做 IP 预热。
- 两个现实前置：
  1) SES 沙盒：初始只能向已验证地址发信。需通过 Service Quotas 申请移出沙盒并提升 Daily Quota。
  2) 速率（TPS）：默认约 1 封/秒，需单独申请提升（按区域与账户历史批准）。
- 含义：配额足够时，即使首日发送到万级量，因流量分散在 AWS 共享池，也不会因“冷 IP”被强限。但仍需监控退信/投诉。

### 18.2 专用 IP（Dedicated IP）
- 购买 Dedicated IP（标准或托管）后：AWS 会执行约 45 天的自动预热（每日递增直至达到账户上限）。
- 注意：预热期间不宜直接进行大批量促销邮件，否则仍可能被 ISP 限流/入垃圾箱。
- 手动爬坡（可在控制台关闭自动预热后自行控制；示例）：
  - Day1 50 → Day2 100 → … → 两周左右提升到万级/日；
  - 过程中持续监控：投诉率、退信率均保持 < 0.1%（或按更严格内控阈值）。

### 18.3 域名层面的“内容预热”（不随 IP 改变而免除）
- 无论 IP 是否在共享池或已“热”，新域名突发高并发营销流量仍可能被 Gmail、QQ 等限流。
- 推荐策略：
  - 先用事务类邮件（注册/订单/验证码）发送 1–2 周，积累正向互动；
  - 再逐步引入营销流量，事务:营销 由 7:3 → 5:5 → 3:7 过渡。

### 18.4 快速决策表

| 场景 | 是否需预热 | 关键动作 |
|---|---|---|
| 新账号 + 共享 IP + 配额低 | 需“配额热身”而非 IP 热身 | 申请提升 Daily Quota / TPS（移出沙盒） |
| 新账号 + 购买专用 IP | 必须 IP 预热 | 使用 SES 自动 45 天或改为手动阶梯放量 |
| 老账号 + 高配额 + 共享 IP | 基本不需 | 可直接发，持续监控退信/投诉 |
| 任何场景更换新域名 | 域名需内容预热 | 先事务后营销，循序渐进 |

> 总结：用 SES 不预热也能发，但前提是未使用专用 IP 且账户配额已放开；购买专用 IP 或启用新域名时，仍需按 AWS 规则或行业通用做法进行预热，否则到达率风险显著。

