# 原型设计｜自动化旅程

## 页面地图
| 页面 | 目标 | 操作 |
|---|---|---|
| 旅程列表 | 管理旅程 | 新建/发布/暂停/版本 |
| 画布编辑器 | 编排与调试 | 拖拽节点/连线/频控并发/仿真/健康检查 |
| 实例监控 | 追踪与回放 | 实例列表/状态/节点轨迹/错误重放 |

## 关键原型
- 画布：左节点库（触发/发送/等待/条件/AB/Webhook/设置/退出/Goal），中部连线，右侧节点属性。
- 仿真：输入虚拟事件或联系人，逐步执行并观察输出。
 - 仿真：输入虚拟事件或联系人，逐步执行并观察输出。

## 自动化设计要点（UI）
- 条件构建器：支持属性/行为/标签/列表，并在事件类条件显示时间窗口输入。
- 动作节点：发送邮件（模板选择）、设置标签/字段、Webhook、退出、Goal。
- A/B 节点：设置分流比例与胜出指标，支持快速替换为胜出版本。
- 受众限定：在旅程设置中可选择“特定标签/列表”过滤进入人群。
- 模板库：画布顶部提供“模板库”入口，发送节点右侧属性面板可更换模板。

## 流程
```mermaid
flowchart LR
  C[创建旅程] --> D[画布编排]
  D --> V[校验]
  V -->|通过| P[发布]
  P --> M[实例监控]
```

## 组件/状态
- 节点卡、连线、错误提示、健康概览、实例时间线。

## 校验
- 进入/退出策略、频控并发合法性、节点参数必填。

## 空态/错误
- 画布空态给出模版旅程；实例失败支持退避重试与回放。