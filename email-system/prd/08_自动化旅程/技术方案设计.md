# 技术方案设计｜自动化旅程

## HLD
- 组件：触发接入、编排引擎（有向图）、节点执行器、实例存储、调试与回放
- 数据：`journeys`、`journey_versions`、`journey_instances`、`node_logs`

## 时序
```mermaid
sequenceDiagram
participant EVT as 事件源
participant ORC as Orchestrator
participant EXE as NodeExecutor
participant DB
EVT->>ORC: 触发
ORC->>DB: 创建/读取实例
ORC->>EXE: 执行节点
EXE->>DB: 写节点日志
```

## LLD
- 幂等：实例+节点去重；
- 频控：租户/旅程/联系人多级；
- 回放：按实例时间线重放至断点。

---

## 1. 目标与范围
- 目标：触发展开 P95<1s；失败自动重试；实例可回放。
- 范围：触发器、节点类型、频控与并发、版本与回放、可视调试与指标。

## 2. 数据与接口
- 表：`journeys`、`journey_versions`、`journey_instances`、`journey_instance_nodes`、`journey_ingest_events`、`email_messages`；
- API：POST `/api/journeys/create|update|publish|pause|resume|stop|clone|get|instances/search|replay|ingest_event`。

## 3. 规则与算法
- 进入策略：可重复/不重复、冷却时间；
- 节点：发送/等待/条件/AB 随机/Webhook/设置标签/字段/退出/Goal；
- 频控与并发：旅程与全局阈值、互斥旅程策略。
 - 频控与并发：旅程与全局阈值、互斥旅程策略。
 - 入口受众限定：支持基于标签/列表在进入时过滤联系人（评估于实例创建前执行）。

## 4. 可靠性
- 幂等键：`(tenant_id, journey_id, contact_id)` 与 `(instance_id, node_id)`；
- 失败退避重试，最大次数后走兜底路径；
- 节点执行与发送队列分离，使用 `email_messages`。

## 5. 安全与合规
- 进入/退出与字段变更操作写审计；
- Webhook 节点签名与超时处理。

## 6. 可观测与性能
- 指标：节点吞吐、实例时长、失败率、等待时长；
- 调试：可视回放、断点与采样。

## 7. 测试与验收
- 节点组合覆盖、频控、互斥、失败重试；
- 回放正确性；
- 性能容量：N=1e6 实例基线（按业务评估）。

## 8. 发布与回滚
- 版本化；画布/DSL 变更灰y
- 节点实现回滚与兼容层。

## 9. 风险与对策
- 条件/规则错误导致抖动：发布前校验与仿真；
- 长等待节点堆积：分层队列与定期清扫。

## 10. Checklist
- 统一响应；
- 幂等与回放；
- 指标/告警齐备。
