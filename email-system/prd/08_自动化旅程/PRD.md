# PRD｜自动化旅程（Journeys）

## 1. 背景与目标
- 背景：生命周期编排提升留存与转化；
- 目标：触发展开 P95<1s；失败自动重试；实例可回放。

## 2. 用户画像与需求
- 运营/增长：拖拽画布、可视调试、频控并发；
- 开发：触发 API 与事件接入。

## 3. 功能模块
- 触发器（联系人创建/字段变更/事件/细分/日期/API）
- 节点（发送/等待/条件/AB 随机/Webhook/设置标签/字段/退出/Goal）
- 频控与并发、进入/退出策略、版本与回放
 - 频控与并发、进入/退出策略、版本与回放

## 自动化设计要点
- 条件：进入触发（联系人创建/字段变更/事件/细分/日期/API）、流程条件节点（属性、行为、标签、列表）。
- 动作：发送邮件（选择模板）、设置标签、字段更新、Webhook、Goal、退出。
- A/B 测试：AB 随机节点按比例分流，支持胜出策略（打开率/点击率/转化）。
- 受众限定：支持按特定标签/列表过滤进入旅程。

### 边界/异常
- 实例幂等；失败退避；互斥旅程策略。

## 4. 非功能
- 水平扩展节点执行；采样追踪与可视回放。

## 5. 用户故事
1) 新客欢迎 2 封；
2) 加购未下单，48h 触发挽回；
3) 达成 Goal 自动退出旅程。

## 6. 流程图
```mermaid
flowchart LR
  T[触发事件] --> E[进入判定]
  E --> N[按节点执行]
  N --> R{成功?}
  R -- 否 --> Retry[退避重试]
  R -- 是 --> Next[下个节点]
```

## 7. 接口
- `/api/journeys/create|update|publish|pause|resume|stop|clone|get|instances/search|replay|ingest_event`

## 8. 竞品与差异
- HubSpot 强大；本产品强调“可观察性与调试易用”。

## 9. 里程碑
- P1 触发/节点；P2 频控并发与回放；P3 可视调试与指标。