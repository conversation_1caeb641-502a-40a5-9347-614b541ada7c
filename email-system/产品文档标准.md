# 可用于方案设计的产品文档标准（PRD/HLD/LLD）

本文档提供一套可直接落地的产品文档标准，覆盖从需求到方案设计（高/低保真）直至交付验收的全流程，适用于中大型 B2B/B2C 企业产品与平台类系统（以电子邮件营销系统为参考场景）。团队可按此标准产出 PRD、方案设计说明（HLD/LLD）、API 契约、数据与埋点方案、验收与风控清单等，确保“能对齐、能落地、能验证”。

---

## 1. 目的与适用范围
- **目的**：统一产品文档结构与口径，提升跨职能协作效率，降低理解与实现偏差，确保可测试与可运维。
- **适用范围**：平台型系统、增长/营销系统、SaaS 多租户产品、企业内部工具与对外开放平台。
- **不含**：项目排期、迭代计划、人员分工（可在项目计划中单列）。

---

## 2. 文档分层与产物清单
- **BRD（业务需求文档）**：机会/商业目标/成功标准/边界（选填）。
- **MRD（市场需求文档）**：竞品/市场洞察/用户画像/细分（选填）。
- **PRD（产品需求文档）［必备］**：范围、用户故事、验收标准、业务流程、IA、关键规则。
- **HLD（高层方案设计）［必备］**：系统边界、架构视图、能力地图、接口与数据流、非功能性目标。
- **LLD（详细设计）［强烈建议］**：模块/状态机/时序/数据模型/异常策略/可观测性/运维方案。
- **API 契约与错误码**：仅 GET/POST、统一响应结构、错误码规范、版本策略。
- **数据与埋点方案**：实体与字段、口径、事件模型、看板指标、导出/订阅。
- **风险与合规评审**：隐私、合规、可达性/信誉（邮件）、安全、回滚与灰度。
- **验收与发布**：验收用例、回归范围、发布策略与监控（不含排期）。

---

## 3. 最小合格标准（Definition of Ready / Done）
- **DoR（准备完成）**：
  - 目标、范围、非目标、业务流程、主要页面/节点与状态、用户故事＋验收标准、关键指标口径、接口与数据流草案、主要风险与假设、权限与合规点，均已明示。
- **DoD（完成定义）**：
  - 方案评审通过；API 契约冻结；埋点/监控落地；测试用例通过；文档更新与审计留痕；回滚预案与验收看板上线。

---

## 4. PRD 模板（可复制）

### 文档头
- 标题、版本/状态（Draft/Ready/Approved）、作者/评审人、最后更新日期、关联需求/任务/原型链接

### 1. 概述
- 背景与问题陈述（含现状与痛点）
- 目标与成功指标（北极星指标 + 关键过程指标）
- 目标用户与使用场景（含边界与反例）

### 2. 范围与假设
- In Scope / Out of Scope
- 业务与技术假设、依赖项、限制与合规边界

### 3. 用户故事与验收标准
- 用户故事格式：作为【角色】，我想要【动机/目标】，以便【价值】
- 验收标准示例（Gherkin）：
  ```gherkin
  Given 联系人邮箱有效且未被抑制
  When 启动“新客欢迎”旅程并触发事件
  Then 在 5 分钟内收到欢迎邮件并记录一次发送事件
  And 邮件中包含有效退订链接
  ```
- 负例与异常路径（如退信、投诉、超限、权限不足）

### 4. 业务流程与信息架构（IA）
- 端到端流程图（创建 → 审核 → 发送/触发 → 报表）
- IA：导航结构、对象层级关系（例如 模板/活动/旅程/联系人/细分/报告/设置）

### 5. 规则与状态机
- 关键业务规则（频控、抑制、A/B 选择、事件窗口、幂等）
- 状态机示意（如 联系人状态、活动/旅程状态与转换条件）

### 6. 权限与合规
- 角色与权限矩阵（管理员/运营/设计/分析/开发/只读）
- 合规要求（退订、隐私、区域政策、存证与审计）

### 7. 非功能性需求
- 性能（吞吐/延迟）、可用性（SLO/错误预算）、容量（配额/限流）
- 稳定性与恢复（重试/退避/回滚）、安全与审计、可观测性（日志/指标/追踪）

### 8. 里程碑与验收（无排期）
- 验收用例清单、回归范围、可观测看板与告警项、上线/回滚策略（不含日程）

### 9. 附录
- 原型链接、术语表、参考资料、历史变更记录

---

## 5. HLD（高层方案设计）模板

### 1. 系统边界与上下文
- 相关系统与外部依赖（身份/计费/内容/CDN/存储/队列/第三方邮件通道）
- 信任边界、数据流向、网络与访问控制简述

### 2. 架构视图与能力地图
- 组件图（控制平面/数据平面）、部署拓扑（多租户/多地域可选）
- 核心能力：联系人、模板、活动、旅程、投递与可达性、追踪与报表、偏好中心、开放平台

### 3. 关键流程与时序
- 典型链路的时序图（如活动发送链路、旅程触发链路、退信/投诉回流）

### 4. 接口与数据流
- 内部接口与事件（主题/Schema/幂等键/重试策略）
- 外部 API（仅 GET/POST），统一响应结构与错误码映射

### 5. 数据存储与模型边界
- 热/冷数据分层、索引与查询模式、分区与归档策略
- 多租户隔离策略（逻辑/物理）

### 6. 非功能性设计
- 性能目标与压测基线、可用性与降级策略、扩缩容与灾备
- 可观测性（Tracing/Metrics/Logging）与安全（加密、密钥、审计）

### 7. 风险与对策
- 可达性波动、数据质量、隐私合规、链路瓶颈、第三方依赖

---

## 6. LLD（详细设计）模板

### 1. 模块设计
- 模块职责、输入/输出、依赖、边界与约束
- 算法与规则实现要点（如频控、A/B、队列分片、预热曲线）

### 2. 数据模型与字典
- 实体/字段/类型/约束/默认值/索引/唯一性
- 变更与迁移策略、回滚策略

### 3. 接口契约与时序
- 请求/响应/错误码/幂等键/限流/签名
- 关键时序（重试、补偿、最终一致性）

### 4. 异常处理与韧性
- 失败模式、告警与自愈、熔断与降级、重放与幂等

### 5. 安全与合规细节
- PII 分类与保护、数据保留、访问审计、区域化策略

### 6. 测试与验证
- 单元/集成/回归测试清单、数据回灌方案、金丝雀与灰度

---

## 7. API 契约标准（示例）
- 仅支持 **GET/POST**，禁止路径参数嵌套复杂层级；分页/筛选用查询与请求体。
- 统一响应格式：
  ```json
  {
    "code": 0,
    "message": "ok",
    "data": {"items": [], "total": 0},
    "errors": null,
    "meta": {"request_id": "...", "trace_id": "..."}
  }
  ```
- 错误码分层：系统级、业务级、第三方映射；需列出枚举与含义。
- 版本策略：URL 或 Header 版本号（v1），含向后兼容策略与弃用公告窗口。
- 安全：鉴权（API Key/JWT）、签名、防重放、速率限制与配额、审计。

---

## 8. 数据与埋点（事件追踪）标准
- 事件命名规范、字段字典、必填/可选、去重规则、窗口期与会话定义。
- 关键指标口径（打开率/点击率/退订率/投诉率/硬退回率/转化率等）需公式化定义。
- 数据导出与订阅、隐私合规（同意/撤回/删除请求）、数据保留周期。

---

## 9. 权限与合规标准
- 角色-资源-动作矩阵；对象级/租户级作用域；敏感操作二次确认与审批流。
- 合规清单：GDPR/CCPA、CAN-SPAM/CASL、退订可达、公司信息、审计与存证。

---

## 10. 写作规范与格式约定
- 标题与编号：1/1.1/1.1.1；表格用于矩阵与字典；术语首现加粗。
- 术语统一：在术语表集中维护；同义词与别名需交叉引用。
- 图示：流程图、组件图、时序图需配说明与图例；外链原型要可访问。
- 变更记录：版本号、变更人、变更摘要、日期；评审记录与结论。

---

## 11. 质量评审清单（Checklist）
- **目标**：是否可衡量、与业务目标对齐、成功指标可观测？
- **范围**：In/Out 是否清晰？关键假设与依赖是否列出？
- **可实现**：规则是否完整/无冲突？边界与异常是否覆盖？
- **可测试**：验收标准是否可执行？负例是否明确？
- **可运维**：指标/日志/告警是否定义？回滚与灰度是否具备？
- **合规安全**：隐私/退订/审计/权限是否到位？
- **一致性**：术语口径、错误码、API 规范是否统一？

---

## 12. MRD 可复制模板

```markdown
# MRD | 【模块名称】

- 版本：v1.0（Draft/Ready/Approved）
- 作者/评审人：
- 更新时间：
- 目标市场：【明确目标客户群体】

## 1. 市场背景与机会点
- 行业趋势与驱动因素
- 市场规模与增长预测
- 技术变革与政策影响

## 2. 竞品分析与对比
- 主要竞品列表及其特点
- 功能对比矩阵（3-5项核心能力）
- 优势与劣势分析
- 定价策略对比

## 3. 目标用户画像
- 主要用户角色（3-5个）
- 用户使用场景描述
- 用户痛点与需求分析
- 用户决策链与影响因素

## 4. 市场定位与价值主张
- 产品定位陈述
- 核心价值主张（3-5个）
- 差异化竞争优势
- 目标市场份额

## 5. 商业模式与变现
- 收入模式（付费/免费增值/企业版）
- 定价策略与包装
- 客户获取成本分析
- 生命周期价值预估

## 6. 市场进入策略
- 推广渠道与方式
- 合作伙伴策略
- 品牌宣传重点
- 上市时机规划

## 7. 风险评估与应对
- 市场竞争风险
- 技术实现风险
- 合规法律风险
- 资源投入风险

## 8. 成功指标与里程碑
- 市场渗透率目标
- 用户增长指标
- 收入目标
- 关键里程碑时间点

## 附录
- 市场调研数据
- 用户访谈摘要
- 参考资料
```

## 13. PRD 可复制模板

```markdown
# PRD | 【模块名称】

- 版本/状态：vX.Y（Draft/Ready/Approved）
- 作者/评审：
- 更新时间：
- 关联链接：需求/原型/跟踪看板

## 1. 概述
### 1.1 背景与问题
- 现状痛点描述
- 业务影响与紧迫性
- 解决问题的价值

### 1.2 目标与成功指标
- 业务目标（North Star）
- 用户体验目标
- 技术性能目标
- 可量化的成功指标（SMART原则）

### 1.3 目标用户与使用场景
- 主要用户角色
- 典型使用场景（3-5个）
- 用户旅程概述

## 2. 范围与假设
### 2.1 功能范围
- In Scope：本期包含功能
- Out of Scope：明确不包含内容
- Future Scope：后续版本考虑

### 2.2 约束与假设
- 技术约束
- 业务约束
- 资源约束
- 依赖项说明

## 3. 用户故事与验收标准
### 3.1 用户故事
- 作为【角色】，我想要【功能】，以便【价值】（至少5个核心故事）

### 3.2 验收标准（Gherkin格式）
```gherkin
Given 【前置条件】
When 【操作步骤】
Then 【预期结果】
And 【附加验证】
```

### 3.3 边界与异常场景
- 错误处理场景
- 极限值测试
- 权限不足场景

## 4. 功能详细设计
### 4.1 核心功能模块
- 功能模块划分
- 模块职责说明
- 模块间交互关系

### 4.2 页面/界面设计要求
- 关键页面布局
- 交互流程说明
- UI组件规范

### 4.3 业务规则与逻辑
- 业务规则定义
- 计算逻辑说明
- 状态机转换

## 5. 数据设计
### 5.1 数据模型
- 核心实体定义
- 实体关系图
- 字段说明与约束

### 5.2 数据流设计
- 数据流向图
- 数据处理逻辑
- 数据同步策略

## 6. 接口设计
### 6.1 API列表（仅GET/POST）
- 接口清单
- 请求/响应格式（统一JSON格式）
- 错误码定义

### 6.2 第三方集成
- 外部系统接口
- 数据交换格式
- 集成时序要求

## 7. 非功能性需求
### 7.1 性能要求
- 响应时间要求（P95/P99）
- 并发用户数支持
- 数据处理能力

### 7.2 安全与合规
- 数据安全要求
- 访问控制策略
- 合规标准遵循

### 7.3 可用性要求
- 系统可用性目标（SLA）
- 容错处理机制
- 降级策略

## 8. 测试策略
### 8.1 测试范围
- 功能测试用例
- 性能测试场景
- 安全测试要点

### 8.2 验收标准
- 功能完整性验收
- 性能基准验收
- 用户体验验收

## 9. 风险评估与预案
### 9.1 技术风险
- 主要技术风险点
- 风险影响评估
- 应对预案

### 9.2 业务风险
- 业务连续性风险
- 合规风险
- 用户接受度风险

## 10. 发布计划
### 10.1 里程碑定义
- 关键里程碑节点
- 交付物清单
- 验收标准

### 10.2 上线策略
- 灰度发布计划
- 回滚方案
- 监控告警设置

## 附录
### A. 术语表
### B. 参考资料
### C. 变更记录
- 版本记录
- 变更摘要
- 影响分析
```

---

## 13. 示例片段（便于工程对齐）

- 验收标准（负例）：
  ```gherkin
  Given 邮箱已在抑制清单
  When 触发发送
  Then 系统拒绝投递并记录原因 suppression_reason = "unsubscribe"
  And 不产生下游投递事件
  ```

- 统一响应（示例）：
  ```json
  {"code": 400100, "message": "invalid email", "data": null, "errors": [{"field": "email", "reason": "format"}], "meta": {"request_id": "..."}}
  ```

- 指标口径（示例）：
  - 点击打开率（CTOR）= unique_clicks / unique_opens

---

## 14. 版本与评审
- 版本策略：语义化版本（Major.Minor.Patch），重要变更需评审记录与公告。
- 评审流程：准备 → 评审会 → 结论（通过/修订/搁置）→ 文档更新与归档。

---

以上标准可直接复制为实际项目模板，并结合本仓库工程规范（仅 GET/POST、统一响应、可观测与合规模块）执行。