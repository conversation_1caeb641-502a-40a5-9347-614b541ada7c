# 技术设计文档标准（HLD/LLD/可运维）

说明
- 目的：为工程团队提供可直接落地的技术设计文档标准，确保“能对齐、能落地、能验证、能运维”。
- 适用：平台/多租户/SaaS/增长营销类系统。与本仓库的《产品文档标准.md》《产品设计方案.md》配套使用。
- 约束：仅 GET/POST 接口；统一响应结构以 code 判断成功；不引入消息队列时，采用数据库表（如 email_messages）承载请求与执行隔离的队列化模型。

---

## 1. 文档元信息
- 标题、版本/状态（Draft/Ready/Approved）、作者/评审、最后更新日期、关联需求/原型/任务链接
- 变更记录（版本、摘要、影响、日期、评审结论）

## 2. 目标与范围
- 背景与问题陈述（现状、痛点、业务影响）
- 目标与成功指标（可量化，含性能/可靠性/安全）
- In/Out Scope 与关键假设/依赖/边界

## 3. 系统边界与上下文（HLD）
- 上下游系统与外部依赖（身份/计费/对象存储/第三方通道/Webhook）
- 信任边界与访问控制（鉴权/签名/速率限制/配额）
- 数据流与控制流总览（Mermaid Context/Container 图）

```mermaid
flowchart LR
  Client-->API
  API-->DB
  API-->Storage[(Object Storage)]
  API-->Webhook[Webhook]
  API-->Provider[ESP/SMTP]
```

## 4. 架构视图与部署（HLD）
- 架构视图：Context/Container/Component/Deployment（C4）
- 部署与环境：环境矩阵（dev/stg/prod）、配置与密钥、弹性与扩缩容策略
- 多租户隔离：以 tenant_id 逻辑隔离，遵循现有用户系统；本系统不自建用户/角色表

## 5. 核心流程与时序（HLD）
- 关键业务链路的时序图（至少 2 个：正向成功链路 + 异常重试链路）
- 状态机：对象/任务/实例的状态与转换条件

```mermaid
sequenceDiagram
participant C as Client
participant S as Service
participant DB as MySQL
C->>S: Request (JSON)
S->>DB: Read/Write
alt Success
  S-->>C: {code:0, data}
else Fail & Retryable
  S->>S: Backoff & Retry (idempotency-key)
  S-->>C: {code:5xx/4xx, message}
end
```

## 6. 数据设计（LLD）
- 实体与关系：实体清单、关系图、读写模式与查询路径
- 字段字典：字段/类型/非空/默认值/约束/索引/唯一性/备注
- 迁移与DDL：新增/变更的 SQL（必须提供），含回滚脚本与数据修复方案
- 性能与生命周期：分区/归档/保留策略、冷热分层与热点规避

> 要求：每次表结构变更均产出对应 SQL 语句与回滚方案；大表变更需离线影子表或在线 DDL 方案说明。

## 7. API 契约（仅 GET/POST）
- 列表：路径、方法、描述、鉴权、速率限制、幂等键
- 请求/响应 JSON Schema（含示例），统一响应：

```json
{
  "code": 0,
  "message": "ok",
  "data": {},
  "errors": null,
  "meta": {"request_id": "...", "trace_id": "..."}
}
```

- 错误码：系统/业务/第三方映射分层，枚举与含义表
- 版本化：v1（URL 或 Header），弃用策略与兼容窗口
- 校验：参数校验规范（如 Go 使用 go-playground/validator 标签与错误映射）

## 8. 业务规则与算法（LLD）
- 规则清单：频控、抑制、A/B、快照、事件窗口、合并策略
- 算法要点：去重/合并、限速/退避、优胜选择、打分或推荐（如有）
- 边界与异常：阈值、极限与反例说明

## 9. 可靠性与韧性设计
- 幂等：Idempotency-Key、资源幂等键（如 tenant_id+email）
- 重试：退避策略、最大尝试次数、可回放点
- 隔离：优先级/并发控制、租户/域名/ISP 维度限流
- 队列化模型：无独立 MQ 时，采用数据库表（如 email_messages）进行请求与执行隔离，包含取数锁、状态机、失败重试与死信

## 10. 安全、隐私与合规
- 鉴权/授权：API Key/JWT、角色与对象级授权（由上游用户系统负责）
- 隐私：PII 分类、字段级加密（可选）、脱敏展示
- 合规：退订与公司信息校验、同意记录、数据保留与驻留策略、审计不可篡改

## 11. 可观测性与运维
- 指标：核心 KPI 与 SLI（吞吐、延迟 P95/P99、错误率、队列深度、锁等待等）
- 日志：结构化日志字段与采样策略
- 追踪：Trace/Span 命名规范与关键标签（tenant_id、request_id）
- 告警：阈值/趋势、值班与渠道、故障分级
- 运维：运行手册、常见故障与自愈步骤、仪表盘清单

## 12. 性能与容量规划
- 目标：吞吐/延迟/并发/容量（数据量/增长率）
- 压测：场景/数据集/基线/瓶颈定位方法与调优建议

---

## 附录 A：可复制的 HLD 模板

```markdown
# 高层方案设计（HLD）｜【模块名称】 vX.Y（Draft/Ready/Approved）

1. 目标与范围（背景/目标/KPI、In/Out、假设/依赖）
2. 系统边界与上下文（上下游、信任边界、数据/控制流总览）
3. 架构视图（C4：Context/Container/Component/Deployment）
4. 关键流程与时序（至少成功链路+异常链路各一）
5. 数据与模型边界（热/冷分层、索引与查询、分区与归档、多租户策略）
6. 接口与数据流（内部接口/幂等/重试、外部 API 仅 GET/POST、统一响应与错误码）
7. 非功能性设计（性能、可用性、扩缩容、可观测、安全）
8. 风险与对策（技术/合规/依赖/容量）
9. 验收与发布（DoD、回滚策略、看板与告警）
10. 变更记录（版本/摘要/影响/评审）
```

## 附录 B：可复制的 LLD 模板

```markdown
# 详细设计（LLD）｜【模块名称】 vX.Y（Draft/Ready/Approved）

1. 模块职责与边界（输入/输出、依赖、约束）
2. 数据模型与字典（字段/类型/约束/索引/唯一性，附 ER/时序图）
3. DDL 与迁移（新增/变更 SQL 与回滚脚本，影子表或在线 DDL 方案）
4. 接口契约（仅 GET/POST、统一响应、错误码、签名、限流、幂等键）
5. 规则与算法（频控、抑制、A/B、窗口、合并策略）
6. 可靠性（幂等、退避重试、失败回放、隔离与熔断）
7. 可观测性（指标、日志、追踪、告警）
8. 性能基线与优化点（场景、基线、瓶颈与调优）
9. 测试用例与验收（正/负/极限/异常/E2E/性能）
10. 发布与回滚（特性开关、灰度、回滚步骤）
11. 风险清单与对策
12. 变更记录
```

## 附录 C：API 样例（统一响应/仅 GET/POST）

```http
POST /api/contacts/import
Content-Type: application/json
Idempotency-Key: 5b6c...

{
  "file_url": "s3://bucket/contacts.csv",
  "mapping": {"email": "Email", "first_name": "FirstName"},
  "dedupe": "merge"
}
```

```json
{
  "code": 0,
  "message": "ok",
  "data": {"job_id": 12345},
  "errors": null,
  "meta": {"request_id": "..."}
}
```

## 附录 D：数据库队列化模型（无 MQ 场景）

- 原则：使用数据库表（如 `email_messages`）承载待处理任务；消费者按优先级与计划时间拉取，使用锁标记与状态机推进；失败退避重试，超过上限写入死信并告警。
- 必要元素：状态（queued/sending/sent/failed/...）、优先级、计划时间、锁标记/持有者、幂等键、错误摘要、重试计数与下一次时间。

## 附录 E：质量评审清单（扩展）

- 术语/口径统一；数据/接口/规则覆盖完整；
- DDL 与回滚方案齐备、能在预生产演练；
- SLO/SLA、监控与告警闭环；
- 安全/隐私/合规闭环（退订、审计、数据保留）；
- 灰度与回滚可执行；
- 风险台账有量化阈值与触发条件。


