# 电子邮件营销系统（EMS）产品设计方案

本文档从产品视角完整阐述电子邮件营销系统（Email Marketing System，简称 EMS）的目标、竞品分析、核心能力、扩展能力、信息架构、数据模型、权限与合规、API 概览、非功能性要求与运营支持等，指导产品、设计、工程、运营协同落地。

---

## 1. 产品定位与目标

- **定位**：中大型 B2B/B2C 企业与开发者友好的全链路邮件营销平台，覆盖联系人管理、模板编辑、智能投放、自动化旅程、数据分析与增长闭环。
- **核心价值**：
  - **增长**：更高到达率与更强分层运营能力，提升打开/点击/转化。
  - **效率**：所见即得的模板与自动化流，缩短营销上线周期。
  - **可控**：多租户、角色权限、审计、合规、可观测，企业级可控性。
  - **开放**：标准 API、Webhook、SDK 与生态连接。
- **关键指标（北极星 + 过程指标）**：
  - 北极星：每月有效触达（发送-退回-投诉）且产生有效互动（打开/点击/转化）的用户量。
  - 过程：到达率、打开率、点击率、退订率、硬退回率、投诉率、旅程触达完成率、模板复用率、联系人增长与留存、投放耗时、API 可用性。

---

## 2. 竞品分析（市场扫描）

- **Mailchimp**：全栈营销及自动化，优势在模板生态、旅程与集成；对专业投递与自定义能力偏中端。
- **SendGrid Marketing Campaigns**：传输层与营销一体，投递稳定、API 友好，营销功能覆盖较全但自动化深度一般。
- **HubSpot Marketing Hub**：CRM 驱动的自动化营销，旅程、细分与报告强，价格较高且开发者自定义相对保守。
- **Salesforce Marketing Cloud**：企业级、强数据模型与自动化工作台，学习曲线陡峭、实施成本高。
- **Klaviyo**：面向电商用户增长，细分与行为触发能力优秀，行业聚焦强。
- **MailerLite / ActiveCampaign**：中小团队友好，功能均衡，生态适配一般。

- **机会点**：
  - 将“企业级可控性（多租户、合规、审计、可观测）”与“开发者友好（API/SDK/事件流/可扩展数据模型）”结合。
  - 行为事件驱动的实时细分 + 旅程，支持复杂条件与频控。
  - 深度投递治理（域名预热、分池、FBL、退信治理）开箱即用。

---

## 3. 用户画像与使用场景

- **市场运营经理**：创建并管理模板、分组、活动、自动化旅程，查看报告与复盘。
- **品牌/内容设计师**：设计品牌一致的邮件模板、组件库与素材库。
- **增长/数据分析师**：构建细分规则、A/B 测试、归因与漏斗分析。
- **开发者/集成人员**：通过 API/SDK 接入联系人、事件、发送与回调；构建自定义工作流与看板。
- **企业管理员**：租户、角色、额度、合规模块的配置与审计。

典型场景：新客欢迎与激活、购物车挽回、促销活动、内容周报、会员生命周期关怀、付费转化与留存挽回、事件触发通知等。

---

## 4. 产品范围与非目标

- **范围**：营销邮件（Bulk/Broadcast）、自动化旅程（Lifecycle/Drip/Event-based）、模板设计、联系人与细分、活动与 A/B、数据追踪与报表、投递与合规治理、开放平台（API/Webhook/SDK）。
- **非目标**：
  - 不提供自建 SMTP 中继的本地部署安装手册（可评估企业版）。
  - 不覆盖短信/推送等多渠道（可纳入增长套件的后续阶段）。

---

## 5. 核心功能设计

### 5.1 多租户与权限管理
- **租户（Workspace）**：企业/团队级隔离，独立数据域、配额与配置。
- **角色与权限**：内置角色（管理员、运营、设计、分析、开发、只读），支持自定义。权限粒度覆盖：联系人、模板、活动、旅程、域名、集成、报告、系统设置、计费。
- **审计日志**：关键资产（模板、活动、旅程、域名、配置）变更记录与导出。

### 5.2 联系人与数据平台
- **联系人模型**：
  - 基础字段：email、name、locale、time_zone、status（active、unsubscribed、bounced、complained、suppressed）、created_at、updated_at、last_activity。
  - 可扩展自定义字段：文本、数值、布尔、枚举、日期；支持校验与可见性控制。
- **数据采集**：
  - 导入：CSV/Excel、API、Webhook、表单。支持字段映射、去重、预校验、分批导入与进度反馈。
  - 实时事件：浏览、加购、下单、升级等，作为细分与旅程触发条件。
- **列表与标签**：
  - 列表（静态集合）与标签（多对多属性）；批量操作、合并与归档。
- **细分（Segments）**：
  - 可视化规则构建：字段条件、标签、行为事件、时间窗口、频次、地理/设备、属性组合（AND/OR/括号）。
  - 动态/静态两类；支持实时刷新与投放时冻结快照。
- **抑制清单与同意管理**：
  - 全局/域名级抑制（退订、硬退回、投诉、管理员添加）。
  - 偏好中心（订阅主题、邮件频率、语言）；双重确认（Double Opt-in）。

### 5.3 模板与内容管理
- **模板编辑器**：
  - 可视化拖拽 + 代码编辑（HTML/MJML 导入），响应式预览（桌面/移动）。
  - 组件库（区块、页眉、页脚、商品卡片、CTA、社媒、动态列表），样式主题与品牌库（色彩、字体、Logo）。
  - 变量与占位符（Handlebars/液态语法风格）：联系人的属性、系统变量、运算与条件渲染；片段/片段库（partials/snippets）。
  - 链接重写与追踪参数（UTM），一键插入退订链接与偏好中心。
- **模板资产**：素材库（图片/图标）、版本管理、差异对比、草稿/发布态、审批流（多人审核与评论）。
- **模板校验**：
  - 必填变量检查、退订/公司信息校验、垃圾词与图片占比提示、暗色模式兼容性提醒。

### 5.4 活动（Campaigns）
- **类型**：一次性群发（Broadcast）、定期播报（Recurring）、触发型（基于事件的即时单次发送）。
- **核心要素**：受众（列表/细分 + 排除规则）、主题行与预览文本、发件人身份（From/Reply-To）、内容模板、A/B 变体、计划与节流（按时区）、频控、预检清单。
- **A/B 测试**：主题/发件人/内容/发送时机；流量分配（固定/自动优胜）；统计口径（打开/点击/转化）。
- **投放保障**：发送前测试邮件、受众去重、抑制清单过滤、黑名单过滤、域名/配额/速率校验；可回滚至草稿。
- **发送执行**：
  - 批次化、并发控制、域名/IP 池路由、退避重试、幂等与去重、优先级队列（交易类 > 营销类）。

### 5.5 自动化旅程（Journeys/Flows）
- **画布构建**：拖拽节点，支持缩放、版本管理、发布/下线、仿真与健康检查。
- **触发器**：联系人创建/字段变更、事件（如下单）、加入列表/细分、日期型（生日/到期）、API 调用。
- **节点类型**：发送邮件、等待（时间/事件）、条件分支（属性/事件/细分/AB 随机）、循环与上限、调用 Webhook/函数、设定标签/字段、退出旅程、设置目标（Goal）。
- **频控与并发**：全局与旅程级节流/并发上限；进入/退出与去重策略；超时与兜底路径。
- **监控与调试**：节点吞吐、失败重试、实例追踪、可视回放与采样。

### 5.6 投递与可达性（Deliverability）
- **身份与信誉**：
  - 发送域名接入（DNS：SPF、DKIM、DMARC），CNAME 跟踪域，子域策略。
  - IP 池与域名预热（自动曲线）、域名/ISP 速率限制、品牌标识（BIMI）。
- **反馈与治理**：
  - 反馈回路（FBL）、退信分类（硬/软）、自动退订头（List-Unsubscribe）、投诉自动抑制、列表清洗与高风险邮箱识别。
- **合规内容**：公司信息、退订入口、内容与频率守则、存证与审计。

### 5.7 追踪与分析（Analytics）
- **事件流**：已发送、投递、打开（像素）、点击（重定向）、退回、投诉、退订、转化（JS SDK 或后端事件）。
- **报告视图**：
  - 活动报告：整体/分变体、时序、链接热力图、设备/地域/客户端、域名维度、渠道归因（UTM）。
  - 旅程报告：节点转化、路径漏斗、达成目标、拥塞与等待时长。
  - 联系人画像：生命周期、互动频次、健康评分。
- **导出与订阅**：CSV/Excel 导出、报告订阅邮件、Webhook 推送。

### 5.8 表单与偏好中心
- **表单**：内嵌/弹窗/独立页，字段可配置、样式主题、验证码、防刷、双重确认。
- **偏好中心**：订阅主题、频率、语言；一键退订与局部退订；自定义问题。

### 5.9 开放平台与生态
- **API**：REST（仅 GET/POST），遵循统一响应格式；API Key/Signed JWT；速率限制与审计。
- **Webhook**：事件订阅（投递、打开、点击、退信、投诉、退订、转化），重试与签名校验。
- **SDK**：前端 JS（表单/像素/UTM）、后端（Node/Go/Python/Java）。
- **预置集成（可选）**：Shopify、WooCommerce、Zapier、Segment、GA4、Snowflake（导出）。

### 5.10 运维与管理
- **看板**：系统健康（队列、速率、失败）、域名/IP 健康、告警与阈值。
- **任务中心**：导入/导出、重算细分、旅程批处理、回溯修复、批次重发。
- **审计与合规**：全链路操作日志、数据访问轨迹、PII 加密（可选）与脱敏策略。

---

## 6. 扩展功能设计（可配置/增值）
- **多变体/多臂 Bandit**：多变量实验（主题+内容+时间）与在线最佳臂选择。
- **智能选时**：基于历史互动与用户时区、设备偏好，预测最佳发送时机。
- **内容个性化**：商品/内容推荐（基于规则或模型），动态内容源（Feed）。
- **邮箱客户端预览**：主流客户端渲染与垃圾箱测试（第三方对接）。
- **数据仓库直连**：BI 直连导出、物化视图、增量同步。
- **合规套件**：区域化数据驻留、S/MIME、加密归档与保留策略。
- **团队协作**：评论、@成员、审批模板可配置、发布窗审批。
- **计费与额度**：套餐、超量策略、充值与账单、成本中心。

---

## 7. 信息架构（IA）
- **一级导航**：概览、联系人、细分、模板、活动、旅程、表单、报告、设置（域名、集成、团队、配额、审计）。
- **对象层级**：
  - 活动 → 变体 → 任务批次 → 收件人明细/事件
  - 旅程 → 版本 → 节点/边 → 实例
  - 联系人 → 列表/标签 → 细分 → 事件
  - 模板 → 片段 → 素材

---

## 8. 数据模型（核心实体与关系）

> 说明：以下为产品级模型视图，工程可在此基础上做范式/性能拆分。

- **Tenant（租户）**：id、name、plan、quotas、settings、created_at。
- **User（用户）**：id、tenant_id、email、role、status、last_login。
- **Contact（联系人）**：id、tenant_id、email、status、locale、tz、attributes(json)、tags、lists、created_at、last_activity。
- **List/Tag（列表/标签）**：id、tenant_id、name、type、description、counts。
- **Segment（细分）**：id、tenant_id、name、rule_tree(json)、is_dynamic、refresh_policy、last_built_at、size_snapshot。
- **Template（模板）**：id、tenant_id、name、editor_type、html/mjml、variables、partials、version、status(draft/published)、approvals。
- **Asset（素材）**：id、tenant_id、name、url、size、created_by。
- **Campaign（活动）**：id、tenant_id、name、type、audience(definition)、schedule、throttle、ab_config、from/reply_to、tracking、status、report_refs。
- **CampaignVariant（活动变体）**：id、campaign_id、subject、from、template_ref、traffic_split。
- **Message（消息/邮件）**：id、tenant_id、campaign_id/journey_id、variant_id、contact_id、status、provider_id、headers、sent_at、delivered_at、opened_at、clicked_at、bounced_at、complained_at、unsubscribed_at、metadata。
- **Journey（旅程）**：id、tenant_id、name、trigger、graph(nodes/edges)、version、status、entry/exit_policies、goals。
- **JourneyInstance（实例）**：id、journey_id、contact_id、node_pointer、state、started_at、ended_at。
- **Event（事件）**：id、tenant_id、contact_id、type（send/deliver/open/click/bounce/complaint/unsubscribe/custom）、props、occurred_at、source。
- **Suppression（抑制）**：id、tenant_id、email、reason（unsubscribe/bounce/complaint/admin）、scope(global/domain)、created_at。
- **Domain（域名）**：id、tenant_id、domain、spf/dkim/dmarc_status、tracking_domain、ip_pool、reputation。
- **Webhook（回调）**：id、tenant_id、events[]、endpoint、secret、status、retry_policy。
- **AuditLog（审计）**：id、tenant_id、actor、object_type/id、action、before/after、created_at、ip。
- **Quota/Usage（额度/用量）**：tenant_id、window、sent、delivered、errors、limits。

关键关系：Tenant 作为多租户主键；Contact 与 List/Tag 多对多；Segment 依赖 Contact 数据与事件计算；Campaign 与 Variant 一对多；Message 记录投放全生命周期；Journey 与 Instance 记录自动化执行；Suppression 与 Domain 保障合规与可达性。

---

## 9. 业务规则与状态机（选摘）
- **联系人状态**：active → unsubscribed/bounced/complained/suppressed；被抑制后不可被再次发送（除非管理员解除且合规允许）。
- **退信策略**：硬退信（hard bounce）→ 立即加入抑制；软退信（soft bounce）按次数阈值升级为抑制。
- **投诉策略**：投诉即加入抑制并计入发件人信誉，触发域名/IP 降速。
- **A/B 最优选择**：设定观察期；到期后按目标指标选择优胜变体推送剩余人群。
- **旅程进入**：可重复/不重复、冷却时间、最大并发与频控、冲突旅程互斥策略。
- **发送限流**：全局/租户/域名/ISP 多级限流；优先级路由；重试退避与幂等键。

---

## 10. API 概览（仅 GET/POST，统一响应）

> 说明：遵循项目统一 GET/POST 与统一响应规范（code/message/data/errors/meta）。以下为产品层级路由草案，实际以工程实现为准。

- 联系人：
  - POST `/api/contacts/create`（单个/批量）
  - GET `/api/contacts/get`（按 id/email 查询）
  - POST `/api/contacts/search`（条件分页）
  - POST `/api/contacts/import`（上传任务）
  - POST `/api/contacts/update`，POST `/api/contacts/delete`
  - POST `/api/contacts/tags/update`，POST `/api/contacts/lists/update`
- 列表/标签/细分：
  - POST `/api/lists/create|update|delete|members`
  - POST `/api/tags/create|update|delete|assign`
  - POST `/api/segments/create|update|delete|rebuild`，GET `/api/segments/get`，POST `/api/segments/preview`
- 模板：
  - POST `/api/templates/create|update|publish|archive|validate`，GET `/api/templates/get`，POST `/api/templates/list`
- 活动：
  - POST `/api/campaigns/create|update|schedule|pause|resume|cancel|ab/config`，GET `/api/campaigns/get`
  - POST `/api/campaigns/preview|send_test|report`
- 旅程：
  - POST `/api/journeys/create|update|publish|pause|resume|stop|clone`，GET `/api/journeys/get`
  - POST `/api/journeys/instances/search|replay`，POST `/api/journeys/ingest_event`
- 事件与报告：
  - POST `/api/events/ingest`，POST `/api/reports/export`，POST `/api/reports/subscribe`
- 可达性与域名：
  - POST `/api/domains/connect|verify|delete|throttle`，GET `/api/domains/get`
  - POST `/api/suppression/add|remove|search`
- 集成与 Webhook：
  - POST `/api/webhooks/create|update|delete|list|test`
  - POST `/api/integrations/connect|disconnect|settings`
- 管理与审计：
  - POST `/api/tenants/settings/update|quotas`，GET `/api/audit/search`

---

## 11. 权限与安全合规
- **权限模型**：RBAC + 资源作用域（租户全局/对象级），敏感操作二次确认与审批流。
- **安全**：API Key 轮换、IP 白名单、速率限制、签名与回放攻击防护、数据加密（静态/传输）。
- **合规**：
  - GDPR/CCPA：同意与撤回、数据导出与删除、目的限制、最小化原则。
  - CAN-SPAM/CASL：退订与公司信息、主题与内容合规、频率管理。
  - 数据保留：可配置留存周期与审计需求。

---

## 12. 可观测与稳定性目标
- **SLO**：
  - 控制平面 99.95% 可用性，数据平面（事件接收与投放）99.9% 可用性。
  - API P95 < 300ms（读取），P95 < 500ms（写入/任务类除外）。
- **监控**：OpenTelemetry 追踪 + 指标（队列深度、吞吐、失败率、退信/投诉、ISP 限速）。
- **告警**：关键链路阈值与趋势波动；域名/IP 健康异常；旅程阻塞。
- **弹性**：按租户/域名/ISP 维度的隔离与熔断；自动扩缩容。

---

## 13. 关键流程（体验与系统）

### 13.1 发送域名接入
1) 添加域名 → 2) 下发 DNS 记录 → 3) 自动校验 SPF/DKIM/DMARC/跟踪域 → 4) 预热策略选择 → 5) 成功后可投放。

### 13.2 创建与发送活动
1) 选择受众 → 2) 选择模板与变量校验 → 3) 配置主题/发件人/追踪 → 4) A/B（可选）→ 5) 预检清单 → 6) 测试邮件 → 7) 定时/立即发送 → 8) 实时监控与报告。

### 13.3 自动化旅程搭建
1) 选择触发器 → 2) 拖拽节点与条件 → 3) 频控与并发 → 4) 校验并发布 → 5) 监控节点指标与复盘。

### 13.4 联系人与细分
1) 导入联系人与字段映射 → 2) 标签/列表归类 → 3) 构建动态细分（规则/行为）→ 4) 用于活动或旅程。

---

## 14. 报表与指标定义（标准口径）
- **到达率** = delivered / sent
- **打开率（唯一）** = unique_opens / delivered
- **点击率（唯一）** = unique_clicks / delivered
- **点击打开率** = unique_clicks / unique_opens
- **退订率** = unsubscribes / delivered
- **硬退回率** = hard_bounces / sent
- **投诉率** = complaints / delivered
- **转化率** = conversions / delivered（或 / unique_clicks，按维度可切）
- 统计窗口与去重规则需在报告中清晰标注。

---

## 15. 风险与对策
- **可达性波动**：
  - 对策：域名/IP 预热、速率自适应、信誉监控、FBL、列表清洁策略、内容合规校验。
- **数据质量**：
  - 对策：导入预校验、硬退自动抑制、风险邮箱识别、活跃度打分与沉睡用户再唤醒策略。
- **隐私与合规**：
  - 对策：合法合规模板、偏好中心、DSR 自助与审计、区域化与加密。
- **规模化性能**：
  - 对策：分层队列、批处理、异步事件、幂等键、冷热分离与索引治理、租户隔离。

---

## 16. 最小可行集（MVP）建议（无排期）
- 联系人/标签/细分（字段 + 规则 + 导入）
- 模板编辑（所见即得 + 变量 + 资产）
- 活动发送（预检 + A/B 主题 + 基础报告）
- 域名接入（SPF/DKIM + 追踪域）
- 事件追踪（送达/打开/点击/退信/投诉/退订）
- 抑制与偏好中心（退订页）
- API（联系人、活动发送、事件回调）

---

## 17. 术语表
- **Suppression**：抑制清单，屏蔽不可发送的邮箱。
- **FBL**：反馈回路，ISP 提供的投诉反馈。
- **DMARC**：基于 SPF/DKIM 的域名身份策略与报告。
- **Journey**：基于触发与条件的自动化编排。

---

## 18. 成功标准（上线后观测）
- 90 天内：
  - 到达率 ≥ 98%，硬退率 ≤ 0.6%，投诉率 ≤ 0.08%。
  - 平台 P95 响应 < 400ms（控制面），事件延迟 P95 < 2s。
  - 模板复用 ≥ 30%，自动化旅程覆盖 ≥ 50% 活动发送量。

---

本文档将随实现不断细化与校准，用于指导详细 PRD、交互稿与工程实现对齐。