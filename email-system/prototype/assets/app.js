function setActiveNav(page){
  document.querySelectorAll('nav a').forEach(a=>{
    a.classList.toggle('active', a.dataset.page===page);
  });
}

async function loadPage(page){
  // 路由兼容：旧的 campaigns-ab 重定向到 campaigns
  if(page === 'campaigns-ab'){
    page = 'campaigns';
    history.replaceState(null, '', '#campaigns');
  }
  try {
    const res = await fetch(`pages/${page}.html`);
    const html = await res.text();
    const app = document.getElementById('app');
    app.innerHTML = html;
    setActiveNav(page);
    initPageInteractions();
  } catch (e) {
    document.getElementById('app').innerHTML = `<div class="card">无法加载页面：${page}</div>`;
  }
}

function initRouter(){
  function go(){
    const hash = location.hash.replace('#','') || 'contacts';
    loadPage(hash);
  }
  window.addEventListener('hashchange', go);
  document.querySelectorAll('nav a').forEach(a=>{
    a.addEventListener('click', ()=>{
      // hashchange 会触发加载
    });
  });
  go();
}

function initPageInteractions(){
  // 面板开合
  document.querySelectorAll('[data-open]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const id = btn.getAttribute('data-open');
      const el = document.getElementById(id);
      if(el){ el.style.display='block'; }
    });
  });
  document.querySelectorAll('[data-close]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const id = btn.getAttribute('data-close');
      const el = document.getElementById(id);
      if(el){ el.style.display='none'; }
    });
  });

  // Tabs 切换（data-tab="name" data-tab-target="#id"）
  document.querySelectorAll('[data-tab-target]').forEach(tab=>{
    tab.addEventListener('click',()=>{
      const t = tab.getAttribute('data-tab-target');
      const group = tab.getAttribute('data-tab-group') || 'default';
      document.querySelectorAll(`[data-tab-group="${group}"]`).forEach(x=>x.classList.remove('active'));
      tab.classList.add('active');
      document.querySelectorAll(`[data-tab-panel="${group}"]`).forEach(p=>p.style.display='none');
      const panel = document.querySelector(t);
      if(panel){ panel.style.display='block'; }
    });
  });

  // 条件构建器：添加条件（作用域到最近的容器）
  document.querySelectorAll('[data-action="add-seg-row"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const context = btn.closest('.dialog') || btn.closest('.card') || document;
      let wrap = context.querySelector('[data-role="seg-rows"]');
      if(!wrap){ wrap = context.querySelector('#seg-rows'); }
      if(!wrap) return;
      const row = document.createElement('div');
      row.className='row';
      row.innerHTML = `
        <div>
          <label>字段</label>
          <select class="seg-field">
            <option value="event:purchase">行为：购买</option>
            <option value="event:open">行为：打开</option>
            <option value="event:click">行为：点击</option>
            <option value="status">订阅状态</option>
            <option value="attr:region">属性：地区</option>
            <option value="rel:tag">关系：标签</option>
            <option value="rel:list">关系：列表</option>
          </select>
        </div>
        <div>
          <label>运算符</label>
          <select><option>&gt;=</option><option>&lt;=</option><option>包含</option><option>不包含</option><option>等于</option></select>
        </div>
        <div class="seg-value"><label>值</label><input placeholder="7 天 / 1 次 / VIP / active"/></div>
        <div class="seg-window" style="display:none"><label>时间窗口（天）</label><input type="number" min="1" placeholder="例如：7"/></div>
        <div style="flex:0"><button class="btn secondary">删除</button></div>
      `;
      row.querySelector('button').addEventListener('click',()=>row.remove());
      // 行为类字段显示时间窗口输入
      const fieldSel = row.querySelector('.seg-field');
      const winBox = row.querySelector('.seg-window');
      const onFieldChange = ()=>{
        const v = fieldSel.value || '';
        const isEvent = v.startsWith('event:');
        winBox.style.display = isEvent ? 'block' : 'none';
      };
      fieldSel.addEventListener('change', onFieldChange);
      onFieldChange();
      wrap.appendChild(row);
    });
  });

  // 人群圈选：预估人数（示例演示，作用域到容器）
  document.querySelectorAll('[data-action="estimate"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const context = btn.closest('.dialog') || btn.closest('.card') || document;
      const ests = context.querySelectorAll('[data-role="est-count"], #est-count');
      ests.forEach(el=>{ el.textContent = '12,345 人'; });
    });
  });

  // 新建标签弹窗：类型与圈选联动
  const dlg = document.getElementById('dlg-new-tag');
  if(dlg){
    const typeSel = dlg.querySelector('#tag-type');
    const dyn = dlg.querySelector('#dyn-section');
    const stat = dlg.querySelector('#static-section');
    const dynEst = dlg.querySelector('#dynamic-estimate');
    const staticPickRadios = dlg.querySelectorAll('input[name="static-pick"]');
    const staticRules = dlg.querySelector('#static-rules-wrapper');

    const render = ()=>{
      const isDyn = typeSel && typeSel.value === 'dynamic';
      if(dyn) dyn.style.display = isDyn ? 'block' : 'none';
      if(dynEst) dynEst.style.display = isDyn ? 'block' : 'none';
      if(stat) stat.style.display = isDyn ? 'none' : 'block';

      const pick = Array.from(staticPickRadios).find(r=>r.checked);
      const useRules = pick && pick.value === 'rules';
      if(staticRules) staticRules.style.display = (!isDyn && useRules) ? 'block' : 'none';
    };

    if(typeSel){ typeSel.addEventListener('change', render); }
    staticPickRadios.forEach(r=> r.addEventListener('change', render));
    render();
  }

  // 批量操作：联动额外参数输入
  const bulkAction = document.getElementById('bulk-action');
  const bulkExtra = document.getElementById('bulk-extra');
  if(bulkAction && bulkExtra){
    const renderExtra = ()=>{
      const v = bulkAction.value;
      if(v==='tag'){
        bulkExtra.innerHTML = '<label>标签</label><input id="bulk-tags" placeholder="输入多个标签，用逗号分隔">';
      }else if(v==='add-to-list'){
        bulkExtra.innerHTML = '<label>列表</label><select id="bulk-list"><option>黑五预热</option><option>VIP 用户</option></select>';
      }else{
        bulkExtra.innerHTML = '';
      }
    };
    bulkAction.addEventListener('change', renderExtra);
    renderExtra();
  }

  // 批量操作：执行（示例提示）
  document.querySelectorAll('[data-action="apply-bulk"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const scope = (document.getElementById('bulk-scope')||{}).value || 'all';
      const action = (document.getElementById('bulk-action')||{}).value || 'tag';
      const msg = scope==='all' ? '已对当前预估人群执行：' : '已对选中联系人执行：';
      alert(msg + action);
    });
  });

  // 预览样本：全选
  const chkAll = document.getElementById('chk-all');
  if(chkAll){
    chkAll.addEventListener('change',()=>{
      document.querySelectorAll('.chk').forEach(c=>{ c.checked = chkAll.checked; });
    });
  }

  // 模板多语言：语言切换与新增语言（示例交互）
  const localeSel = document.getElementById('tpl-locale');
  const addLocaleBtn = document.getElementById('add-locale');
  const subjectInput = document.getElementById('tpl-subject');
  const preheaderInput = document.getElementById('tpl-preheader');
  const htmlInput = document.getElementById('tpl-html');
  const previewTitle = document.getElementById('preview-title');
  const previewSub = document.getElementById('preview-sub');
  const previewCta = document.getElementById('preview-cta');

  // 简单内存存储不同语言的字段
  const defaultLocale = 'zh-CN';
  const localeStore = window.__tplLocaleStore || (window.__tplLocaleStore = {
    'zh-CN': {
      subject: '本周特惠来啦！限时7折优惠等你来抢！',
      preheader: '内含 7 折券码与精选商品，限时抢购，先到先得！',
      title: '🎉 本周特惠来啦！',
      sub: 'Hello {{first_name}}，专属优惠等你来拿',
      cta: '🚀 立即抢购'
    },
    'en-US': {
      subject: 'This Week Only! 30% OFF awaits you!',
      preheader: 'Exclusive coupon and picks inside. Limited time.',
      title: '🎉 Big Deals This Week!',
      sub: 'Hello {{first_name}}, your exclusive offer is here',
      cta: '🚀 Shop Now'
    },
    'es-ES': {
      subject: '¡Sólo esta semana! ¡30% de descuento!',
      preheader: 'Cupón exclusivo y selección especial. Tiempo limitado.',
      title: '🎉 Grandes ofertas esta semana',
      sub: 'Hola {{first_name}}, tu oferta exclusiva ya está aquí',
      cta: '🚀 Comprar ahora'
    }
  });

  const renderLocale = ()=>{
    if(!localeSel) return;
    const loc = localeSel.value || defaultLocale;
    const data = localeStore[loc] || localeStore[defaultLocale];
    if(subjectInput) subjectInput.value = data.subject || '';
    if(preheaderInput) preheaderInput.value = data.preheader || '';
    if(previewTitle) previewTitle.textContent = data.title || '';
    if(previewSub) previewSub.textContent = (data.sub || '').replace('{{first_name}}','John');
    if(previewCta) previewCta.textContent = data.cta || '';
  };

  if(localeSel){
    localeSel.addEventListener('change', renderLocale);
    renderLocale();
  }

  if(addLocaleBtn && localeSel){
    addLocaleBtn.addEventListener('click',()=>{
      const newCode = prompt('输入新的语言代码（如 fr-FR）：');
      if(!newCode) return;
      if(!localeStore[newCode]){
        // 从当前语言拷贝
        const from = localeSel.value || defaultLocale;
        localeStore[newCode] = { ...localeStore[from] };
        const opt = document.createElement('option');
        opt.value = newCode; opt.textContent = newCode; // 简化展示
        localeSel.appendChild(opt);
        localeSel.value = newCode;
        renderLocale();
        alert('已新增语言：' + newCode + '（基于 ' + from + ' 拷贝）');
      } else {
        alert('语言已存在：' + newCode);
      }
    });
  }
}

document.addEventListener('DOMContentLoaded', initRouter);


