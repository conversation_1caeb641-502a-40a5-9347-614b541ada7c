<!-- 发件渠道管理页面 -->
<div class="toolbar">
  <button class="btn" onclick="showCreateChannel()">新建渠道</button>
  <button class="btn secondary" onclick="showChannelPermissions()">权限管理</button>
  <button class="btn ghost" onclick="exportChannels()">导出配置</button>
</div>

<div class="kpi">
  <div class="item"><div class="mini">总渠道数</div><div class="num">3</div></div>
  <div class="item"><div class="mini">活跃渠道</div><div class="num">2</div></div>
  <div class="item"><div class="mini">平均投递率</div><div class="num">98.5%</div></div>
  <div class="item"><div class="mini">资源利用率</div><div class="num">67%</div></div>
</div>

<div class="card">
  <div class="mini">发件渠道列表</div>
  <div class="toolbar">
    <input placeholder="搜索渠道..." />
    <select><option>全部类型</option><option>营销渠道</option><option>交易渠道</option><option>系统渠道</option></select>
    <select><option>全部状态</option><option>活跃</option><option>暂停</option><option>配置中</option></select>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>渠道名称</th>
        <th>类型</th>
        <th>状态</th>
        <th>默认域名</th>
        <th>日配额</th>
        <th>投递率</th>
        <th>时间表</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <div><strong>营销主渠道</strong></div>
          <div class="mini">marketing-main</div>
        </td>
        <td><span class="badge marketing">营销</span></td>
        <td><span class="badge active">活跃</span></td>
        <td>marketing.example.com</td>
        <td>50,000</td>
        <td>98.7%</td>
        <td><span class="badge timezone">时区匹配</span></td>
        <td>
          <button class="btn mini" onclick="editChannel(1)">编辑</button>
          <button class="btn mini secondary" onclick="viewChannelDetails(1)">详情</button>
          <button class="btn mini ghost" onclick="showChannelSchedule(1)">时间表</button>
        </td>
      </tr>
      <tr>
        <td>
          <div><strong>交易通知</strong></div>
          <div class="mini">transactional</div>
        </td>
        <td><span class="badge transactional">交易</span></td>
        <td><span class="badge active">活跃</span></td>
        <td>notify.example.com</td>
        <td>10,000</td>
        <td>99.2%</td>
        <td><span class="badge fixed">固定时间</span></td>
        <td>
          <button class="btn mini" onclick="editChannel(2)">编辑</button>
          <button class="btn mini secondary" onclick="viewChannelDetails(2)">详情</button>
          <button class="btn mini ghost" onclick="showChannelSchedule(2)">时间表</button>
        </td>
      </tr>
      <tr>
        <td>
          <div><strong>系统告警</strong></div>
          <div class="mini">system-alerts</div>
        </td>
        <td><span class="badge system">系统</span></td>
        <td><span class="badge suspended">暂停</span></td>
        <td>alerts.example.com</td>
        <td>1,000</td>
        <td>—</td>
        <td><span class="badge immediate">立即发送</span></td>
        <td>
          <button class="btn mini" onclick="editChannel(3)">编辑</button>
          <button class="btn mini secondary" onclick="viewChannelDetails(3)">详情</button>
          <button class="btn mini ghost" onclick="showChannelSchedule(3)">时间表</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">渠道类型分布</div>
    <div class="chart-placeholder">
      <div style="height:200px; display:flex; align-items:center; justify-content:center; color:#666;">
        饼图：营销 1个，交易 1个，系统 1个
      </div>
    </div>
  </div>
  <div class="card">
    <div class="mini">资源使用情况</div>
    <div class="row">
      <div>
        <label>域名使用率</label>
        <div class="progress"><div style="width:75%"></div></div>
        <div class="mini">3/4 个域名已分配</div>
      </div>
      <div>
        <label>IP池使用率</label>
        <div class="progress"><div style="width:60%"></div></div>
        <div class="mini">3/5 个IP池已分配</div>
      </div>
    </div>
  </div>
</div>

<!-- 创建渠道弹窗 -->
<div id="create-channel" class="card" style="display:none; max-width:800px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">新建发件渠道</div>
  
  <div class="grid cols-2">
    <div>
      <label>渠道名称 *</label>
      <input placeholder="如：营销主渠道" />
      
      <label>渠道代码 *</label>
      <input placeholder="如：marketing-main" />
      <div class="hint">唯一标识符，仅支持字母、数字、连字符</div>
      
      <label>渠道类型 *</label>
      <select>
        <option value="">请选择类型</option>
        <option value="marketing">营销渠道</option>
        <option value="transactional">交易渠道</option>
        <option value="system">系统渠道</option>
      </select>
      
      <label>描述</label>
      <textarea placeholder="渠道用途说明..."></textarea>
    </div>
    
    <div>
      <label>默认域名</label>
      <select>
        <option value="">请选择域名</option>
        <option>marketing.example.com</option>
        <option>notify.example.com</option>
        <option>alerts.example.com</option>
      </select>
      
      <label>默认IP池</label>
      <select>
        <option value="">请选择IP池</option>
        <option>Pool-Marketing</option>
        <option>Pool-Transactional</option>
        <option>Pool-System</option>
      </select>
      
      <label>日发送配额</label>
      <input type="number" placeholder="如：50000" />
      
      <label>月发送配额</label>
      <input type="number" placeholder="如：1500000" />
    </div>
  </div>
  
  <div class="divider"></div>
  <div class="mini">高级配置</div>
  <div class="grid cols-2">
    <div>
      <label>限速策略</label>
      <select>
        <option>继承租户默认</option>
        <option>保守策略</option>
        <option>标准策略</option>
        <option>激进策略</option>
      </select>
      
      <label>预热策略</label>
      <select>
        <option>继承域名设置</option>
        <option>保守预热</option>
        <option>标准预热</option>
        <option>激进预热</option>
      </select>
    </div>
    
    <div>
      <label>监控阈值</label>
      <div class="row">
        <div>
          <label>投诉率阈值</label>
          <input value="0.08" />
        </div>
        <div>
          <label>硬退率阈值</label>
          <input value="0.6" />
        </div>
      </div>
      
      <label>告警设置</label>
      <div>
        <input type="checkbox" checked/> 启用实时告警
      </div>
      <div>
        <input type="checkbox" checked/> 异常自动暂停
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="createChannel()">创建渠道</button>
    <button class="btn secondary" onclick="hideCreateChannel()">取消</button>
  </div>
</div>

<!-- 渠道权限管理弹窗 -->
<div id="channel-permissions" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">渠道权限管理</div>
  
  <div class="grid cols-2">
    <div>
      <label>选择渠道</label>
      <select onchange="loadChannelPermissions()">
        <option value="">请选择渠道</option>
        <option value="1">营销主渠道</option>
        <option value="2">交易通知</option>
        <option value="3">系统告警</option>
      </select>
    </div>
    <div>
      <button class="btn" onclick="addChannelPermission()">添加权限</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">当前权限列表</div>
    <table class="table">
      <thead>
        <tr>
          <th>用户</th>
          <th>角色</th>
          <th>权限</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>张三 (<EMAIL>)</td>
          <td><span class="badge owner">Owner</span></td>
          <td>完全控制</td>
          <td><button class="btn mini secondary">编辑</button></td>
        </tr>
        <tr>
          <td>李四 (<EMAIL>)</td>
          <td><span class="badge admin">Admin</span></td>
          <td>管理配置</td>
          <td><button class="btn mini secondary">编辑</button></td>
        </tr>
        <tr>
          <td>王五 (<EMAIL>)</td>
          <td><span class="badge operator">Operator</span></td>
          <td>发送操作</td>
          <td><button class="btn mini secondary">编辑</button></td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideChannelPermissions()">关闭</button>
  </div>
</div>

<!-- 渠道时间表配置弹窗 -->
<div id="channel-schedule" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">渠道时间表配置</div>
  
  <div class="grid cols-2">
    <div>
      <label>时间表类型</label>
      <select id="schedule-type" onchange="toggleScheduleType()">
        <option value="fixed">固定时间</option>
        <option value="timezone">时区匹配</option>
        <option value="immediate">立即发送</option>
        <option value="custom">自定义规则</option>
      </select>
      
      <div id="fixed-time-config">
        <label>统一发送时间</label>
        <input type="time" value="10:00" />
        <div class="hint">所有邮件将在指定时间发送</div>
      </div>
      
      <div id="timezone-config" style="display:none;">
        <label>基准时间</label>
        <input type="time" value="10:00" />
        <div class="hint">系统将根据用户时区自动调整发送时间</div>
        
        <label>时区匹配策略</label>
        <select>
          <option value="user-timezone">用户所在时区</option>
          <option value="business-hours">工作时间（9:00-18:00）</option>
          <option value="optimal-hours">最佳时间（10:00-14:00）</option>
        </select>
        
        <label>时区数据来源</label>
        <div>
          <input type="checkbox" checked/> 用户注册时区
          <input type="checkbox" checked/> IP地理位置
          <input type="checkbox"/> 浏览器时区
        </div>
      </div>
      
      <div id="custom-config" style="display:none;">
        <label>自定义规则</label>
        <textarea placeholder="例如：工作日 9:00-18:00，周末 10:00-16:00"></textarea>
      </div>
    </div>
    
    <div>
      <label>国家/地区特定时间</label>
      <div class="card" style="max-height:300px; overflow-y:auto;">
        <div class="row">
          <div>
            <select>
              <option>中国 (CN)</option>
              <option>美国 (US)</option>
              <option>欧洲 (EU)</option>
              <option>日本 (JP)</option>
            </select>
          </div>
          <div>
            <input type="time" value="10:00" />
          </div>
          <div>
            <button class="btn mini">添加</button>
          </div>
        </div>
        
        <div class="mini">已配置的国家/地区</div>
        <table class="table mini">
          <thead>
            <tr><th>国家/地区</th><th>发送时间</th><th>操作</th></tr>
          </thead>
          <tbody>
            <tr>
              <td>中国 (CN)</td>
              <td>10:00</td>
              <td><button class="btn mini secondary">删除</button></td>
            </tr>
            <tr>
              <td>美国 (US)</td>
              <td>09:00</td>
              <td><button class="btn mini secondary">删除</button></td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="divider"></div>
      <div class="mini">高级设置</div>
      <div>
        <input type="checkbox" checked/> 启用智能时间优化
        <div class="hint">基于历史数据自动调整最佳发送时间</div>
      </div>
      <div>
        <input type="checkbox" checked/> 避免节假日发送
        <div class="hint">自动跳过主要节假日</div>
      </div>
      <div>
        <input type="checkbox"/> 工作日限制
        <div class="hint">仅在工作日发送</div>
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="saveChannelSchedule()">保存时间表</button>
    <button class="btn secondary" onclick="hideChannelSchedule()">取消</button>
  </div>
</div>

<style>
.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.marketing {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.transactional {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.system {
  background: #fff3e0;
  color: #f57c00;
}

.badge.active {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.suspended {
  background: #ffebee;
  color: #d32f2f;
}

.badge.owner {
  background: #f3e5f5;
  color: #7b1fa2;
}

.badge.admin {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.operator {
  background: #fff3e0;
  color: #f57c00;
}

.badge.timezone {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.fixed {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.immediate {
  background: #fff3e0;
  color: #f57c00;
}

.chart-placeholder {
  border: 1px dashed #ddd;
  border-radius: 8px;
  margin: 10px 0;
}
</style>

<script>
function showCreateChannel() {
  document.getElementById('create-channel').style.display = 'block';
}

function hideCreateChannel() {
  document.getElementById('create-channel').style.display = 'none';
}

function createChannel() {
  // 创建渠道逻辑
  alert('渠道创建成功！');
  hideCreateChannel();
}

function editChannel(id) {
  // 编辑渠道逻辑
  window.location.href = 'channel-edit.html?id=' + id;
}

function viewChannelDetails(id) {
  // 查看渠道详情逻辑
  window.location.href = 'channel-details.html?id=' + id;
}

function showChannelSchedule(id) {
  document.getElementById('channel-schedule').style.display = 'block';
  // 加载渠道时间表配置
  loadChannelSchedule(id);
}

function hideChannelSchedule() {
  document.getElementById('channel-schedule').style.display = 'none';
}

function toggleScheduleType() {
  const scheduleType = document.getElementById('schedule-type').value;
  const fixedConfig = document.getElementById('fixed-time-config');
  const timezoneConfig = document.getElementById('timezone-config');
  const customConfig = document.getElementById('custom-config');
  
  // 隐藏所有配置
  fixedConfig.style.display = 'none';
  timezoneConfig.style.display = 'none';
  customConfig.style.display = 'none';
  
  // 显示对应配置
  switch(scheduleType) {
    case 'fixed':
      fixedConfig.style.display = 'block';
      break;
    case 'timezone':
      timezoneConfig.style.display = 'block';
      break;
    case 'custom':
      customConfig.style.display = 'block';
      break;
  }
}

function loadChannelSchedule(id) {
  // 加载渠道时间表配置逻辑
  console.log('Loading schedule for channel:', id);
}

function saveChannelSchedule() {
  // 保存时间表配置逻辑
  alert('时间表配置保存成功！');
  hideChannelSchedule();
}

function showChannelPermissions() {
  document.getElementById('channel-permissions').style.display = 'block';
}

function hideChannelPermissions() {
  document.getElementById('channel-permissions').style.display = 'none';
}

function loadChannelPermissions() {
  // 加载渠道权限逻辑
}

function addChannelPermission() {
  // 添加权限逻辑
}

function exportChannels() {
  // 导出配置逻辑
  alert('渠道配置导出成功！');
}
</script>
