<div class="toolbar">
  <button class="btn" onclick="showImportPanel()">📥 导入联系人</button>
  <button class="btn secondary" onclick="showCreateContact()">➕ 新建联系人</button>
  <button class="btn ghost" onclick="showImportHistory()">📄 查看导入记录</button>
  <button class="btn" onclick="showBulkTag()">🏷️ 批量操作</button>
  <div class="hint">管理联系人数据，支持批量导入、自动匹配自定义字段、批量标记与备注</div>
  
  <div style="margin-left:auto; display:flex; gap:8px; align-items:center;">
    <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" onclick="showLanguageStats()">🌍 语言分布</button>
    <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" onclick="showLocationStats()">📍 地理分布</button>
    <label style="font-size:12px;color:var(--muted)">队列滞后</label>
    <span class="pill" id="queue-lag">--</span>
  </div>
</div>

<div class="card">
  <div style="display: flex; gap: 24px; align-items: center; margin-bottom: 8px;">
    <div style="font-size: 13px; color: var(--text-secondary);">
      总数 <b>128,532</b>
    </div>
    <div style="font-size: 13px; color: var(--text-secondary);">
      有效邮箱 <b>118,904</b>（92.5%）
    </div>
    <div style="font-size: 13px; color: var(--text-secondary);">
      活跃用户 <b>64,217</b>
    </div>
    <div style="font-size: 13px; color: var(--text-secondary);">
      本月新增 <b>3,241</b>
    </div>
  </div>
  <div class="row" style="margin-bottom: 0; gap: 16px; align-items: flex-end;">
    <div>
      <label style="font-size: 13px;">关键词</label>
      <input id="search-keyword" style="height: 28px; font-size: 13px;" placeholder="邮箱、姓名、标签..." />
    </div>
    <div>
      <label style="font-size: 13px;">状态</label>
      <select id="filter-status" style="height: 28px; font-size: 13px;">
        <option>全部状态</option>
        <option>活跃</option>
        <option>退订</option>
        <option>硬退回</option>
        <option>未确认</option>
      </select>
    </div>
    <div>
      <label style="font-size: 13px;">所属列表</label>
      <select id="filter-list" style="height: 28px; font-size: 13px;">
        <option>全部列表</option>
        <option>双11活动</option>
        <option>VIP客户</option>
        <option>新注册用户</option>
      </select>
    </div>
    <div>
      <label style="font-size: 13px;">标记语言</label>
      <select id="filter-language" style="height: 28px; font-size: 13px;">
        <option>全部语言</option>
        <option>简体中文 (zh-CN)</option>
        <option>繁体中文 (zh-TW)</option>
        <option>英语 (en-US)</option>
        <option>日语 (ja-JP)</option>
        <option>韩语 (ko-KR)</option>
        <option>法语 (fr-FR)</option>
        <option>德语 (de-DE)</option>
      </select>
    </div>
    <div>
      <label style="font-size: 13px;">地理位置</label>
      <select id="filter-country" style="height: 28px; font-size: 13px;">
        <option>全部地区</option>
        <option>中国大陆</option>
        <option>台湾</option>
        <option>香港</option>
        <option>美国</option>
        <option>日本</option>
        <option>韩国</option>
        <option>欧洲</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn" style="height: 28px; font-size: 13px;" onclick="applyFilters()">🔍 搜索</button>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">联系人列表</div>
  <div class="hint">已选 <span id="selected-count">0</span> 个</div>
  <table class="table" id="contacts-table">
    <thead>
      <tr>
        <th class="sortable" data-sort-key="contact">联系人信息 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="language">标记语言 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="country">地理位置 <span class="sort-indicator">↕</span></th>
        <th>标签</th>
        <th>备注</th>
        <th class="sortable" data-sort-key="consent">同意状态 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="status">活跃状态 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="open">打开率 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="reply">回复率 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="bounce">拒收率 <span class="sort-indicator">↕</span></th>
        <th class="sortable" data-sort-key="created">创建时间 <span class="sort-indicator">↕</span></th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">Alice Johnson</div>
        </td>
        <td>
          <span class="pill">🇺🇸 en-US</span>
        </td>
        <td>
          <div style="font-size: 13px">🇺🇸 美国</div>
          <div style="color: var(--text-secondary); font-size: 12px">New York, NY</div>
        </td>
        <td>
          <span class="pill ok">VIP</span>
          <span class="pill">复购客户</span>
        </td>
        <td><span class="pill">近期下单，重点维护</span></td>
        <td><span class="ok">✅ 已同意</span></td>
        <td><span class="ok">活跃</span></td>
        <td><span class="ok">46.2%</span></td>
        <td><span class="ok">2.1%</span></td>
        <td><span>0.0%</span></td>
        <td>2025-07-01</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" onclick="showContactDetail('<EMAIL>')">详情</button>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">Bob Smith</div>
        </td>
        <td>
          <span class="pill">🇨🇳 zh-CN</span>
        </td>
        <td>
          <div style="font-size: 13px">🇨🇳 中国</div>
          <div style="color: var(--text-secondary); font-size: 12px">北京, 北京</div>
        </td>
        <td>
          <span class="pill warnTxt">新客</span>
        </td>
        <td><span class="warnTxt">⚠️ 未明确</span></td>
        <td><span class="err">退订</span></td>
        <td>—</td>
        <td><span>8.3%</span></td>
        <td><span>0.0%</span></td>
        <td><span class="warnTxt">0.2%</span></td>
        <td>2025-05-18</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">Carol Davis</div>
        </td>
        <td>
          <span class="pill">🇯🇵 ja-JP</span>
        </td>
        <td>
          <div style="font-size: 13px">🇯🇵 日本</div>
          <div style="color: var(--text-secondary); font-size: 12px">东京, 东京都</div>
        </td>
        <td>
          <span class="pill">企业客户</span>
        </td>
        <td><span class="ok">✅ 已同意</span></td>
        <td><span class="ok">活跃</span></td>
        <td><span class="ok">41.5%</span></td>
        <td><span class="ok">1.4%</span></td>
        <td><span>0.0%</span></td>
        <td>2025-06-15</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">David Wilson</div>
        </td>
        <td>
          <span class="pill">🇬🇧 en-GB</span>
        </td>
        <td>
          <div style="font-size: 13px">🇬🇧 英国</div>
          <div style="color: var(--text-secondary); font-size: 12px">London, England</div>
        </td>
        <td>
          <span class="pill">欧洲客户</span>
        </td>
        <td><span class="ok">✅ 已同意</span></td>
        <td><span class="ok">活跃</span></td>
        <td><span class="ok">39.8%</span></td>
        <td><span>0.9%</span></td>
        <td><span>0.0%</span></td>
        <td>2025-06-20</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">Emma Brown</div>
        </td>
        <td>
          <span class="pill">🇫🇷 fr-FR</span>
        </td>
        <td>
          <div style="font-size: 13px">🇫🇷 法国</div>
          <div style="color: var(--text-secondary); font-size: 12px">Paris, Île-de-France</div>
        </td>
        <td>
          <span class="pill">法语客户</span>
        </td>
        <td><span class="ok">✅ 已同意</span></td>
        <td><span class="ok">活跃</span></td>
        <td><span class="ok">43.7%</span></td>
        <td><span>1.2%</span></td>
        <td><span>0.0%</span></td>
        <td>2025-06-25</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">Hans Mueller</div>
        </td>
        <td>
          <span class="pill">🇩🇪 de-DE</span>
        </td>
        <td>
          <div style="font-size: 13px">🇩🇪 德国</div>
          <div style="color: var(--text-secondary); font-size: 12px">Berlin, Berlin</div>
        </td>
        <td>
          <span class="pill">德语客户</span>
        </td>
        <td><span class="ok">✅ 已同意</span></td>
        <td><span class="ok">活跃</span></td>
        <td><span class="ok">40.2%</span></td>
        <td><span>0.8%</span></td>
        <td><span>0.0%</span></td>
        <td>2025-06-28</td>
        <td>
          <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" data-open="contact-notes">备注</button>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="pagination">
    <div class="info">共 <span id="total-count">128,532</span> 条 · 每页
      <select id="page-size" style="height:28px;font-size:13px;width:auto">
        <option>10</option>
        <option selected>25</option>
        <option>50</option>
        <option>100</option>
      </select>
    </div>
    <div class="pages" id="pages"></div>
  </div>
</div>

<!-- 导入联系人弹窗（隐藏时 display:none，可用js控制显示） -->
<div id="import-contacts" class="card" style="display:none; max-width: 900px; position:fixed; top:6%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📥 批量导入联系人（自动匹配 + 预校验）</div>
  <div class="row">
    <div>
      <label>CSV 文件 URL</label>
      <input id="csv-url" placeholder="s3://bucket/contacts.csv" />
    </div>
    <div>
      <label>去重策略</label>
      <select id="dedupe">
        <option value="merge">按 email 合并（推荐）</option>
        <option value="skip">拒绝冲突</option>
        <option value="overwrite">覆盖现有</option>
      </select>
    </div>
    <div>
      <label>预览行数</label>
      <input id="sample-size" type="number" value="200" />
    </div>
  </div>
  <div class="toolbar">
    <button class="btn" onclick="simulatePlan()">🔍 自动匹配与预览</button>
    <button class="btn ghost" onclick="simulatePreview()">🧪 预校验报告</button>
  </div>
  <div class="divider"></div>
  <div>
    <div class="mini">字段映射</div>
    <div class="hint">系统将根据列名与样本自动匹配，低置信度项需人工确认。可在右侧创建为自定义字段。</div>
    <table class="table">
      <thead>
        <tr>
          <th>源列</th>
          <th>建议映射</th>
          <th>置信度</th>
          <th>类型推断</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody id="mapping-rows">
        <!-- 动态填充 -->
      </tbody>
    </table>
  </div>
  <div class="divider"></div>
  <div class="row">
    <button class="btn" onclick="startImport()">🚀 提交导入</button>
    <button class="btn secondary" onclick="hideImportPanel()">❌ 关闭</button>
  </div>
</div>

<!-- 导入记录弹窗（隐藏时 display:none，可用js控制显示） -->
<div id="import-history" class="card" style="display:none; max-width:600px; position:fixed; top:10%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📄 导入记录</div>
  <table class="table">
    <thead>
      <tr>
        <th>任务名称</th>
        <th>状态</th>
        <th>结果</th>
        <th>时间</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>contacts_2025_08_01.csv</td>
        <td><span class="ok">✅ 完成</span></td>
        <td>10,240 / 12</td>
        <td>10:24</td>
      </tr>
      <tr>
        <td>vip_update.csv</td>
        <td><span class="warnTxt">⚠️ 警告</span></td>
        <td>1,240 / 3</td>
        <td>昨天</td>
      </tr>
      <tr>
        <td>new_users.csv</td>
        <td><span class="ok">✅ 完成</span></td>
        <td>5,120 / 0</td>
        <td>2天前</td>
      </tr>
    </tbody>
  </table>
  <div class="toolbar">
    <button class="btn secondary" onclick="hideImportHistory()">❌ 关闭</button>
  </div>
</div>

<!-- 批量标记标签 -->
<div id="bulk-tag" class="card" style="display:none; max-width:800px; position:fixed; top:12%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">🏷️ 批量操作</div>
  <div class="grid cols-2">
    <div>
      <label>选择范围</label>
      <select id="bulk-scope">
        <option value="selected">当前选中</option>
        <option value="filter">当前筛选结果</option>
        <option value="all">全部联系人</option>
      </select>
    </div>
    <div>
      <label>按语言筛选</label>
      <select id="bulk-language-filter">
        <option value="">全部语言</option>
        <option value="zh-CN">🇨🇳 简体中文</option>
        <option value="en-US">🇺🇸 英语(美国)</option>
        <option value="en-GB">🇬🇧 英语(英国)</option>
        <option value="ja-JP">🇯🇵 日语</option>
        <option value="ko-KR">🇰🇷 韩语</option>
        <option value="fr-FR">🇫🇷 法语</option>
        <option value="de-DE">🇩🇪 德语</option>
      </select>
    </div>
    <div>
      <label>按国家筛选</label>
      <select id="bulk-country-filter">
        <option value="">全部国家</option>
        <option value="CN">🇨🇳 中国</option>
        <option value="US">🇺🇸 美国</option>
        <option value="GB">🇬🇧 英国</option>
        <option value="JP">🇯🇵 日本</option>
        <option value="KR">🇰🇷 韩国</option>
        <option value="FR">🇫🇷 法国</option>
        <option value="DE">🇩🇪 德国</option>
      </select>
    </div>
    <div>
      <label>操作类型</label>
      <select id="bulk-operation">
        <option value="tag">标记标签</option>
        <option value="language">更新语言</option>
        <option value="country">更新国家</option>
        <option value="list">添加到列表</option>
      </select>
    </div>
    <div>
      <label>添加标签（逗号分隔）</label>
      <input id="bulk-add-tags" placeholder="VIP, 企业" />
    </div>
    <div>
      <label>移除标签（逗号分隔）</label>
      <input id="bulk-remove-tags" placeholder="新客" />
    </div>
    <div>
      <label>设置语言</label>
      <select id="bulk-set-language">
        <option value="">保持不变</option>
        <option value="zh-CN">🇨🇳 简体中文</option>
        <option value="en-US">🇺🇸 英语(美国)</option>
        <option value="en-GB">🇬🇧 英语(英国)</option>
        <option value="ja-JP">🇯🇵 日语</option>
        <option value="ko-KR">🇰🇷 韩语</option>
        <option value="fr-FR">🇫🇷 法语</option>
        <option value="de-DE">🇩🇪 德语</option>
      </select>
    </div>
    <div>
      <label>设置国家</label>
      <select id="bulk-set-country">
        <option value="">保持不变</option>
        <option value="CN">🇨🇳 中国</option>
        <option value="US">🇺🇸 美国</option>
        <option value="GB">🇬🇧 英国</option>
        <option value="JP">🇯🇵 日本</option>
        <option value="KR">🇰🇷 韩国</option>
        <option value="FR">🇫🇷 法国</option>
        <option value="DE">🇩🇪 德国</option>
      </select>
    </div>
  </div>
  <div class="toolbar">
    <button class="btn" onclick="executeBulkOperation()">执行批量操作</button>
    <button class="btn secondary" onclick="hideBulkTag()">关闭</button>
  </div>
</div>

<!-- 联系人详情弹窗 -->
<div id="contact-detail" class="card" style="display:none; max-width:700px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">👤 联系人详情</div>
  <div class="grid cols-2">
    <div>
      <label>邮箱地址</label>
      <div id="detail-email" style="font-weight: 600; padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>姓名</label>
      <div id="detail-name" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>公司</label>
      <div id="detail-company" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>标记语言</label>
      <div id="detail-language" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>国家/地区</label>
      <div id="detail-country" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>省/州/地区</label>
      <div id="detail-region" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>城市</label>
      <div id="detail-city" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>时区</label>
      <div id="detail-timezone" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>标签</label>
      <div id="detail-tags" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>同意状态</label>
      <div id="detail-consent" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>活跃状态</label>
      <div id="detail-status" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
    <div>
      <label>创建时间</label>
      <div id="detail-created" style="padding: 8px; background: var(--bg-secondary); border-radius: 4px;"></div>
    </div>
  </div>
  <div class="divider"></div>
  <div class="mini">📊 行为指标</div>
  <div class="tabs" data-tab-group="contact-detail">
    <button class="tab active" data-tab-group="contact-detail" data-tab-target="#tab-metrics">指标</button>
    <button class="tab" data-tab-group="contact-detail" data-tab-target="#tab-activity">时间线</button>
    <button class="tab" data-tab-group="contact-detail" data-tab-target="#tab-lists">列表/标签</button>
  </div>
  <div id="tab-metrics" data-tab-panel="contact-detail" class="grid cols-3">
    <div>
      <label>打开率</label>
      <div style="font-size:13px;">
        30天：<span id="detail-open-30">—</span>；90天：<span id="detail-open-90">—</span>；总：<span id="detail-open-all">—</span>
      </div>
    </div>
    <div>
      <label>回复率</label>
      <div style="font-size:13px;">
        30天：<span id="detail-reply-30">—</span>；总：<span id="detail-reply-all">—</span>
      </div>
    </div>
    <div>
      <label>拒收率</label>
      <div style="font-size:13px;">
        总：<span id="detail-bounce-all">—</span>
      </div>
    </div>
  </div>
  <div id="tab-activity" data-tab-panel="contact-detail" style="display:none">
    <div class="mini">最近活动</div>
    <ul style="margin:0;padding-left:18px;color:var(--text-secondary);font-size:13px">
      <li>2025-07-12 10:21 打开：夏日促销</li>
      <li>2025-07-10 09:03 点击：欢迎礼包 CTA</li>
      <li>2025-07-05 08:15 发送：欢迎邮件</li>
    </ul>
  </div>
  <div id="tab-lists" data-tab-panel="contact-detail" style="display:none">
    <div class="mini">所属列表与标签</div>
    <div id="detail-lists" class="hint">VIP客户、复购客户</div>
    <div id="detail-tags-panel" class="hint">标签：VIP, 复购客户</div>
  </div>
  <div class="toolbar">
    <button class="btn" onclick="editContact()">✏️ 编辑</button>
    <button class="btn secondary" onclick="hideContactDetail()">关闭</button>
  </div>
</div>

<!-- 联系人备注抽屉 -->
<div id="contact-notes" class="card" style="display:none; max-width:600px; position:fixed; top:12%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📝 联系人备注</div>
  <div id="notes-list" class="hint">暂无备注</div>
  <div class="row">
    <div>
      <label>新增备注</label>
      <input id="note-input" placeholder="输入备注..." />
    </div>
    <div class="flex-0">
      <button class="btn" onclick="addNote()">添加</button>
    </div>
  </div>
  <div class="toolbar">
    <button class="btn secondary" data-close="contact-notes">关闭</button>
  </div>
</div>

<!-- 新建联系人面板 -->
<div id="create-contact" class="card" style="display:none; max-width:800px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">➕ 新建联系人</div>
  <div class="grid cols-2">
    <div>
      <label>邮箱地址 *</label>
      <input type="email" id="contact-email" placeholder="<EMAIL>" />
    </div>
    <div>
      <label>姓名</label>
      <input id="contact-name" placeholder="张三" />
    </div>
    <div>
      <label>公司</label>
      <input id="contact-company" placeholder="公司名称" />
    </div>
    <div>
      <label>标记语言 *</label>
      <select id="contact-language">
        <option value="zh-CN">🇨🇳 简体中文 (zh-CN)</option>
        <option value="zh-TW">🇹🇼 繁体中文 (zh-TW)</option>
        <option value="zh-HK">🇭🇰 繁体中文 (zh-HK)</option>
        <option value="en-US">🇺🇸 英语 (en-US)</option>
        <option value="en-GB">🇬🇧 英语 (en-GB)</option>
        <option value="ja-JP">🇯🇵 日语 (ja-JP)</option>
        <option value="ko-KR">🇰🇷 韩语 (ko-KR)</option>
        <option value="fr-FR">🇫🇷 法语 (fr-FR)</option>
        <option value="de-DE">🇩🇪 德语 (de-DE)</option>
        <option value="es-ES">🇪🇸 西班牙语 (es-ES)</option>
        <option value="pt-BR">🇧🇷 葡萄牙语 (pt-BR)</option>
        <option value="ru-RU">🇷🇺 俄语 (ru-RU)</option>
        <option value="ar-SA">🇸🇦 阿拉伯语 (ar-SA)</option>
        <option value="hi-IN">🇮🇳 印地语 (hi-IN)</option>
        <option value="th-TH">🇹🇭 泰语 (th-TH)</option>
      </select>
    </div>
    <div>
      <label>国家/地区 *</label>
      <select id="country-select">
        <option value="">请选择国家</option>
        <option value="CN">🇨🇳 中国</option>
        <option value="TW">🇹🇼 台湾</option>
        <option value="HK">🇭🇰 香港</option>
        <option value="US">🇺🇸 美国</option>
        <option value="GB">🇬🇧 英国</option>
        <option value="JP">🇯🇵 日本</option>
        <option value="KR">🇰🇷 韩国</option>
        <option value="FR">🇫🇷 法国</option>
        <option value="DE">🇩🇪 德国</option>
        <option value="ES">🇪🇸 西班牙</option>
        <option value="BR">🇧🇷 巴西</option>
        <option value="RU">🇷🇺 俄罗斯</option>
        <option value="SA">🇸🇦 沙特阿拉伯</option>
        <option value="IN">🇮🇳 印度</option>
        <option value="TH">🇹🇭 泰国</option>
        <option value="CA">🇨🇦 加拿大</option>
        <option value="AU">🇦🇺 澳大利亚</option>
        <option value="SG">🇸🇬 新加坡</option>
        <option value="MY">🇲🇾 马来西亚</option>
        <option value="ID">🇮🇩 印度尼西亚</option>
        <option value="PH">🇵🇭 菲律宾</option>
        <option value="VN">🇻🇳 越南</option>
        <option value="IT">🇮🇹 意大利</option>
        <option value="NL">🇳🇱 荷兰</option>
        <option value="SE">🇸🇪 瑞典</option>
        <option value="NO">🇳🇴 挪威</option>
        <option value="DK">🇩🇰 丹麦</option>
        <option value="FI">🇫🇮 芬兰</option>
        <option value="CH">🇨🇭 瑞士</option>
        <option value="AT">🇦🇹 奥地利</option>
        <option value="BE">🇧🇪 比利时</option>
        <option value="IE">🇮🇪 爱尔兰</option>
        <option value="NZ">🇳🇿 新西兰</option>
        <option value="MX">🇲🇽 墨西哥</option>
        <option value="AR">🇦🇷 阿根廷</option>
        <option value="CL">🇨🇱 智利</option>
        <option value="CO">🇨🇴 哥伦比亚</option>
        <option value="PE">🇵🇪 秘鲁</option>
        <option value="ZA">🇿🇦 南非</option>
        <option value="EG">🇪🇬 埃及</option>
        <option value="TR">🇹🇷 土耳其</option>
        <option value="IL">🇮🇱 以色列</option>
        <option value="AE">🇦🇪 阿联酋</option>
        <option value="QA">🇶🇦 卡塔尔</option>
        <option value="KW">🇰🇼 科威特</option>
        <option value="BH">🇧🇭 巴林</option>
        <option value="OM">🇴🇲 阿曼</option>
        <option value="JO">🇯🇴 约旦</option>
        <option value="LB">🇱🇧 黎巴嫩</option>
        <option value="SY">🇸🇾 叙利亚</option>
        <option value="IQ">🇮🇶 伊拉克</option>
        <option value="IR">🇮🇷 伊朗</option>
        <option value="PK">🇵🇰 巴基斯坦</option>
        <option value="BD">🇧🇩 孟加拉国</option>
        <option value="LK">🇱🇰 斯里兰卡</option>
        <option value="NP">🇳🇵 尼泊尔</option>
        <option value="MM">🇲🇲 缅甸</option>
        <option value="KH">🇰🇭 柬埔寨</option>
        <option value="LA">🇱🇦 老挝</option>
        <option value="MN">🇲🇳 蒙古</option>
        <option value="KZ">🇰🇿 哈萨克斯坦</option>
        <option value="UZ">🇺🇿 乌兹别克斯坦</option>
        <option value="KG">🇰🇬 吉尔吉斯斯坦</option>
        <option value="TJ">🇹🇯 塔吉克斯坦</option>
        <option value="TM">🇹🇲 土库曼斯坦</option>
        <option value="AF">🇦🇫 阿富汗</option>
        <option value="GE">🇬🇪 格鲁吉亚</option>
        <option value="AM">🇦🇲 亚美尼亚</option>
        <option value="AZ">🇦🇿 阿塞拜疆</option>
        <option value="BY">🇧🇾 白俄罗斯</option>
        <option value="MD">🇲🇩 摩尔多瓦</option>
        <option value="UA">🇺🇦 乌克兰</option>
        <option value="PL">🇵🇱 波兰</option>
        <option value="CZ">🇨🇿 捷克</option>
        <option value="SK">🇸🇰 斯洛伐克</option>
        <option value="HU">🇭🇺 匈牙利</option>
        <option value="RO">🇷🇴 罗马尼亚</option>
        <option value="BG">🇧🇬 保加利亚</option>
        <option value="HR">🇭🇷 克罗地亚</option>
        <option value="SI">🇸🇮 斯洛文尼亚</option>
        <option value="RS">🇷🇸 塞尔维亚</option>
        <option value="BA">🇧🇦 波斯尼亚和黑塞哥维那</option>
        <option value="ME">🇲🇪 黑山</option>
        <option value="MK">🇲🇰 北马其顿</option>
        <option value="AL">🇦🇱 阿尔巴尼亚</option>
        <option value="GR">🇬🇷 希腊</option>
        <option value="CY">🇨🇾 塞浦路斯</option>
        <option value="MT">🇲🇹 马耳他</option>
        <option value="PT">🇵🇹 葡萄牙</option>
        <option value="LU">🇱🇺 卢森堡</option>
        <option value="IS">🇮🇸 冰岛</option>
        <option value="EE">🇪🇪 爱沙尼亚</option>
        <option value="LV">🇱🇻 拉脱维亚</option>
        <option value="LT">🇱🇹 立陶宛</option>
        <option value="other">🌍 其他国家</option>
      </select>
    </div>
    <div>
      <label>省/州/地区</label>
      <input id="region-input" placeholder="省/州/地区名称" />
    </div>
    <div>
      <label>城市</label>
      <input id="city-input" placeholder="城市名称" />
    </div>
    <div>
      <label>标签（逗号分隔）</label>
      <input id="contact-tags" placeholder="VIP, 新客户, 企业" />
    </div>
    <div>
      <label>同意来源</label>
      <select id="contact-source">
        <option value="form">表单订阅</option>
        <option value="import">线下导入</option>
        <option value="api">API 同步</option>
        <option value="manual">手动添加</option>
        <option value="other">其他渠道</option>
      </select>
    </div>
    <div>
      <label>备注</label>
      <input id="contact-notes" placeholder="额外信息..." />
    </div>
  </div>
  <div class="toolbar">
    <button class="btn" onclick="saveContact()">💾 保存联系人</button>
    <button class="btn secondary" onclick="hideCreateContact()">❌ 取消</button>
  </div>
</div>

<script>
// Toasts
function toast(msg, type='success', title='提示'){
  let c = document.querySelector('.toast-container');
  if(!c){ c = document.createElement('div'); c.className='toast-container'; document.body.appendChild(c); }
  const t = document.createElement('div');
  t.className = 'toast ' + (type||'');
  t.innerHTML = `<div class="title">${title}</div><div class="msg">${msg}</div>`;
  c.appendChild(t);
  setTimeout(()=>{ t.remove(); if(c.childElementCount===0) c.remove(); }, 3200);
}

function showImportPanel() {
  document.getElementById('import-contacts').style.display = '';
}
function hideImportPanel() {
  document.getElementById('import-contacts').style.display = 'none';
}
function showImportHistory() {
  document.getElementById('import-history').style.display = '';
}
function hideImportHistory() {
  document.getElementById('import-history').style.display = 'none';
}

function simulatePlan(){
  const rows = [
    { source:'Email', target:'email', conf:0.98, type:'email' },
    { source:'姓名', target:'name', conf:0.92, type:'text' },
    { source:'公司', target:'company', conf:0.76, type:'text' },
    { source:'语言', target:'preferred_language', conf:0.95, type:'enum' },
    { source:'国家', target:'location.country', conf:0.90, type:'text' },
    { source:'城市', target:'location.city', conf:0.88, type:'text' },
    { source:'是否VIP', target:'custom:vip', conf:0.64, type:'bool' },
    { source:'生日', target:'custom:birth_date', conf:0.58, type:'date' }
  ];
  const tbody = document.getElementById('mapping-rows');
  tbody.innerHTML = rows.map(r=>`
    <tr>
      <td>${r.source}</td>
      <td>
        <select>
          <option ${r.target==='email'?'selected':''}>email</option>
          <option ${r.target==='name'?'selected':''}>name</option>
          <option ${r.target==='company'?'selected':''}>company</option>
          <option ${r.target==='preferred_language'?'selected':''}>preferred_language</option>
          <option ${r.target==='location.country'?'selected':''}>location.country</option>
          <option ${r.target==='location.city'?'selected':''}>location.city</option>
          <option ${r.target==='custom:vip'?'selected':''}>custom:vip</option>
          <option ${r.target==='custom:birth_date'?'selected':''}>custom:birth_date</option>
          <option>创建为新自定义字段...</option>
        </select>
      </td>
      <td>
        <span class="pill ${r.conf>=0.9?'ok':(r.conf>=0.7?'':'warnTxt')}">${(r.conf*100).toFixed(0)}%</span>
      </td>
      <td><span class="pill">${r.type}</span></td>
      <td><button class="btn secondary" style="padding:6px 12px;font-size:12px;">编辑字段</button></td>
    </tr>
  `).join('');
}

function simulatePreview(){
  alert('预校验完成：预计新增 10,240，更新 1,320。问题：非法邮箱 12，缺失 email 3，枚举越界 7。');
}

function startImport(){
  toast('已提交导入任务，任务ID=job_123','success','已提交');
}

function simulateBulkTag(){
  alert('批量标签任务已创建，task_456，后台将按筛选结果异步执行。');
}

function executeBulkOperation(){
  const scope = document.getElementById('bulk-scope').value;
  const languageFilter = document.getElementById('bulk-language-filter').value;
  const countryFilter = document.getElementById('bulk-country-filter').value;
  const operation = document.getElementById('bulk-operation').value;
  const addTags = document.getElementById('bulk-add-tags').value;
  const removeTags = document.getElementById('bulk-remove-tags').value;
  const setLanguage = document.getElementById('bulk-set-language').value;
  const setCountry = document.getElementById('bulk-set-country').value;
  
  let message = `批量操作详情：\n`;
  message += `范围：${scope}\n`;
  if(languageFilter) message += `语言筛选：${languageFilter}\n`;
  if(countryFilter) message += `国家筛选：${countryFilter}\n`;
  message += `操作类型：${operation}\n`;
  
  if(operation === 'tag') {
    if(addTags) message += `添加标签：${addTags}\n`;
    if(removeTags) message += `移除标签：${removeTags}\n`;
  } else if(operation === 'language' && setLanguage) {
    message += `设置语言：${setLanguage}\n`;
  } else if(operation === 'country' && setCountry) {
    message += `设置国家：${setCountry}\n`;
  }
  
  message += `\n任务已创建，后台将异步执行。`;
  toast(message,'success','批量任务已创建');
}

function hideBulkTag(){
  document.getElementById('bulk-tag').style.display = 'none';
}

// 显示批量操作面板
function showBulkTag(){
  document.getElementById('bulk-tag').style.display = '';
}

// 显示联系人详情
function showContactDetail(email) {
  // 模拟联系人数据（含行为指标）
  const contactData = {
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'Alice Johnson',
      company: 'Tech Corp',
      language: '🇺🇸 en-US',
      country: '🇺🇸 美国',
      region: 'New York',
      city: 'New York',
      timezone: 'America/New_York',
      tags: 'VIP, 复购客户',
      consent: '✅ 已同意',
      status: '活跃',
      created: '2025-07-01',
      metrics: { open30:'46.2%', open90:'52.1%', openAll:'38.4%', reply30:'2.3%', replyAll:'1.2%', bounceAll:'0.0%' }
    },
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'Bob Smith',
      company: 'Startup Inc',
      language: '🇨🇳 zh-CN',
      country: '🇨🇳 中国',
      region: '北京',
      city: '北京',
      timezone: 'Asia/Shanghai',
      tags: '新客',
      consent: '⚠️ 未明确',
      status: '退订',
      created: '2025-05-18',
      metrics: { open30:'8.3%', open90:'12.5%', openAll:'2.1%', reply30:'0.0%', replyAll:'0.0%', bounceAll:'0.2%' }
    },
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'Carol Davis',
      company: 'Enterprise Ltd',
      language: '🇯🇵 ja-JP',
      country: '🇯🇵 日本',
      region: '东京都',
      city: '东京',
      timezone: 'Asia/Tokyo',
      tags: '企业客户',
      consent: '✅ 已同意',
      status: '活跃',
      created: '2025-06-15',
      metrics: { open30:'41.5%', open90:'47.8%', openAll:'35.2%', reply30:'1.1%', replyAll:'0.7%', bounceAll:'0.0%' }
    },
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'David Wilson',
      company: 'UK Corp',
      language: '🇬🇧 en-GB',
      country: '🇬🇧 英国',
      region: 'England',
      city: 'London',
      timezone: 'Europe/London',
      tags: '欧洲客户',
      consent: '✅ 已同意',
      status: '活跃',
      created: '2025-06-20',
      metrics: { open30:'39.8%', open90:'44.0%', openAll:'31.4%', reply30:'0.8%', replyAll:'0.6%', bounceAll:'0.0%' }
    },
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'Emma Brown',
      company: 'FR Co',
      language: '🇫🇷 fr-FR',
      country: '🇫🇷 法国',
      region: 'Île-de-France',
      city: 'Paris',
      timezone: 'Europe/Paris',
      tags: '法语客户',
      consent: '✅ 已同意',
      status: '活跃',
      created: '2025-06-25',
      metrics: { open30:'43.7%', open90:'49.2%', openAll:'36.0%', reply30:'1.3%', replyAll:'0.9%', bounceAll:'0.0%' }
    },
    '<EMAIL>': {
      email: '<EMAIL>',
      name: 'Hans Mueller',
      company: 'DE AG',
      language: '🇩🇪 de-DE',
      country: '🇩🇪 德国',
      region: 'Berlin',
      city: 'Berlin',
      timezone: 'Europe/Berlin',
      tags: '德语客户',
      consent: '✅ 已同意',
      status: '活跃',
      created: '2025-06-28',
      metrics: { open30:'40.2%', open90:'45.1%', openAll:'33.5%', reply30:'0.7%', replyAll:'0.5%', bounceAll:'0.0%' }
    }
  };
  
  const contact = contactData[email] || {
    email: email,
    name: '未知',
    company: '未知',
    language: '🇨🇳 zh-CN',
    country: '🇨🇳 中国',
    region: '未知',
    city: '未知',
    timezone: 'Asia/Shanghai',
    tags: '无',
    consent: '未知',
    status: '未知',
    created: '未知',
    metrics: { open30:'—', open90:'—', openAll:'—', reply30:'—', replyAll:'—', bounceAll:'—' }
  };
  
  // 填充详情数据
  document.getElementById('detail-email').textContent = contact.email;
  document.getElementById('detail-name').textContent = contact.name;
  document.getElementById('detail-company').textContent = contact.company;
  document.getElementById('detail-language').textContent = contact.language;
  document.getElementById('detail-country').textContent = contact.country;
  document.getElementById('detail-region').textContent = contact.region;
  document.getElementById('detail-city').textContent = contact.city;
  document.getElementById('detail-timezone').textContent = contact.timezone;
  document.getElementById('detail-tags').textContent = contact.tags;
  document.getElementById('detail-consent').textContent = contact.consent;
  document.getElementById('detail-status').textContent = contact.status;
  document.getElementById('detail-created').textContent = contact.created;
  document.getElementById('detail-open-30').textContent = contact.metrics.open30;
  document.getElementById('detail-open-90').textContent = contact.metrics.open90;
  document.getElementById('detail-open-all').textContent = contact.metrics.openAll;
  document.getElementById('detail-reply-30').textContent = contact.metrics.reply30;
  document.getElementById('detail-reply-all').textContent = contact.metrics.replyAll;
  document.getElementById('detail-bounce-all').textContent = contact.metrics.bounceAll;
  
  // 显示详情弹窗
  document.getElementById('contact-detail').style.display = '';
}

// 隐藏联系人详情
function hideContactDetail() {
  document.getElementById('contact-detail').style.display = 'none';
}

// 编辑联系人
function editContact() {
  alert('编辑功能开发中...');
}

function addNote(){
  const list = document.getElementById('notes-list');
  const input = document.getElementById('note-input');
  const val = (input.value||'').trim();
  if(!val) return;
  const item = document.createElement('div');
  item.className = 'pill';
  item.textContent = val;
  list.appendChild(item);
  input.value = '';
}

function saveContact() {
  const email = document.getElementById('contact-email').value;
  const name = document.getElementById('contact-name').value;
  const company = document.getElementById('contact-company').value;
  const language = document.getElementById('contact-language').value;
  const country = document.getElementById('country-select').value;
  const region = document.getElementById('region-input').value;
  const city = document.getElementById('city-input').value;
  const tags = document.getElementById('contact-tags').value;
  const source = document.getElementById('contact-source').value;
  const notes = document.getElementById('contact-notes').value;

  if (!email || !name) {
    alert('邮箱地址和姓名不能为空！');
    return;
  }

  // 模拟保存逻辑
  alert(`保存联系人: ${email}, ${name}, ${company}, ${language}, ${country}, ${region}, ${city}, ${tags}, ${source}, ${notes}`);
  // 实际应用中会调用后端API保存联系人
}

function hideCreateContact() {
  document.getElementById('create-contact').style.display = 'none';
}

// 显示创建联系人面板
function showCreateContact() {
  document.getElementById('create-contact').style.display = '';
  // 重置表单
  document.getElementById('contact-email').value = '';
  document.getElementById('contact-name').value = '';
  document.getElementById('contact-company').value = '';
  document.getElementById('contact-language').value = 'zh-CN';
  document.getElementById('country-select').value = '';
  document.getElementById('region-input').value = '';
  document.getElementById('city-input').value = '';
  document.getElementById('contact-tags').value = '';
  document.getElementById('contact-source').value = 'form';
  document.getElementById('contact-notes').value = '';
}

// 根据语言自动设置默认国家
document.getElementById('contact-language')?.addEventListener('change', function() {
  const language = this.value;
  const countrySelect = document.getElementById('country-select');
  
  // 语言到国家的映射
  const languageToCountry = {
    'zh-CN': 'CN',
    'zh-TW': 'TW', 
    'zh-HK': 'HK',
    'en-US': 'US',
    'en-GB': 'GB',
    'ja-JP': 'JP',
    'ko-KR': 'KR',
    'fr-FR': 'FR',
    'de-DE': 'DE',
    'es-ES': 'ES',
    'pt-BR': 'BR',
    'ru-RU': 'RU',
    'ar-SA': 'SA',
    'hi-IN': 'IN',
    'th-TH': 'TH'
  };
  
  if (languageToCountry[language]) {
    countrySelect.value = languageToCountry[language];
  }
});

// 根据国家自动设置默认语言
document.getElementById('country-select')?.addEventListener('change', function() {
  const country = this.value;
  const languageSelect = document.getElementById('contact-language');
  
  // 国家到语言的映射
  const countryToLanguage = {
    'CN': 'zh-CN',
    'TW': 'zh-TW',
    'HK': 'zh-HK', 
    'US': 'en-US',
    'GB': 'en-GB',
    'JP': 'ja-JP',
    'KR': 'ko-KR',
    'FR': 'fr-FR',
    'DE': 'de-DE',
    'ES': 'es-ES',
    'BR': 'pt-BR',
    'RU': 'ru-RU',
    'SA': 'ar-SA',
    'IN': 'hi-IN',
    'TH': 'th-TH'
  };
  
  if (countryToLanguage[country]) {
    languageSelect.value = countryToLanguage[country];
  }
});

// 模拟队列滞后指标
setInterval(()=>{
  const el = document.getElementById('queue-lag');
  if(!el) return;
  el.textContent = Math.floor(Math.random()*50)+ ' msgs';
}, 3000);

// 地理位置字段联动
document.getElementById('country-select')?.addEventListener('change', function() {
  const country = this.value;
  const regionInput = document.getElementById('region-input');
  const cityInput = document.getElementById('city-input');
  
  if(country === 'CN') {
    regionInput.placeholder = '省份名称';
    cityInput.placeholder = '城市名称';
  } else if(country === 'US') {
    regionInput.placeholder = 'State';
    cityInput.placeholder = 'City';
  } else {
    regionInput.placeholder = '省/州/地区名称';
    cityInput.placeholder = '城市名称';
  }
});

// 语言分布统计模拟
function showLanguageStats() {
  const stats = {
    'zh-CN': 45678,
    'en-US': 23456,
    'ja-JP': 12345,
    'ko-KR': 8765,
    'fr-FR': 5432,
    'de-DE': 3456,
    '其他': 23456
  };
  
  let message = '语言分布统计：\n';
  for(const [lang, count] of Object.entries(stats)) {
    const percentage = ((count / 128532) * 100).toFixed(1);
    message += `${lang}: ${count} (${percentage}%)\n`;
  }
  
  alert(message);
}

// 地理位置分布统计模拟
function showLocationStats() {
  const stats = {
    'CN': 56789,
    'US': 23456,
    'JP': 12345,
    'KR': 8765,
    'GB': 5432,
    'DE': 3456,
    '其他': 17189
  };
  
  let message = '地理位置分布统计：\n';
  for(const [country, count] of Object.entries(stats)) {
    const percentage = ((count / 128532) * 100).toFixed(1);
    message += `${country}: ${count} (${percentage}%)\n`;
  }
  
  alert(message);
}

// 通用 data-open / data-close 事件委托（避免为每个按钮写重复逻辑）
document.addEventListener('click', function(e){
  const opener = e.target.closest('[data-open]');
  if (opener) {
    const id = opener.getAttribute('data-open');
    const el = document.getElementById(id);
    if (el) el.style.display = '';
  }
  const closer = e.target.closest('[data-close]');
  if (closer) {
    const id = closer.getAttribute('data-close');
    const el = document.getElementById(id);
    if (el) el.style.display = 'none';
  }
});

// 在联系人列表表格前面动态插入选择列与表头全选
function initSelectionColumn(){
  const tables = Array.from(document.querySelectorAll('table.table'));
  let contactTable = null;
  for (const t of tables){
    const headText = (t.querySelector('thead')?.textContent || '').trim();
    if (headText.includes('联系人信息') && headText.includes('标记语言')){ contactTable = t; break; }
  }
  if (!contactTable) return;
  const theadRow = contactTable.tHead?.rows?.[0];
  if (theadRow && !document.getElementById('chk-all')){
    const th = document.createElement('th');
    th.innerHTML = '<input type="checkbox" id="chk-all" onchange="toggleAllContacts()"/>';
    theadRow.insertBefore(th, theadRow.firstElementChild);
  }
  const rows = Array.from(contactTable.tBodies?.[0]?.rows || []);
  rows.forEach(r => {
    if (!r.querySelector('input.chk')){
      const td = document.createElement('td');
      td.innerHTML = '<input type="checkbox" class="chk" onchange="updateSelectedCount()" />';
      r.insertBefore(td, r.firstElementChild);
    }
  });
}

function updateSelectedCount(){
  const count = document.querySelectorAll('input.chk:checked').length;
  const el = document.getElementById('selected-count');
  if (el) el.textContent = String(count);
}

function toggleAllContacts(){
  const master = document.getElementById('chk-all');
  const boxes = document.querySelectorAll('input.chk');
  boxes.forEach(b => { b.checked = !!master?.checked; });
  updateSelectedCount();
}

// 简易筛选（基于DOM文本）
function applyFilters(){
  const kw = (document.getElementById('search-keyword')?.value || '').trim().toLowerCase();
  const status = document.getElementById('filter-status')?.value || '全部状态';
  const list = document.getElementById('filter-list')?.value || '全部列表';
  const langText = document.getElementById('filter-language')?.value || document.getElementById('filter-language')?.options[document.getElementById('filter-language').selectedIndex]?.text || '全部语言';
  const countryText = document.getElementById('filter-country')?.value || document.getElementById('filter-country')?.options[document.getElementById('filter-country').selectedIndex]?.text || '全部地区';

  // 解析语言代码（形如 英语 (en-US) 提取 en-US）
  let langCode = '';
  const match = /\(([^)]+)\)/.exec(langText);
  if (match && match[1]) langCode = match[1];

  const countryMap = { '中国大陆':'中国', '台湾':'台湾', '香港':'香港', '美国':'美国', '日本':'日本', '韩国':'韩国', '欧洲':'欧洲' };
  const countryKey = countryMap[countryText] || '';

  const tables = Array.from(document.querySelectorAll('table.table'));
  let contactTable = null;
  for (const t of tables){
    const headText = (t.querySelector('thead')?.textContent || '').trim();
    if (headText.includes('联系人信息') && headText.includes('标记语言')){ contactTable = t; break; }
  }
  if (!contactTable) return;

  const rows = Array.from(contactTable.tBodies?.[0]?.rows || []);
  rows.forEach(r => {
    const text = r.textContent?.toLowerCase() || '';
    let visible = true;
    if (kw && !text.includes(kw)) visible = false;
    if (status && status !== '全部状态' && !r.textContent.includes(status)) visible = false;
    if (list && list !== '全部列表') {
      // 示例页未提供列表字段，这里保留接口占位（不筛掉）
    }
    if (langCode && !r.textContent.includes(langCode)) visible = false;
    if (countryKey && !r.textContent.includes(countryKey)) visible = false;
    r.style.display = visible ? '' : 'none';
  });

  // 筛选后重置选择
  const master = document.getElementById('chk-all');
  if (master) master.checked = false;
  updateSelectedCount();
}

// 页面就绪后注入选择列
document.addEventListener('DOMContentLoaded', function(){
  initSelectionColumn();
  initSorting();
  renderPagination(1, Math.ceil(128532/25));
});

// 简易排序（示例：按文本排序）
function initSorting(){
  const table = document.getElementById('contacts-table');
  if(!table) return;
  const headers = table.querySelectorAll('th.sortable');
  headers.forEach((th, idx)=>{
    th.addEventListener('click', ()=>{
      const tbody = table.tBodies[0];
      const rows = Array.from(tbody.rows);
      const asc = !th.classList.contains('active') || !th.dataset.asc || th.dataset.asc==='false';
      headers.forEach(h=>{ h.classList.remove('active'); h.removeAttribute('data-asc'); });
      th.classList.add('active'); th.dataset.asc = asc ? 'true' : 'false';
      const dir = asc ? 1 : -1;
      rows.sort((a,b)=>{
        const av = (a.cells[idx].innerText||'').trim().toLowerCase();
        const bv = (b.cells[idx].innerText||'').trim().toLowerCase();
        return av.localeCompare(bv) * dir;
      });
      rows.forEach(r=>tbody.appendChild(r));
    });
  });
}

// 简易分页（静态示例）
function renderPagination(page, total){
  const wrap = document.getElementById('pages');
  if(!wrap) return;
  wrap.innerHTML='';
  const make = (p, label=p)=>{
    const b = document.createElement('button');
    b.className = 'page-btn' + (p===page?' active':'');
    b.textContent = label;
    b.onclick = ()=>{ toast(`切换到第 ${p} 页`,'success','分页'); };
    return b;
  };
  wrap.appendChild(make(Math.max(1, page-1), '‹'));
  for(let p=Math.max(1,page-2); p<=Math.min(total,page+2); p++) wrap.appendChild(make(p));
  wrap.appendChild(make(Math.min(total, page+1), '›'));
}
</script>


