<div class="grid cols-2">
  <div class="card">
    <div class="mini">API Keys</div>
    <div class="row"><div><label>备注</label><input placeholder="后端服务调用"/></div><div><label>权限</label><select><option>发送/联系人/报表</option></select></div><div style="flex:0"><button class="btn">创建</button></div></div>
    <table class="table"><thead><tr><th>标识</th><th>Scopes</th><th>创建时间</th></tr></thead><tbody><tr><td>pk_live_xxx</td><td>send,contacts</td><td>2025-07-01</td></tr></tbody></table>
  </div>
  <div class="card">
    <div class="mini">Webhooks</div>
    <div class="row"><div><label>回调 URL</label><input placeholder="https://example.com/webhook"/></div><div style="flex:0"><button class="btn secondary">添加</button></div></div>
    <div class="row"><div><label>事件</label><select><option>delivered</option><option>opened</option><option>clicked</option><option>bounced</option><option>complained</option></select></div></div>
    <table class="table"><thead><tr><th>URL</th><th>事件</th><th>状态</th></tr></thead><tbody><tr><td>https://ex/webhook</td><td>delivered,opened</td><td class="ok">启用</td></tr></tbody></table>
  </div>
</div>
<div class="card">
  <div class="mini">第三方连接</div>
  <table class="table"><thead><tr><th>集成</th><th>状态</th><th>说明</th></tr></thead>
    <tbody><tr><td>Shopify</td><td>未连接</td><td>同步顾客与订单事件</td></tr><tr><td>Zapier</td><td>已连接</td><td>自动化触发器</td></tr></tbody></table>
  </div>


