<div class="toolbar">
  <button class="btn" onclick="refreshExtensions()">🔄 刷新状态</button>
  <button class="btn secondary" onclick="showExtensionMarket()">🛒 扩展市场</button>
  <button class="btn ghost" onclick="showExtensionLogs()">📄 扩展日志</button>
  <div class="hint">扩展功能增强邮件营销能力，提供个性化、自动化和优化工具</div>
</div>

<div class="grid cols-3">
  <div class="card">
    <div class="mini">⏰ 发送时间优化</div>
    <div class="hint">基于历史行为分析，自动选择最佳发送时间</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="ok">✅ 已启用</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>优化效果</span>
        <span style="color: var(--success);">+15.2%</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>覆盖用户</span>
        <span>89,432</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('send-time-optimization')">⚙️ 配置</button>
      <button class="btn ghost" onclick="disableExtension('send-time-optimization')">禁用</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">📱 预览收件箱</div>
    <div class="hint">各邮件客户端预览截图，确保兼容性</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="ok">✅ 已启用</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>支持客户端</span>
        <span>12个</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>预览准确率</span>
        <span>98.7%</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('inbox-preview')">⚙️ 配置</button>
      <button class="btn ghost" onclick="disableExtension('inbox-preview')">禁用</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🧩 个性化片段库</div>
    <div class="hint">常用内容区块复用，提升邮件制作效率</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="ok">✅ 已启用</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>片段数量</span>
        <span>156</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>使用次数</span>
        <span>2,847</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('personalization-blocks')">⚙️ 配置</button>
      <button class="btn ghost" onclick="disableExtension('personalization-blocks')">禁用</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🎯 智能A/B测试</div>
    <div class="hint">自动测试邮件元素，优化转化率</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="warnTxt">⚠️ 配置中</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>测试项目</span>
        <span>8个</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>优化效果</span>
        <span>--</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('smart-ab-testing')">⚙️ 配置</button>
      <button class="btn" onclick="enableExtension('smart-ab-testing')">启用</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🌍 多语言本地化</div>
    <div class="hint">自动检测用户语言，提供本地化内容</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="err">❌ 已禁用</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>支持语言</span>
        <span>24种</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>本地化率</span>
        <span>--</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('multi-language')">⚙️ 配置</button>
      <button class="btn" onclick="enableExtension('multi-language')">启用</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">📊 高级分析</div>
    <div class="hint">深度用户行为分析，提供个性化建议</div>
    <div style="margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>状态</span>
        <span class="err">❌ 已禁用</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>分析维度</span>
        <span>15个</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>预测准确率</span>
        <span>--</span>
      </div>
    </div>
    <div class="toolbar">
      <button class="btn secondary" onclick="configureExtension('advanced-analytics')">⚙️ 配置</button>
      <button class="btn" onclick="enableExtension('advanced-analytics')">启用</button>
    </div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📈 扩展性能统计</div>
    <div class="hint">各扩展功能的性能表现和影响分析</div>
    
    <table class="table">
      <thead>
        <tr>
          <th>扩展名称</th>
          <th>启用状态</th>
          <th>性能影响</th>
          <th>用户满意度</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>发送时间优化</td>
          <td><span class="ok">✅ 启用</span></td>
          <td><span class="success">+15.2%</span></td>
          <td>⭐⭐⭐⭐⭐</td>
        </tr>
        <tr>
          <td>预览收件箱</td>
          <td><span class="ok">✅ 启用</span></td>
          <td><span class="success">+8.7%</span></td>
          <td>⭐⭐⭐⭐</td>
        </tr>
        <tr>
          <td>个性化片段库</td>
          <td><span class="ok">✅ 启用</span></td>
          <td><span class="success">+12.3%</span></td>
          <td>⭐⭐⭐⭐⭐</td>
        </tr>
        <tr>
          <td>智能A/B测试</td>
          <td><span class="warnTxt">⚠️ 配置中</span></td>
          <td><span class="text-secondary">--</span></td>
          <td>--</td>
        </tr>
        <tr>
          <td>多语言本地化</td>
          <td><span class="err">❌ 禁用</span></td>
          <td><span class="text-secondary">--</span></td>
          <td>--</td>
        </tr>
        <tr>
          <td>高级分析</td>
          <td><span class="err">❌ 禁用</span></td>
          <td><span class="text-secondary">--</span></td>
          <td>--</td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="card">
    <div class="mini">🔧 扩展管理</div>
    <div class="hint">扩展功能的安装、更新和配置管理</div>
    
    <div style="margin: 16px 0;">
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--success-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">✅</div>
        <div>
          <div style="font-weight: 600; color: var(--success);">系统健康</div>
          <div style="font-size: 12px; color: var(--text-secondary);">所有启用的扩展运行正常</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--warning-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">⚠️</div>
        <div>
          <div style="font-weight: 600; color: var(--warning);">配置待完成</div>
          <div style="font-size: 12px; color: var(--text-secondary);">智能A/B测试扩展需要完成配置</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; padding: 12px; background: var(--err-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">🚨</div>
        <div>
          <div style="font-weight: 600; color: var(--err);">更新可用</div>
          <div style="font-size: 12px; color: var(--text-secondary);">2个扩展有新版本可用</div>
        </div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="checkForUpdates()">🔄 检查更新</button>
      <button class="btn secondary" onclick="showExtensionSettings()">⚙️ 全局设置</button>
    </div>
  </div>
</div>

<!-- 扩展配置弹窗 -->
<div id="extension-config" class="card" style="display:none; max-width:800px; position:fixed; top:8%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">⚙️ 扩展配置</div>
  <div id="config-content">
    <!-- 动态填充配置内容 -->
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="saveExtensionConfig()">💾 保存配置</button>
    <button class="btn secondary" onclick="hideExtensionConfig()">❌ 关闭</button>
  </div>
</div>

<!-- 扩展市场弹窗 -->
<div id="extension-market" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">🛒 扩展市场</div>
  <div class="hint">发现和安装新的扩展功能</div>
  
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>分类筛选</label>
      <select id="market-category">
        <option>全部分类</option>
        <option>性能优化</option>
        <option>用户体验</option>
        <option>数据分析</option>
        <option>自动化</option>
        <option>集成</option>
      </select>
    </div>
    <div>
      <label>排序方式</label>
      <select id="market-sort">
        <option>推荐度</option>
        <option>下载量</option>
        <option>评分</option>
        <option>更新时间</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn secondary" onclick="searchExtensions()">🔍 搜索</button>
    </div>
  </div>
  
  <div class="grid cols-2">
    <div class="card">
      <div class="mini">🎨 邮件模板库</div>
      <div class="hint">丰富的邮件模板，支持多种行业和场景</div>
      <div style="margin: 16px 0;">
        <div style="font-size: 12px; color: var(--text-secondary);">
          • 500+ 专业模板<br>
          • 响应式设计<br>
          • 多行业覆盖<br>
          • 一键应用
        </div>
      </div>
      <div class="toolbar">
        <button class="btn" onclick="installExtension('email-templates')">📥 安装</button>
        <button class="btn ghost" onclick="viewExtensionDetails('email-templates')">详情</button>
      </div>
    </div>
    
    <div class="card">
      <div class="mini">🤖 智能回复助手</div>
      <div class="hint">AI驱动的邮件回复建议和自动回复</div>
      <div style="margin: 16px 0;">
        <div style="font-size: 12px; color: var(--text-secondary);">
          • AI回复建议<br>
          • 自动回复规则<br>
          • 情感分析<br>
          • 多语言支持
        </div>
      </div>
      <div class="toolbar">
        <button class="btn" onclick="installExtension('ai-reply-assistant')">📥 安装</button>
        <button class="btn ghost" onclick="viewExtensionDetails('ai-reply-assistant')">详情</button>
      </div>
    </div>
    
    <div class="card">
      <div class="mini">📱 移动端优化</div>
      <div class="hint">专门针对移动设备的邮件优化</div>
      <div style="margin: 16px 0;">
        <div style="font-size: 12px; color: var(--text-secondary);">
          • 触摸友好设计<br>
          • 快速加载<br>
          • 手势支持<br>
          • 离线阅读
        </div>
      </div>
      <div class="toolbar">
        <button class="btn" onclick="installExtension('mobile-optimization')">📥 安装</button>
        <button class="btn ghost" onclick="viewExtensionDetails('mobile-optimization')">详情</button>
      </div>
    </div>
    
    <div class="card">
      <div class="mini">🔗 社交媒体集成</div>
      <div class="hint">邮件与社交媒体的无缝集成</div>
      <div style="margin: 16px 0;">
        <div style="font-size: 12px; color: var(--text-secondary);">
          • 社交分享按钮<br>
          • 用户画像同步<br>
          • 跨平台数据<br>
          • 统一分析
        </div>
      </div>
      <div class="toolbar">
        <button class="btn" onclick="installExtension('social-media-integration')">📥 安装</button>
        <button class="btn ghost" onclick="viewExtensionDetails('social-media-integration')">详情</button>
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideExtensionMarket()">❌ 关闭</button>
  </div>
</div>

<script>
// 扩展管理相关函数
function refreshExtensions() {
  alert('扩展状态已刷新！\n\n正在检查：\n• 扩展运行状态\n• 性能指标\n• 更新可用性\n• 配置完整性\n\n请稍候查看最新状态。');
}

function showExtensionMarket() {
  document.getElementById('extension-market').style.display = '';
}

function hideExtensionMarket() {
  document.getElementById('extension-market').style.display = 'none';
}

function showExtensionLogs() {
  alert('查看扩展日志功能开发中...\n\n将显示：\n• 扩展运行日志\n• 错误和警告\n• 性能统计\n• 用户操作记录');
}

function configureExtension(extensionId) {
  const configDiv = document.getElementById('config-content');
  let configHtml = '';
  
  switch (extensionId) {
    case 'send-time-optimization':
      configHtml = `
        <div class="mini">⏰ 发送时间优化配置</div>
        <div class="grid cols-2">
          <div>
            <label>优化算法</label>
            <select id="optimization-algorithm">
              <option selected>机器学习</option>
              <option>统计分析</option>
              <option>混合模式</option>
            </select>
          </div>
          <div>
            <label>学习周期</label>
            <select id="learning-period">
              <option>7天</option>
              <option selected>30天</option>
              <option>90天</option>
            </select>
          </div>
          <div>
            <label>最小样本数</label>
            <input id="min-samples" type="number" value="100" min="50" max="1000" />
          </div>
          <div>
            <label>置信度阈值</label>
            <input id="confidence-threshold" type="number" value="0.8" min="0.5" max="1.0" step="0.1" />
          </div>
        </div>
        <div class="hint">系统将基于用户历史行为自动学习最佳发送时间，提升邮件打开率</div>
      `;
      break;
      
    case 'inbox-preview':
      configHtml = `
        <div class="mini">📱 预览收件箱配置</div>
        <div class="grid cols-2">
          <div>
            <label>预览客户端</label>
            <div style="margin: 8px 0;">
              <label><input type="checkbox" checked /> Gmail</label><br>
              <label><input type="checkbox" checked /> Outlook</label><br>
              <label><input type="checkbox" checked /> Apple Mail</label><br>
              <label><input type="checkbox" checked /> Yahoo Mail</label>
            </div>
          </div>
          <div>
            <label>预览质量</label>
            <select id="preview-quality">
              <option>标准</option>
              <option selected>高清</option>
              <option>超高清</option>
            </select>
          </div>
          <div>
            <label>自动预览</label>
            <select id="auto-preview">
              <option selected>启用</option>
              <option>禁用</option>
            </select>
          </div>
          <div>
            <label>缓存时间</label>
            <input id="cache-duration" type="number" value="24" min="1" max="168" /> 小时
          </div>
        </div>
        <div class="hint">为不同邮件客户端生成预览截图，确保邮件在各种环境下都能正常显示</div>
      `;
      break;
      
    case 'personalization-blocks':
      configHtml = `
        <div class="mini">🧩 个性化片段库配置</div>
        <div class="grid cols-2">
          <div>
            <label>片段分类</label>
            <select id="block-categories">
              <option selected>自动分类</option>
              <option>手动分类</option>
              <option>混合模式</option>
            </select>
          </div>
          <div>
            <label>智能推荐</label>
            <select id="smart-recommendations">
              <option selected>启用</option>
              <option>禁用</option>
            </select>
          </div>
          <div>
            <label>最大片段数</label>
            <input id="max-blocks" type="number" value="1000" min="100" max="10000" />
          </div>
          <div>
            <label>版本控制</label>
            <select id="version-control">
              <option selected>启用</option>
              <option>禁用</option>
            </select>
          </div>
        </div>
        <div class="hint">管理可复用的邮件内容片段，支持版本控制和智能推荐</div>
      `;
      break;
      
    default:
      configHtml = `
        <div class="mini">⚙️ 扩展配置</div>
        <div class="hint">该扩展的配置选项正在开发中...</div>
      `;
  }
  
  configDiv.innerHTML = configHtml;
  document.getElementById('extension-config').style.display = '';
}

function hideExtensionConfig() {
  document.getElementById('extension-config').style.display = 'none';
}

function saveExtensionConfig() {
  alert('扩展配置已保存！\n\n配置将在5分钟内生效，请稍候。');
  hideExtensionConfig();
}

function enableExtension(extensionId) {
  alert(`扩展已启用：${extensionId}\n\n系统将：\n• 安装扩展组件\n• 配置默认参数\n• 启动相关服务\n• 开始数据收集\n\n预计10分钟内完成初始化。`);
}

function disableExtension(extensionId) {
  if (confirm(`确定要禁用扩展 ${extensionId} 吗？\n\n禁用后：\n• 相关功能将停止工作\n• 已收集的数据将保留\n• 可以随时重新启用`)) {
    alert(`扩展已禁用：${extensionId}\n\n系统将：\n• 停止相关服务\n• 保存当前状态\n• 清理临时资源\n\n扩展已成功禁用。`);
  }
}

function checkForUpdates() {
  alert('正在检查扩展更新...\n\n发现以下更新：\n• 发送时间优化 v2.1.0 → v2.2.0\n• 预览收件箱 v1.5.0 → v1.6.0\n\n建议在维护时间进行更新。');
}

function showExtensionSettings() {
  alert('扩展全局设置功能开发中...\n\n将支持：\n• 扩展权限管理\n• 性能监控配置\n• 更新策略设置\n• 故障恢复选项');
}

function searchExtensions() {
  const category = document.getElementById('market-category').value;
  const sort = document.getElementById('market-sort').value;
  
  alert(`搜索扩展中...\n\n筛选条件：\n• 分类：${category}\n• 排序：${sort}\n\n正在搜索相关扩展，请稍候...`);
}

function installExtension(extensionId) {
  alert(`正在安装扩展：${extensionId}\n\n安装步骤：\n• 下载扩展包\n• 验证完整性\n• 安装组件\n• 配置参数\n• 启动服务\n\n预计5分钟内完成安装。`);
}

function viewExtensionDetails(extensionId) {
  alert(`查看扩展详情：${extensionId}\n\n功能开发中，将显示：\n• 详细功能介绍\n• 安装要求\n• 使用说明\n• 用户评价\n• 更新日志`);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 可以在这里添加初始化逻辑
});
</script>


