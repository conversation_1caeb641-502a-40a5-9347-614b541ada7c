<div class="toolbar">
  <button class="btn" onclick="showCreateJourney()">新建旅程</button>
  <button class="btn secondary" onclick="showTemplateLibrary()">模板库</button>
  <button class="btn ghost" onclick="exportJourneys()">导出旅程</button>
</div>

<!-- 创建自动化旅程弹窗 -->
<div id="create-journey" class="card" style="display:none; max-width:1200px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">创建自动化旅程</div>
  
  <div class="grid cols-2">
    <div>
      <label>旅程名称 *</label>
      <input id="journey-name" placeholder="如：新注册用户欢迎旅程" />
      
      <label>发件渠道 *</label>
      <select id="journey-channel" onchange="loadChannelInfo()">
        <option value="">请选择发件渠道</option>
        <option value="1">营销主渠道 (marketing-main)</option>
        <option value="2">交易通知 (transactional)</option>
        <option value="3">系统告警 (system-alerts)</option>
      </select>
      <div class="hint">选择用于发送此旅程的发件渠道</div>
      
      <label>目标受众 *</label>
      <select>
        <option value="">请选择受众</option>
        <option>列表：新注册用户</option>
        <option>细分：VIP用户</option>
        <option>细分：流失风险用户</option>
      </select>
      
      <div id="journey-channel-info" class="card" style="background: var(--panel-2); display:none;">
        <div class="mini">渠道信息</div>
        <div class="row">
          <div><strong>默认域名：</strong><span id="journey-domain">—</span></div>
          <div><strong>日配额：</strong><span id="journey-quota">—</span></div>
          <div><strong>时间表：</strong><span id="journey-schedule">—</span></div>
        </div>
      </div>
    </div>
    
    <div>
      <label>旅程类型</label>
      <select id="journey-type" onchange="loadJourneyTemplate()">
        <option value="">请选择类型</option>
        <option value="welcome">欢迎旅程</option>
        <option value="onboarding">用户引导</option>
        <option value="re-engagement">重新激活</option>
        <option value="abandoned-cart">购物车放弃</option>
        <option value="custom">自定义旅程</option>
      </select>
      
      <label>模板选择</label>
      <select id="journey-template" onchange="loadTemplateInfo()">
        <option value="">请选择模板</option>
        <option value="1">欢迎邮件模板 (支持多语言)</option>
        <option value="2">引导邮件模板 (支持多语言)</option>
        <option value="3">激活邮件模板 (仅中文)</option>
      </select>
      <div class="hint">选择邮件模板，支持多语言模板将显示语言选项</div>
      
      <div id="journey-multilingual" style="display:none;">
        <label>多语言配置</label>
        <div class="card" style="background: var(--panel-2);">
          <div class="row">
            <div><input type="checkbox" checked/> 中文 (zh-CN)</div>
            <div><input type="checkbox" checked/> English (en-US)</div>
            <div><input type="checkbox"/> Español (es-ES)</div>
          </div>
          <div class="hint">系统将根据用户语言偏好自动选择对应版本</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="divider"></div>
  <div class="mini">旅程流程设计</div>
  <div class="grid cols-2">
    <div>
      <div class="card" style="background: var(--panel-2);">
        <div class="mini">流程步骤</div>
        <div id="journey-steps" class="grid">
          <div class="card" onclick="editStep(1)">
            <div class="mini">触发：加入列表</div>
            <div class="hint">当用户加入"新注册用户"列表时</div>
          </div>
          <div class="card" onclick="editStep(2)">
            <div class="mini">等待：2 天</div>
            <div class="hint">等待2天后继续</div>
          </div>
          <div class="card" onclick="editStep(3)">
            <div class="mini">动作：发送邮件</div>
            <div class="hint">发送欢迎邮件模板</div>
          </div>
        </div>
        
        <div class="toolbar">
          <button class="btn mini" onclick="addStep('trigger')">+ 触发</button>
          <button class="btn mini" onclick="addStep('wait')">+ 等待</button>
          <button class="btn mini" onclick="addStep('condition')">+ 条件</button>
          <button class="btn mini" onclick="addStep('ab-test')">+ A/B 测试</button>
          <button class="btn mini" onclick="addStep('action')">+ 动作</button>
        </div>
      </div>
    </div>
    
    <div>
      <div class="card">
        <div class="mini">旅程设置</div>
        
        <label>进入频率</label>
        <select>
          <option value="once">首次进入</option>
          <option value="repeat">可重复（去重 7 天）</option>
          <option value="always">每次触发都进入</option>
        </select>
        
        <label>上限控制</label>
        <select>
          <option value="daily-1">每天最多 1 封</option>
          <option value="weekly-3">每周最多 3 封</option>
          <option value="monthly-10">每月最多 10 封</option>
          <option value="unlimited">无限制</option>
        </select>
        
        <label>目标受众过滤</label>
        <div class="row">
          <div class="flex-0"><input type="checkbox" id="limit-by-tags" checked></div>
          <div><label for="limit-by-tags">仅包含具备以下标签的联系人</label></div>
        </div>
        
        <label>特定标签</label>
        <select multiple>
          <option>VIP</option>
          <option>新注册</option>
          <option>黑五预热</option>
          <option>活跃用户</option>
        </select>
        
        <div class="divider"></div>
        <div class="mini">高级设置</div>
        
        <label>时区处理</label>
        <select>
          <option value="channel-default">使用渠道默认设置</option>
          <option value="user-timezone">用户所在时区</option>
          <option value="business-hours">工作时间发送</option>
        </select>
        
        <label>退出条件</label>
        <div><input type="checkbox" checked/> 用户退订</div>
        <div><input type="checkbox" checked/> 达到发送上限</div>
        <div><input type="checkbox"/> 完成目标动作</div>
        <div><input type="checkbox"/> 手动退出</div>
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="createJourney()">创建旅程</button>
    <button class="btn secondary" onclick="saveAsTemplate()">保存为模板</button>
    <button class="btn ghost" onclick="hideCreateJourney()">取消</button>
  </div>
</div>

<!-- 模板库弹窗 -->
<div id="template-library" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">自动化旅程模板库</div>
  
  <div class="toolbar">
    <input placeholder="搜索模板..." />
    <select><option>全部类型</option><option>欢迎</option><option>引导</option><option>激活</option></select>
    <button class="btn" onclick="showCreateTemplate()">新建模板</button>
  </div>
  
  <div class="grid cols-3">
    <div class="card" style="cursor: pointer;" onclick="selectTemplate(1)">
      <div class="mini">新用户欢迎旅程</div>
      <div style="height: 100px; background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #1976d2;">
          <div style="font-size: 24px; margin-bottom: 8px;">👋</div>
          <div style="font-weight: 600;">欢迎旅程</div>
        </div>
      </div>
      <div class="hint">3步欢迎流程，提升用户激活率</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已验证</div>
    </div>
    
    <div class="card" style="cursor: pointer;" onclick="selectTemplate(2)">
      <div class="mini">购物车放弃挽回</div>
      <div style="height: 100px; background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #388e3c;">
          <div style="font-size: 24px; margin-bottom: 8px;">🛒</div>
          <div style="font-weight: 600;">挽回旅程</div>
        </div>
      </div>
      <div class="hint">5步挽回流程，提高转化率</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已验证</div>
    </div>
    
    <div class="card" style="cursor: pointer;" onclick="selectTemplate(3)">
      <div class="mini">用户重新激活</div>
      <div style="height: 100px; background: linear-gradient(135deg, #fff3e0, #ffe0b2); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #f57c00;">
          <div style="font-size: 24px; margin-bottom: 8px;">🔥</div>
          <div style="font-weight: 600;">激活旅程</div>
        </div>
      </div>
      <div class="hint">7步激活流程，重新激活流失用户</div>
      <div style="font-size: 12px; color: var(--warning);">⚠️ 测试中</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideTemplateLibrary()">关闭</button>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">步骤（简化画布）</div>
    <div id="flow-steps" class="grid">
      <div class="card">触发：加入列表</div>
      <div class="card">等待：2 天</div>
      <div class="card">动作：发送邮件 模板#1</div>
    </div>
    <div class="toolbar">
      <button class="btn ghost">+ 触发</button>
      <button class="btn ghost">+ 等待</button>
      <button class="btn ghost">+ 条件</button>
      <button class="btn ghost">+ A/B 随机</button>
      <button class="btn ghost">+ 发送邮件</button>
    </div>
  </div>
  <div class="card">
    <div class="mini">旅程设置</div>
    <label>进入频率</label><select><option>首次进入</option><option>可重复（去重 7 天）</option></select>
    <label>上限控制</label><select><option>每天最多 1 封</option><option>每周最多 3 封</option></select>
    <label>目标受众</label>
    <div class="row">
      <div class="flex-0"><input type="checkbox" id="limit-by-tags" checked></div>
      <div><label for="limit-by-tags">仅包含具备以下标签的联系人</label></div>
    </div>
    <label>特定标签</label>
    <select multiple>
      <option>VIP</option>
      <option>新注册</option>
      <option>黑五预热</option>
    </select>
    <div class="toolbar"><button class="btn">发布旅程</button></div>
  </div>
</div>

<div class="card">
  <div class="mini">自动化设计要点</div>
  <ul>
    <li>条件：触发条件（联系人创建/字段变更/事件/细分/日期/API），流程中条件节点（属性、行为、标签、列表）。</li>
    <li>动作：发送邮件（选择模板）、设置标签/字段、Webhook、Goal、退出。</li>
    <li>A/B 测试：支持 AB 随机节点，按比例分流，选择胜出策略。</li>
    <li>受众：可按特定标签/列表限定进入。</li>
    <li>模板：提供模板库快捷选择与替换。</li>
  </ul>
</div>

<div class="card">
  <div class="mini">旅程列表</div>
  <div class="toolbar">
    <input placeholder="搜索旅程..." />
    <select><option>全部状态</option><option>运行中</option><option>暂停</option><option>草稿</option></select>
    <select><option>全部类型</option><option>欢迎</option><option>引导</option><option>激活</option></select>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>名称</th>
        <th>类型</th>
        <th>发件渠道</th>
        <th>状态</th>
        <th>在流人数</th>
        <th>过去7天发送</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>新注册欢迎 (示例)</td>
        <td><span class="badge welcome">欢迎</span></td>
        <td><span class="badge marketing">营销主渠道</span></td>
        <td><span class="badge running">运行中</span></td>
        <td>2,431</td>
        <td>6,121</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">编辑</button>
          <button class="btn mini ghost">暂停</button>
        </td>
      </tr>
      <tr>
        <td>购物车放弃挽回</td>
        <td><span class="badge re-engagement">挽回</span></td>
        <td><span class="badge transactional">交易通知</span></td>
        <td><span class="badge running">运行中</span></td>
        <td>1,856</td>
        <td>3,245</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">编辑</button>
          <button class="btn mini ghost">暂停</button>
        </td>
      </tr>
      <tr>
        <td>用户重新激活</td>
        <td><span class="badge activation">激活</span></td>
        <td><span class="badge marketing">营销主渠道</span></td>
        <td><span class="badge paused">暂停</span></td>
        <td>0</td>
        <td>0</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">编辑</button>
          <button class="btn mini ghost">启动</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<style>
.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.welcome {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.re-engagement {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.activation {
  background: #fff3e0;
  color: #f57c00;
}

.badge.marketing {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.transactional {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.running {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.paused {
  background: #ffebee;
  color: #d32f2f;
}
</style>

<script>
function showCreateJourney() {
  document.getElementById('create-journey').style.display = 'block';
}

function hideCreateJourney() {
  document.getElementById('create-journey').style.display = 'none';
}

function showTemplateLibrary() {
  document.getElementById('template-library').style.display = 'block';
}

function hideTemplateLibrary() {
  document.getElementById('template-library').style.display = 'none';
}

function loadChannelInfo() {
  const channelId = document.getElementById('journey-channel').value;
  const channelInfo = document.getElementById('journey-channel-info');
  
  if (channelId) {
    // 模拟加载渠道信息
    const channelData = {
      '1': {
        domain: 'marketing.example.com',
        quota: '50,000 (已用 32,450)',
        schedule: '时区匹配 (10:00)'
      },
      '2': {
        domain: 'notify.example.com',
        quota: '10,000 (已用 8,200)',
        schedule: '固定时间 (09:00)'
      },
      '3': {
        domain: 'alerts.example.com',
        quota: '1,000 (已用 150)',
        schedule: '立即发送'
      }
    };
    
    const data = channelData[channelId];
    document.getElementById('journey-domain').textContent = data.domain;
    document.getElementById('journey-quota').textContent = data.quota;
    document.getElementById('journey-schedule').textContent = data.schedule;
    
    channelInfo.style.display = 'block';
  } else {
    channelInfo.style.display = 'none';
  }
}

function loadJourneyTemplate() {
  const journeyType = document.getElementById('journey-type').value;
  // 根据旅程类型加载对应的模板建议
  console.log('Loading template for journey type:', journeyType);
}

function loadTemplateInfo() {
  const templateId = document.getElementById('journey-template').value;
  const multilingualOptions = document.getElementById('journey-multilingual');
  
  if (templateId === '1' || templateId === '2') {
    multilingualOptions.style.display = 'block';
  } else {
    multilingualOptions.style.display = 'none';
  }
}

function addStep(stepType) {
  const stepsContainer = document.getElementById('journey-steps');
  const stepDiv = document.createElement('div');
  stepDiv.className = 'card';
  stepDiv.onclick = function() { editStep(stepsContainer.children.length + 1); };
  
  switch(stepType) {
    case 'trigger':
      stepDiv.innerHTML = '<div class="mini">触发：新步骤</div><div class="hint">点击编辑触发条件</div>';
      break;
    case 'wait':
      stepDiv.innerHTML = '<div class="mini">等待：新步骤</div><div class="hint">点击编辑等待时间</div>';
      break;
    case 'condition':
      stepDiv.innerHTML = '<div class="mini">条件：新步骤</div><div class="hint">点击编辑条件逻辑</div>';
      break;
    case 'ab-test':
      stepDiv.innerHTML = '<div class="mini">A/B 测试：新步骤</div><div class="hint">点击编辑测试配置</div>';
      break;
    case 'action':
      stepDiv.innerHTML = '<div class="mini">动作：新步骤</div><div class="hint">点击编辑动作内容</div>';
      break;
  }
  
  stepsContainer.appendChild(stepDiv);
}

function editStep(stepIndex) {
  // 编辑步骤逻辑
  console.log('Editing step:', stepIndex);
  // 这里可以打开步骤编辑弹窗
}

function selectTemplate(templateId) {
  // 选择模板逻辑
  console.log('Selected template:', templateId);
  hideTemplateLibrary();
  // 可以在这里加载模板配置到旅程创建表单
}

function createJourney() {
  // 创建旅程逻辑
  alert('自动化旅程创建成功！');
  hideCreateJourney();
}

function saveAsTemplate() {
  // 保存为模板逻辑
  alert('旅程模板保存成功！');
}

function exportJourneys() {
  // 导出旅程逻辑
  alert('旅程数据导出成功！');
}
</script>


