<div class="kpi">
  <div class="item">
    <div class="mini">送达率</div>
    <div class="num">98.3%</div>
    <div class="hint">优秀水平</div>
  </div>
  <div class="item">
    <div class="mini">退回率</div>
    <div class="num">0.7%</div>
    <div class="hint">低于行业平均</div>
  </div>
  <div class="item">
    <div class="mini">投诉率</div>
    <div class="num">0.03%</div>
    <div class="hint">极低水平</div>
  </div>
  <div class="item">
    <div class="mini">垃圾箱率</div>
    <div class="num">1.2%</div>
    <div class="hint">需要关注</div>
  </div>
</div>

<div class="toolbar">
  <button class="btn" onclick="showWarmupConfig()">⚙️ 配置预热</button>
  <button class="btn" onclick="startWarmup()">🚀 启动预热</button>
  <button class="btn secondary" onclick="pauseWarmup()">⏸ 暂停预热</button>
  <button class="btn ghost" onclick="showVerificationPanel()">✅ 邮箱验证</button>
  <button class="btn ghost" onclick="showVerificationJobs()">📄 验证任务</button>
  <button class="btn ghost" onclick="showDeliverabilityReport()">📊 投递报告</button>
  <div class="hint">监控和优化邮件投递性能，确保高送达率和低垃圾箱率</div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">🌐 发信域名</div>
    <table class="table">
      <thead>
        <tr>
          <th>域名</th>
          <th>SPF</th>
          <th>DKIM</th>
          <th>DMARC</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>example.com</td>
          <td><span class="ok">✅ 通过</span></td>
          <td><span class="ok">✅ 通过</span></td>
          <td><span class="warnTxt">⚠️ 警告</span></td>
          <td><span class="warnTxt">部分配置</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewDomainDetails('example.com')">详情</button>
          </td>
        </tr>
        <tr>
          <td>news.example.com</td>
          <td><span class="ok">✅ 通过</span></td>
          <td><span class="ok">✅ 通过</span></td>
          <td><span class="ok">✅ 通过</span></td>
          <td><span class="ok">完全配置</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewDomainDetails('news.example.com')">详情</button>
          </td>
        </tr>
        <tr>
          <td>marketing.example.com</td>
          <td><span class="err">❌ 失败</span></td>
          <td><span class="err">❌ 失败</span></td>
          <td><span class="err">❌ 失败</span></td>
          <td><span class="err">配置错误</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="fixDomainConfig('marketing.example.com')">修复</button>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="toolbar">
      <button class="btn secondary" onclick="addNewDomain()">➕ 添加域名</button>
      <button class="btn ghost" onclick="checkAllDomains()">🔍 检查所有域名</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🖥️ IP 池与预热</div>
    <table class="table">
      <thead>
        <tr>
          <th>IP 池</th>
          <th>类型</th>
          <th>预热进度</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Pool-A</td>
          <td>独享</td>
          <td>
            <div class="progress">
              <div style="width:80%"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">80% 完成</div>
          </td>
          <td><span class="ok">预热中</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewPoolDetails('Pool-A')">详情</button>
          </td>
        </tr>
        <tr>
          <td>Pool-B</td>
          <td>共享</td>
          <td>
            <div class="progress">
              <div style="width:100%"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">100% 完成</div>
          </td>
          <td><span class="ok">已就绪</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewPoolDetails('Pool-B')">详情</button>
          </td>
        </tr>
        <tr>
          <td>Pool-C</td>
          <td>独享</td>
          <td>
            <div class="progress">
              <div style="width:45%"></div>
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">45% 完成</div>
          </td>
          <td><span class="warnTxt">预热中</span></td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewPoolDetails('Pool-C')">详情</button>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="hint">💡 预热异常将自动暂停并告警，可手动恢复。建议新IP池采用渐进式预热策略。</div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📊 投递性能趋势</div>
    <div id="delivery-trend" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">📈</div>
        <div>投递率趋势图</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📈 关键指标变化</div>
    <div class="grid cols-2" style="font-size: 12px;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">本周 vs 上周</div>
        <div style="color: var(--success);">送达率: +0.2% ↗️</div>
        <div style="color: var(--success);">退回率: -0.1% ↘️</div>
        <div style="color: var(--warning);">垃圾箱率: +0.3% ↗️</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">本月 vs 上月</div>
        <div style="color: var(--success);">送达率: +0.8% ↗️</div>
        <div style="color: var(--success);">退回率: -0.3% ↘️</div>
        <div style="color: var(--success);">投诉率: -0.01% ↘️</div>
      </div>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🚨 智能预警</div>
    <div class="hint">系统自动检测投递异常并发出预警</div>
    
    <div style="margin: 16px 0;">
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--warning-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">⚠️</div>
        <div>
          <div style="font-weight: 600; color: var(--warning);">垃圾箱率上升</div>
          <div style="font-size: 12px; color: var(--text-secondary);">检测到垃圾箱率从0.9%上升到1.2%</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--err-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">🚨</div>
        <div>
          <div style="font-weight: 600; color: var(--err);">域名配置错误</div>
          <div style="font-size: 12px; color: var(--text-secondary);">marketing.example.com 的SPF记录无效</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; padding: 12px; background: var(--success-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">✅</div>
        <div>
          <div style="font-weight: 600; color: var(--success);">预热进度良好</div>
          <div style="font-size: 12px; color: var(--text-secondary);">Pool-A 预热进度达到80%</div>
        </div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn secondary" onclick="acknowledgeAlerts()">✅ 确认预警</button>
      <button class="btn ghost" onclick="viewAllAlerts()">📋 查看全部</button>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">📧 投递事件监控</div>
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>时间范围</label>
      <select id="event-time-range">
        <option>最近1小时</option>
        <option>最近24小时</option>
        <option>最近7天</option>
        <option>最近30天</option>
      </select>
    </div>
    <div>
      <label>事件类型</label>
      <select id="event-type">
        <option>全部事件</option>
        <option>delivered</option>
        <option>bounced</option>
        <option>complained</option>
        <option>unsubscribed</option>
      </select>
    </div>
    <div>
      <label>域名筛选</label>
      <select id="event-domain">
        <option>全部域名</option>
        <option>example.com</option>
        <option>news.example.com</option>
        <option>marketing.example.com</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn secondary" onclick="refreshEvents()">🔄 刷新</button>
    </div>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>时间</th>
        <th>事件</th>
        <th>收件人</th>
        <th>活动</th>
        <th>域名</th>
        <th>IP地址</th>
        <th>详情</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody id="events-tbody">
      <tr>
        <td>10:21:02</td>
        <td><span class="ok">delivered</span></td>
        <td><EMAIL></td>
        <td>双11预热-1</td>
        <td>example.com</td>
        <td>*************</td>
        <td>OK</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewEventDetail('event_001')">详情</button>
        </td>
      </tr>
      <tr>
        <td>10:20:58</td>
        <td><span class="err">bounced</span></td>
        <td><EMAIL></td>
        <td>双11预热-1</td>
        <td>example.com</td>
        <td>*************</td>
        <td>用户不存在</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewEventDetail('event_002')">详情</button>
        </td>
      </tr>
      <tr>
        <td>10:20:45</td>
        <td><span class="warnTxt">complained</span></td>
        <td><EMAIL></td>
        <td>双11预热-1</td>
        <td>example.com</td>
        <td>*************</td>
        <td>用户投诉</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewEventDetail('event_003')">详情</button>
        </td>
      </tr>
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">共显示 <b id="events-count">3</b> 条事件记录</span>
    <button class="btn secondary" onclick="exportEvents()">📤 导出事件</button>
  </div>
</div>

<!-- 预热配置弹窗 -->
<div id="warmup-config" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">⚙️ 预热配置</div>
  
  <div class="grid cols-2">
    <div>
      <label>域名</label>
      <select id="warmup-domain">
        <option>example.com</option>
        <option>news.example.com</option>
        <option>marketing.example.com</option>
      </select>
    </div>
    <div>
      <label>IP 池</label>
      <select id="warmup-pool">
        <option>Pool-A（独享）</option>
        <option>Pool-B（共享）</option>
        <option>Pool-C（独享）</option>
      </select>
    </div>
    <div>
      <label>预热曲线</label>
      <select id="warmup-curve">
        <option>保守（推荐新IP）</option>
        <option selected>标准</option>
        <option>激进（仅限老IP）</option>
      </select>
    </div>
    <div>
      <label>每日目标量</label>
      <input id="warmup-daily-target" type="number" value="500" min="100" max="10000" />
    </div>
    <div>
      <label>ISP 差异化</label>
      <select id="warmup-isp">
        <option selected>自动（推荐）</option>
        <option>均匀分配</option>
        <option>按权重分配</option>
      </select>
    </div>
    <div>
      <label>异常阈值（投诉%）</label>
      <input id="warmup-complaint-threshold" type="number" step="0.01" value="0.08" min="0.01" max="1.0" />
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">📋 预热策略详情</div>
  <div class="hint">系统将根据配置自动调整发送策略，确保IP声誉稳步提升</div>
  
  <div class="grid cols-3" style="font-size: 12px; margin: 16px 0;">
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">第1周</div>
      <div style="color: var(--text-secondary);">
        • 每日发送: 100-500<br>
        • 重点监控投诉率<br>
        • 逐步增加发送量
      </div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">第2-3周</div>
      <div style="color: var(--text-secondary);">
        • 每日发送: 500-2000<br>
        • 监控送达率<br>
        • 优化发送时间
      </div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">第4周+</div>
      <div style="color: var(--text-secondary);">
        • 每日发送: 2000+<br>
        • 稳定投递<br>
        • 持续监控
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="saveWarmupConfig()">💾 保存配置</button>
    <button class="btn secondary" onclick="hideWarmupConfig()">❌ 关闭</button>
  </div>
</div>

<!-- 邮箱验证面板 -->
<div id="verification-panel" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">✅ 创建邮箱验证任务</div>
  
  <div class="grid cols-2">
    <div>
      <label>CSV 文件 URL</label>
      <input id="verify-csv-url" placeholder="s3://bucket/emails.csv" />
    </div>
    <div>
      <label>验证深度</label>
      <select id="verify-depth">
        <option>Syntax + MX</option>
        <option selected>Syntax + MX + SMTP（推荐）</option>
        <option>Syntax + MX + SMTP + 投递测试</option>
      </select>
    </div>
    <div>
      <label>并发数</label>
      <input id="verify-concurrency" type="number" value="50" min="10" max="200" />
    </div>
    <div>
      <label>RPM限制</label>
      <input id="verify-rpm" type="number" value="3000" min="1000" max="10000" />
    </div>
    <div>
      <label>失败重试（次）</label>
      <input id="verify-retries" type="number" value="2" min="0" max="5" />
    </div>
    <div>
      <label>超时（秒）</label>
      <input id="verify-timeout" type="number" value="10" min="5" max="30" />
    </div>
    <div>
      <label>Unknown 处理</label>
      <select id="verify-unknown">
        <option selected>保留为 unknown</option>
        <option>按 risky 处理</option>
        <option>按 invalid 处理</option>
      </select>
    </div>
    <div>
      <label>结果通知</label>
      <select id="verify-notification">
        <option selected>邮件通知</option>
        <option>Webhook回调</option>
        <option>不通知</option>
      </select>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">📊 预估结果</div>
  <div class="hint">基于文件大小和验证深度，预估验证结果和耗时</div>
  
  <div class="grid cols-3" style="font-size: 12px; margin: 16px 0;">
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">验证结果</div>
      <div style="color: var(--text-secondary);">
        • Valid: ~85%<br>
        • Invalid: ~10%<br>
        • Risky: ~3%<br>
        • Unknown: ~2%
      </div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">处理时间</div>
      <div style="color: var(--text-secondary);">
        • 50,000邮箱<br>
        • 约2-3小时<br>
        • 实时进度更新
      </div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">费用预估</div>
      <div style="color: var(--text-secondary);">
        • 按验证数量计费<br>
        • 约$0.001/邮箱<br>
        • 总计约$50
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="startVerification()">🚀 启动验证</button>
    <button class="btn secondary" onclick="hideVerificationPanel()">❌ 关闭</button>
  </div>
</div>

<!-- 验证任务列表 -->
<div id="verification-jobs" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📄 验证任务</div>
  
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>状态筛选</label>
      <select id="job-status-filter">
        <option>全部状态</option>
        <option>进行中</option>
        <option>已完成</option>
        <option>已暂停</option>
        <option>已失败</option>
      </select>
    </div>
    <div>
      <label>时间范围</label>
      <select id="job-time-filter">
        <option>最近7天</option>
        <option>最近30天</option>
        <option>最近90天</option>
        <option>全部时间</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn secondary" onclick="refreshJobs()">🔄 刷新</button>
    </div>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>任务名称</th>
        <th>总数</th>
        <th>Valid</th>
        <th>Invalid</th>
        <th>Risky</th>
        <th>Unknown</th>
        <th>进度</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>verify_2025_08_11.csv</td>
        <td>50,000</td>
        <td><span class="ok">44,120</span></td>
        <td><span class="err">3,412</span></td>
        <td><span class="warnTxt">1,230</span></td>
        <td><span class="text-secondary">1,238</span></td>
        <td>
          <div class="progress">
            <div style="width:100%"></div>
          </div>
          <div style="font-size: 12px; color: var(--text-secondary);">100%</div>
        </td>
        <td><span class="ok">✅ 完成</span></td>
        <td>2025-08-11 10:24</td>
        <td>
          <button class="btn secondary" style="padding:6px 10px; font-size:12px;" onclick="downloadResults('job_001')">📥 下载</button>
          <button class="btn ghost" style="padding:6px 10px; font-size:12px;" onclick="viewJobDetails('job_001')">👁️ 查看</button>
        </td>
      </tr>
      <tr>
        <td>verify_vip_list.csv</td>
        <td>25,000</td>
        <td><span class="ok">22,150</span></td>
        <td><span class="err">1,890</span></td>
        <td><span class="warnTxt">560</span></td>
        <td><span class="text-secondary">400</span></td>
        <td>
          <div class="progress">
            <div style="width:100%"></div>
          </div>
          <div style="font-size: 12px; color: var(--text-secondary);">100%</div>
        </td>
        <td><span class="ok">✅ 完成</span></td>
        <td>2025-08-10 15:30</td>
        <td>
          <button class="btn secondary" style="padding:6px 10px; font-size:12px;" onclick="downloadResults('job_002')">📥 下载</button>
          <button class="btn ghost" style="padding:6px 10px; font-size:12px;" onclick="viewJobDetails('job_002')">👁️ 查看</button>
        </td>
      </tr>
      <tr>
        <td>verify_new_users.csv</td>
        <td>100,000</td>
        <td><span class="ok">--</span></td>
        <td><span class="err">--</span></td>
        <td><span class="warnTxt">--</span></td>
        <td><span class="text-secondary">--</span></td>
        <td>
          <div class="progress">
            <div style="width:65%"></div>
          </div>
          <div style="font-size: 12px; color: var(--text-secondary);">65%</div>
        </td>
        <td><span class="warnTxt">🔄 进行中</span></td>
        <td>2025-08-11 09:15</td>
        <td>
          <button class="btn secondary" style="padding:6px 10px; font-size:12px;" onclick="pauseJob('job_003')">⏸ 暂停</button>
          <button class="btn ghost" style="padding:6px 10px; font-size:12px;" onclick="viewJobDetails('job_003')">👁️ 查看</button>
        </td>
      </tr>
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">共 <b id="jobs-count">3</b> 个验证任务</span>
    <button class="btn secondary" onclick="hideVerificationJobs()">❌ 关闭</button>
  </div>
</div>

<!-- 投递报告弹窗 -->
<div id="deliverability-report" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📊 投递性报告</div>
  <div class="hint">综合评估邮件投递性能，提供优化建议</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">📈 整体评分</div>
      <div style="text-align: center; margin: 20px 0;">
        <div style="font-size: 48px; color: var(--success);">A+</div>
        <div style="font-size: 18px; font-weight: 600;">优秀</div>
        <div style="font-size: 14px; color: var(--text-secondary);">得分: 92/100</div>
      </div>
    </div>
    
    <div>
      <div class="mini">🎯 关键指标</div>
      <div style="font-size: 13px; margin: 16px 0;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>送达率</span>
          <span style="color: var(--success);">98.3% (优秀)</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>退回率</span>
          <span style="color: var(--success);">0.7% (优秀)</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>投诉率</span>
          <span style="color: var(--success);">0.03% (优秀)</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>垃圾箱率</span>
          <span style="color: var(--warning);">1.2% (良好)</span>
        </div>
      </div>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">💡 优化建议</div>
  <div style="font-size: 13px; margin: 16px 0;">
    <div style="color: var(--success); margin-bottom: 8px;">✅ <strong>继续保持</strong></div>
    <div style="color: var(--text-secondary); margin-left: 20px; margin-bottom: 12px;">
      • 定期清理无效邮箱地址<br>
      • 监控IP声誉变化<br>
      • 优化邮件内容质量
    </div>
    
    <div style="color: var(--warning); margin-bottom: 8px;">⚠️ <strong>需要关注</strong></div>
    <div style="color: var(--text-secondary); margin-left: 20px;">
      • 垃圾箱率略有上升趋势<br>
      • 建议优化邮件主题行<br>
      • 考虑调整发送频率
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="generateDetailedReport()">📋 生成详细报告</button>
    <button class="btn secondary" onclick="hideDeliverabilityReport()">❌ 关闭</button>
  </div>
</div>

<script>
// 预热配置相关函数
function showWarmupConfig() {
  document.getElementById('warmup-config').style.display = '';
}

function hideWarmupConfig() {
  document.getElementById('warmup-config').style.display = 'none';
}

function saveWarmupConfig() {
  const domain = document.getElementById('warmup-domain').value;
  const pool = document.getElementById('warmup-pool').value;
  const curve = document.getElementById('warmup-curve').value;
  const dailyTarget = document.getElementById('warmup-daily-target').value;
  const isp = document.getElementById('warmup-isp').value;
  const threshold = document.getElementById('warmup-complaint-threshold').value;
  
  alert(`预热配置已保存！\n\n域名：${domain}\nIP池：${pool}\n预热曲线：${curve}\n每日目标：${dailyTarget}\nISP策略：${isp}\n投诉阈值：${threshold}%\n\n配置将在下次预热时生效。`);
  hideWarmupConfig();
}

function startWarmup() {
  alert('预热已启动！\n\n系统将按照配置的预热策略逐步提升发送量，请密切关注投递指标变化。');
}

function pauseWarmup() {
  alert('预热已暂停！\n\n当前发送量将保持在当前水平，直到您重新启动预热。');
}

// 邮箱验证相关函数
function showVerificationPanel() {
  document.getElementById('verification-panel').style.display = '';
}

function hideVerificationPanel() {
  document.getElementById('verification-panel').style.display = 'none';
}

function startVerification() {
  const csvUrl = document.getElementById('verify-csv-url').value;
  const depth = document.getElementById('verify-depth').value;
  const concurrency = document.getElementById('verify-concurrency').value;
  const rpm = document.getElementById('verify-rpm').value;
  
  if (!csvUrl.trim()) {
    alert('请输入CSV文件URL！');
    return;
  }
  
  alert(`邮箱验证任务已启动！\n\n文件：${csvUrl}\n验证深度：${depth}\n并发数：${concurrency}\nRPM限制：${rpm}\n\n任务ID：verify_${Date.now()}\n您可以在"验证任务"中查看进度。`);
  hideVerificationPanel();
}

function showVerificationJobs() {
  document.getElementById('verification-jobs').style.display = '';
}

function hideVerificationJobs() {
  document.getElementById('verification-jobs').style.display = 'none';
}

// 投递报告相关函数
function showDeliverabilityReport() {
  document.getElementById('deliverability-report').style.display = '';
}

function hideDeliverabilityReport() {
  document.getElementById('deliverability-report').style.display = 'none';
}

function generateDetailedReport() {
  alert('详细报告生成中...\n\n包含内容：\n• 详细的投递性能分析\n• 历史趋势对比\n• 具体优化建议\n• 行业基准对比\n\n完成后将发送到您的邮箱。');
}

// 其他功能函数
function addNewDomain() {
  alert('添加新域名功能开发中...\n\n将支持：\n• 域名输入和验证\n• 自动DNS记录检查\n• SPF/DKIM/DMARC配置指导');
}

function checkAllDomains() {
  alert('正在检查所有域名配置...\n\n检查项目：\n• SPF记录有效性\n• DKIM密钥状态\n• DMARC策略配置\n• DNS解析状态\n\n完成后将显示详细报告。');
}

function viewDomainDetails(domain) {
  alert(`查看域名详情：${domain}\n\n功能开发中，将显示：\n• 完整的DNS配置\n• 投递性能统计\n• 配置历史记录\n• 优化建议`);
}

function fixDomainConfig(domain) {
  alert(`修复域名配置：${domain}\n\n系统将自动：\n• 检查DNS记录\n• 生成配置建议\n• 提供修复步骤\n• 验证修复结果`);
}

function viewPoolDetails(pool) {
  alert(`查看IP池详情：${pool}\n\n功能开发中，将显示：\n• IP地址列表\n• 声誉评分\n• 投递统计\n• 预热历史`);
}

function acknowledgeAlerts() {
  alert('预警已确认！\n\n系统将：\n• 标记预警为已处理\n• 记录处理时间\n• 跟踪后续变化\n• 生成处理报告');
}

function viewAllAlerts() {
  alert('查看全部预警功能开发中...\n\n将显示：\n• 历史预警记录\n• 预警分类统计\n• 处理状态跟踪\n• 预警趋势分析');
}

function refreshEvents() {
  const timeRange = document.getElementById('event-time-range').value;
  const eventType = document.getElementById('event-type').value;
  const domain = document.getElementById('event-domain').value;
  
  alert(`事件数据已刷新！\n\n筛选条件：\n• 时间范围：${timeRange}\n• 事件类型：${eventType}\n• 域名：${domain}\n\n数据已更新，请查看最新结果。`);
}

function viewEventDetail(eventId) {
  alert(`查看事件详情：${eventId}\n\n功能开发中，将显示：\n• 完整的事件信息\n• 相关邮件内容\n• 处理建议\n• 历史记录`);
}

function exportEvents() {
  alert('事件数据导出任务已创建！\n\n导出内容：\n• 筛选后的事件记录\n• 详细的事件信息\n• 统计汇总数据\n• 分析报告\n\n完成后将发送下载链接。');
}

function downloadResults(jobId) {
  alert(`下载验证结果：${jobId}\n\n文件格式：CSV\n包含字段：\n• 邮箱地址\n• 验证结果\n• 验证时间\n• 详细信息\n\n文件已准备就绪，开始下载...`);
}

function viewJobDetails(jobId) {
  alert(`查看任务详情：${jobId}\n\n功能开发中，将显示：\n• 任务配置信息\n• 实时进度更新\n• 详细结果统计\n• 错误日志分析`);
}

function pauseJob(jobId) {
  alert(`任务已暂停：${jobId}\n\n暂停后：\n• 停止处理新邮箱\n• 保存当前进度\n• 可随时恢复\n• 已处理结果保留`);
}

function refreshJobs() {
  const statusFilter = document.getElementById('job-status-filter').value;
  const timeFilter = document.getElementById('job-time-filter').value;
  
  alert(`任务列表已刷新！\n\n筛选条件：\n• 状态：${statusFilter}\n• 时间：${timeFilter}\n\n数据已更新，请查看最新结果。`);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 设置默认值
  document.getElementById('event-time-range').value = '最近24小时';
  document.getElementById('event-type').value = '全部事件';
  document.getElementById('event-domain').value = '全部域名';
  document.getElementById('job-status-filter').value = '全部状态';
  document.getElementById('job-time-filter').value = '最近7天';
  
  // 模拟投递趋势图
  setTimeout(() => {
    const trendDiv = document.getElementById('delivery-trend');
    if (trendDiv) {
      trendDiv.innerHTML = `
        <div style="text-align: center; width: 100%;">
          <div style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">送达率趋势</div>
          <div style="height: 150px; background: linear-gradient(90deg, var(--success-light), var(--warning-light)); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--success); font-weight: 600;">
            模拟趋势图<br>
            98.3% → 98.5% → 98.2% → 98.4%
          </div>
        </div>
      `;
    }
  }, 1000);
});
</script>


