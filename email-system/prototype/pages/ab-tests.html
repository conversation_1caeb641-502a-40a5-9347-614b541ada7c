<div class="toolbar">
  <button class="btn" onclick="showCreateExperiment()">新建实验</button>
  <button class="btn secondary" onclick="showStrategyLibrary()">实验策略库</button>
  <button class="btn ghost" onclick="exportExperiments()">导出实验</button>
</div>

<!-- 创建实验弹窗 -->
<div id="create-experiment" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">创建A/B测试实验</div>
  
  <div class="grid cols-2">
    <div>
      <label>实验名称 *</label>
      <input id="exp-name" placeholder="如：主题行测试-双11" />
      
      <label>作用对象 *</label>
      <select id="exp-target" onchange="loadTargetInfo()">
        <option value="">请选择对象</option>
        <option value="campaign">活动：选择已有活动</option>
        <option value="template">模板：选择模板</option>
        <option value="strategy">策略：从策略库选择</option>
      </select>
      
      <div id="campaign-select" style="display:none;">
        <label>选择活动</label>
        <select>
          <option value="">请选择活动</option>
          <option value="1">双11预热-1</option>
          <option value="2">圣诞促销</option>
          <option value="3">新年祝福</option>
        </select>
      </div>
      
      <div id="template-select" style="display:none;">
        <label>选择模板</label>
        <select>
          <option value="">请选择模板</option>
          <option value="1">简约促销</option>
          <option value="2">节日祝福</option>
          <option value="3">周报模板</option>
        </select>
      </div>
      
      <div id="strategy-select" style="display:none;">
        <label>选择策略</label>
        <select>
          <option value="">请选择策略</option>
          <option value="1">主题行优化策略</option>
          <option value="2">发送时间优化策略</option>
          <option value="3">内容块优化策略</option>
        </select>
      </div>
      
      <label>实验类型 *</label>
      <select id="exp-type" onchange="loadExperimentConfig()">
        <option value="">请选择类型</option>
        <option value="subject">主题行测试</option>
        <option value="sender">发件人名称</option>
        <option value="send-time">发送时间</option>
        <option value="content">内容块</option>
        <option value="template">模板版本</option>
        <option value="landing">落地页</option>
        <option value="cta">行动号召</option>
      </select>
    </div>
    
    <div>
      <label>样本配置</label>
      <div class="row">
        <div>
          <label>总样本比例</label>
          <input type="number" id="exp-sample" value="20" min="5" max="100" />%
          <div class="hint">A/B/C 总样本百分比</div>
        </div>
        <div>
          <label>并发实验</label>
          <input type="number" value="2" min="2" max="5" />
          <div class="hint">支持多个实验同步运行</div>
        </div>
      </div>
      
      <label>流量分配</label>
      <select id="traffic-allocation" onchange="toggleManualAllocation()">
        <option value="equal">平均分配</option>
        <option value="weighted">加权分配</option>
        <option value="bayesian">贝叶斯多臂赌博机</option>
        <option value="manual">手动分配</option>
      </select>
      
      <div id="manual-allocation" style="display:none;">
        <label>手动分配比例</label>
        <div id="allocation-inputs">
          <div class="row">
            <div><label>变体A</label><input type="number" value="50" min="0" max="100" />%</div>
            <div><label>变体B</label><input type="number" value="50" min="0" max="100" />%</div>
          </div>
        </div>
      </div>
      
      <label>胜出指标</label>
      <select id="winning-metric">
        <option value="open_rate">打开率</option>
        <option value="click_rate">点击率</option>
        <option value="conversion_rate">转化率</option>
        <option value="revenue">收入</option>
        <option value="custom">自定义指标</option>
      </select>
    </div>
  </div>
  
  <div class="divider"></div>
  <div class="mini">实验变体配置</div>
  <div id="experiment-variants" class="card" style="background: var(--panel-2);">
    <div id="variant-config">
      <!-- 动态生成变体配置 -->
    </div>
    <button class="btn mini" onclick="addExperimentVariant()">添加变体</button>
  </div>
  
  <div class="grid cols-2">
    <div>
      <label>观察期</label>
      <input type="number" value="6" /> 小时
      <div class="hint">实验运行时间</div>
    </div>
    <div>
      <label>优胜策略</label>
      <select>
        <option value="auto">观察期结束自动选择</option>
        <option value="significance">达到显著性提前胜出</option>
        <option value="manual">仅记录不自动发送</option>
      </select>
    </div>
  </div>
  
  <div class="divider"></div>
  <div class="mini">高级设置</div>
  <div class="grid cols-2">
    <div>
      <label>统计显著性</label>
      <select>
        <option value="0.05">95% 置信度 (p < 0.05)</option>
        <option value="0.01">99% 置信度 (p < 0.01)</option>
        <option value="0.1">90% 置信度 (p < 0.1)</option>
      </select>
      
      <label>最小样本量</label>
      <input type="number" value="1000" />
      <div class="hint">每个变体的最小样本数量</div>
    </div>
    
    <div>
      <label>实验设置</label>
      <div><input type="checkbox" checked/> 启用实时监控</div>
      <div><input type="checkbox" checked/> 自动停止无效实验</div>
      <div><input type="checkbox"/> 保存为策略模板</div>
      
      <label>通知设置</label>
      <div><input type="checkbox" checked/> 实验完成通知</div>
      <div><input type="checkbox"/> 异常情况告警</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="createExperiment()">创建实验</button>
    <button class="btn secondary" onclick="saveAsStrategy()">保存为策略</button>
    <button class="btn ghost" onclick="hideCreateExperiment()">取消</button>
  </div>
</div>

<!-- 策略库弹窗 -->
<div id="strategy-library" class="card" style="display:none; max-width:1200px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">实验策略库</div>
  
  <div class="toolbar">
    <input placeholder="搜索策略..." />
    <select><option>全部类型</option><option>主题行</option><option>发送时间</option><option>内容</option></select>
    <button class="btn" onclick="showCreateStrategy()">新建策略</button>
  </div>
  
  <div class="grid cols-3">
    <div class="card" style="cursor: pointer;" onclick="selectStrategy(1)">
      <div class="mini">主题行优化策略</div>
      <div style="height: 100px; background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #1976d2;">
          <div style="font-size: 24px; margin-bottom: 8px;">📧</div>
          <div style="font-weight: 600;">主题行测试</div>
        </div>
      </div>
      <div class="hint">测试不同主题行的打开率效果</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已验证</div>
    </div>
    
    <div class="card" style="cursor: pointer;" onclick="selectStrategy(2)">
      <div class="mini">发送时间优化策略</div>
      <div style="height: 100px; background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #388e3c;">
          <div style="font-size: 24px; margin-bottom: 8px;">⏰</div>
          <div style="font-weight: 600;">时间优化</div>
        </div>
      </div>
      <div class="hint">测试不同发送时间的最佳效果</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已验证</div>
    </div>
    
    <div class="card" style="cursor: pointer;" onclick="selectStrategy(3)">
      <div class="mini">内容块优化策略</div>
      <div style="height: 100px; background: linear-gradient(135deg, #fff3e0, #ffe0b2); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 10px 0;">
        <div style="text-align: center; color: #f57c00;">
          <div style="font-size: 24px; margin-bottom: 8px;">📝</div>
          <div style="font-weight: 600;">内容测试</div>
        </div>
      </div>
      <div class="hint">测试不同内容块的点击率效果</div>
      <div style="font-size: 12px; color: var(--warning);">⚠️ 测试中</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideStrategyLibrary()">关闭</button>
  </div>
</div>

<div class="card">
  <div class="mini">实验列表</div>
  <div class="toolbar">
    <input placeholder="搜索实验..." />
    <select><option>全部状态</option><option>进行中</option><option>观察中</option><option>已完成</option><option>已停止</option></select>
    <select><option>全部类型</option><option>主题行</option><option>发送时间</option><option>内容</option></select>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>实验名称</th>
        <th>对象</th>
        <th>类型</th>
        <th>样本</th>
        <th>胜出指标</th>
        <th>状态</th>
        <th>胜出变体</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>主题行-双11</td>
        <td>活动：双11预热-1</td>
        <td><span class="badge subject">主题行</span></td>
        <td>20%（各10%）</td>
        <td>打开率</td>
        <td><span class="badge observing">观察中</span></td>
        <td>—</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">停止</button>
        </td>
      </tr>
      <tr>
        <td>发送时间-圣诞</td>
        <td>活动：圣诞促销</td>
        <td><span class="badge time">发送时间</span></td>
        <td>30%（各10%）</td>
        <td>点击率</td>
        <td><span class="badge running">进行中</span></td>
        <td>—</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">停止</button>
        </td>
      </tr>
      <tr>
        <td>内容块-促销</td>
        <td>模板：简约促销</td>
        <td><span class="badge content">内容</span></td>
        <td>25%（各12.5%）</td>
        <td>转化率</td>
        <td><span class="badge completed">已完成</span></td>
        <td>变体B (+15%)</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">应用</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">实验效果统计</div>
    <div class="chart-placeholder">
      <div style="height:200px; display:flex; align-items:center; justify-content:center; color:#666;">
        图表：实验成功率 75%，平均提升 12%
      </div>
    </div>
  </div>
  <div class="card">
    <div class="mini">策略使用情况</div>
    <div class="row">
      <div>
        <label>主题行策略</label>
        <div class="progress"><div style="width:60%"></div></div>
        <div class="mini">使用率 60%</div>
      </div>
      <div>
        <label>时间优化策略</label>
        <div class="progress"><div style="width:40%"></div></div>
        <div class="mini">使用率 40%</div>
      </div>
    </div>
  </div>
</div>

<style>
.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.subject {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.time {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.content {
  background: #fff3e0;
  color: #f57c00;
}

.badge.running {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.observing {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.completed {
  background: #f3e5f5;
  color: #7b1fa2;
}

.chart-placeholder {
  border: 1px dashed #ddd;
  border-radius: 8px;
  margin: 10px 0;
}
</style>

<script>
function showCreateExperiment() {
  document.getElementById('create-experiment').style.display = 'block';
}

function hideCreateExperiment() {
  document.getElementById('create-experiment').style.display = 'none';
}

function showStrategyLibrary() {
  document.getElementById('strategy-library').style.display = 'block';
}

function hideStrategyLibrary() {
  document.getElementById('strategy-library').style.display = 'none';
}

function loadTargetInfo() {
  const target = document.getElementById('exp-target').value;
  const campaignSelect = document.getElementById('campaign-select');
  const templateSelect = document.getElementById('template-select');
  const strategySelect = document.getElementById('strategy-select');
  
  // 隐藏所有选择器
  campaignSelect.style.display = 'none';
  templateSelect.style.display = 'none';
  strategySelect.style.display = 'none';
  
  // 显示对应选择器
  switch(target) {
    case 'campaign':
      campaignSelect.style.display = 'block';
      break;
    case 'template':
      templateSelect.style.display = 'block';
      break;
    case 'strategy':
      strategySelect.style.display = 'block';
      break;
  }
}

function loadExperimentConfig() {
  const expType = document.getElementById('exp-type').value;
  const variantConfig = document.getElementById('variant-config');
  
  // 清空现有配置
  variantConfig.innerHTML = '';
  
  if (expType) {
    // 根据实验类型生成默认变体
    const variants = getDefaultVariants(expType);
    variants.forEach((variant, index) => {
      addExperimentVariant(variant);
    });
  }
}

function getDefaultVariants(expType) {
  const variants = {
    'subject': [
      { name: '变体A', value: '🎉 双11预热，限时优惠等你来！', description: 'emoji + 优惠信息' },
      { name: '变体B', value: '🔥 双11大促即将开始，抢先一步！', description: 'emoji + 紧迫感' }
    ],
    'sender': [
      { name: '变体A', value: 'Marketing Team', description: '团队名称' },
      { name: '变体B', value: 'Customer Success', description: '客户成功' }
    ],
    'send-time': [
      { name: '变体A', value: '09:00', description: '上午发送' },
      { name: '变体B', value: '14:00', description: '下午发送' }
    ],
    'content': [
      { name: '变体A', value: '简约版本', description: '简洁内容' },
      { name: '变体B', value: '详细版本', description: '详细说明' }
    ],
    'template': [
      { name: '变体A', value: '模板A', description: '版本A' },
      { name: '变体B', value: '模板B', description: '版本B' }
    ]
  };
  
  return variants[expType] || [];
}

function addExperimentVariant(defaultVariant = null) {
  const variantConfig = document.getElementById('variant-config');
  const variantIndex = variantConfig.children.length;
  
  const variantDiv = document.createElement('div');
  variantDiv.className = 'card';
  variantDiv.style.marginBottom = '10px';
  
  const variantName = defaultVariant ? defaultVariant.name : `变体${String.fromCharCode(65 + variantIndex)}`;
  const variantValue = defaultVariant ? defaultVariant.value : '';
  const variantDesc = defaultVariant ? defaultVariant.description : '';
  
  variantDiv.innerHTML = `
    <div class="grid cols-3">
      <div>
        <label>变体名称</label>
        <input placeholder="变体名称" value="${variantName}" />
      </div>
      <div>
        <label>变体内容</label>
        <input placeholder="变体内容" value="${variantValue}" />
      </div>
      <div>
        <label>描述</label>
        <input placeholder="变体描述" value="${variantDesc}" />
      </div>
    </div>
    <div style="margin-top: 10px;">
      <button class="btn mini secondary" onclick="removeExperimentVariant(this)">删除变体</button>
    </div>
  `;
  
  variantConfig.appendChild(variantDiv);
}

function removeExperimentVariant(button) {
  button.parentElement.parentElement.remove();
}

function toggleManualAllocation() {
  const allocation = document.getElementById('traffic-allocation').value;
  const manualAllocation = document.getElementById('manual-allocation');
  
  if (allocation === 'manual') {
    manualAllocation.style.display = 'block';
  } else {
    manualAllocation.style.display = 'none';
  }
}

function selectStrategy(strategyId) {
  // 选择策略逻辑
  console.log('Selected strategy:', strategyId);
  hideStrategyLibrary();
  // 可以在这里加载策略配置到实验创建表单
}

function createExperiment() {
  // 创建实验逻辑
  alert('实验创建成功！');
  hideCreateExperiment();
}

function saveAsStrategy() {
  // 保存为策略逻辑
  alert('策略保存成功！');
}

function exportExperiments() {
  // 导出实验逻辑
  alert('实验数据导出成功！');
}
</script>


