<div class="toolbar">
  <button class="btn" onclick="saveSegment()">💾 保存人群</button>
  <button class="btn secondary" onclick="estimateSegment()">🔍 预估人数</button>
  <span class="mini">当前预估人数：<b id="est-count">—</b></span>
  <button class="btn ghost" onclick="showBulkPanel()">🏷️ 批量操作</button>
  <div class="hint">构建精准的人群细分，支持多条件组合、智能建议和实时预估</div>
</div>

<div class="card">
  <div class="mini">📝 人群基本信息</div>
  <div class="row">
    <div>
      <label>人群名称 *</label>
      <input id="segment-name" placeholder="例如：VIP客户-最近30天活跃" />
    </div>
    <div>
      <label>描述</label>
      <input id="segment-description" placeholder="人群特征描述..." />
    </div>
    <div>
      <label>标签</label>
      <input id="segment-tags" placeholder="VIP, 活跃用户, 高价值" />
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">🔧 条件构建器</div>
  <div id="seg-rows">
    <!-- 动态生成的条件行 -->
  </div>
  <div class="toolbar">
    <button class="btn ghost" onclick="addSegmentRow()">➕ 添加条件</button>
    <button class="btn ghost" onclick="addSegmentGroup()">📦 添加条件组</button>
    <button class="btn ghost" onclick="showSmartSuggestions()">💡 智能建议</button>
  </div>
  <div class="hint">💡 示例：最近 30 天打开过 且 标签包含 VIP 且 未退订</div>
</div>

<div class="card" id="bulk-panel" style="display:none">
  <div class="mini">🏷️ 批量操作</div>
  <div class="row">
    <div>
      <label>作用范围</label>
      <select id="bulk-scope">
        <option value="all">当前预估人群（全部）</option>
        <option value="selected">仅已选联系人</option>
      </select>
    </div>
    <div>
      <label>操作</label>
      <select id="bulk-action" onchange="toggleBulkExtra()">
        <option value="tag">打标签</option>
        <option value="add-to-list">加入列表</option>
        <option value="export-snapshot">导出静态人群快照</option>
        <option value="send-test-campaign">发送测试活动</option>
        <option value="update-fields">更新字段</option>
      </select>
    </div>
    <div id="bulk-extra"></div>
    <div style="flex:0;align-self:flex-end">
      <button class="btn" onclick="executeBulkOperation()">🚀 执行</button>
      <button class="btn secondary" onclick="hideBulkPanel()">❌ 关闭</button>
    </div>
  </div>
  <div class="hint">💡 示例：对预估人群一次性打上"黑五预热""高价值"标签，或导出为一个可复用的静态人群快照。</div>
</div>

<div class="card">
  <div class="mini">👥 预览样本</div>
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>预览数量</label>
      <select id="preview-count">
        <option value="10">10条</option>
        <option value="50" selected>50条</option>
        <option value="100">100条</option>
      </select>
    </div>
    <div>
      <label>排序方式</label>
      <select id="preview-sort">
        <option value="recent">最近活动</option>
        <option value="email">邮箱地址</option>
        <option value="created">创建时间</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn secondary" onclick="refreshPreview()">🔄 刷新预览</button>
    </div>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th><input type="checkbox" id="chk-all" onchange="toggleAllCheckboxes()"/></th>
        <th>联系人信息</th>
        <th>最近活动</th>
        <th>标签</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody id="preview-tbody">
      <!-- 动态填充预览数据 -->
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">已选择 <b id="selected-count">0</b> 个联系人</span>
    <button class="btn secondary" onclick="exportSelected()">📤 导出选中</button>
  </div>
</div>

<!-- 智能建议弹窗 -->
<div id="smart-suggestions" class="card" style="display:none; max-width:800px; position:fixed; top:8%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">💡 智能建议</div>
  <div class="hint">基于历史数据和最佳实践，为您推荐常用的人群细分条件</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">🎯 高价值客户</div>
      <div class="hint">识别最有价值的客户群体</div>
      <button class="btn secondary" onclick="applySuggestion('high-value')" style="width:100%; margin-bottom:8px;">应用建议</button>
      <div style="font-size:12px; color:var(--text-secondary);">
        • 最近90天有购买记录<br>
        • 标签包含"VIP"或"复购"<br>
        • 打开率 > 40%
      </div>
    </div>
    
    <div>
      <div class="mini">🔄 流失风险</div>
      <div class="hint">识别可能流失的客户</div>
      <button class="btn secondary" onclick="applySuggestion('churn-risk')" style="width:100%; margin-bottom:8px;">应用建议</button>
      <div style="font-size:12px; color:var(--text-secondary);">
        • 30天内未打开邮件<br>
        • 最近一次活动 > 60天<br>
        • 未退订状态
      </div>
    </div>
    
    <div>
      <div class="mini">🌟 新客户激活</div>
      <div class="hint">帮助新客户快速融入</div>
      <button class="btn secondary" onclick="applySuggestion('new-activation')" style="width:100%; margin-bottom:8px;">应用建议</button>
      <div style="font-size:12px; color:var(--text-secondary);">
        • 注册时间 < 30天<br>
        • 标签包含"新客"<br>
        • 打开率 < 20%
      </div>
    </div>
    
    <div>
      <div class="mini">📧 高参与度</div>
      <div class="hint">识别最活跃的邮件订阅者</div>
      <button class="btn secondary" onclick="applySuggestion('high-engagement')" style="width:100%; margin-bottom:8px;">应用建议</button>
      <div style="font-size:12px; color:var(--text-secondary);">
        • 最近30天打开率 > 50%<br>
        • 点击率 > 8%<br>
        • 未退订状态
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideSmartSuggestions()">❌ 关闭</button>
  </div>
</div>

<script>
let segmentRows = [];
let segmentCounter = 0;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  addSegmentRow(); // 添加第一个条件行
  refreshPreview();
});

// 添加条件行
function addSegmentRow() {
  const rowId = `row_${segmentCounter++}`;
  const row = {
    id: rowId,
    field: '',
    operator: '',
    value: '',
    connector: segmentRows.length === 0 ? 'and' : 'and'
  };
  
  segmentRows.push(row);
  renderSegmentRows();
}

// 添加条件组
function addSegmentGroup() {
  const groupId = `group_${segmentCounter++}`;
  const group = {
    id: groupId,
    type: 'group',
    connector: segmentRows.length === 0 ? 'and' : 'and',
    conditions: []
  };
  
  segmentRows.push(group);
  renderSegmentRows();
}

// 渲染所有条件行
function renderSegmentRows() {
  const container = document.getElementById('seg-rows');
  container.innerHTML = '';
  
  segmentRows.forEach((row, index) => {
    if (row.type === 'group') {
      container.appendChild(createGroupElement(row, index));
    } else {
      container.appendChild(createRowElement(row, index));
    }
  });
}

// 创建条件行元素
function createRowElement(row, index) {
  const div = document.createElement('div');
  div.className = 'row';
  div.style.marginBottom = '16px';
  div.style.alignItems = 'center';
  
  // 连接符
  if (index > 0) {
    const connector = document.createElement('select');
    connector.value = row.connector;
    connector.onchange = (e) => row.connector = e.target.value;
    connector.innerHTML = `
      <option value="and">且 (AND)</option>
      <option value="or">或 (OR)</option>
    `;
    div.appendChild(connector);
  } else {
    div.appendChild(document.createElement('div')); // 占位
  }
  
  // 字段选择
  const fieldSelect = document.createElement('select');
  fieldSelect.value = row.field;
  fieldSelect.onchange = (e) => row.field = e.target.value;
  fieldSelect.innerHTML = `
    <option value="">选择字段</option>
    <option value="email">邮箱地址</option>
    <option value="name">姓名</option>
    <option value="company">公司</option>
    <option value="tags">标签</option>
    <option value="location.country">国家/地区</option>
    <option value="location.city">城市</option>
    <option value="preferred_language">标记语言</option>
    <option value="consent_status">同意状态</option>
    <option value="subscription_status">订阅状态</option>
    <option value="last_open_date">最近打开时间</option>
    <option value="last_click_date">最近点击时间</option>
    <option value="open_count">打开次数</option>
    <option value="click_count">点击次数</option>
    <option value="created_at">创建时间</option>
    <option value="custom_fields">自定义字段</option>
  `;
  div.appendChild(fieldSelect);
  
  // 操作符选择
  const operatorSelect = document.createElement('select');
  operatorSelect.value = row.operator;
  operatorSelect.onchange = (e) => row.operator = e.target.value;
  operatorSelect.innerHTML = `
    <option value="">选择操作符</option>
    <option value="equals">等于</option>
    <option value="not_equals">不等于</option>
    <option value="contains">包含</option>
    <option value="not_contains">不包含</option>
    <option value="starts_with">开头是</option>
    <option value="ends_with">结尾是</option>
    <option value="greater_than">大于</option>
    <option value="less_than">小于</option>
    <option value="between">介于</option>
    <option value="in">在列表中</option>
    <option value="not_in">不在列表中</option>
    <option value="is_empty">为空</option>
    <option value="is_not_empty">不为空</option>
  `;
  div.appendChild(operatorSelect);
  
  // 值输入
  const valueInput = document.createElement('input');
  valueInput.value = row.value;
  valueInput.placeholder = '输入值...';
  valueInput.onchange = (e) => row.value = e.target.value;
  div.appendChild(valueInput);
  
  // 删除按钮
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'btn ghost';
  deleteBtn.textContent = '🗑️';
  deleteBtn.onclick = () => removeSegmentRow(row.id);
  div.appendChild(deleteBtn);
  
  return div;
}

// 创建条件组元素
function createGroupElement(group, index) {
  const div = document.createElement('div');
  div.className = 'card';
  div.style.marginBottom = '16px';
  
  const header = document.createElement('div');
  header.className = 'row';
  header.style.alignItems = 'center';
  header.style.marginBottom = '16px';
  
  // 连接符
  if (index > 0) {
    const connector = document.createElement('select');
    connector.value = group.connector;
    connector.onchange = (e) => group.connector = e.target.value;
    connector.innerHTML = `
      <option value="and">且 (AND)</option>
      <option value="or">或 (OR)</option>
    `;
    header.appendChild(connector);
  } else {
    header.appendChild(document.createElement('div')); // 占位
  }
  
  const title = document.createElement('div');
  title.className = 'mini';
  title.textContent = '条件组';
  header.appendChild(title);
  
  const addBtn = document.createElement('button');
  addBtn.className = 'btn ghost';
  addBtn.textContent = '➕ 添加条件';
  addBtn.onclick = () => addConditionToGroup(group.id);
  header.appendChild(addBtn);
  
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'btn ghost';
  deleteBtn.textContent = '🗑️ 删除组';
  deleteBtn.onclick = () => removeSegmentRow(group.id);
  header.appendChild(deleteBtn);
  
  div.appendChild(header);
  
  // 组内条件
  const conditionsDiv = document.createElement('div');
  conditionsDiv.id = `group_${group.id}`;
  conditionsDiv.style.marginLeft = '20px';
  div.appendChild(conditionsDiv);
  
  return div;
}

// 向条件组添加条件
function addConditionToGroup(groupId) {
  const group = segmentRows.find(r => r.id === groupId);
  if (group && group.type === 'group') {
    const condition = {
      id: `cond_${segmentCounter++}`,
      field: '',
      operator: '',
      value: ''
    };
    group.conditions.push(condition);
    renderSegmentRows();
  }
}

// 删除条件行或组
function removeSegmentRow(id) {
  segmentRows = segmentRows.filter(r => r.id !== id);
  renderSegmentRows();
}

// 预估人数
function estimateSegment() {
  // 模拟预估逻辑
  const estimatedCount = Math.floor(Math.random() * 50000) + 1000;
  document.getElementById('est-count').textContent = estimatedCount.toLocaleString();
  
  // 显示预估详情
  alert(`预估结果：\n\n当前条件组合预估匹配 ${estimatedCount.toLocaleString()} 个联系人\n\n预估基于：\n• 历史数据统计\n• 实时索引查询\n• 缓存结果优化\n\n注意：实际发送时人数可能略有差异`);
}

// 保存人群
function saveSegment() {
  const name = document.getElementById('segment-name').value;
  if (!name.trim()) {
    alert('请输入人群名称！');
    return;
  }
  
  if (segmentRows.length === 0) {
    alert('请至少添加一个条件！');
    return;
  }
  
  // 验证条件完整性
  for (let row of segmentRows) {
    if (row.type !== 'group' && (!row.field || !row.operator)) {
      alert('请完善所有条件设置！');
      return;
    }
  }
  
  // 模拟保存
  alert(`人群保存成功！\n\n名称：${name}\n预估人数：${document.getElementById('est-count').textContent}\n条件数量：${segmentRows.length}\n\n已保存到数据库，可在活动创建时选择使用。`);
}

// 显示批量操作面板
function showBulkPanel() {
  document.getElementById('bulk-panel').style.display = '';
  toggleBulkExtra();
}

// 隐藏批量操作面板
function hideBulkPanel() {
  document.getElementById('bulk-panel').style.display = 'none';
}

// 切换批量操作额外选项
function toggleBulkExtra() {
  const action = document.getElementById('bulk-action').value;
  const extraDiv = document.getElementById('bulk-extra');
  
  let html = '';
  switch (action) {
    case 'tag':
      html = `
        <div>
          <label>添加标签（逗号分隔）</label>
          <input id="bulk-add-tags" placeholder="VIP, 高价值, 黑五预热" />
        </div>
        <div>
          <label>移除标签（逗号分隔）</label>
          <input id="bulk-remove-tags" placeholder="新客, 测试" />
        </div>
      `;
      break;
    case 'add-to-list':
      html = `
        <div>
          <label>选择列表</label>
          <select id="bulk-list">
            <option value="">请选择列表</option>
            <option value="vip">VIP客户</option>
            <option value="new">新客户</option>
            <option value="active">活跃用户</option>
          </select>
        </div>
      `;
      break;
    case 'update-fields':
      html = `
        <div>
          <label>更新字段</label>
          <select id="bulk-field">
            <option value="">选择字段</option>
            <option value="preferred_language">标记语言</option>
            <option value="location.country">国家/地区</option>
            <option value="custom:source">来源标记</option>
          </select>
        </div>
        <div>
          <label>新值</label>
          <input id="bulk-value" placeholder="输入新值..." />
        </div>
      `;
      break;
  }
  
  extraDiv.innerHTML = html;
}

// 执行批量操作
function executeBulkOperation() {
  const scope = document.getElementById('bulk-scope').value;
  const action = document.getElementById('bulk-action').value;
  
  let message = `批量操作详情：\n\n`;
  message += `作用范围：${scope === 'all' ? '当前预估人群' : '已选联系人'}\n`;
  message += `操作类型：${action}\n\n`;
  
  switch (action) {
    case 'tag':
      const addTags = document.getElementById('bulk-add-tags')?.value;
      const removeTags = document.getElementById('bulk-remove-tags')?.value;
      if (addTags) message += `添加标签：${addTags}\n`;
      if (removeTags) message += `移除标签：${removeTags}\n`;
      break;
    case 'add-to-list':
      const list = document.getElementById('bulk-list')?.value;
      if (list) message += `目标列表：${list}\n`;
      break;
    case 'update-fields':
      const field = document.getElementById('bulk-field')?.value;
      const value = document.getElementById('bulk-value')?.value;
      if (field && value) message += `更新字段：${field} = ${value}\n`;
      break;
  }
  
  message += `\n任务已创建，后台将异步执行。`;
  alert(message);
}

// 显示智能建议
function showSmartSuggestions() {
  document.getElementById('smart-suggestions').style.display = '';
}

// 隐藏智能建议
function hideSmartSuggestions() {
  document.getElementById('smart-suggestions').style.display = 'none';
}

// 应用智能建议
function applySuggestion(type) {
  // 清空现有条件
  segmentRows = [];
  
  switch (type) {
    case 'high-value':
      segmentRows = [
        { id: 'row_1', field: 'last_purchase_date', operator: 'greater_than', value: '90', connector: 'and' },
        { id: 'row_2', field: 'tags', operator: 'contains', value: 'VIP,复购', connector: 'and' },
        { id: 'row_3', field: 'open_rate', operator: 'greater_than', value: '40', connector: 'and' }
      ];
      break;
    case 'churn-risk':
      segmentRows = [
        { id: 'row_1', field: 'last_open_date', operator: 'less_than', value: '30', connector: 'and' },
        { id: 'row_2', field: 'last_activity_date', operator: 'less_than', value: '60', connector: 'and' },
        { id: 'row_3', field: 'subscription_status', operator: 'equals', value: 'active', connector: 'and' }
      ];
      break;
    case 'new-activation':
      segmentRows = [
        { id: 'row_1', field: 'created_at', operator: 'less_than', value: '30', connector: 'and' },
        { id: 'row_2', field: 'tags', operator: 'contains', value: '新客', connector: 'and' },
        { id: 'row_3', field: 'open_rate', operator: 'less_than', value: '20', connector: 'and' }
      ];
      break;
    case 'high-engagement':
      segmentRows = [
        { id: 'row_1', field: 'open_rate_30d', operator: 'greater_than', value: '50', connector: 'and' },
        { id: 'row_2', field: 'click_rate_30d', operator: 'greater_than', value: '8', connector: 'and' },
        { id: 'row_3', field: 'subscription_status', operator: 'equals', value: 'active', connector: 'and' }
      ];
      break;
  }
  
  renderSegmentRows();
  hideSmartSuggestions();
  
  // 自动预估
  setTimeout(() => {
    estimateSegment();
  }, 500);
  
  alert(`已应用"${type}"建议，请检查并调整条件参数。`);
}

// 刷新预览
function refreshPreview() {
  const count = document.getElementById('preview-count').value;
  const sort = document.getElementById('preview-sort').value;
  
  // 模拟预览数据
  const previewData = [
    { email: '<EMAIL>', name: 'VIP客户', lastActivity: '2天前打开', tags: 'VIP,复购', status: '活跃' },
    { email: '<EMAIL>', name: '活跃用户', lastActivity: '1天前点击', tags: '高价值', status: '活跃' },
    { email: '<EMAIL>', name: '新客户', lastActivity: '3天前打开', tags: '新客', status: '活跃' },
    { email: '<EMAIL>', name: '忠诚客户', lastActivity: '5天前打开', tags: 'VIP,忠诚', status: '活跃' },
    { email: '<EMAIL>', name: '高级客户', lastActivity: '1周前点击', tags: '高级,VIP', status: '活跃' }
  ];
  
  const tbody = document.getElementById('preview-tbody');
  tbody.innerHTML = previewData.slice(0, count).map((item, index) => `
    <tr>
      <td><input type="checkbox" class="chk" onchange="updateSelectedCount()"/></td>
      <td>
        <div style="font-weight: 600">${item.email}</div>
        <div style="color: var(--text-secondary); font-size: 13px">${item.name}</div>
      </td>
      <td>${item.lastActivity}</td>
      <td>${item.tags}</td>
      <td><span class="ok">${item.status}</span></td>
      <td>
        <button class="btn secondary" style="padding: 6px 12px; font-size: 12px;" onclick="viewContactDetail('${item.email}')">详情</button>
      </td>
    </tr>
  `).join('');
  
  updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
  const checkboxes = document.querySelectorAll('.chk:checked');
  document.getElementById('selected-count').textContent = checkboxes.length;
}

// 全选/取消全选
function toggleAllCheckboxes() {
  const mainCheckbox = document.getElementById('chk-all');
  const checkboxes = document.querySelectorAll('.chk');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = mainCheckbox.checked;
  });
  
  updateSelectedCount();
}

// 导出选中的联系人
function exportSelected() {
  const selectedCount = document.querySelectorAll('.chk:checked').length;
  if (selectedCount === 0) {
    alert('请先选择要导出的联系人！');
    return;
  }
  
  alert(`导出任务已创建！\n\n选中联系人：${selectedCount} 个\n格式：CSV\n状态：后台处理中\n\n完成后将发送通知到您的邮箱。`);
}

// 查看联系人详情
function viewContactDetail(email) {
  alert(`查看联系人详情：${email}\n\n功能开发中，将显示完整的联系人信息和历史活动记录。`);
}
</script>


