<div class="kpi">
  <div class="item">
    <div class="mini">🔄 系统状态</div>
    <div class="num">健康</div>
    <div class="hint">所有核心服务正常</div>
  </div>
  <div class="item">
    <div class="mini">📤 队列延迟</div>
    <div class="num">1.2s</div>
    <div class="hint">低于阈值 2s</div>
  </div>
  <div class="item">
    <div class="mini">⚡ 并发处理</div>
    <div class="num">4,856</div>
    <div class="hint">当前活跃任务</div>
  </div>
  <div class="item">
    <div class="mini">📊 成功率</div>
    <div class="num">99.7%</div>
    <div class="hint">任务执行成功率</div>
  </div>
</div>

<div class="toolbar">
  <button class="btn" onclick="showSystemConfig()">⚙️ 系统配置</button>
  <button class="btn secondary" onclick="showTaskManager()">📋 任务管理</button>
  <button class="btn ghost" onclick="showMonitoring()">📊 监控面板</button>
  <button class="btn ghost" onclick="showMaintenance()">🔧 维护工具</button>
  <div class="hint">系统运营管理，监控服务状态，配置任务参数，处理异常情况</div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">🖥️ 系统状态监控</div>
    <table class="table">
      <thead>
        <tr>
          <th>组件</th>
          <th>状态</th>
          <th>性能指标</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <div style="font-weight: 600">投递队列</div>
            <div style="color: var(--text-secondary); font-size: 13px">邮件发送核心服务</div>
          </td>
          <td><span class="ok">✅ 健康</span></td>
          <td>
            <div style="font-size: 12px;">延迟: 1.2s</div>
            <div style="font-size: 12px; color: var(--success);">队列长度: 2,341</div>
          </td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewQueueDetails()">详情</button>
          </td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">Webhook 回调</div>
            <div style="color: var(--text-secondary); font-size: 13px">事件通知服务</div>
          </td>
          <td><span class="warnTxt">⚠️ 警告</span></td>
          <td>
            <div style="font-size: 12px;">失败率: 1.2%</div>
            <div style="font-size: 12px; color: var(--warning);">重试次数: 3</div>
          </td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="fixWebhook()">修复</button>
          </td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">数据库连接</div>
            <div style="color: var(--text-secondary); font-size: 13px">数据存储服务</div>
          </td>
          <td><span class="ok">✅ 健康</span></td>
          <td>
            <div style="font-size: 12px;">连接数: 45/100</div>
            <div style="font-size: 12px; color: var(--success);">响应时间: 12ms</div>
          </td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewDBStatus()">详情</button>
          </td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">缓存服务</div>
            <div style="color: var(--text-secondary); font-size: 13px">Redis缓存</div>
          </td>
          <td><span class="ok">✅ 健康</span></td>
          <td>
            <div style="font-size: 12px;">内存使用: 68%</div>
            <div style="font-size: 12px; color: var(--success);">命中率: 94.2%</div>
          </td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewCacheStatus()">详情</button>
          </td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">邮件验证</div>
            <div style="color: var(--text-secondary); font-size: 13px">邮箱有效性检查</div>
          </td>
          <td><span class="ok">✅ 健康</span></td>
          <td>
            <div style="font-size: 12px;">处理速度: 2,500/分钟</div>
            <div style="font-size: 12px; color: var(--success);">准确率: 98.7%</div>
          </td>
          <td>
            <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewVerificationStatus()">详情</button>
          </td>
        </tr>
      </tbody>
    </table>
    
    <div class="toolbar">
      <button class="btn secondary" onclick="refreshSystemStatus()">🔄 刷新状态</button>
      <button class="btn ghost" onclick="showSystemLogs()">📄 查看日志</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">⚙️ 任务与限流配置</div>
    
    <div class="grid cols-2">
      <div>
        <label>全局发送开关</label>
        <select id="global-send-switch">
          <option selected>开启</option>
          <option>关闭</option>
          <option>维护模式</option>
        </select>
      </div>
      <div>
        <label>每分钟并发上限</label>
        <input id="concurrency-limit" type="number" value="5000" min="1000" max="10000" />
      </div>
      <div>
        <label>单IP发送限制</label>
        <input id="ip-send-limit" type="number" value="100" min="10" max="500" />
      </div>
      <div>
        <label>队列优先级</label>
        <select id="queue-priority">
          <option selected>FIFO（先进先出）</option>
          <option>优先级队列</option>
          <option>时间加权</option>
        </select>
      </div>
      <div>
        <label>失败重试次数</label>
        <input id="retry-count" type="number" value="3" min="0" max="10" />
      </div>
      <div>
        <label>重试间隔（秒）</label>
        <input id="retry-interval" type="number" value="300" min="60" max="3600" />
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 当前限流状态</div>
    <div class="grid cols-2" style="font-size: 12px; margin: 16px 0;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">发送限流</div>
        <div style="color: var(--success);">当前: 4,856/5,000</div>
        <div style="color: var(--text-secondary);">剩余容量: 144</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">IP限流</div>
        <div style="color: var(--success);">活跃IP: 23</div>
        <div style="color: var(--text-secondary);">平均负载: 78%</div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="saveSystemConfig()">💾 保存配置</button>
      <button class="btn secondary" onclick="resetToDefaults()">🔄 重置默认</button>
    </div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📈 系统性能趋势</div>
    <div id="performance-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">📊</div>
        <div>性能监控图表</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 关键指标</div>
    <div class="grid cols-2" style="font-size: 12px;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">CPU使用率</div>
        <div style="color: var(--success);">当前: 23%</div>
        <div style="color: var(--text-secondary);">峰值: 67%</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">内存使用率</div>
        <div style="color: var(--success);">当前: 45%</div>
        <div style="color: var(--text-secondary);">峰值: 78%</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">磁盘I/O</div>
        <div style="color: var(--success);">当前: 12MB/s</div>
        <div style="color: var(--text-secondary);">峰值: 45MB/s</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">网络带宽</div>
        <div style="color: var(--success);">当前: 8MB/s</div>
        <div style="color: var(--text-secondary);">峰值: 32MB/s</div>
      </div>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🚨 告警与通知</div>
    <div class="hint">系统自动检测异常并发送告警</div>
    
    <div style="margin: 16px 0;">
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--err-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">🚨</div>
        <div>
          <div style="font-weight: 600; color: var(--err);">高优先级告警</div>
          <div style="font-size: 12px; color: var(--text-secondary);">Webhook回调失败率超过阈值</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--warning-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">⚠️</div>
        <div>
          <div style="font-weight: 600; color: var(--warning);">中优先级告警</div>
          <div style="font-size: 12px; color: var(--text-secondary);">队列延迟接近阈值</div>
        </div>
      </div>
      
      <div style="display: flex; align-items: center; padding: 12px; background: var(--success-light); border-radius: 8px;">
        <div style="font-size: 24px; margin-right: 12px;">✅</div>
        <div>
          <div style="font-weight: 600; color: var(--success);">系统正常</div>
          <div style="font-size: 12px; color: var(--text-secondary);">所有核心服务运行正常</div>
        </div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn secondary" onclick="acknowledgeAlerts()">✅ 确认告警</button>
      <button class="btn ghost" onclick="configureAlerts()">⚙️ 配置告警</button>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">🔄 失败回放与重试</div>
  <div class="hint">重新处理失败的邮件发送任务</div>
  
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>选择时间窗口</label>
      <input id="replay-time-start" type="datetime-local" value="2025-08-01T10:00" />
    </div>
    <div>
      <label>至</label>
      <input id="replay-time-end" type="datetime-local" value="2025-08-01T11:00" />
    </div>
    <div>
      <label>失败类型</label>
      <select id="replay-failure-type">
        <option>全部失败</option>
        <option>网络超时</option>
        <option>服务器错误</option>
        <option>用户不存在</option>
        <option>其他错误</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn" onclick="startReplay()">🚀 开始回放</button>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">📊 回放任务状态</div>
  <table class="table">
    <thead>
      <tr>
        <th>任务ID</th>
        <th>时间范围</th>
        <th>失败数量</th>
        <th>重试进度</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>replay_001</td>
        <td>2025-08-01 10:00-11:00</td>
        <td>1,234</td>
        <td>
          <div class="progress">
            <div style="width:100%"></div>
          </div>
          <div style="font-size: 12px; color: var(--text-secondary);">100% 完成</div>
        </td>
        <td><span class="ok">✅ 已完成</span></td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewReplayResult('replay_001')">查看结果</button>
        </td>
      </tr>
      <tr>
        <td>replay_002</td>
        <td>2025-08-01 14:00-15:00</td>
        <td>856</td>
        <td>
          <div class="progress">
            <div style="width:65%"></div>
          </div>
          <div style="font-size: 12px; color: var(--text-secondary);">65% 进行中</div>
        </td>
        <td><span class="warnTxt">🔄 进行中</span></td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="pauseReplay('replay_002')">暂停</button>
        </td>
      </tr>
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">共 <b id="replay-count">2</b> 个回放任务</span>
    <button class="btn secondary" onclick="exportReplayResults()">📤 导出结果</button>
  </div>
</div>

<!-- 系统配置弹窗 -->
<div id="system-config" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">⚙️ 系统配置</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">📤 发送配置</div>
      <div>
        <label>最大并发数</label>
        <input id="config-max-concurrency" type="number" value="5000" min="1000" max="20000" />
      </div>
      <div>
        <label>队列大小</label>
        <input id="config-queue-size" type="number" value="100000" min="10000" max="1000000" />
      </div>
      <div>
        <label>超时时间（秒）</label>
        <input id="config-timeout" type="number" value="30" min="10" max="300" />
      </div>
    </div>
    
    <div>
      <div class="mini">🔄 重试配置</div>
      <div>
        <label>最大重试次数</label>
        <input id="config-max-retries" type="number" value="3" min="0" max="10" />
      </div>
      <div>
        <label>重试间隔（秒）</label>
        <input id="config-retry-interval" type="number" value="300" min="60" max="3600" />
      </div>
      <div>
        <label>指数退避</label>
        <select id="config-exponential-backoff">
          <option selected>启用</option>
          <option>禁用</option>
        </select>
      </div>
    </div>
    
    <div>
      <div class="mini">📊 监控配置</div>
      <div>
        <label>监控间隔（秒）</label>
        <input id="config-monitor-interval" type="number" value="30" min="10" max="300" />
      </div>
      <div>
        <label>告警阈值</label>
        <input id="config-alert-threshold" type="number" value="80" min="50" max="95" />
      </div>
      <div>
        <label>日志级别</label>
        <select id="config-log-level">
          <option>DEBUG</option>
          <option>INFO</option>
          <option selected>WARN</option>
          <option>ERROR</option>
        </select>
      </div>
    </div>
    
    <div>
      <div class="mini">🔒 安全配置</div>
      <div>
        <label>IP白名单</label>
        <input id="config-ip-whitelist" placeholder="***********/24, 10.0.0.0/8" />
      </div>
      <div>
        <label>API限流</label>
        <input id="config-api-rate-limit" type="number" value="1000" min="100" max="10000" />
      </div>
      <div>
        <label>会话超时（分钟）</label>
        <input id="config-session-timeout" type="number" value="30" min="5" max="120" />
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="saveSystemConfig()">💾 保存配置</button>
    <button class="btn secondary" onclick="hideSystemConfig()">❌ 关闭</button>
  </div>
</div>

<!-- 任务管理弹窗 -->
<div id="task-manager" class="card" style="display:none; max-width:1000px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📋 任务管理</div>
  
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>任务类型</label>
      <select id="task-type-filter">
        <option>全部任务</option>
        <option>邮件发送</option>
        <option>邮箱验证</option>
        <option>数据导入</option>
        <option>报表生成</option>
      </select>
    </div>
    <div>
      <label>任务状态</label>
      <select id="task-status-filter">
        <option>全部状态</option>
        <option>等待中</option>
        <option>进行中</option>
        <option>已完成</option>
        <option>已失败</option>
        <option>已暂停</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn secondary" onclick="refreshTasks()">🔄 刷新</button>
    </div>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>任务ID</th>
        <th>类型</th>
        <th>状态</th>
        <th>进度</th>
        <th>创建时间</th>
        <th>预计完成</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody id="tasks-tbody">
      <!-- 动态填充任务数据 -->
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">共 <b id="tasks-count">0</b> 个任务</span>
    <button class="btn secondary" onclick="hideTaskManager()">❌ 关闭</button>
  </div>
</div>

<script>
// 系统配置相关函数
function showSystemConfig() {
  document.getElementById('system-config').style.display = '';
}

function hideSystemConfig() {
  document.getElementById('system-config').style.display = 'none';
}

function saveSystemConfig() {
  // 获取所有配置值
  const maxConcurrency = document.getElementById('config-max-concurrency').value;
  const queueSize = document.getElementById('config-queue-size').value;
  const timeout = document.getElementById('config-timeout').value;
  const maxRetries = document.getElementById('config-max-retries').value;
  const retryInterval = document.getElementById('config-retry-interval').value;
  const exponentialBackoff = document.getElementById('config-exponential-backoff').value;
  const monitorInterval = document.getElementById('config-monitor-interval').value;
  const alertThreshold = document.getElementById('config-alert-threshold').value;
  const logLevel = document.getElementById('config-log-level').value;
  const ipWhitelist = document.getElementById('config-ip-whitelist').value;
  const apiRateLimit = document.getElementById('config-api-rate-limit').value;
  const sessionTimeout = document.getElementById('config-session-timeout').value;
  
  alert(`系统配置已保存！\n\n主要配置：\n• 最大并发数: ${maxConcurrency}\n• 队列大小: ${queueSize}\n• 超时时间: ${timeout}s\n• 最大重试: ${maxRetries}\n• 重试间隔: ${retryInterval}s\n• 监控间隔: ${monitorInterval}s\n• 告警阈值: ${alertThreshold}%\n• 日志级别: ${logLevel}\n\n配置将在5分钟内生效。`);
  hideSystemConfig();
}

// 任务管理相关函数
function showTaskManager() {
  document.getElementById('task-manager').style.display = '';
  loadTasks();
}

function hideTaskManager() {
  document.getElementById('task-manager').style.display = 'none';
}

function loadTasks() {
  // 模拟任务数据
  const tasks = [
    {
      id: 'task_001',
      type: '邮件发送',
      status: '进行中',
      progress: 75,
      created: '2025-08-11 10:00',
      estimated: '2025-08-11 10:15'
    },
    {
      id: 'task_002',
      type: '邮箱验证',
      status: '已完成',
      progress: 100,
      created: '2025-08-11 09:00',
      estimated: '2025-08-11 09:30'
    },
    {
      id: 'task_003',
      type: '数据导入',
      status: '等待中',
      progress: 0,
      created: '2025-08-11 10:30',
      estimated: '2025-08-11 11:00'
    }
  ];
  
  const tbody = document.getElementById('tasks-tbody');
  tbody.innerHTML = tasks.map(task => `
    <tr>
      <td>${task.id}</td>
      <td>${task.type}</td>
      <td>
        ${task.status === '进行中' ? '<span class="warnTxt">🔄 进行中</span>' : 
          task.status === '已完成' ? '<span class="ok">✅ 已完成</span>' : 
          '<span class="text-secondary">⏳ 等待中</span>'}
      </td>
      <td>
        <div class="progress">
          <div style="width:${task.progress}%"></div>
        </div>
        <div style="font-size: 12px; color: var(--text-secondary);">${task.progress}%</div>
      </td>
      <td>${task.created}</td>
      <td>${task.estimated}</td>
      <td>
        <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewTaskDetails('${task.id}')">详情</button>
        ${task.status === '进行中' ? 
          '<button class="btn ghost" style="padding: 6px 10px; font-size: 12px;" onclick="pauseTask(\'' + task.id + '\')">暂停</button>' : ''}
      </td>
    </tr>
  `).join('');
  
  document.getElementById('tasks-count').textContent = tasks.length;
}

function refreshTasks() {
  loadTasks();
  alert('任务列表已刷新！');
}

// 其他功能函数
function refreshSystemStatus() {
  alert('系统状态已刷新！\n\n正在检查：\n• 服务健康状态\n• 性能指标\n• 资源使用情况\n• 网络连接状态\n\n请稍候查看最新状态。');
}

function showSystemLogs() {
  alert('查看系统日志功能开发中...\n\n将显示：\n• 实时日志流\n• 日志搜索过滤\n• 错误日志分析\n• 性能日志统计');
}

function viewQueueDetails() {
  alert('查看队列详情功能开发中...\n\n将显示：\n• 队列长度统计\n• 处理速度监控\n• 延迟分析\n• 队列配置');
}

function fixWebhook() {
  alert('正在修复Webhook服务...\n\n修复步骤：\n• 检查网络连接\n• 验证目标服务器\n• 重置重试计数器\n• 测试回调功能\n\n预计5分钟内完成。');
}

function viewDBStatus() {
  alert('查看数据库状态功能开发中...\n\n将显示：\n• 连接池状态\n• 查询性能\n• 锁等待情况\n• 慢查询分析');
}

function viewCacheStatus() {
  alert('查看缓存状态功能开发中...\n\n将显示：\n• 内存使用情况\n• 命中率统计\n• 键值分布\n• 过期策略');
}

function viewVerificationStatus() {
  alert('查看验证服务状态功能开发中...\n\n将显示：\n• 验证队列状态\n• 成功率统计\n• 响应时间分析\n• 错误类型分布');
}

function acknowledgeAlerts() {
  alert('告警已确认！\n\n系统将：\n• 标记告警为已处理\n• 记录确认时间\n• 跟踪后续变化\n• 生成处理报告');
}

function configureAlerts() {
  alert('配置告警功能开发中...\n\n将支持：\n• 告警阈值设置\n• 通知方式配置\n• 告警级别定义\n• 静默期设置');
}

function startReplay() {
  const startTime = document.getElementById('replay-time-start').value;
  const endTime = document.getElementById('replay-time-end').value;
  const failureType = document.getElementById('replay-failure-type').value;
  
  if (!startTime || !endTime) {
    alert('请选择完整的时间范围！');
    return;
  }
  
  alert(`失败回放任务已启动！\n\n时间范围：${startTime} 至 ${endTime}\n失败类型：${failureType}\n\n系统将：\n• 扫描指定时间段的失败任务\n• 重新尝试发送\n• 记录重试结果\n• 生成回放报告`);
}

function viewReplayResult(replayId) {
  alert(`查看回放结果：${replayId}\n\n功能开发中，将显示：\n• 重试成功数量\n• 仍然失败的原因\n• 性能统计\n• 详细日志`);
}

function pauseReplay(replayId) {
  alert(`回放任务已暂停：${replayId}\n\n暂停后：\n• 停止处理新任务\n• 保存当前进度\n• 可随时恢复\n• 已处理结果保留`);
}

function exportReplayResults() {
  alert('回放结果导出任务已创建！\n\n导出内容：\n• 重试成功统计\n• 失败原因分析\n• 性能指标\n• 详细日志\n\n完成后将发送下载链接。');
}

function resetToDefaults() {
  if (confirm('确定要重置为默认配置吗？这将覆盖所有自定义设置。')) {
    document.getElementById('global-send-switch').value = '开启';
    document.getElementById('concurrency-limit').value = '5000';
    document.getElementById('ip-send-limit').value = '100';
    document.getElementById('queue-priority').value = 'FIFO（先进先出）';
    document.getElementById('retry-count').value = '3';
    document.getElementById('retry-interval').value = '300';
    
    alert('配置已重置为默认值！\n\n默认配置：\n• 全局发送：开启\n• 并发上限：5000/分钟\n• IP限制：100/分钟\n• 队列优先级：FIFO\n• 重试次数：3次\n• 重试间隔：300秒');
  }
}

function viewTaskDetails(taskId) {
  alert(`查看任务详情：${taskId}\n\n功能开发中，将显示：\n• 任务配置信息\n• 执行日志\n• 性能指标\n• 错误详情`);
}

function pauseTask(taskId) {
  alert(`任务已暂停：${taskId}\n\n暂停后：\n• 停止执行\n• 保存当前状态\n• 可随时恢复\n• 已处理结果保留`);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 模拟性能图表
  setTimeout(() => {
    const chartDiv = document.getElementById('performance-chart');
    if (chartDiv) {
      chartDiv.innerHTML = `
        <div style="text-align: center; width: 100%;">
          <div style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">系统性能监控</div>
          <div style="height: 150px; background: linear-gradient(90deg, var(--success-light), var(--warning-light), var(--err-light)); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--success); font-weight: 600;">
            模拟性能图表<br>
            CPU: 23% | 内存: 45% | 磁盘: 12MB/s
          </div>
        </div>
      `;
    }
  }, 1000);
});
</script>


