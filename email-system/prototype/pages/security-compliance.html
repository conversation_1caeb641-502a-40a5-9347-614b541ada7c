<div class="kpi">
  <div class="item">
    <div class="mini">🔒 安全评分</div>
    <div class="num">A+</div>
    <div class="hint">优秀水平</div>
  </div>
  <div class="item">
    <div class="mini">📋 合规状态</div>
    <div class="num">100%</div>
    <div class="hint">完全合规</div>
  </div>
  <div class="item">
    <div class="mini">🔍 审计记录</div>
    <div class="num">12,847</div>
    <div class="hint">本月记录数</div>
  </div>
  <div class="item">
    <div class="mini">⚠️ 安全事件</div>
    <div class="num">0</div>
    <div class="hint">本月无事件</div>
  </div>
</div>

<div class="toolbar">
  <button class="btn" onclick="runSecurityScan()">🔍 安全扫描</button>
  <button class="btn secondary" onclick="showComplianceReport()">📊 合规报告</button>
  <button class="btn ghost" onclick="showAuditLogs()">📄 审计日志</button>
  <button class="btn ghost" onclick="showPrivacySettings()">🔐 隐私设置</button>
  <div class="hint">确保系统安全性和合规性，保护用户隐私，满足法规要求</div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">🔐 安全设置</div>
    
    <div class="grid cols-2">
      <div>
        <label>双因素认证</label>
        <select id="2fa-enabled">
          <option selected>强制启用</option>
          <option>可选启用</option>
          <option>禁用</option>
        </select>
      </div>
      <div>
        <label>密码策略</label>
        <select id="password-policy">
          <option>低强度</option>
          <option selected>中等强度</option>
          <option>高强度</option>
          <option>企业级</option>
        </select>
      </div>
      <div>
        <label>会话超时</label>
        <select id="session-timeout">
          <option>15分钟</option>
          <option>30分钟</option>
          <option selected>1小时</option>
          <option>4小时</option>
          <option>永不</option>
        </select>
      </div>
      <div>
        <label>登录尝试限制</label>
        <select id="login-attempts">
          <option>3次</option>
          <option selected>5次</option>
          <option>10次</option>
          <option>无限制</option>
        </select>
      </div>
      <div>
        <label>IP白名单</label>
        <input id="ip-whitelist" placeholder="***********/24, 10.0.0.0/8" />
      </div>
      <div>
        <label>API密钥轮换</label>
        <select id="api-key-rotation">
          <option>30天</option>
          <option selected>90天</option>
          <option>180天</option>
          <option>365天</option>
        </select>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">🔒 安全状态</div>
    <div class="grid cols-2" style="font-size: 12px; margin: 16px 0;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">认证安全</div>
        <div style="color: var(--success);">✅ 双因素认证已启用</div>
        <div style="color: var(--success);">✅ 强密码策略已配置</div>
        <div style="color: var(--success);">✅ 会话管理正常</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">网络安全</div>
        <div style="color: var(--success);">✅ HTTPS强制启用</div>
        <div style="color: var(--success);">✅ 防火墙配置正常</div>
        <div style="color: var(--success);">✅ DDoS防护已启用</div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="saveSecuritySettings()">💾 保存设置</button>
      <button class="btn secondary" onclick="testSecurityConfig()">🧪 测试配置</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">📋 合规管理</div>
    
    <div class="grid cols-2">
      <div>
        <label>数据保护法规</label>
        <div style="margin: 8px 0;">
          <label><input type="checkbox" checked /> GDPR (欧盟)</label><br>
          <label><input type="checkbox" checked /> CCPA (加州)</label><br>
          <label><input type="checkbox" checked /> PIPEDA (加拿大)</label><br>
          <label><input type="checkbox" checked /> LGPD (巴西)</label>
        </div>
      </div>
      <div>
        <label>行业标准</label>
        <div style="margin: 8px 0;">
          <label><input type="checkbox" checked /> ISO 27001</label><br>
          <label><input type="checkbox" checked /> SOC 2 Type II</label><br>
          <label><input type="checkbox" checked /> PCI DSS</label><br>
          <label><input type="checkbox" /> HIPAA</label>
        </div>
      </div>
      <div>
        <label>合规检查频率</label>
        <select id="compliance-check-frequency">
          <option>每周</option>
          <option selected>每月</option>
          <option>每季度</option>
          <option>每年</option>
        </select>
      </div>
      <div>
        <label>自动合规报告</label>
        <select id="auto-compliance-report">
          <option selected>启用</option>
          <option>禁用</option>
        </select>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 合规状态</div>
    <div style="font-size: 12px; margin: 16px 0;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>GDPR合规</span>
        <span style="color: var(--success);">✅ 100%</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>CCPA合规</span>
        <span style="color: var(--success);">✅ 100%</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>ISO 27001</span>
        <span style="color: var(--success);">✅ 100%</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span>PCI DSS</span>
        <span style="color: var(--success);">✅ 100%</span>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="runComplianceCheck()">🔍 运行检查</button>
      <button class="btn secondary" onclick="exportComplianceReport()">📤 导出报告</button>
    </div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📧 同意与退订管理</div>
    
    <div class="grid cols-2">
      <div>
        <label>默认同意通道</label>
        <select id="default-consent-channel">
          <option selected>邮箱</option>
          <option>短信</option>
          <option>双重确认</option>
        </select>
      </div>
      <div>
        <label>同意记录保留</label>
        <select id="consent-retention">
          <option>6个月</option>
          <option selected>12个月</option>
          <option>24个月</option>
          <option>永久</option>
        </select>
      </div>
      <div>
        <label>退订页模板</label>
        <input id="unsubscribe-template" placeholder="https://unsubscribe.example.com/tpl-1" />
      </div>
      <div>
        <label>退订确认</label>
        <select id="unsubscribe-confirmation">
          <option selected>立即退订</option>
          <option>确认后退订</option>
          <option>延迟退订</option>
        </select>
      </div>
      <div>
        <label>重新订阅冷却期</label>
        <select id="resubscribe-cooldown">
          <option>无限制</option>
          <option>7天</option>
          <option selected>30天</option>
          <option>90天</option>
        </select>
      </div>
      <div>
        <label>同意撤回</label>
        <select id="consent-withdrawal">
          <option selected>允许</option>
          <option>限制</option>
          <option>禁止</option>
        </select>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 同意统计</div>
    <div class="grid cols-2" style="font-size: 12px; margin: 16px 0;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">同意状态</div>
        <div style="color: var(--success);">明确同意: 89,432</div>
        <div style="color: var(--warning);">隐含同意: 12,567</div>
        <div style="color: var(--err);">未同意: 3,241</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">退订情况</div>
        <div style="color: var(--text-secondary);">本月退订: 1,234</div>
        <div style="color: var(--text-secondary);">重新订阅: 567</div>
        <div style="color: var(--text-secondary);">退订率: 1.2%</div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="saveConsentSettings()">💾 保存设置</button>
      <button class="btn secondary" onclick="viewConsentHistory()">📋 查看历史</button>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🗄️ 数据治理</div>
    
    <div class="grid cols-2">
      <div>
        <label>数据保留期</label>
        <select id="data-retention-period">
          <option>6个月</option>
          <option selected>12个月</option>
          <option>24个月</option>
          <option>36个月</option>
          <option>永不删除</option>
        </select>
      </div>
      <div>
        <label>数据分类</label>
        <select id="data-classification">
          <option selected>自动分类</option>
          <option>手动分类</option>
          <option>混合模式</option>
        </select>
      </div>
      <div>
        <label>敏感数据处理</label>
        <select id="sensitive-data-handling">
          <option selected>加密存储</option>
          <option>脱敏处理</option>
          <option>访问控制</option>
        </select>
      </div>
      <div>
        <label>数据备份</label>
        <select id="data-backup">
          <option>每日</option>
          <option selected>每周</option>
          <option>每月</option>
        </select>
      </div>
      <div>
        <label>数据销毁</label>
        <select id="data-destruction">
          <option selected>安全删除</option>
          <option>物理销毁</option>
          <option>加密销毁</option>
        </select>
      </div>
      <div>
        <label>审计日志保留</label>
        <select id="audit-log-retention">
          <option>6个月</option>
          <option selected>12个月</option>
          <option>24个月</option>
          <option>永久</option>
        </select>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 数据统计</div>
    <div class="grid cols-2" style="font-size: 12px; margin: 16px 0;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">存储状态</div>
        <div style="color: var(--success);">总数据量: 2.3TB</div>
        <div style="color: var(--success);">已加密: 100%</div>
        <div style="color: var(--success);">备份状态: 正常</div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">数据质量</div>
        <div style="color: var(--success);">完整性: 99.8%</div>
        <div style="color: var(--success);">准确性: 99.5%</div>
        <div style="color: var(--success);">一致性: 99.7%</div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="saveDataGovernance()">💾 保存设置</button>
      <button class="btn secondary" onclick="runDataAudit()">🔍 数据审计</button>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">📋 数据请求管理</div>
  <div class="hint">处理用户的数据访问、导出和删除请求</div>
  
  <div class="row" style="margin-bottom: 16px;">
    <div>
      <label>搜索用户</label>
      <input id="user-search" placeholder="输入邮箱地址或用户ID" />
    </div>
    <div>
      <label>请求类型</label>
      <select id="request-type">
        <option>全部类型</option>
        <option>数据访问</option>
        <option>数据导出</option>
        <option>数据删除</option>
        <option>同意撤回</option>
      </select>
    </div>
    <div>
      <label>状态筛选</label>
      <select id="request-status">
        <option>全部状态</option>
        <option>待处理</option>
        <option>处理中</option>
        <option>已完成</option>
        <option>已拒绝</option>
      </select>
    </div>
    <div style="flex: 0">
      <button class="btn" onclick="searchRequests()">🔍 搜索</button>
      <button class="btn secondary" onclick="createNewRequest()">➕ 新建请求</button>
    </div>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>请求ID</th>
        <th>用户信息</th>
        <th>请求类型</th>
        <th>提交时间</th>
        <th>状态</th>
        <th>处理时间</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>DR_001</td>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">用户ID: 12345</div>
        </td>
        <td>数据导出</td>
        <td>2025-08-11 10:00</td>
        <td><span class="warnTxt">🔄 处理中</span></td>
        <td>--</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewRequestDetails('DR_001')">详情</button>
        </td>
      </tr>
      <tr>
        <td>DR_002</td>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">用户ID: 67890</div>
        </td>
        <td>数据删除</td>
        <td>2025-08-11 09:30</td>
        <td><span class="ok">✅ 已完成</span></td>
        <td>2025-08-11 10:15</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewRequestDetails('DR_002')">详情</button>
        </td>
      </tr>
      <tr>
        <td>DR_003</td>
        <td>
          <div style="font-weight: 600"><EMAIL></div>
          <div style="color: var(--text-secondary); font-size: 13px">用户ID: 11111</div>
        </td>
        <td>同意撤回</td>
        <td>2025-08-11 08:45</td>
        <td><span class="err">❌ 已拒绝</span></td>
        <td>2025-08-11 09:00</td>
        <td>
          <button class="btn secondary" style="padding: 6px 10px; font-size: 12px;" onclick="viewRequestDetails('DR_003')">详情</button>
        </td>
      </tr>
    </tbody>
  </table>
  
  <div class="toolbar">
    <span class="mini">共 <b id="requests-count">3</b> 个数据请求</span>
    <button class="btn secondary" onclick="exportRequests()">📤 导出请求</button>
  </div>
</div>

<!-- 安全扫描弹窗 -->
<div id="security-scan" class="card" style="display:none; max-width:800px; position:fixed; top:8%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">🔍 安全扫描</div>
  <div class="hint">全面检查系统安全状态，识别潜在风险</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">🔒 扫描范围</div>
      <div style="margin: 8px 0;">
        <label><input type="checkbox" checked /> 认证系统</label><br>
        <label><input type="checkbox" checked /> 网络安全</label><br>
        <label><input type="checkbox" checked /> 数据保护</label><br>
        <label><input type="checkbox" checked /> 应用安全</label><br>
        <label><input type="checkbox" checked /> 基础设施</label>
      </div>
    </div>
    
    <div>
      <div class="mini">⚙️ 扫描选项</div>
      <div>
        <label>扫描深度</label>
        <select id="scan-depth">
          <option>快速扫描</option>
          <option selected>标准扫描</option>
          <option>深度扫描</option>
        </select>
      </div>
      <div>
        <label>自动修复</label>
        <select id="auto-fix">
          <option selected>启用</option>
          <option>禁用</option>
        </select>
      </div>
      <div>
        <label>生成报告</label>
        <select id="generate-report">
          <option selected>是</option>
          <option>否</option>
        </select>
      </div>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">📊 预估结果</div>
  <div class="hint">基于上次扫描结果，预估本次扫描发现的问题</div>
  
  <div class="grid cols-3" style="font-size: 12px; margin: 16px 0;">
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">严重问题</div>
      <div style="color: var(--success);">预计: 0个</div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">中等问题</div>
      <div style="color: var(--warning);">预计: 2-3个</div>
    </div>
    <div>
      <div style="font-weight: 600; margin-bottom: 8px;">轻微问题</div>
      <div style="color: var(--text-secondary);">预计: 5-8个</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="startSecurityScan()">🚀 开始扫描</button>
    <button class="btn secondary" onclick="hideSecurityScan()">❌ 关闭</button>
  </div>
</div>

<!-- 合规报告弹窗 -->
<div id="compliance-report" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">📊 合规报告</div>
  <div class="hint">综合评估系统合规状态，提供改进建议</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">📈 整体评分</div>
      <div style="text-align: center; margin: 20px 0;">
        <div style="font-size: 48px; color: var(--success);">A+</div>
        <div style="font-size: 18px; font-weight: 600;">优秀</div>
        <div style="font-size: 14px; color: var(--text-secondary);">得分: 98/100</div>
      </div>
    </div>
    
    <div>
      <div class="mini">🎯 合规详情</div>
      <div style="font-size: 13px; margin: 16px 0;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>GDPR合规</span>
          <span style="color: var(--success);">100%</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>CCPA合规</span>
          <span style="color: var(--success);">100%</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span>ISO 27001</span>
          <span style="color: var(--success);">100%</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>PCI DSS</span>
          <span style="color: var(--success);">100%</span>
        </div>
      </div>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">💡 改进建议</div>
  <div style="font-size: 13px; margin: 16px 0;">
    <div style="color: var(--success); margin-bottom: 8px;">✅ <strong>继续保持</strong></div>
    <div style="color: var(--text-secondary); margin-left: 20px; margin-bottom: 12px;">
      • 定期安全培训<br>
      • 持续监控系统<br>
      • 及时更新策略
    </div>
    
    <div style="color: var(--warning); margin-bottom: 8px;">⚠️ <strong>需要关注</strong></div>
    <div style="color: var(--text-secondary); margin-left: 20px;">
      • 第三方供应商评估<br>
      • 新兴法规跟踪<br>
      • 技术更新适配
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="generateDetailedReport()">📋 生成详细报告</button>
    <button class="btn secondary" onclick="hideComplianceReport()">❌ 关闭</button>
  </div>
</div>

<script>
// 安全相关函数
function runSecurityScan() {
  document.getElementById('security-scan').style.display = '';
}

function hideSecurityScan() {
  document.getElementById('security-scan').style.display = 'none';
}

function startSecurityScan() {
  const scanDepth = document.getElementById('scan-depth').value;
  const autoFix = document.getElementById('auto-fix').value;
  const generateReport = document.getElementById('generate-report').value;
  
  alert(`安全扫描已启动！\n\n扫描配置：\n• 扫描深度：${scanDepth}\n• 自动修复：${autoFix}\n• 生成报告：${generateReport}\n\n预计扫描时间：15-30分钟\n完成后将发送通知。`);
  hideSecurityScan();
}

function saveSecuritySettings() {
  const twoFactor = document.getElementById('2fa-enabled').value;
  const passwordPolicy = document.getElementById('password-policy').value;
  const sessionTimeout = document.getElementById('session-timeout').value;
  const loginAttempts = document.getElementById('login-attempts').value;
  const ipWhitelist = document.getElementById('ip-whitelist').value;
  const apiKeyRotation = document.getElementById('api-key-rotation').value;
  
  alert(`安全设置已保存！\n\n主要设置：\n• 双因素认证：${twoFactor}\n• 密码策略：${passwordPolicy}\n• 会话超时：${sessionTimeout}\n• 登录尝试限制：${loginAttempts}\n• IP白名单：${ipWhitelist}\n• API密钥轮换：${apiKeyRotation}\n\n设置将在5分钟内生效。`);
}

function testSecurityConfig() {
  alert('正在测试安全配置...\n\n测试项目：\n• 认证流程\n• 网络连接\n• 权限控制\n• 加密功能\n\n预计2分钟内完成测试。');
}

// 合规相关函数
function showComplianceReport() {
  document.getElementById('compliance-report').style.display = '';
}

function hideComplianceReport() {
  document.getElementById('compliance-report').style.display = 'none';
}

function runComplianceCheck() {
  alert('合规检查已启动！\n\n检查项目：\n• 法规合规性\n• 政策执行\n• 数据处理\n• 用户权利\n\n预计10分钟内完成检查。');
}

function exportComplianceReport() {
  alert('合规报告导出任务已创建！\n\n导出内容：\n• 合规状态评估\n• 风险评估报告\n• 改进建议\n• 历史记录\n\n完成后将发送下载链接。');
}

function generateDetailedReport() {
  alert('详细报告生成中...\n\n包含内容：\n• 完整的合规分析\n• 风险评估详情\n• 具体改进措施\n• 时间计划\n\n完成后将发送到您的邮箱。');
}

// 同意管理相关函数
function saveConsentSettings() {
  const defaultChannel = document.getElementById('default-consent-channel').value;
  const consentRetention = document.getElementById('consent-retention').value;
  const unsubscribeTemplate = document.getElementById('unsubscribe-template').value;
  const unsubscribeConfirmation = document.getElementById('unsubscribe-confirmation').value;
  const resubscribeCooldown = document.getElementById('resubscribe-cooldown').value;
  const consentWithdrawal = document.getElementById('consent-withdrawal').value;
  
  alert(`同意设置已保存！\n\n主要设置：\n• 默认同意通道：${defaultChannel}\n• 同意记录保留：${consentRetention}\n• 退订页模板：${unsubscribeTemplate}\n• 退订确认：${unsubscribeConfirmation}\n• 重新订阅冷却期：${resubscribeCooldown}\n• 同意撤回：${consentWithdrawal}\n\n设置已生效。`);
}

function viewConsentHistory() {
  alert('查看同意历史功能开发中...\n\n将显示：\n• 用户同意记录\n• 同意时间线\n• 同意来源\n• 同意状态变化');
}

// 数据治理相关函数
function saveDataGovernance() {
  const retentionPeriod = document.getElementById('data-retention-period').value;
  const dataClassification = document.getElementById('data-classification').value;
  const sensitiveDataHandling = document.getElementById('sensitive-data-handling').value;
  const dataBackup = document.getElementById('data-backup').value;
  const dataDestruction = document.getElementById('data-destruction').value;
  const auditLogRetention = document.getElementById('audit-log-retention').value;
  
  alert(`数据治理设置已保存！\n\n主要设置：\n• 数据保留期：${retentionPeriod}\n• 数据分类：${dataClassification}\n• 敏感数据处理：${sensitiveDataHandling}\n• 数据备份：${dataBackup}\n• 数据销毁：${dataDestruction}\n• 审计日志保留：${auditLogRetention}\n\n设置将在10分钟内生效。`);
}

function runDataAudit() {
  alert('数据审计已启动！\n\n审计项目：\n• 数据完整性\n• 数据准确性\n• 数据一致性\n• 数据安全性\n• 合规性检查\n\n预计30分钟内完成审计。');
}

// 数据请求管理相关函数
function searchRequests() {
  const userSearch = document.getElementById('user-search').value;
  const requestType = document.getElementById('request-type').value;
  const requestStatus = document.getElementById('request-status').value;
  
  if (!userSearch.trim()) {
    alert('请输入搜索条件！');
    return;
  }
  
  alert(`搜索数据请求中...\n\n搜索条件：\n• 用户：${userSearch}\n• 请求类型：${requestType}\n• 状态：${requestStatus}\n\n正在搜索相关请求，请稍候...`);
}

function createNewRequest() {
  alert('新建数据请求功能开发中...\n\n将支持：\n• 手动创建请求\n• 批量导入请求\n• 请求模板管理\n• 自动分配处理');
}

function viewRequestDetails(requestId) {
  alert(`查看请求详情：${requestId}\n\n功能开发中，将显示：\n• 完整的请求信息\n• 处理进度\n• 相关文档\n• 历史记录`);
}

function exportRequests() {
  alert('数据请求导出任务已创建！\n\n导出内容：\n• 筛选后的请求列表\n• 请求详细信息\n• 处理状态\n• 统计汇总\n\n完成后将发送下载链接。');
}

// 其他功能函数
function showAuditLogs() {
  alert('查看审计日志功能开发中...\n\n将显示：\n• 系统操作日志\n• 用户行为记录\n• 安全事件日志\n• 合规检查记录');
}

function showPrivacySettings() {
  alert('隐私设置功能开发中...\n\n将支持：\n• 隐私政策配置\n• 数据使用设置\n• 用户偏好管理\n• 隐私通知设置');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 设置默认值
  document.getElementById('user-search').value = '';
  document.getElementById('request-type').value = '全部类型';
  document.getElementById('request-status').value = '全部状态';
});
</script>


