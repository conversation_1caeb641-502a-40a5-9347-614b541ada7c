<div class="toolbar">
  <button class="btn">🧱 新建模板</button>
  <button class="btn secondary">📥 导入 HTML</button>
  <button class="btn ghost">📚 模板库</button>
  <button class="btn ghost">🔧 变量字典</button>
  <div style="margin-left: auto; display:flex; gap:8px; align-items:center;">
    <label class="mini" style="margin:0;">语言</label>
    <select id="tpl-locale" style="min-width: 120px;">
      <option value="zh-CN" selected>中文（简体） zh-CN</option>
      <option value="en-US">English en-US</option>
      <option value="es-ES">Español es-ES</option>
    </select>
    <button class="btn ghost" id="add-locale">➕ 新增语言</button>
  </div>
</div>

<div class="grid cols-3">
  <div class="card" style="cursor: pointer; transition: all 0.2s ease;">
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
      <div class="mini">简约促销</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已发布</div>
    </div>
    <div style="height: 120px; background: linear-gradient(135deg, #f0f9ff, #e0f2fe); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
      <div style="text-align: center; color: var(--pri);">
        <div style="font-size: 24px; margin-bottom: 8px;">🎯</div>
        <div style="font-weight: 600;">促销模板</div>
      </div>
    </div>
    <div class="hint">点击编辑 | 使用次数: 1,234</div>
  </div>
  
  <div class="card" style="cursor: pointer; transition: all 0.2s ease;">
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
      <div class="mini">周报模板</div>
      <div style="font-size: 12px; color: var(--success);">✅ 已发布</div>
    </div>
    <div style="height: 120px; background: linear-gradient(135deg, #f0fdf4, #dcfce7); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
      <div style="text-align: center; color: var(--success);">
        <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
        <div style="font-weight: 600;">周报模板</div>
      </div>
    </div>
    <div class="hint">点击编辑 | 使用次数: 567</div>
  </div>
  
  <div class="card" style="cursor: pointer; transition: all 0.2s ease;">
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
      <div class="mini">节日祝福</div>
      <div style="font-size: 12px; color: var(--warning);">⚠️ 草稿</div>
    </div>
    <div style="height: 120px; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
      <div style="text-align: center; color: var(--warning);">
        <div style="font-size: 24px; margin-bottom: 8px;">🎉</div>
        <div style="font-weight: 600;">节日模板</div>
      </div>
    </div>
    <div class="hint">点击编辑 | 使用次数: 89</div>
  </div>
</div>

<div class="card">
  <div class="mini">✏️ 模板编辑器</div>
  <div class="grid cols-2">
    <div>
      <div class="row">
        <div>
          <label>邮件主题 *</label>
          <input id="tpl-subject" value="本周特惠来啦！限时7折优惠等你来抢！" />
        </div>
        <div>
          <label>发件人名称</label>
          <input value="Marketing Team" />
        </div>
      </div>
      
      <label>预览文本</label>
      <input id="tpl-preheader" value="内含 7 折券码与精选商品，限时抢购，先到先得！" />
      
      <label>邮件正文（HTML）</label>
      <textarea id="tpl-html" style="min-height: 200px;">&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;meta charset="utf-8"&gt;
  &lt;title&gt;本周特惠&lt;/title&gt;
&lt;/head&gt;
&lt;body style="margin: 0; padding: 20px; font-family: Arial, sans-serif;"&gt;
  &lt;div style="max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);"&gt;
    
    &lt;!-- Header --&gt;
    &lt;div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; text-align: center;"&gt;
      &lt;h1 style="margin: 0; font-size: 28px;"&gt;🎉 本周特惠来啦！&lt;/h1&gt;
      &lt;p style="margin: 10px 0 0; opacity: 0.9;"&gt;Hello {{first_name}}，专属优惠等你来拿&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;!-- Content --&gt;
    &lt;div style="padding: 30px;"&gt;
      &lt;h2 style="color: #1f2937; margin-bottom: 20px;"&gt;🔥 限时7折优惠&lt;/h2&gt;
      &lt;p style="color: #4b5563; line-height: 1.6; margin-bottom: 25px;"&gt;
        亲爱的 {{first_name}}，感谢您一直以来的支持！本周我们为您准备了超值优惠活动。
      &lt;/p&gt;
      
      &lt;div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 25px 0;"&gt;
        &lt;h3 style="color: #1f2937; margin: 0 0 15px;"&gt;🎁 优惠详情&lt;/h3&gt;
        &lt;ul style="color: #4b5563; margin: 0; padding-left: 20px;"&gt;
          &lt;li&gt;全场商品7折优惠&lt;/li&gt;
          &lt;li&gt;满200减50&lt;/li&gt;
          &lt;li&gt;新用户额外9折&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
      
      &lt;div style="text-align: center; margin: 30px 0;"&gt;
        &lt;a href="#" style="background: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block;"&gt;
          🚀 立即抢购
        &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Footer --&gt;
    &lt;div style="background: #f9fafb; padding: 20px; text-align: center; color: #6b7280;"&gt;
      &lt;p style="margin: 0; font-size: 14px;"&gt;优惠码：WEEK70&lt;/p&gt;
      &lt;p style="margin: 5px 0 0; font-size: 12px;"&gt;活动时间：2025年8月1日-8月7日&lt;/p&gt;
    &lt;/div&gt;
    
  &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</textarea>
    </div>
    
    <div class="sticky">
      <div class="mini">📱 实时预览</div>
      <div class="card" style="background: var(--panel-2); border: 2px solid var(--border); max-height: 500px; overflow-y: auto;">
        <div style="background: white; color: #111; border-radius: 10px; padding: 0; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 20px; text-align: center;">
            <h1 id="preview-title" style="margin: 0; font-size: 20px;">🎉 本周特惠来啦！</h1>
            <p id="preview-sub" style="margin: 8px 0 0; opacity: 0.9; font-size: 14px;">Hello John，专属优惠等你来拿</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 20px;">
            <h2 style="color: #1f2937; margin-bottom: 15px; font-size: 18px;">🔥 限时7折优惠</h2>
            <p style="color: #4b5563; line-height: 1.5; margin-bottom: 20px; font-size: 14px;">
              亲爱的 John，感谢您一直以来的支持！本周我们为您准备了超值优惠活动。
            </p>
            
            <div style="background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h3 style="color: #1f2937; margin: 0 0 10px; font-size: 16px;">🎁 优惠详情</h3>
              <ul style="color: #4b5563; margin: 0; padding-left: 15px; font-size: 14px;">
                <li>全场商品7折优惠</li>
                <li>满200减50</li>
                <li>新用户额外9折</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
               <a id="preview-cta" href="#" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block; font-size: 14px;">🚀 立即抢购</a>
            </div>
          </div>
          
          <!-- Footer -->
          <div style="background: #f9fafb; padding: 15px; text-align: center; color: #6b7280;">
            <p style="margin: 0; font-size: 12px;">优惠码：WEEK70</p>
            <p style="margin: 3px 0 0; font-size: 11px;">活动时间：2025年8月1日-8月7日</p>
          </div>
        </div>
      </div>
      
      <div class="toolbar">
        <button class="btn">💾 保存模板</button>
        <button class="btn secondary">👁️ 预览测试</button>
        <button class="btn ghost">📤 发布</button>
      </div>
      
      <div class="hint">💡 提示：使用 {{变量名}} 语法插入动态内容，支持 first_name、company、unsubscribe_url 等</div>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">📚 模板分类管理</div>
  <div class="grid cols-4">
    <div style="text-align: center; padding: 20px; background: var(--panel-2); border-radius: 8px;">
      <div style="font-size: 24px; margin-bottom: 8px;">🎯</div>
      <div style="font-weight: 600; margin-bottom: 4px;">促销营销</div>
      <div style="font-size: 12px; color: var(--muted);">12 个模板</div>
    </div>
    <div style="text-align: center; padding: 20px; background: var(--panel-2); border-radius: 8px;">
      <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
      <div style="font-weight: 600; margin-bottom: 4px;">内容营销</div>
      <div style="font-size: 12px; color: var(--muted);">8 个模板</div>
    </div>
    <div style="text-align: center; padding: 20px; background: var(--panel-2); border-radius: 8px;">
      <div style="font-size: 24px; margin-bottom: 8px;">🎉</div>
      <div style="font-weight: 600; margin-bottom: 4px;">节日祝福</div>
      <div style="font-size: 12px; color: var(--muted);">6 个模板</div>
    </div>
    <div style="text-align: center; padding: 20px; background: var(--panel-2); border-radius: 8px;">
      <div style="font-size: 24px; margin-bottom: 8px;">🔔</div>
      <div style="font-weight: 600; margin-bottom: 4px;">通知提醒</div>
      <div style="font-size: 12px; color: var(--muted);">4 个模板</div>
    </div>
  </div>
</div>


