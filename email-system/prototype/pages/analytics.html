<div class="kpi">
  <div class="item">
    <div class="mini">📊 总发送量</div>
    <div class="num">1,203,114</div>
    <div class="hint">较上月 +8.7%</div>
  </div>
  <div class="item">
    <div class="mini">📈 平均打开率</div>
    <div class="num">35.1%</div>
    <div class="hint">行业平均 32.4%</div>
  </div>
  <div class="item">
    <div class="mini">🔗 平均点击率</div>
    <div class="num">4.9%</div>
    <div class="hint">较上月 +0.3%</div>
  </div>
  <div class="item">
    <div class="mini">💌 送达率</div>
    <div class="num">98.3%</div>
    <div class="hint">优秀水平</div>
  </div>
</div>

<div class="toolbar">
  <button class="btn" onclick="generateReport()">📊 生成报表</button>
  <button class="btn secondary" onclick="exportData()">📤 导出数据</button>
  <button class="btn ghost" onclick="showInsights()">💡 智能洞察</button>
  <button class="btn ghost" onclick="showRealTime()">⏰ 实时监控</button>
  <div class="hint">深度分析邮件营销效果，发现优化机会，提升转化率</div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📋 快速报表生成</div>
    <div class="row">
      <div>
        <label>分析维度</label>
        <select id="analysis-dimension">
          <option>按活动</option>
          <option>按列表</option>
          <option>按域名</option>
          <option>按时间段</option>
          <option>按用户类型</option>
          <option>按地理位置</option>
          <option>按设备类型</option>
        </select>
      </div>
      <div>
        <label>核心指标</label>
        <select id="core-metrics">
          <option>发送/投递/打开/点击/退回/投诉</option>
          <option>转化漏斗</option>
          <option>用户行为</option>
          <option>地域分布</option>
          <option>时间分布</option>
          <option>设备偏好</option>
        </select>
      </div>
      <div style="flex: 0">
        <button class="btn" onclick="generateCustomReport()">📊 生成报表</button>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 数据概览</div>
    <table class="table">
      <thead>
        <tr>
          <th>维度值</th>
          <th>发送量</th>
          <th>打开率</th>
          <th>点击率</th>
          <th>转化率</th>
          <th>趋势</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <div style="font-weight: 600">双11预热-1</div>
            <div style="color: var(--text-secondary); font-size: 13px">促销活动</div>
          </td>
          <td>120,114</td>
          <td><span class="ok">38.2%</span></td>
          <td><span class="ok">6.1%</span></td>
          <td><span class="ok">2.3%</span></td>
          <td><span class="ok">↗️ +12%</span></td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">周报推送</div>
            <div style="color: var(--text-secondary); font-size: 13px">内容营销</div>
          </td>
          <td>89,432</td>
          <td><span class="ok">42.1%</span></td>
          <td><span class="ok">8.7%</span></td>
          <td><span class="ok">1.8%</span></td>
          <td><span class="ok">↗️ +5%</span></td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">VIP专享</div>
            <div style="color: var(--text-secondary); font-size: 13px">会员营销</div>
          </td>
          <td>45,203</td>
          <td><span class="ok">51.3%</span></td>
          <td><span class="ok">12.4%</span></td>
          <td><span class="ok">4.2%</span></td>
          <td><span class="ok">↗️ +8%</span></td>
        </tr>
        <tr>
          <td>
            <div style="font-weight: 600">新用户欢迎</div>
            <div style="color: var(--text-secondary); font-size: 13px">用户激活</div>
          </td>
          <td>67,890</td>
          <td><span class="warnTxt">28.7%</span></td>
          <td><span class="warnTxt">3.2%</span></td>
          <td><span class="warnTxt">0.8%</span></td>
          <td><span class="err">↘️ -3%</span></td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="card">
    <div class="mini">📈 转化漏斗分析</div>
    <div class="hint">发送 → 投递 → 打开 → 点击 → 转化</div>
    
    <div style="margin: 20px 0;">
      <div style="display: flex; align-items: center; margin-bottom: 16px;">
        <div style="width: 120px; font-weight: 600; color: var(--text-secondary);">发送</div>
        <div style="flex: 1; height: 40px; background: var(--pri-light); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--pri); font-weight: 600;">100%</div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 16px;">
        <div style="width: 120px; font-weight: 600; color: var(--text-secondary);">投递</div>
        <div style="flex: 1; height: 40px; background: var(--success-light); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--success); font-weight: 600;">98.3%</div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 16px;">
        <div style="width: 120px; font-weight: 600; color: var(--text-secondary);">打开</div>
        <div style="flex: 1; height: 40px; background: var(--warning-light); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--warning); font-weight: 600;">35.1%</div>
      </div>
      
      <div style="display: flex; align-items: center; margin-bottom: 16px;">
        <div style="width: 120px; font-weight: 600; color: var(--text-secondary);">点击</div>
        <div style="flex: 1; height: 40px; background: var(--pri-light); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--pri); font-weight: 600;">4.9%</div>
      </div>
      
      <div style="display: flex; align-items: center;">
        <div style="width: 120px; font-weight: 600; color: var(--text-secondary);">转化</div>
        <div style="flex: 1; height: 40px; background: var(--success-light); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--success); font-weight: 600;">1.2%</div>
      </div>
    </div>
    
    <div class="hint">💡 建议：优化邮件主题行和内容，提高打开率；优化CTA按钮，提升点击转化</div>
    
    <div class="divider"></div>
    
    <div class="mini">🎯 优化建议</div>
    <div style="font-size: 13px; color: var(--text-secondary);">
      • <strong>打开率提升</strong>：测试不同主题行，优化发送时间<br>
      • <strong>点击率提升</strong>：优化邮件布局，突出CTA按钮<br>
      • <strong>转化率提升</strong>：个性化内容，减少跳出率
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">📊 趋势分析</div>
  <div class="grid cols-4">
    <div>
      <label>时间范围</label>
      <select id="time-range">
        <option>最近7天</option>
        <option>最近30天</option>
        <option>最近90天</option>
        <option>最近6个月</option>
        <option>最近1年</option>
        <option>自定义</option>
      </select>
    </div>
    <div>
      <label>对比周期</label>
      <select id="compare-period">
        <option>无对比</option>
        <option>上一周期</option>
        <option>去年同期</option>
        <option>历史最佳</option>
      </select>
    </div>
    <div>
      <label>图表类型</label>
      <select id="chart-type">
        <option>折线图</option>
        <option>柱状图</option>
        <option>面积图</option>
        <option>热力图</option>
        <option>散点图</option>
      </select>
    </div>
    <div>
      <label>指标选择</label>
      <select id="metric-select">
        <option>打开率</option>
        <option>点击率</option>
        <option>转化率</option>
        <option>送达率</option>
        <option>投诉率</option>
      </select>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div id="trend-chart" style="height: 300px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
    <div style="text-align: center;">
      <div style="font-size: 48px; margin-bottom: 16px;">📈</div>
      <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">趋势图表</div>
      <div style="font-size: 14px;">选择参数后生成可视化图表</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="generateTrendChart()">🔄 生成图表</button>
    <button class="btn ghost" onclick="saveChart()">💾 保存图表</button>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">🌍 地域分布</div>
    <div id="geo-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">🗺️</div>
        <div>地域热力图</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 地域数据</div>
    <table class="table" style="font-size: 12px;">
      <thead>
        <tr>
          <th>地区</th>
          <th>发送量</th>
          <th>打开率</th>
          <th>点击率</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>🇨🇳 中国</td>
          <td>456,789</td>
          <td>38.2%</td>
          <td>5.1%</td>
        </tr>
        <tr>
          <td>🇺🇸 美国</td>
          <td>234,567</td>
          <td>42.1%</td>
          <td>6.8%</td>
        </tr>
        <tr>
          <td>🇯🇵 日本</td>
          <td>123,456</td>
          <td>35.7%</td>
          <td>4.2%</td>
        </tr>
        <tr>
          <td>🇬🇧 英国</td>
          <td>89,123</td>
          <td>41.3%</td>
          <td>5.9%</td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="card">
    <div class="mini">⏰ 时间分布</div>
    <div id="time-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">🕐</div>
        <div>发送时间分析</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">⏰ 最佳发送时间</div>
    <div class="grid cols-2" style="font-size: 12px;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">工作日</div>
        <div style="color: var(--text-secondary);">
          • 周二 10:00-11:00<br>
          • 周三 14:00-15:00<br>
          • 周四 09:00-10:00
        </div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">周末</div>
        <div style="color: var(--text-secondary);">
          • 周六 11:00-12:00<br>
          • 周日 15:00-16:00
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">📱 设备分析</div>
    <div id="device-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">📱</div>
        <div>设备分布图</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 设备数据</div>
    <div class="grid cols-2" style="font-size: 12px;">
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">移动端</div>
        <div style="color: var(--text-secondary);">
          • 智能手机: 45.2%<br>
          • 平板电脑: 12.8%<br>
          • 打开率: 32.1%
        </div>
      </div>
      <div>
        <div style="font-weight: 600; margin-bottom: 8px;">桌面端</div>
        <div style="color: var(--text-secondary);">
          • 电脑: 38.7%<br>
          • 笔记本: 3.3%<br>
          • 打开率: 41.2%
        </div>
      </div>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">🎨 内容分析</div>
    <div id="content-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
      <div style="text-align: center;">
        <div style="font-size: 32px; margin-bottom: 8px;">🎨</div>
        <div>内容效果分析</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="mini">📊 内容类型表现</div>
    <div style="font-size: 12px;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>促销邮件</span>
        <span style="color: var(--success);">打开率 42.3%</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>内容邮件</span>
        <span style="color: var(--warning);">打开率 38.7%</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <span>欢迎邮件</span>
        <span style="color: var(--err);">打开率 28.9%</span>
      </div>
    </div>
  </div>
</div>

<!-- 智能洞察弹窗 -->
<div id="insights-panel" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">💡 智能洞察</div>
  <div class="hint">基于AI分析，为您提供邮件营销优化建议</div>
  
  <div class="grid cols-2">
    <div>
      <div class="mini">🚀 表现优异</div>
      <div class="hint">这些方面表现良好，可以继续保持</div>
      <div style="font-size: 13px; color: var(--success); margin: 16px 0;">
        ✅ VIP客户邮件打开率51.3%，高于平均水平<br>
        ✅ 周二发送的邮件效果最佳<br>
        ✅ 移动端用户参与度持续提升
      </div>
    </div>
    
    <div>
      <div class="mini">⚠️ 需要关注</div>
      <div class="hint">这些方面需要优化改进</div>
      <div style="font-size: 13px; color: var(--warning); margin: 16px 0;">
        ⚠️ 新用户欢迎邮件打开率偏低<br>
        ⚠️ 周末发送效果不如工作日<br>
        ⚠️ 某些地区打开率低于预期
      </div>
    </div>
    
    <div>
      <div class="mini">🎯 优化建议</div>
      <div class="hint">具体的改进措施</div>
      <div style="font-size: 13px; color: var(--pri); margin: 16px 0;">
        💡 优化新用户欢迎邮件主题行<br>
        💡 调整周末发送时间策略<br>
        💡 针对低效地区进行A/B测试
      </div>
    </div>
    
    <div>
      <div class="mini">📈 预测趋势</div>
      <div class="hint">基于历史数据的趋势预测</div>
      <div style="font-size: 13px; color: var(--text-secondary); margin: 16px 0;">
        🔮 预计下月打开率提升2-3%<br>
        🔮 移动端用户占比将继续增长<br>
        🔮 个性化内容效果将更显著
      </div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="applyInsights()">🚀 应用建议</button>
    <button class="btn secondary" onclick="hideInsights()">❌ 关闭</button>
  </div>
</div>

<!-- 实时监控弹窗 -->
<div id="realtime-panel" class="card" style="display:none; max-width:800px; position:fixed; top:8%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">⏰ 实时监控</div>
  <div class="hint">实时监控邮件发送状态和关键指标</div>
  
  <div class="grid cols-3">
    <div>
      <div class="mini">📤 发送状态</div>
      <div style="text-align: center; margin: 16px 0;">
        <div style="font-size: 32px; color: var(--success);">🟢</div>
        <div style="font-size: 18px; font-weight: 600;">正常</div>
        <div style="font-size: 12px; color: var(--text-secondary);">队列延迟: 2ms</div>
      </div>
    </div>
    
    <div>
      <div class="mini">📊 实时指标</div>
      <div style="text-align: center; margin: 16px 0;">
        <div style="font-size: 24px; font-weight: 600;">1,234</div>
        <div style="font-size: 12px; color: var(--text-secondary);">本小时发送</div>
        <div style="font-size: 12px; color: var(--success);">+12% vs 上小时</div>
      </div>
    </div>
    
    <div>
      <div class="mini">⚠️ 告警信息</div>
      <div style="text-align: center; margin: 16px 0;">
        <div style="font-size: 32px; color: var(--warning);">🟡</div>
        <div style="font-size: 18px; font-weight: 600;">1个告警</div>
        <div style="font-size: 12px; color: var(--text-secondary);">投诉率略高</div>
      </div>
    </div>
  </div>
  
  <div class="divider"></div>
  
  <div class="mini">📈 实时趋势</div>
  <div id="realtime-chart" style="height: 200px; background: var(--panel-2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted); border: 2px dashed var(--border);">
    <div style="text-align: center;">
      <div style="font-size: 32px; margin-bottom: 8px;">📊</div>
      <div>实时数据图表</div>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn secondary" onclick="hideRealTime()">❌ 关闭</button>
  </div>
</div>

<script>
// 生成自定义报表
function generateCustomReport() {
  const dimension = document.getElementById('analysis-dimension').value;
  const metrics = document.getElementById('core-metrics').value;
  
  alert(`报表生成中...\n\n分析维度：${dimension}\n核心指标：${metrics}\n\n系统正在处理数据，完成后将发送通知。`);
}

// 生成趋势图表
function generateTrendChart() {
  const timeRange = document.getElementById('time-range').value;
  const comparePeriod = document.getElementById('compare-period').value;
  const chartType = document.getElementById('chart-type').value;
  const metric = document.getElementById('metric-select').value;
  
  // 模拟图表生成
  const chartDiv = document.getElementById('trend-chart');
  chartDiv.innerHTML = `
    <div style="text-align: center; width: 100%;">
      <div style="font-size: 24px; font-weight: 600; margin-bottom: 16px;">${metric}趋势图</div>
      <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 20px;">
        时间范围：${timeRange} | 对比：${comparePeriod} | 图表类型：${chartType}
      </div>
      <div style="height: 200px; background: linear-gradient(45deg, var(--pri-light), var(--success-light)); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--pri); font-weight: 600;">
        模拟图表区域<br>
        ${metric}数据可视化
      </div>
    </div>
  `;
}

// 保存图表
function saveChart() {
  alert('图表已保存到您的仪表板！');
}

// 显示智能洞察
function showInsights() {
  document.getElementById('insights-panel').style.display = '';
}

// 隐藏智能洞察
function hideInsights() {
  document.getElementById('insights-panel').style.display = 'none';
}

// 应用洞察建议
function applyInsights() {
  alert('已应用智能建议！\n\n系统将自动：\n• 优化新用户欢迎邮件模板\n• 调整发送时间策略\n• 设置A/B测试计划\n\n预计下周开始生效。');
  hideInsights();
}

// 显示实时监控
function showRealTime() {
  document.getElementById('realtime-panel').style.display = '';
  
  // 模拟实时数据更新
  setInterval(() => {
    const chartDiv = document.getElementById('realtime-chart');
    if (chartDiv && chartDiv.style.display !== 'none') {
      chartDiv.innerHTML = `
        <div style="text-align: center; width: 100%;">
          <div style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">实时发送趋势</div>
          <div style="height: 150px; background: linear-gradient(90deg, var(--pri-light), var(--warning-light), var(--success-light)); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--pri); font-weight: 600;">
            实时数据流<br>
            更新时间: ${new Date().toLocaleTimeString()}
          </div>
        </div>
      `;
    }
  }, 5000);
}

// 隐藏实时监控
function hideRealTime() {
  document.getElementById('realtime-panel').style.display = 'none';
}

// 生成报表
function generateReport() {
  alert('正在生成综合报表...\n\n包含内容：\n• 关键指标汇总\n• 趋势分析图表\n• 地域分布数据\n• 设备分析报告\n• 优化建议\n\n完成后将发送到您的邮箱。');
}

// 导出数据
function exportData() {
  alert('数据导出任务已创建！\n\n导出内容：\n• 所有分析数据\n• 图表截图\n• 原始数据CSV\n• 分析报告PDF\n\n预计10分钟内完成，完成后将发送下载链接。');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 设置默认值
  document.getElementById('time-range').value = '最近30天';
  document.getElementById('compare-period').value = '上一周期';
  document.getElementById('chart-type').value = '折线图';
  document.getElementById('metric-select').value = '打开率';
  
  // 自动生成初始图表
  setTimeout(() => {
    generateTrendChart();
  }, 1000);
});
</script>


