<!-- 发件渠道详情页面 -->
<div class="toolbar">
  <button class="btn" onclick="editChannel()">编辑渠道</button>
  <button class="btn secondary" onclick="suspendChannel()">暂停渠道</button>
  <button class="btn ghost" onclick="backToList()">返回列表</button>
</div>

<div class="kpi">
  <div class="item"><div class="mini">投递率</div><div class="num">98.7%</div></div>
  <div class="item"><div class="mini">打开率</div><div class="num">38.2%</div></div>
  <div class="item"><div class="mini">点击率</div><div class="num">6.1%</div></div>
  <div class="item"><div class="mini">投诉率</div><div class="num">0.03%</div></div>
</div>

<div class="grid cols-3">
  <div class="card">
    <div class="mini">基本信息</div>
    <div class="row">
      <div><strong>渠道名称：</strong>营销主渠道</div>
      <div><strong>渠道代码：</strong>marketing-main</div>
      <div><strong>渠道类型：</strong><span class="badge marketing">营销</span></div>
      <div><strong>状态：</strong><span class="badge active">活跃</span></div>
      <div><strong>创建时间：</strong>2024-01-15 10:30:00</div>
      <div><strong>最后更新：</strong>2024-01-20 14:22:15</div>
    </div>
    <div class="hint">用于促销、新闻、活动通知等营销邮件发送</div>
  </div>
  
  <div class="card">
    <div class="mini">资源配置</div>
    <div class="row">
      <div><strong>默认域名：</strong>marketing.example.com</div>
      <div><strong>默认IP池：</strong>Pool-Marketing</div>
      <div><strong>日发送配额：</strong>50,000</div>
      <div><strong>月发送配额：</strong>1,500,000</div>
      <div><strong>已用配额：</strong>32,450 / 50,000 (64.9%)</div>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">策略配置</div>
    <div class="row">
      <div><strong>限速策略：</strong>标准策略</div>
      <div><strong>预热策略：</strong>标准预热</div>
      <div><strong>投诉率阈值：</strong>0.08%</div>
      <div><strong>硬退率阈值：</strong>0.6%</div>
      <div><strong>实时告警：</strong>已启用</div>
      <div><strong>自动暂停：</strong>已启用</div>
    </div>
  </div>
</div>

<div class="grid cols-2">
  <div class="card">
    <div class="mini">投递表现趋势</div>
    <div class="chart-placeholder">
      <div style="height:200px; display:flex; align-items:center; justify-content:center; color:#666;">
        折线图：最近7天投递率趋势
      </div>
    </div>
  </div>
  
  <div class="card">
    <div class="mini">ISP分布</div>
    <div class="chart-placeholder">
      <div style="height:200px; display:flex; align-items:center; justify-content:center; color:#666;">
        饼图：Gmail 45%, Outlook 25%, Yahoo 15%, 其他 15%
      </div>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">关联域名</div>
  <table class="table">
    <thead>
      <tr>
        <th>域名</th>
        <th>SPF</th>
        <th>DKIM</th>
        <th>DMARC</th>
        <th>BIMI</th>
        <th>信誉分</th>
        <th>预热状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>marketing.example.com</td>
        <td class="ok">通过</td>
        <td class="ok">通过</td>
        <td class="ok">通过</td>
        <td class="warnTxt">警告</td>
        <td>95.2</td>
        <td><span class="badge active">已完成</span></td>
        <td><button class="btn mini secondary">查看详情</button></td>
      </tr>
      <tr>
        <td>news.example.com</td>
        <td class="ok">通过</td>
        <td class="ok">通过</td>
        <td class="warnTxt">警告</td>
        <td class="errorTxt">失败</td>
        <td>92.8</td>
        <td><span class="badge active">已完成</span></td>
        <td><button class="btn mini secondary">查看详情</button></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="card">
  <div class="mini">关联IP池</div>
  <table class="table">
    <thead>
      <tr>
        <th>IP池名称</th>
        <th>IP数量</th>
        <th>类型</th>
        <th>信誉分</th>
        <th>预热状态</th>
        <th>使用率</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Pool-Marketing</td>
        <td>5</td>
        <td>独享</td>
        <td>94.5</td>
        <td><span class="badge active">已完成</span></td>
        <td>85%</td>
        <td><button class="btn mini secondary">查看详情</button></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="card">
  <div class="mini">限速策略</div>
  <table class="table">
    <thead>
      <tr>
        <th>范围</th>
        <th>目标</th>
        <th>速率(封/分)</th>
        <th>突发量</th>
        <th>优先级</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>渠道</td>
        <td>marketing-main</td>
        <td>1,000</td>
        <td>100</td>
        <td>100</td>
        <td><span class="badge active">启用</span></td>
        <td><button class="btn mini secondary">编辑</button></td>
      </tr>
      <tr>
        <td>ISP</td>
        <td>Gmail</td>
        <td>500</td>
        <td>50</td>
        <td>200</td>
        <td><span class="badge active">启用</span></td>
        <td><button class="btn mini secondary">编辑</button></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="card">
  <div class="mini">权限列表</div>
  <table class="table">
    <thead>
      <tr>
        <th>用户</th>
        <th>角色</th>
        <th>权限</th>
        <th>添加时间</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>张三 (<EMAIL>)</td>
        <td><span class="badge owner">Owner</span></td>
        <td>完全控制</td>
        <td>2024-01-15 10:30:00</td>
        <td><button class="btn mini secondary">编辑</button></td>
      </tr>
      <tr>
        <td>李四 (<EMAIL>)</td>
        <td><span class="badge admin">Admin</span></td>
        <td>管理配置</td>
        <td>2024-01-16 09:15:00</td>
        <td><button class="btn mini secondary">编辑</button></td>
      </tr>
      <tr>
        <td>王五 (<EMAIL>)</td>
        <td><span class="badge operator">Operator</span></td>
        <td>发送操作</td>
        <td>2024-01-17 14:22:00</td>
        <td><button class="btn mini secondary">编辑</button></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="card">
  <div class="mini">最近活动</div>
  <table class="table">
    <thead>
      <tr>
        <th>时间</th>
        <th>活动</th>
        <th>操作者</th>
        <th>详情</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>2024-01-20 14:22:15</td>
        <td>更新配置</td>
        <td>李四</td>
        <td>修改日发送配额：45,000 → 50,000</td>
      </tr>
      <tr>
        <td>2024-01-19 16:30:00</td>
        <td>添加域名</td>
        <td>张三</td>
        <td>新增域名：news.example.com</td>
      </tr>
      <tr>
        <td>2024-01-18 11:45:00</td>
        <td>权限变更</td>
        <td>张三</td>
        <td>为王五添加Operator权限</td>
      </tr>
      <tr>
        <td>2024-01-17 09:20:00</td>
        <td>渠道启用</td>
        <td>张三</td>
        <td>渠道状态：配置中 → 活跃</td>
      </tr>
    </tbody>
  </table>
</div>

<style>
.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.marketing {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.active {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.owner {
  background: #f3e5f5;
  color: #7b1fa2;
}

.badge.admin {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.operator {
  background: #fff3e0;
  color: #f57c00;
}

.chart-placeholder {
  border: 1px dashed #ddd;
  border-radius: 8px;
  margin: 10px 0;
}

.ok {
  color: #388e3c;
  font-weight: 500;
}

.warnTxt {
  color: #f57c00;
  font-weight: 500;
}

.errorTxt {
  color: #d32f2f;
  font-weight: 500;
}
</style>

<script>
function editChannel() {
  // 编辑渠道逻辑
  window.location.href = 'channel-edit.html?id=1';
}

function suspendChannel() {
  if (confirm('确定要暂停此渠道吗？暂停后该渠道将无法发送邮件。')) {
    // 暂停渠道逻辑
    alert('渠道已暂停');
  }
}

function backToList() {
  window.location.href = 'channels.html';
}
</script>
