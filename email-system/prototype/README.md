# Email System 原型设计

这是一个完整的邮件营销系统原型，展示了除用户系统外的所有核心功能模块。

## 🚀 快速开始

### 本地运行
```bash
# 方法1：使用 Python 内置服务器
python3 -m http.server 5173 --directory prototype

# 方法2：使用 Node.js
npx serve prototype -p 5173

# 方法3：使用 PHP
php -S localhost:5173 -t prototype
```

然后在浏览器中访问：`http://localhost:5173`

### 直接打开
也可以直接双击 `prototype/index.html` 文件在浏览器中打开（但某些功能可能受限）。

## 🎯 功能模块

### 数据与人群管理
- **📇 联系人管理** - 联系人导入、管理、标签分类
- **🏷️ 列表与标签** - 创建和管理联系人列表与标签
- **🧩 细分** - 基于条件的用户细分创建
- **🚫 抑制与同意** - 退订、硬退回、投诉管理

### 内容与触达
- **🧱 模板与内容** - 邮件模板创建、编辑、预览
- **🚀 活动与 A/B 测试** - 邮件活动创建和 A/B 测试
- **🔁 自动化旅程** - 用户行为触发的自动化邮件流程

### 投递与追踪
- **📤 投递与可达性** - 域名配置、IP 池管理、投递状态
- **📈 追踪与报表** - 邮件打开、点击、转化数据分析

### 采集与平台
- **📝 表单与偏好中心** - 订阅表单和用户偏好设置
- **🔌 开放平台与集成** - API 管理、Webhook 配置
- **🧩 扩展功能** - 发送时间优化、预览收件箱等

### 合规与商业
- **💳 计费与额度** - 套餐管理和使用量统计
- **🛡️ 安全与合规** - 同意管理、数据治理

### 管理与设置
- **⚙️ 工作区设置** - 品牌配置、发送策略
- **🧭 运维与管理** - 系统状态监控、任务管理

## 🎨 设计特色

### 现代化 B 端设计
- 浅色主题，专业商务风格
- 圆角卡片设计，层次分明
- 柔和的阴影和过渡动画
- 响应式布局，支持多设备

### 丰富的视觉元素
- Emoji 图标增强可读性
- 渐变色彩和状态指示
- 图表占位符和可视化元素
- 悬停效果和交互反馈

### 完整的功能流程
- 从数据导入到邮件发送的完整链路
- 真实的表单和配置界面
- 详细的数据展示和统计
- 实用的操作提示和帮助信息

## 🔧 技术特性

### 前端技术
- 纯 HTML/CSS/JavaScript
- CSS Grid 和 Flexbox 布局
- CSS 变量和现代特性
- 响应式设计和移动适配

### 交互功能
- 单页应用导航
- 动态内容加载
- 面板展开/收起
- Tab 标签切换
- 表单验证和提交

### 浏览器兼容
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持 ES6+ 语法
- 响应式设计
- 触摸设备友好

## 📱 响应式设计

- **桌面端** (>1024px): 完整功能，双栏布局
- **平板端** (768px-1024px): 自适应网格，单栏导航
- **移动端** (<768px): 垂直堆叠，触摸优化

## 🎭 自定义主题

通过修改 `assets/style.css` 中的 CSS 变量可以轻松自定义主题：

```css
:root {
  --pri: #3b82f6;        /* 主色调 */
  --success: #10b981;     /* 成功色 */
  --warning: #f59e0b;     /* 警告色 */
  --danger: #ef4444;      /* 危险色 */
  --bg: #f8fafc;         /* 背景色 */
  --panel: #ffffff;       /* 面板色 */
}
```

## 🔄 更新日志

### v1.0.0 (2025-08-11)
- ✨ 完成所有核心功能模块
- 🎨 现代化 B 端设计风格
- 📱 响应式布局支持
- 🚀 丰富的交互体验

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个原型：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 这是一个原型设计，主要用于展示界面和交互流程，不包含实际的后端功能实现。
