# Users业务系统领域驱动设计（DDD）建模分析

## 概述

本文档作为DDD专家咨询报告，基于对Users业务系统的深入分析，提供完整的领域建模设计方案。通过严格遵循DDD标准，识别并设计系统的限界上下文、聚合、实体、值对象、领域服务等核心组件，为业务系统构建清晰、可维护、可扩展的领域模型。

## 1. 领域分析与限界上下文划分

### 1.1 业务领域识别

基于对Users业务系统的分析，我们识别出以下核心业务领域：

**用户身份与认证领域（User Identity & Authentication）**
- 核心职责：用户身份的创建、验证、认证和生命周期管理
- 业务边界：用户注册、登录认证、密码管理、账户状态管理、多因素认证
- 关键业务规则：密码策略、登录限制、账户锁定规则、会话管理

**组织架构管理领域（Organization Management）**
- 核心职责：企业组织架构的建立、维护和关系管理
- 业务边界：部门管理、职位管理、用户与组织关系、层级结构
- 关键业务规则：部门层级关系、职位权限体系、用户分配规则

**权限与访问控制领域（Permission & Access Control）**
- 核心职责：系统访问权限的定义、分配和控制
- 业务边界：角色定义、权限分配、访问控制策略、资源保护
- 关键业务规则：RBAC模型、权限继承、最小权限原则

**第三方集成领域（Third-party Integration）**
- 核心职责：外部身份提供商的集成和OAuth认证
- 业务边界：OAuth流程、第三方用户映射、身份联合
- 关键业务规则：OAuth协议、身份映射规则、安全策略

**应用管理领域（Application Management）**
- 核心职责：多应用环境下的用户和权限管理
- 业务边界：应用注册、应用级权限、跨应用用户管理
- 关键业务规则：应用隔离、权限范围、数据隔离

**审计与安全监控领域（Audit & Security Monitoring）**
- 核心职责：用户行为监控、安全事件记录和合规性管理
- 业务边界：操作日志、安全事件、合规报告、威胁检测
- 关键业务规则：日志完整性、数据保留策略、隐私保护

### 1.2 限界上下文划分图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Users 业务域                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  用户身份认证    │  │   组织架构管理   │  │   权限访问控制   │                   │
│  │     上下文      │  │     上下文      │  │     上下文      │                   │
│  │                │  │                │  │                │                   │
│  │ • 用户注册      │  │ • 部门管理      │  │ • 角色管理      │                   │
│  │ • 身份认证      │  │ • 职位管理      │  │ • 权限分配      │                   │
│  │ • 密码管理      │  │ • 组织关系      │  │ • 访问控制      │                   │
│  │ • 账户状态      │  │ • 层级结构      │  │ • 权限验证      │                   │
│  │ • MFA管理       │  │ • 用户分配      │  │ • 资源保护      │                   │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                   │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  第三方集成     │  │   应用管理      │  │  审计安全监控    │                   │
│  │     上下文      │  │     上下文      │  │     上下文      │                   │
│  │                │  │                │  │                │                   │
│  │ • OAuth认证     │  │ • 应用注册      │  │ • 操作审计      │                   │
│  │ • 身份联合      │  │ • 应用权限      │  │ • 安全监控      │                   │
│  │ • 用户映射      │  │ • 跨应用管理    │  │ • 合规报告      │                   │
│  │ • 第三方同步    │  │ • 数据隔离      │  │ • 威胁检测      │                   │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.3 上下文间关系与协作

**核心协作关系**：
- 用户身份认证 ←→ 组织架构管理：用户与部门/职位的分配关系
- 用户身份认证 ←→ 权限访问控制：用户角色分配和权限验证
- 用户身份认证 ←→ 第三方集成：OAuth用户身份映射
- 应用管理 → 所有上下文：应用级别的数据隔离和权限范围
- 所有上下文 → 审计安全监控：操作记录和安全事件

**集成策略**：
- **领域事件**：异步通信（用户创建、权限变更、登录事件）
- **应用服务协调**：同步操作编排（用户创建时的权限初始化）
- **共享内核**：共同的值对象（TenantID、UserID、AppID）
- **防腐层**：第三方系统集成的适配和转换

### 1.4 主要业务场景

**用户身份认证上下文**：
- 用户注册与激活
- 用户登录认证
- 密码重置与变更
- 多因素认证设置
- 账户锁定与解锁

**组织架构管理上下文**：
- 部门创建与管理
- 用户部门分配
- 职位管理
- 组织架构调整

**权限访问控制上下文**：
- 角色权限分配
- 用户权限验证
- 资源访问控制
- 权限继承计算

## 2. 领域对象识别与建模

### 2.1 用户身份认证上下文

#### 聚合设计

**User聚合（聚合根）**
```
User聚合根
├── 实体
│   └── User（聚合根实体）
├── 值对象
│   ├── UserID
│   ├── Username
│   ├── Email
│   ├── Password
│   ├── UserStatus
│   ├── RealName
│   ├── Phone
│   ├── Avatar
│   ├── TenantID
│   └── AppID
└── 领域服务
    ├── UserAuthenticationService
    ├── PasswordPolicyService
    ├── LoginLimitService
    └── MFAService
```

**核心实体定义**：

```go
// User聚合根
type User struct {
    // 标识信息
    id       UserID
    tenantID TenantID
    appID    AppID
    
    // 基本信息
    username Username
    email    Email
    realName RealName
    phone    Phone
    avatar   Avatar
    
    // 认证信息
    password Password
    status   UserStatus
    
    // 安全信息
    failedAttempts    int
    lastLoginAt       *time.Time
    lastLoginIP       string
    lockReason        string
    lockExpiredAt     *time.Time
    passwordExpiredAt *time.Time
    
    // 组织关系（引用其他聚合）
    departmentID *DepartmentID
    positionID   *PositionID
    
    // 系统信息
    isSystem    bool
    isActivated bool
    createdAt   time.Time
    updatedAt   time.Time
    
    // 领域事件
    events []DomainEvent
}

// 核心业务行为
func (u *User) Authenticate(password string, policy PasswordPolicy) error {
    if !u.status.CanLogin() {
        return NewUserCannotLoginError(u.id, u.status)
    }
    
    if u.IsLocked() {
        return NewUserLockedError(u.id, u.lockReason)
    }
    
    if !u.password.Verify(password) {
        u.failedAttempts++
        u.AddEvent(NewLoginFailedEvent(u.id, u.failedAttempts))
        
        if u.failedAttempts >= policy.MaxFailedAttempts() {
            u.Lock("Too many failed login attempts", policy.LockDuration())
            u.AddEvent(NewUserLockedEvent(u.id, u.lockReason))
        }
        
        return NewInvalidPasswordError()
    }
    
    // 认证成功
    u.failedAttempts = 0
    u.lastLoginAt = &time.Time{}
    u.AddEvent(NewUserLoggedInEvent(u.id, u.tenantID))
    
    return nil
}

func (u *User) ChangePassword(oldPassword, newPassword string, policy PasswordPolicy) error {
    if !u.password.Verify(oldPassword) {
        return NewInvalidOldPasswordError()
    }
    
    if err := policy.ValidatePassword(newPassword); err != nil {
        return err
    }
    
    newPasswordVO, err := NewPassword(newPassword)
    if err != nil {
        return err
    }
    
    u.password = newPasswordVO
    u.passwordExpiredAt = policy.CalculateExpiry()
    u.updatedAt = time.Now()
    
    u.AddEvent(NewPasswordChangedEvent(u.id))
    
    return nil
}

func (u *User) Lock(reason string, duration time.Duration) error {
    if u.isSystem {
        return NewSystemUserCannotBeLocked()
    }
    
    u.status = NewUserStatus(UserStatusLocked)
    u.lockReason = reason
    expiry := time.Now().Add(duration)
    u.lockExpiredAt = &expiry
    
    u.AddEvent(NewUserLockedEvent(u.id, reason))
    
    return nil
}

func (u *User) Unlock() error {
    if !u.IsLocked() {
        return NewUserNotLockedError(u.id)
    }
    
    u.status = NewUserStatus(UserStatusActive)
    u.lockReason = ""
    u.lockExpiredAt = nil
    u.failedAttempts = 0
    
    u.AddEvent(NewUserUnlockedEvent(u.id))
    
    return nil
}

func (u *User) UpdateProfile(realName RealName, phone Phone, avatar Avatar) error {
    u.realName = realName
    u.phone = phone
    u.avatar = avatar
    u.updatedAt = time.Now()
    
    u.AddEvent(NewUserProfileUpdatedEvent(u.id))
    
    return nil
}

func (u *User) AssignToDepartment(departmentID DepartmentID) error {
    oldDepartmentID := u.departmentID
    u.departmentID = &departmentID
    u.updatedAt = time.Now()
    
    u.AddEvent(NewUserDepartmentAssignedEvent(u.id, oldDepartmentID, &departmentID))
    
    return nil
}

func (u *User) AssignToPosition(positionID PositionID) error {
    oldPositionID := u.positionID
    u.positionID = &positionID
    u.updatedAt = time.Now()
    
    u.AddEvent(NewUserPositionAssignedEvent(u.id, oldPositionID, &positionID))
    
    return nil
}

// 查询方法
func (u *User) ID() UserID { return u.id }
func (u *User) TenantID() TenantID { return u.tenantID }
func (u *User) Username() Username { return u.username }
func (u *User) Email() Email { return u.email }
func (u *User) IsActive() bool { return u.status.IsActive() }
func (u *User) IsLocked() bool { return u.status.IsLocked() && (u.lockExpiredAt == nil || u.lockExpiredAt.After(time.Now())) }
func (u *User) IsPasswordExpired() bool { return u.passwordExpiredAt != nil && u.passwordExpiredAt.Before(time.Now()) }
```

**值对象设计**：

```go
// UserID - 用户唯一标识
type UserID struct {
    value int64
}

func NewUserID(value int64) (UserID, error) {
    if value <= 0 {
        return UserID{}, errors.New("user ID must be positive")
    }
    return UserID{value: value}, nil
}

func (id UserID) Value() int64 { return id.value }
func (id UserID) String() string { return strconv.FormatInt(id.value, 10) }
func (id UserID) Equals(other UserID) bool { return id.value == other.value }

// Username - 用户名
type Username struct {
    value string
}

func NewUsername(value string) (Username, error) {
    if len(value) < 3 || len(value) > 50 {
        return Username{}, errors.New("username length must be between 3 and 50")
    }
    
    if !regexp.MustCompile(`^[a-zA-Z0-9_-]+$`).MatchString(value) {
        return Username{}, errors.New("username contains invalid characters")
    }
    
    return Username{value: value}, nil
}

func (u Username) Value() string { return u.value }
func (u Username) IsValid() bool { return len(u.value) >= 3 && len(u.value) <= 50 }

// Email - 邮箱地址
type Email struct {
    value string
}

func NewEmail(value string) (Email, error) {
    if !isValidEmail(value) {
        return Email{}, errors.New("invalid email format")
    }
    return Email{value: strings.ToLower(value)}, nil
}

func (e Email) Value() string { return e.value }
func (e Email) Domain() string {
    parts := strings.Split(e.value, "@")
    if len(parts) != 2 {
        return ""
    }
    return parts[1]
}
func (e Email) LocalPart() string {
    parts := strings.Split(e.value, "@")
    if len(parts) != 2 {
        return ""
    }
    return parts[0]
}

// Password - 密码
type Password struct {
    hash      string
    algorithm string
    salt      string
}

func NewPassword(plaintext string) (Password, error) {
    if len(plaintext) < 8 {
        return Password{}, errors.New("password too short")
    }
    
    salt := generateSalt()
    hash := hashPassword(plaintext, salt)
    
    return Password{
        hash:      hash,
        algorithm: "bcrypt",
        salt:      salt,
    }, nil
}

func PasswordFromHash(hash, algorithm, salt string) Password {
    return Password{
        hash:      hash,
        algorithm: algorithm,
        salt:      salt,
    }
}

func (p Password) Verify(plaintext string) bool {
    return verifyPassword(plaintext, p.salt, p.hash)
}

func (p Password) Hash() string { return p.hash }
func (p Password) NeedsRehash() bool {
    // 检查是否需要重新哈希（算法升级等）
    return p.algorithm != "bcrypt"
}

// UserStatus - 用户状态
type UserStatus struct {
    value string
}

const (
    UserStatusActive   = "active"
    UserStatusLocked   = "locked"
    UserStatusDisabled = "disabled"
    UserStatusPending  = "pending"
    UserStatusExpired  = "expired"
)

func NewUserStatus(value string) (UserStatus, error) {
    validStatuses := []string{
        UserStatusActive,
        UserStatusLocked,
        UserStatusDisabled,
        UserStatusPending,
        UserStatusExpired,
    }
    
    for _, status := range validStatuses {
        if value == status {
            return UserStatus{value: value}, nil
        }
    }
    
    return UserStatus{}, errors.New("invalid user status")
}

func (s UserStatus) Value() string { return s.value }
func (s UserStatus) IsActive() bool { return s.value == UserStatusActive }
func (s UserStatus) IsLocked() bool { return s.value == UserStatusLocked }
func (s UserStatus) IsDisabled() bool { return s.value == UserStatusDisabled }
func (s UserStatus) IsPending() bool { return s.value == UserStatusPending }
func (s UserStatus) CanLogin() bool { return s.value == UserStatusActive }

// TenantID - 租户标识
type TenantID struct {
    value int64
}

func NewTenantID(value int64) (TenantID, error) {
    if value <= 0 {
        return TenantID{}, errors.New("tenant ID must be positive")
    }
    return TenantID{value: value}, nil
}

func (id TenantID) Value() int64 { return id.value }
func (id TenantID) String() string { return strconv.FormatInt(id.value, 10) }
func (id TenantID) Equals(other TenantID) bool { return id.value == other.value }

// AppID - 应用标识
type AppID struct {
    value int64
}

func NewAppID(value int64) (AppID, error) {
    if value <= 0 {
        return AppID{}, errors.New("app ID must be positive")
    }
    return AppID{value: value}, nil
}

func (id AppID) Value() int64 { return id.value }
func (id AppID) String() string { return strconv.FormatInt(id.value, 10) }
func (id AppID) Equals(other AppID) bool { return id.value == other.value }
```

**领域服务设计**：

```go
// 用户认证服务
type UserAuthenticationService struct {
    passwordPolicy PasswordPolicy
    loginLimit     LoginLimitService
}

func NewUserAuthenticationService(
    passwordPolicy PasswordPolicy,
    loginLimit LoginLimitService,
) *UserAuthenticationService {
    return &UserAuthenticationService{
        passwordPolicy: passwordPolicy,
        loginLimit:     loginLimit,
    }
}

func (s *UserAuthenticationService) Authenticate(
    user *User,
    password string,
    ipAddress string,
) error {
    // 检查登录限制
    if err := s.loginLimit.CheckLimit(user.ID(), ipAddress); err != nil {
        return err
    }
    
    // 执行认证
    if err := user.Authenticate(password, s.passwordPolicy); err != nil {
        // 记录失败尝试
        s.loginLimit.RecordFailedAttempt(user.ID(), ipAddress)
        return err
    }
    
    // 重置登录限制
    s.loginLimit.ResetFailedAttempts(user.ID(), ipAddress)
    
    return nil
}

// 密码策略服务
type PasswordPolicyService struct {
    config PasswordConfig
}

func NewPasswordPolicyService(config PasswordConfig) *PasswordPolicyService {
    return &PasswordPolicyService{config: config}
}

func (s *PasswordPolicyService) ValidatePassword(password string) error {
    if len(password) < s.config.MinLength {
        return NewPasswordTooShortError(s.config.MinLength)
    }
    
    if len(password) > s.config.MaxLength {
        return NewPasswordTooLongError(s.config.MaxLength)
    }
    
    if s.config.RequireUppercase && !hasUppercase(password) {
        return NewPasswordMissingUppercaseError()
    }
    
    if s.config.RequireLowercase && !hasLowercase(password) {
        return NewPasswordMissingLowercaseError()
    }
    
    if s.config.RequireNumbers && !hasNumbers(password) {
        return NewPasswordMissingNumbersError()
    }
    
    if s.config.RequireSpecialChars && !hasSpecialChars(password) {
        return NewPasswordMissingSpecialCharsError()
    }
    
    return nil
}

func (s *PasswordPolicyService) CalculateExpiry() *time.Time {
    if s.config.ExpiryDays <= 0 {
        return nil
    }
    
    expiry := time.Now().AddDate(0, 0, s.config.ExpiryDays)
    return &expiry
}

func (s *PasswordPolicyService) MaxFailedAttempts() int {
    return s.config.MaxFailedAttempts
}

func (s *PasswordPolicyService) LockDuration() time.Duration {
    return time.Duration(s.config.LockDurationMinutes) * time.Minute
}

// 登录限制服务
type LoginLimitService struct {
    config LoginLimitConfig
}

func NewLoginLimitService(config LoginLimitConfig) *LoginLimitService {
    return &LoginLimitService{config: config}
}

func (s *LoginLimitService) CheckLimit(userID UserID, ipAddress string) error {
    // 检查用户级别的限制
    if s.isUserLimited(userID) {
        return NewUserLoginLimitExceededError(userID)
    }
    
    // 检查IP级别的限制
    if s.isIPLimited(ipAddress) {
        return NewIPLoginLimitExceededError(ipAddress)
    }
    
    return nil
}

func (s *LoginLimitService) RecordFailedAttempt(userID UserID, ipAddress string) {
    s.recordUserFailedAttempt(userID)
    s.recordIPFailedAttempt(ipAddress)
}

func (s *LoginLimitService) ResetFailedAttempts(userID UserID, ipAddress string) {
    s.resetUserFailedAttempts(userID)
    s.resetIPFailedAttempts(ipAddress)
}
```

### 2.2 组织架构管理上下文

#### 聚合设计

**Department聚合（聚合根）**
```
Department聚合根
├── 实体
│   └── Department（聚合根实体）
├── 值对象
│   ├── DepartmentID
│   ├── DepartmentName
│   ├── DepartmentCode
│   ├── DepartmentPath
│   ├── DepartmentLevel
│   └── DepartmentStatus
└── 领域服务
    ├── OrganizationService
    └── HierarchyService
```

**Position聚合（聚合根）**
```
Position聚合根
├── 实体
│   └── Position（聚合根实体）
├── 值对象
│   ├── PositionID
│   ├── PositionName
│   ├── PositionCode
│   ├── PositionLevel
│   └── PositionStatus
└── 领域服务
    └── PositionManagementService
```

**核心实体定义**：

```go
// Department聚合根
type Department struct {
    // 标识信息
    id       DepartmentID
    tenantID TenantID
    appID    AppID
    
    // 基本信息
    name        DepartmentName
    code        DepartmentCode
    description string
    
    // 层级信息
    parentID *DepartmentID
    path     DepartmentPath
    level    DepartmentLevel
    
    // 状态信息
    status    DepartmentStatus
    isSystem  bool
    
    // 管理信息
    managerID *UserID
    
    // 系统信息
    createdAt time.Time
    updatedAt time.Time
    
    // 领域事件
    events []DomainEvent
}

// 核心业务行为
func (d *Department) MoveTo(newParent *Department, hierarchyService HierarchyService) error {
    if newParent != nil {
        // 检查循环引用
        if err := hierarchyService.CheckCircularReference(d.id, newParent.id); err != nil {
            return err
        }
        
        // 检查层级限制
        if newParent.level.Value()+1 > hierarchyService.MaxLevel() {
            return NewDepartmentLevelExceededError(hierarchyService.MaxLevel())
        }
        
        d.parentID = &newParent.id
        d.level = NewDepartmentLevel(newParent.level.Value() + 1)
    } else {
        d.parentID = nil
        d.level = NewDepartmentLevel(1)
    }
    
    // 重新计算路径
    newPath, err := hierarchyService.CalculatePath(d.id, d.parentID)
    if err != nil {
        return err
    }
    d.path = newPath
    
    d.updatedAt = time.Now()
    d.AddEvent(NewDepartmentMovedEvent(d.id, d.parentID))
    
    return nil
}

func (d *Department) UpdateInfo(name DepartmentName, description string) error {
    d.name = name
    d.description = description
    d.updatedAt = time.Now()
    
    d.AddEvent(NewDepartmentUpdatedEvent(d.id))
    
    return nil
}

func (d *Department) AssignManager(managerID UserID) error {
    oldManagerID := d.managerID
    d.managerID = &managerID
    d.updatedAt = time.Now()
    
    d.AddEvent(NewDepartmentManagerAssignedEvent(d.id, oldManagerID, &managerID))
    
    return nil
}

func (d *Department) Activate() error {
    if d.status.IsActive() {
        return NewDepartmentAlreadyActiveError(d.id)
    }
    
    d.status = NewDepartmentStatus(DepartmentStatusActive)
    d.updatedAt = time.Now()
    
    d.AddEvent(NewDepartmentActivatedEvent(d.id))
    
    return nil
}

func (d *Department) Deactivate() error {
    if d.isSystem {
        return NewSystemDepartmentCannotBeDeactivatedError(d.id)
    }
    
    if !d.status.IsActive() {
        return NewDepartmentNotActiveError(d.id)
    }
    
    d.status = NewDepartmentStatus(DepartmentStatusInactive)
    d.updatedAt = time.Now()
    
    d.AddEvent(NewDepartmentDeactivatedEvent(d.id))
    
    return nil
}

// Position聚合根
type Position struct {
    // 标识信息
    id       PositionID
    tenantID TenantID
    appID    AppID
    
    // 基本信息
    name        PositionName
    code        PositionCode
    description string
    
    // 级别信息
    level  PositionLevel
    salary *Salary
    
    // 状态信息
    status   PositionStatus
    isSystem bool
    
    // 系统信息
    createdAt time.Time
    updatedAt time.Time
    
    // 领域事件
    events []DomainEvent
}

func (p *Position) UpdateInfo(name PositionName, description string, level PositionLevel) error {
    p.name = name
    p.description = description
    p.level = level
    p.updatedAt = time.Now()
    
    p.AddEvent(NewPositionUpdatedEvent(p.id))
    
    return nil
}

func (p *Position) SetSalary(salary Salary) error {
    p.salary = &salary
    p.updatedAt = time.Now()
    
    p.AddEvent(NewPositionSalaryUpdatedEvent(p.id, salary))
    
    return nil
}
```

**值对象设计**：

```go
// DepartmentID - 部门标识
type DepartmentID struct {
    value int64
}

func NewDepartmentID(value int64) (DepartmentID, error) {
    if value <= 0 {
        return DepartmentID{}, errors.New("department ID must be positive")
    }
    return DepartmentID{value: value}, nil
}

// DepartmentName - 部门名称
type DepartmentName struct {
    value string
}

func NewDepartmentName(value string) (DepartmentName, error) {
    if len(value) == 0 || len(value) > 100 {
        return DepartmentName{}, errors.New("department name length must be between 1 and 100")
    }
    return DepartmentName{value: value}, nil
}

// DepartmentCode - 部门编码
type DepartmentCode struct {
    value string
}

func NewDepartmentCode(value string) (DepartmentCode, error) {
    if !regexp.MustCompile(`^[A-Z0-9_-]+$`).MatchString(value) {
        return DepartmentCode{}, errors.New("department code must contain only uppercase letters, numbers, underscores, and hyphens")
    }
    return DepartmentCode{value: value}, nil
}

// DepartmentPath - 部门路径
type DepartmentPath struct {
    value string
}

func NewDepartmentPath(value string) (DepartmentPath, error) {
    if !strings.HasPrefix(value, "/") {
        return DepartmentPath{}, errors.New("department path must start with /")
    }
    return DepartmentPath{value: value}, nil
}

func (p DepartmentPath) GetParentPath() DepartmentPath {
    parts := strings.Split(strings.Trim(p.value, "/"), "/")
    if len(parts) <= 1 {
        return DepartmentPath{value: "/"}
    }
    return DepartmentPath{value: "/" + strings.Join(parts[:len(parts)-1], "/")}
}

func (p DepartmentPath) GetDepth() int {
    if p.value == "/" {
        return 0
    }
    return len(strings.Split(strings.Trim(p.value, "/"), "/"))
}

// DepartmentLevel - 部门层级
type DepartmentLevel struct {
    value int
}

func NewDepartmentLevel(value int) (DepartmentLevel, error) {
    if value < 1 || value > 10 {
        return DepartmentLevel{}, errors.New("department level must be between 1 and 10")
    }
    return DepartmentLevel{value: value}, nil
}

func (l DepartmentLevel) Value() int { return l.value }
func (l DepartmentLevel) IsRoot() bool { return l.value == 1 }

// DepartmentStatus - 部门状态
type DepartmentStatus struct {
    value string
}

const (
    DepartmentStatusActive   = "active"
    DepartmentStatusInactive = "inactive"
    DepartmentStatusArchived = "archived"
)

func NewDepartmentStatus(value string) (DepartmentStatus, error) {
    validStatuses := []string{
        DepartmentStatusActive,
        DepartmentStatusInactive,
        DepartmentStatusArchived,
    }
    
    for _, status := range validStatuses {
        if value == status {
            return DepartmentStatus{value: value}, nil
        }
    }
    
    return DepartmentStatus{}, errors.New("invalid department status")
}

func (s DepartmentStatus) Value() string { return s.value }
func (s DepartmentStatus) IsActive() bool { return s.value == DepartmentStatusActive }
func (s DepartmentStatus) IsInactive() bool { return s.value == DepartmentStatusInactive }
```

**领域服务设计**：

```go
// 组织架构服务
type OrganizationService struct {
    userRepo       UserRepository
    departmentRepo DepartmentRepository
    positionRepo   PositionRepository
    hierarchyService HierarchyService
}

func NewOrganizationService(
    userRepo UserRepository,
    departmentRepo DepartmentRepository,
    positionRepo PositionRepository,
    hierarchyService HierarchyService,
) *OrganizationService {
    return &OrganizationService{
        userRepo:         userRepo,
        departmentRepo:   departmentRepo,
        positionRepo:     positionRepo,
        hierarchyService: hierarchyService,
    }
}

func (s *OrganizationService) AssignUserToDepartment(
    ctx context.Context,
    userID UserID,
    departmentID DepartmentID,
) error {
    // 验证用户存在
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return err
    }
    if user == nil {
        return NewUserNotFoundError(userID)
    }
    
    // 验证部门存在且激活
    department, err := s.departmentRepo.FindByID(ctx, departmentID)
    if err != nil {
        return err
    }
    if department == nil {
        return NewDepartmentNotFoundError(departmentID)
    }
    if !department.Status().IsActive() {
        return NewDepartmentNotActiveError(departmentID)
    }
    
    // 分配用户到部门
    if err := user.AssignToDepartment(departmentID); err != nil {
        return err
    }
    
    return s.userRepo.Save(ctx, user)
}

func (s *OrganizationService) AssignUserToPosition(
    ctx context.Context,
    userID UserID,
    positionID PositionID,
) error {
    // 验证用户存在
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return err
    }
    if user == nil {
        return NewUserNotFoundError(userID)
    }
    
    // 验证职位存在且激活
    position, err := s.positionRepo.FindByID(ctx, positionID)
    if err != nil {
        return err
    }
    if position == nil {
        return NewPositionNotFoundError(positionID)
    }
    if !position.Status().IsActive() {
        return NewPositionNotActiveError(positionID)
    }
    
    // 分配用户到职位
    if err := user.AssignToPosition(positionID); err != nil {
        return err
    }
    
    return s.userRepo.Save(ctx, user)
}

// 层级计算服务
type HierarchyService struct {
    departmentRepo DepartmentRepository
    maxLevel       int
}

func NewHierarchyService(departmentRepo DepartmentRepository, maxLevel int) *HierarchyService {
    return &HierarchyService{
        departmentRepo: departmentRepo,
        maxLevel:       maxLevel,
    }
}

func (s *HierarchyService) CheckCircularReference(
    departmentID DepartmentID,
    newParentID DepartmentID,
) error {
    // 检查是否会形成循环引用
    visited := make(map[DepartmentID]bool)
    current := newParentID
    
    for current.Value() != 0 {
        if visited[current] {
            return NewCircularReferenceError(departmentID, newParentID)
        }
        
        if current.Equals(departmentID) {
            return NewCircularReferenceError(departmentID, newParentID)
        }
        
        visited[current] = true
        
        // 获取父部门
        parent, err := s.departmentRepo.FindByID(context.Background(), current)
        if err != nil {
            return err
        }
        if parent == nil || parent.ParentID() == nil {
            break
        }
        
        current = *parent.ParentID()
    }
    
    return nil
}

func (s *HierarchyService) CalculatePath(
    departmentID DepartmentID,
    parentID *DepartmentID,
) (DepartmentPath, error) {
    if parentID == nil {
        return NewDepartmentPath("/" + departmentID.String())
    }
    
    parent, err := s.departmentRepo.FindByID(context.Background(), *parentID)
    if err != nil {
        return DepartmentPath{}, err
    }
    if parent == nil {
        return DepartmentPath{}, NewDepartmentNotFoundError(*parentID)
    }
    
    parentPath := parent.Path().Value()
    if parentPath == "/" {
        return NewDepartmentPath("/" + departmentID.String())
    }
    
    return NewDepartmentPath(parentPath + "/" + departmentID.String())
}

func (s *HierarchyService) MaxLevel() int {
    return s.maxLevel
}
```

### 2.3 权限访问控制上下文

#### 聚合设计

**Role聚合（聚合根）**
```
Role聚合根
├── 实体
│   ├── Role（聚合根实体）
│   └── Permission（实体）
├── 值对象
│   ├── RoleID
│   ├── RoleName
│   ├── RoleCode
│   ├── PermissionID
│   ├── PermissionName
│   ├── ResourcePath
│   └── ActionType
└── 领域服务
    ├── RoleAssignmentService
    ├── PermissionCheckService
    └── AccessPolicyService
```

**核心实体定义**：

```go
// Role聚合根
type Role struct {
    // 标识信息
    id       RoleID
    tenantID TenantID
    appID    AppID
    
    // 基本信息
    name        RoleName
    code        RoleCode
    description string
    
    // 权限信息
    permissions []Permission
    
    // 状态信息
    status   RoleStatus
    isSystem bool
    
    // 系统信息
    createdAt time.Time
    updatedAt time.Time
    
    // 领域事件
    events []DomainEvent
}

// Permission实体
type Permission struct {
    id           PermissionID
    name         PermissionName
    resourcePath ResourcePath
    actionType   ActionType
    description  string
}

// 核心业务行为
func (r *Role) AddPermission(permission Permission) error {
    // 检查权限是否已存在
    for _, p := range r.permissions {
        if p.ID().Equals(permission.ID()) {
            return NewPermissionAlreadyExistsError(r.id, permission.ID())
        }
    }
    
    r.permissions = append(r.permissions, permission)
    r.updatedAt = time.Now()
    
    r.AddEvent(NewRolePermissionAddedEvent(r.id, permission.ID()))
    
    return nil
}

func (r *Role) RemovePermission(permissionID PermissionID) error {
    for i, p := range r.permissions {
        if p.ID().Equals(permissionID) {
            r.permissions = append(r.permissions[:i], r.permissions[i+1:]...)
            r.updatedAt = time.Now()
            
            r.AddEvent(NewRolePermissionRemovedEvent(r.id, permissionID))
            
            return nil
        }
    }
    
    return NewPermissionNotFoundInRoleError(r.id, permissionID)
}

func (r *Role) HasPermission(resourcePath ResourcePath, actionType ActionType) bool {
    for _, p := range r.permissions {
        if p.ResourcePath().Matches(resourcePath) && p.ActionType().Equals(actionType) {
            return true
        }
    }
    return false
}

func (r *Role) GetPermissions() []Permission {
    return append([]Permission(nil), r.permissions...)
}
```

**值对象设计**：

```go
// ResourcePath - 资源路径
type ResourcePath struct {
    value string
}

func NewResourcePath(value string) (ResourcePath, error) {
    if !strings.HasPrefix(value, "/") {
        return ResourcePath{}, errors.New("resource path must start with /")
    }
    return ResourcePath{value: value}, nil
}

func (p ResourcePath) Value() string { return p.value }

func (p ResourcePath) Matches(other ResourcePath) bool {
    // 支持通配符匹配
    if strings.HasSuffix(p.value, "/*") {
        prefix := strings.TrimSuffix(p.value, "/*")
        return strings.HasPrefix(other.value, prefix)
    }
    return p.value == other.value
}

// ActionType - 操作类型
type ActionType struct {
    value string
}

const (
    ActionTypeRead   = "read"
    ActionTypeWrite  = "write"
    ActionTypeDelete = "delete"
    ActionTypeAdmin  = "admin"
)

func NewActionType(value string) (ActionType, error) {
    validActions := []string{
        ActionTypeRead,
        ActionTypeWrite,
        ActionTypeDelete,
        ActionTypeAdmin,
    }
    
    for _, action := range validActions {
        if value == action {
            return ActionType{value: value}, nil
        }
    }
    
    return ActionType{}, errors.New("invalid action type")
}

func (a ActionType) Value() string { return a.value }
func (a ActionType) Equals(other ActionType) bool { return a.value == other.value }
```

**领域服务设计**：

```go
// 权限检查服务
type PermissionCheckService struct {
    userRepo UserRepository
    roleRepo RoleRepository
    cache    PermissionCache
}

func NewPermissionCheckService(
    userRepo UserRepository,
    roleRepo RoleRepository,
    cache PermissionCache,
) *PermissionCheckService {
    return &PermissionCheckService{
        userRepo: userRepo,
        roleRepo: roleRepo,
        cache:    cache,
    }
}

func (s *PermissionCheckService) CheckPermission(
    ctx context.Context,
    userID UserID,
    resourcePath ResourcePath,
    actionType ActionType,
) (bool, error) {
    // 尝试从缓存获取
    cacheKey := s.buildCacheKey(userID, resourcePath, actionType)
    if result, found := s.cache.Get(cacheKey); found {
        return result, nil
    }
    
    // 获取用户角色
    userRoles, err := s.getUserRoles(ctx, userID)
    if err != nil {
        return false, err
    }
    
    // 检查权限
    hasPermission := false
    for _, role := range userRoles {
        if role.HasPermission(resourcePath, actionType) {
            hasPermission = true
            break
        }
    }
    
    // 缓存结果
    s.cache.Set(cacheKey, hasPermission, 5*time.Minute)
    
    return hasPermission, nil
}

func (s *PermissionCheckService) getUserRoles(
    ctx context.Context,
    userID UserID,
) ([]Role, error) {
    // 这里需要通过用户角色关联表获取用户的所有角色
    // 具体实现依赖于基础设施层
    return s.roleRepo.FindByUserID(ctx, userID)
}
```

## 3. 典型业务场景梳理与建模细节

### 3.1 用户身份认证上下文典型场景

#### 场景1：用户注册与激活

**业务流程**：
1. 接收用户注册请求
2. 验证用户名和邮箱唯一性
3. 验证密码策略合规性
4. 创建用户实体（待激活状态）
5. 生成激活令牌
6. 发送激活邮件
7. 用户点击激活链接
8. 验证激活令牌
9. 激活用户账户
10. 分配默认角色
11. 记录审计日志

**领域建模细节**：

```go
// 涉及的聚合
- User聚合（主要）
- Role聚合（默认角色分配）
- AuditLog聚合（操作记录）

// 涉及的领域服务
- UserFactory（用户创建工厂）
- PasswordPolicyService（密码策略验证）
- RoleAssignmentService（默认角色分配）
- ActivationTokenService（激活令牌管理）

// 领域事件
- UserRegisteredEvent（用户注册）
- ActivationEmailRequestedEvent（激活邮件请求）
- UserActivatedEvent（用户激活）
- DefaultRoleAssignedEvent（默认角色分配）

// 业务规则
1. 用户名在租户+应用内必须唯一
2. 邮箱在租户+应用内必须唯一
3. 密码必须符合安全策略
4. 新用户默认为待激活状态
5. 激活令牌有效期24小时
6. 激活后自动分配默认角色
7. 系统用户不能通过注册创建

// 状态变化
nil → User(status=pending) → User(status=active)
```

**实现示例**：

```go
// 用户注册应用服务
func (s *UserApplicationService) RegisterUser(
    ctx context.Context,
    cmd RegisterUserCommand,
) (*UserRegistrationResult, error) {
    // 1. 验证唯一性
    if exists, err := s.userRepo.ExistsByTenantAppAndUsername(
        ctx, cmd.TenantID, cmd.AppID, cmd.Username, nil,
    ); err != nil {
        return nil, err
    } else if exists {
        return nil, NewUsernameAlreadyExistsError(cmd.Username)
    }
    
    if exists, err := s.userRepo.ExistsByTenantAppAndEmail(
        ctx, cmd.TenantID, cmd.AppID, cmd.Email, nil,
    ); err != nil {
        return nil, err
    } else if exists {
        return nil, NewEmailAlreadyExistsError(cmd.Email)
    }
    
    // 2. 创建用户聚合
    userAgg, err := s.userFactory.CreateUser(factory.CreateUserRequest{
        TenantID: cmd.TenantID,
        AppID:    cmd.AppID,
        Username: cmd.Username,
        Email:    cmd.Email,
        Password: cmd.Password,
        RealName: cmd.RealName,
        Phone:    cmd.Phone,
        Status:   UserStatusPending, // 待激活状态
    })
    if err != nil {
        return nil, err
    }
    
    // 3. 生成激活令牌
    activationToken, err := s.activationService.GenerateToken(
        userAgg.Root().ID(),
        24*time.Hour, // 24小时有效期
    )
    if err != nil {
        return nil, err
    }
    
    // 4. 保存用户
    if err := s.userRepo.Save(ctx, userAgg); err != nil {
        return nil, err
    }
    
    // 5. 发布事件
    s.publishEvents(userAgg.GetEvents())
    
    // 6. 发送激活邮件事件
    s.eventBus.Publish(NewActivationEmailRequestedEvent(
        userAgg.Root().ID(),
        userAgg.Root().Email(),
        activationToken,
    ))
    
    return &UserRegistrationResult{
        UserID:          userAgg.Root().ID(),
        ActivationToken: activationToken,
        ExpiresAt:       time.Now().Add(24 * time.Hour),
    }, nil
}

// 用户激活应用服务
func (s *UserApplicationService) ActivateUser(
    ctx context.Context,
    cmd ActivateUserCommand,
) error {
    // 1. 验证激活令牌
    userID, err := s.activationService.ValidateToken(cmd.Token)
    if err != nil {
        return err
    }
    
    // 2. 获取用户
    userAgg, err := s.userRepo.FindAggregateByID(ctx, userID)
    if err != nil {
        return err
    }
    if userAgg == nil {
        return NewUserNotFoundError(userID)
    }
    
    user := userAgg.Root()
    
    // 3. 检查用户状态
    if user.IsActivated() {
        return NewUserAlreadyActivatedError(userID)
    }
    
    // 4. 激活用户
    if err := user.Activate(); err != nil {
        return err
    }
    
    // 5. 分配默认角色
    if err := s.roleService.AssignDefaultRole(
        ctx, userID, user.TenantID(), user.AppID(),
    ); err != nil {
        return err
    }
    
    // 6. 保存用户
    if err := s.userRepo.Save(ctx, userAgg); err != nil {
        return err
    }
    
    // 7. 使激活令牌失效
    if err := s.activationService.InvalidateToken(cmd.Token); err != nil {
        // 记录错误但不影响主流程
        log.Error("Failed to invalidate activation token", err)
    }
    
    // 8. 发布事件
    s.publishEvents(userAgg.GetEvents())
    
    return nil
}
```

#### 场景2：用户登录认证

**业务流程**：
1. 接收登录请求
2. 根据用户名查找用户
3. 检查用户状态（激活、未锁定）
4. 验证登录频率限制
5. 验证密码
6. 检查密码是否过期
7. 更新登录信息
8. 生成访问令牌和刷新令牌
9. 记录登录日志
10. 返回认证结果

**领域建模细节**：

```go
// 涉及的聚合
- User聚合（主要）
- AuditLog聚合（登录记录）

// 涉及的领域服务
- UserAuthenticationService（认证逻辑）
- LoginLimitService（登录限制检查）
- TokenService（令牌生成）
- PasswordPolicyService（密码策略）

// 领域事件
- UserLoggedInEvent（成功登录）
- LoginFailedEvent（登录失败）
- UserLockedEvent（超限锁定）
- PasswordExpiredEvent（密码过期）

// 业务规则
1. 只有激活状态的用户可以登录
2. 锁定用户不能登录
3. 连续失败超限自动锁定
4. 登录成功重置失败计数
5. 记录最后登录时间和IP
6. 密码过期需要强制修改
7. 生成JWT令牌包含用户基本信息

// 状态变化
User(failedAttempts=n) → User(failedAttempts=0, lastLoginAt=now)
User(failedAttempts=n) → User(failedAttempts=n+1)
User(status=active) → User(status=locked) // 超限时
```

**实现示例**：

```go
// 用户登录应用服务
func (s *UserApplicationService) LoginUser(
    ctx context.Context,
    cmd LoginUserCommand,
) (*LoginResult, error) {
    // 1. 查找用户
    user, err := s.userRepo.FindByTenantAppAndUsername(
        ctx, cmd.TenantID, cmd.AppID, cmd.Username,
    )
    if err != nil {
        return nil, err
    }
    if user == nil {
        // 记录失败尝试但不暴露用户不存在
        s.auditService.RecordLoginAttempt(ctx, cmd.Username, cmd.IPAddress, false, "user_not_found")
        return nil, NewInvalidCredentialsError()
    }
    
    // 2. 执行认证
    if err := s.authService.Authenticate(
        user, cmd.Password, cmd.IPAddress,
    ); err != nil {
        // 记录失败尝试
        s.auditService.RecordLoginAttempt(ctx, cmd.Username, cmd.IPAddress, false, err.Error())
        return nil, err
    }
    
    // 3. 检查密码是否过期
    if user.IsPasswordExpired() {
        return &LoginResult{
            Success:           false,
            RequirePasswordChange: true,
            UserID:            user.ID(),
            Message:           "Password expired, please change your password",
        }, nil
    }
    
    // 4. 生成访问令牌
    accessToken, err := s.tokenService.GenerateAccessToken(TokenClaims{
        UserID:   user.ID(),
        TenantID: user.TenantID(),
        AppID:    user.AppID(),
        Username: user.Username(),
        Email:    user.Email(),
        Roles:    s.getUserRoles(ctx, user.ID()),
    })
    if err != nil {
        return nil, err
    }
    
    // 5. 生成刷新令牌
    refreshToken, err := s.tokenService.GenerateRefreshToken(user.ID())
    if err != nil {
        return nil, err
    }
    
    // 6. 更新用户登录信息
    user.UpdateLastLogin(cmd.IPAddress)
    
    // 7. 保存用户
    if err := s.userRepo.Save(ctx, user); err != nil {
        return nil, err
    }
    
    // 8. 记录成功登录
    s.auditService.RecordLoginAttempt(ctx, cmd.Username, cmd.IPAddress, true, "success")
    
    // 9. 发布事件
    s.eventBus.Publish(NewUserLoggedInEvent(
        user.ID(), user.TenantID(), cmd.IPAddress,
    ))
    
    return &LoginResult{
        Success:      true,
        UserID:       user.ID(),
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        ExpiresIn:    s.tokenService.GetAccessTokenTTL(),
        TokenType:    "Bearer",
    }, nil
}
```

#### 场景3：密码重置流程

**业务流程**：
1. 用户请求密码重置
2. 验证邮箱存在性
3. 生成重置令牌
4. 发送重置邮件
5. 用户点击重置链接
6. 验证重置令牌
7. 设置新密码
8. 验证密码策略
9. 更新用户密码
10. 使令牌失效
11. 记录密码变更日志

**领域建模细节**：

```go
// 涉及的聚合
- User聚合（主要）
- PasswordResetToken聚合（重置令牌）
- AuditLog聚合（操作记录）

// 涉及的领域服务
- PasswordResetService（密码重置逻辑）
- PasswordPolicyService（密码策略验证）
- TokenService（令牌管理）
- EmailService（邮件发送）

// 领域事件
- PasswordResetRequestedEvent（密码重置请求）
- PasswordResetEmailSentEvent（重置邮件发送）
- PasswordChangedEvent（密码变更）
- PasswordResetCompletedEvent（重置完成）

// 业务规则
1. 只有激活用户可以重置密码
2. 重置令牌有效期2小时
3. 令牌只能使用一次
4. 新密码必须符合安全策略
5. 新密码不能与最近3次密码相同
6. 重置成功后强制重新登录
7. 记录密码变更审计日志

// 状态变化
User(password=old) → User(password=new, passwordExpiredAt=null)
PasswordResetToken(used=false) → PasswordResetToken(used=true)
```

### 3.2 组织架构管理上下文典型场景

#### 场景1：部门创建与层级管理

**业务流程**：
1. 接收部门创建请求
2. 验证部门名称和编码唯一性
3. 验证父部门存在性和状态
4. 检查层级深度限制
5. 计算部门路径
6. 创建部门实体
7. 分配部门管理员
8. 记录操作日志

**领域建模细节**：

```go
// 涉及的聚合
- Department聚合（主要）
- User聚合（部门管理员）
- AuditLog聚合（操作记录）

// 涉及的领域服务
- OrganizationService（组织架构管理）
- HierarchyService（层级计算）
- DepartmentFactory（部门创建工厂）

// 领域事件
- DepartmentCreatedEvent（部门创建）
- DepartmentManagerAssignedEvent（管理员分配）
- OrganizationStructureChangedEvent（组织架构变更）

// 业务规则
1. 部门名称在同级别内唯一
2. 部门编码在租户+应用内唯一
3. 最大层级深度为10级
4. 父部门必须为激活状态
5. 系统部门不能删除
6. 部门管理员必须为激活用户
7. 部门路径自动计算

// 状态变化
nil → Department(status=active, level=n, path=calculated)
```

#### 场景2：用户部门调动

**业务流程**：
1. 接收用户调动请求
2. 验证用户和目标部门存在性
3. 检查用户当前部门
4. 验证调动权限
5. 更新用户部门关系
6. 处理权限继承变更
7. 通知相关人员
8. 记录调动历史

### 3.3 权限访问控制上下文典型场景

#### 场景1：角色权限分配

**业务流程**：
1. 接收权限分配请求
2. 验证角色和权限存在性
3. 检查权限分配权限
4. 验证权限冲突
5. 更新角色权限关系
6. 清除相关权限缓存
7. 通知权限变更
8. 记录权限变更日志

#### 场景2：访问权限验证

**业务流程**：
1. 接收访问请求
2. 解析访问令牌
3. 获取用户角色
4. 检查权限缓存
5. 计算有效权限
6. 验证资源访问权限
7. 记录访问日志
8. 返回验证结果

## 4. 领域模型图与对象关系

### 4.1 整体领域模型关系图

```
用户身份认证上下文
┌─────────────────────────────────────────────────────────────────┐
│  User聚合根                                                      │
│  ├── UserID (值对象)                                             │
│  ├── Username (值对象)                                           │
│  ├── Email (值对象)                                              │
│  ├── Password (值对象)                                           │
│  ├── UserStatus (值对象)                                         │
│  ├── RealName (值对象)                                           │
│  ├── Phone (值对象)                                              │
│  ├── Avatar (值对象)                                             │
│  ├── TenantID (值对象) ────────────────────────────────────────┐ │
│  ├── AppID (值对象) ──────────────────────────────────────────┐ │ │
│  ├── DepartmentID (引用) ──────────────────────────────────┐   │ │ │
│  └── PositionID (引用) ────────────────────────────────┐   │   │ │ │
└─────────────────────────────────────────────────────────│───│───│─│─│─┘
                                                        │   │   │ │ │
组织架构管理上下文                                         │   │   │ │ │
┌─────────────────────────────────────────────────────────│───│───│─│─│─┐
│  Department聚合根                                       │   │   │ │ │ │
│  ├── DepartmentID (值对象) ←────────────────────────────┘   │   │ │ │ │
│  ├── DepartmentName (值对象)                                │   │ │ │ │
│  ├── DepartmentCode (值对象)                                │   │ │ │ │
│  ├── DepartmentPath (值对象)                                │   │ │ │ │
│  ├── DepartmentLevel (值对象)                               │   │ │ │ │
│  ├── DepartmentStatus (值对象)                              │   │ │ │ │
│  ├── TenantID (值对象) ←────────────────────────────────────┘   │ │ │ │
│  ├── AppID (值对象) ←──────────────────────────────────────────┘ │ │ │
│  ├── ParentID (引用)                                             │ │ │
│  └── ManagerID (引用)                                            │ │ │
│                                                                 │ │ │
│  Position聚合根                                                  │ │ │
│  ├── PositionID (值对象) ←───────────────────────────────────────┘ │ │
│  ├── PositionName (值对象)                                         │ │
│  ├── PositionCode (值对象)                                         │ │
│  ├── PositionLevel (值对象)                                        │ │
│  ├── PositionStatus (值对象)                                       │ │
│  ├── TenantID (值对象) ←───────────────────────────────────────────┘ │
│  └── AppID (值对象) ←─────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────────────┘

权限访问控制上下文
┌─────────────────────────────────────────────────────────────────┐
│  Role聚合根                                                      │
│  ├── RoleID (值对象)                                             │
│  ├── RoleName (值对象)                                           │
│  ├── RoleCode (值对象)                                           │
│  ├── RoleStatus (值对象)                                         │
│  ├── TenantID (值对象)                                           │
│  ├── AppID (值对象)                                              │
│  └── Permissions (实体集合)                                      │
│      ├── PermissionID (值对象)                                   │
│      ├── PermissionName (值对象)                                 │
│      ├── ResourcePath (值对象)                                   │
│      └── ActionType (值对象)                                     │
│                                                                 │
│  UserRole关联 (多对多关系)                                        │
│  ├── UserID (引用User聚合)                                       │
│  ├── RoleID (引用Role聚合)                                       │
│  ├── AssignedAt (时间戳)                                         │
│  └── AssignedBy (操作人)                                         │
└─────────────────────────────────────────────────────────────────┘
```

### 4.2 聚合边界与引用方式

**聚合内部关系**：
- User聚合：User实体作为聚合根，包含多个值对象，通过ID引用其他聚合
- Department聚合：Department实体作为聚合根，支持自引用形成层级结构
- Position聚合：Position实体作为聚合根，相对独立的职位管理
- Role聚合：Role实体作为聚合根，包含Permission实体集合

**聚合间引用策略**：
- **ID引用**：User聚合通过DepartmentID和PositionID引用其他聚合
- **事件驱动**：聚合间状态同步通过领域事件实现
- **应用服务协调**：复杂业务流程通过应用服务编排多个聚合
- **最终一致性**：跨聚合的数据一致性通过事件最终达成

**数据一致性保障**：
- **事务边界**：单个聚合内保证强一致性
- **补偿机制**：跨聚合操作失败时的回滚策略
- **幂等性设计**：事件处理和外部调用的幂等性保证
- **重试机制**：临时失败的自动重试策略

## 5. 领域服务与跨上下文协作

### 5.1 核心领域服务识别

**用户身份认证领域服务**：
- `UserAuthenticationService`：用户认证逻辑
- `PasswordPolicyService`：密码策略管理
- `LoginLimitService`：登录限制控制
- `MFAService`：多因素认证
- `PasswordResetService`：密码重置流程
- `ActivationService`：账户激活管理

**组织架构管理领域服务**：
- `OrganizationService`：组织架构管理
- `HierarchyService`：层级结构计算
- `DepartmentTransferService`：部门调动
- `PositionManagementService`：职位管理

**权限访问控制领域服务**：
- `PermissionCheckService`：权限验证
- `RoleAssignmentService`：角色分配
- `AccessPolicyService`：访问策略
- `PermissionInheritanceService`：权限继承

### 5.2 跨上下文协作模式

#### 5.2.1 事件驱动协作

```go
// 用户创建时的跨上下文协作
用户身份认证上下文 → UserCreatedEvent → 权限访问控制上下文
                                    → 审计安全监控上下文
                                    → 应用管理上下文

// 部门变更时的跨上下文协作
组织架构管理上下文 → DepartmentChangedEvent → 权限访问控制上下文
                                         → 审计安全监控上下文

// 权限变更时的跨上下文协作
权限访问控制上下文 → PermissionChangedEvent → 审计安全监控上下文
                                          → 应用管理上下文
```

#### 5.2.2 应用服务协调

```go
// 用户完整创建流程（跨多个上下文）
func (s *UserManagementApplicationService) CreateCompleteUser(
    ctx context.Context,
    cmd CreateCompleteUserCommand,
) error {
    // 1. 创建用户（用户身份认证上下文）
    userResult, err := s.userService.CreateUser(ctx, CreateUserCommand{
        TenantID: cmd.TenantID,
        AppID:    cmd.AppID,
        Username: cmd.Username,
        Email:    cmd.Email,
        Password: cmd.Password,
        RealName: cmd.RealName,
    })
    if err != nil {
        return err
    }
    
    // 2. 分配部门（组织架构管理上下文）
    if cmd.DepartmentID != nil {
        if err := s.orgService.AssignUserToDepartment(
            ctx, userResult.UserID, *cmd.DepartmentID,
        ); err != nil {
            // 补偿：删除已创建的用户
            s.userService.DeleteUser(ctx, userResult.UserID)
            return err
        }
    }
    
    // 3. 分配角色（权限访问控制上下文）
    if len(cmd.RoleIDs) > 0 {
        if err := s.permissionService.AssignRolesToUser(
            ctx, userResult.UserID, cmd.RoleIDs,
        ); err != nil {
            // 补偿：删除用户和部门分配
            s.orgService.RemoveUserFromDepartment(ctx, userResult.UserID)
            s.userService.DeleteUser(ctx, userResult.UserID)
            return err
        }
    }
    
    // 4. 记录审计日志（审计安全监控上下文）
    s.auditService.RecordUserCreation(ctx, AuditUserCreationEvent{
        UserID:       userResult.UserID,
        CreatedBy:    cmd.OperatorID,
        DepartmentID: cmd.DepartmentID,
        RoleIDs:      cmd.RoleIDs,
        Timestamp:    time.Now(),
    })
    
    return nil
}
```

#### 5.2.3 共享内核

```go
// 跨上下文共享的值对象
package shared

// 租户标识 - 所有上下文共享
type TenantID struct {
    value int64
}

// 应用标识 - 所有上下文共享
type AppID struct {
    value int64
}

// 用户标识 - 多个上下文共享
type UserID struct {
    value int64
}

// 通用状态枚举
type EntityStatus string

const (
    StatusActive   EntityStatus = "active"
    StatusInactive EntityStatus = "inactive"
    StatusArchived EntityStatus = "archived"
)

// 通用领域事件接口
type DomainEvent interface {
    EventID() string
    EventType() string
    AggregateID() string
    OccurredAt() time.Time
    Version() int
}
```

#### 5.2.4 防腐层设计

```go
// 第三方身份提供商集成的防腐层
type OAuthProviderAdapter struct {
    provider     OAuthProvider
    userMapper   UserMapper
    configRepo   OAuthConfigRepository
}

func (a *OAuthProviderAdapter) AuthenticateUser(
    ctx context.Context,
    authCode string,
    tenantID TenantID,
    appID AppID,
) (*ExternalUserInfo, error) {
    // 1. 获取配置
    config, err := a.configRepo.GetConfig(ctx, tenantID, appID, a.provider.Type())
    if err != nil {
        return nil, err
    }
    
    // 2. 调用第三方API
    externalUser, err := a.provider.GetUserInfo(ctx, authCode, config)
    if err != nil {
        return nil, NewOAuthAuthenticationError(err)
    }
    
    // 3. 映射到内部用户模型
    userInfo := a.userMapper.MapFromExternal(externalUser, tenantID, appID)
    
    return userInfo, nil
}

// 用户信息映射器
type UserMapper struct{}

func (m *UserMapper) MapFromExternal(
    external *ExternalUser,
    tenantID TenantID,
    appID AppID,
) *ExternalUserInfo {
    return &ExternalUserInfo{
        TenantID:     tenantID,
        AppID:        appID,
        ExternalID:   external.ID,
        Username:     m.generateUsername(external),
        Email:        external.Email,
        RealName:     external.Name,
        Avatar:       external.Avatar,
        ProviderType: external.Provider,
    }
}
```

### 5.3 一致性保障机制

#### 5.3.1 Saga模式实现

```go
// 用户创建Saga编排器
type UserCreationSaga struct {
    userService       UserApplicationService
    orgService        OrganizationApplicationService
    permissionService PermissionApplicationService
    auditService      AuditApplicationService
    eventStore        EventStore
}

func (s *UserCreationSaga) Execute(
    ctx context.Context,
    cmd CreateCompleteUserCommand,
) error {
    sagaID := uuid.New().String()
    
    // 创建Saga实例
    saga := NewSagaInstance(sagaID, "UserCreation", cmd)
    
    // 步骤1：创建用户
    step1 := saga.AddStep("CreateUser", func() error {
        return s.userService.CreateUser(ctx, CreateUserCommand{
            TenantID: cmd.TenantID,
            AppID:    cmd.AppID,
            Username: cmd.Username,
            Email:    cmd.Email,
            Password: cmd.Password,
        })
    }, func() error {
        return s.userService.DeleteUser(ctx, cmd.UserID)
    })
    
    // 步骤2：分配部门
    step2 := saga.AddStep("AssignDepartment", func() error {
        if cmd.DepartmentID == nil {
            return nil
        }
        return s.orgService.AssignUserToDepartment(
            ctx, cmd.UserID, *cmd.DepartmentID,
        )
    }, func() error {
        if cmd.DepartmentID == nil {
            return nil
        }
        return s.orgService.RemoveUserFromDepartment(ctx, cmd.UserID)
    })
    
    // 步骤3：分配角色
    step3 := saga.AddStep("AssignRoles", func() error {
        if len(cmd.RoleIDs) == 0 {
            return nil
        }
        return s.permissionService.AssignRolesToUser(
            ctx, cmd.UserID, cmd.RoleIDs,
        )
    }, func() error {
        if len(cmd.RoleIDs) == 0 {
            return nil
        }
        return s.permissionService.RemoveRolesFromUser(
            ctx, cmd.UserID, cmd.RoleIDs,
        )
    })
    
    // 执行Saga
    return saga.Execute(ctx)
}
```

## 6. 领域建模的注意事项与优化建议

### 6.1 常见建模陷阱

#### 6.1.1 聚合设计陷阱

**陷阱1：聚合过大**
- **问题**：将过多实体放入单个聚合，导致性能问题和并发冲突
- **表现**：User聚合包含用户的所有相关信息（部门、角色、权限等）
- **解决方案**：按业务边界拆分聚合，使用ID引用代替直接包含

**陷阱2：聚合过小**
- **问题**：过度拆分导致业务一致性难以保证
- **表现**：用户基本信息、认证信息、状态信息分别作为独立聚合
- **解决方案**：识别真正的业务不变量，将相关性强的概念放在同一聚合

**陷阱3：聚合间直接引用**
- **问题**：聚合间通过对象引用而非ID引用，破坏聚合边界
- **表现**：User聚合直接包含Department对象
- **解决方案**：使用ID引用，通过仓储或应用服务获取关联对象

#### 6.1.2 值对象设计陷阱

**陷阱1：原始类型偏执**
- **问题**：使用原始类型（string、int）代替值对象
- **表现**：用户名、邮箱使用string类型
- **解决方案**：为有业务含义的概念创建值对象，增强类型安全

**陷阱2：值对象过度设计**
- **问题**：为简单概念创建复杂的值对象
- **表现**：为简单的状态标识创建复杂的状态机
- **解决方案**：保持值对象简单，只包含必要的验证和行为

#### 6.1.3 领域服务滥用

**陷阱1：贫血模型**
- **问题**：将所有业务逻辑放在领域服务中，实体变成数据容器
- **表现**：User实体只有getter/setter，所有逻辑在UserService中
- **解决方案**：将属于实体的行为放回实体，领域服务只处理跨实体逻辑

**陷阱2：领域服务过多**
- **问题**：为每个操作创建领域服务
- **表现**：UserCreationService、UserUpdateService、UserDeletionService
- **解决方案**：按业务能力组织领域服务，避免过度拆分

### 6.2 性能优化建议

#### 6.2.1 聚合加载优化

```go
// 延迟加载策略
type UserAggregate struct {
    root        *User
    roles       []Role        // 延迟加载
    permissions []Permission  // 延迟加载
    department  *Department   // 延迟加载
    
    roleRepo       RoleRepository
    permissionRepo PermissionRepository
    departmentRepo DepartmentRepository
    
    rolesLoaded       bool
    permissionsLoaded bool
    departmentLoaded  bool
}

func (a *UserAggregate) GetRoles(ctx context.Context) ([]Role, error) {
    if !a.rolesLoaded {
        roles, err := a.roleRepo.FindByUserID(ctx, a.root.ID())
        if err != nil {
            return nil, err
        }
        a.roles = roles
        a.rolesLoaded = true
    }
    return a.roles, nil
}
```

#### 6.2.2 查询优化

```go
// 读写分离的查询模型
type UserQueryModel struct {
    ID           int64     `json:"id"`
    Username     string    `json:"username"`
    Email        string    `json:"email"`
    RealName     string    `json:"real_name"`
    Status       string    `json:"status"`
    DepartmentID *int64    `json:"department_id"`
    Department   string    `json:"department"`
    Roles        []string  `json:"roles"`
    CreatedAt    time.Time `json:"created_at"`
}

// 专门的查询仓储
type UserQueryRepository interface {
    FindUserList(ctx context.Context, query UserListQuery) (*PageResult[UserQueryModel], error)
    FindUserDetail(ctx context.Context, userID UserID) (*UserQueryModel, error)
    FindUsersByDepartment(ctx context.Context, departmentID DepartmentID) ([]UserQueryModel, error)
}
```

#### 6.2.3 缓存策略

```go
// 多层缓存策略
type CachedPermissionCheckService struct {
    baseService *PermissionCheckService
    l1Cache     *sync.Map           // 进程内缓存
    l2Cache     redis.Cmdable       // Redis缓存
    l3Cache     *sql.DB             // 数据库缓存表
}

func (s *CachedPermissionCheckService) CheckPermission(
    ctx context.Context,
    userID UserID,
    resource ResourcePath,
    action ActionType,
) (bool, error) {
    cacheKey := fmt.Sprintf("perm:%d:%s:%s", userID.Value(), resource.Value(), action.Value())
    
    // L1缓存检查
    if result, ok := s.l1Cache.Load(cacheKey); ok {
        return result.(bool), nil
    }
    
    // L2缓存检查
    if result, err := s.l2Cache.Get(ctx, cacheKey).Bool(); err == nil {
        s.l1Cache.Store(cacheKey, result)
        return result, nil
    }
    
    // 回源查询
    result, err := s.baseService.CheckPermission(ctx, userID, resource, action)
    if err != nil {
        return false, err
    }
    
    // 更新缓存
    s.l1Cache.Store(cacheKey, result)
    s.l2Cache.Set(ctx, cacheKey, result, 5*time.Minute)
    
    return result, nil
}
```

### 6.3 可扩展性设计

#### 6.3.1 插件化架构

```go
// 认证提供商插件接口
type AuthenticationProvider interface {
    Name() string
    Authenticate(ctx context.Context, credentials Credentials) (*AuthResult, error)
    SupportsCredentialType(credType CredentialType) bool
}

// 认证管理器
type AuthenticationManager struct {
    providers map[string]AuthenticationProvider
    fallback  AuthenticationProvider
}

func (m *AuthenticationManager) RegisterProvider(provider AuthenticationProvider) {
    m.providers[provider.Name()] = provider
}

func (m *AuthenticationManager) Authenticate(
    ctx context.Context,
    providerName string,
    credentials Credentials,
) (*AuthResult, error) {
    provider, exists := m.providers[providerName]
    if !exists {
        provider = m.fallback
    }
    
    if !provider.SupportsCredentialType(credentials.Type()) {
        return nil, NewUnsupportedCredentialTypeError(credentials.Type())
    }
    
    return provider.Authenticate(ctx, credentials)
}
```

#### 6.3.2 事件驱动扩展

```go
// 事件处理器注册机制
type EventHandlerRegistry struct {
    handlers map[string][]EventHandler
    mu       sync.RWMutex
}

func (r *EventHandlerRegistry) RegisterHandler(
    eventType string,
    handler EventHandler,
) {
    r.mu.Lock()
    defer r.mu.Unlock()
    
    r.handlers[eventType] = append(r.handlers[eventType], handler)
}

func (r *EventHandlerRegistry) HandleEvent(
    ctx context.Context,
    event DomainEvent,
) error {
    r.mu.RLock()
    handlers := r.handlers[event.EventType()]
    r.mu.RUnlock()
    
    for _, handler := range handlers {
        if err := handler.Handle(ctx, event); err != nil {
            // 记录错误但继续处理其他处理器
            log.Error("Event handler failed", "event", event.EventType(), "error", err)
        }
    }
    
    return nil
}
```

### 6.4 测试策略

#### 6.4.1 单元测试

```go
// 聚合根测试
func TestUser_Authenticate_Success(t *testing.T) {
    // Arrange
    user := createTestUser()
    password := "correct_password"
    policy := createTestPasswordPolicy()
    
    // Act
    err := user.Authenticate(password, policy)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, 0, user.FailedAttempts())
    assert.NotNil(t, user.LastLoginAt())
    
    // 验证领域事件
    events := user.GetEvents()
    assert.Len(t, events, 1)
    assert.IsType(t, &UserLoggedInEvent{}, events[0])
}

// 领域服务测试
func TestUserAuthenticationService_Authenticate_WithLoginLimit(t *testing.T) {
    // Arrange
    user := createTestUser()
    service := createTestAuthService()
    
    // 模拟登录限制
    mockLoginLimit := &MockLoginLimitService{}
    mockLoginLimit.On("CheckLimit", user.ID(), "192.168.1.1").Return(NewLoginLimitExceededError())
    
    service.loginLimit = mockLoginLimit
    
    // Act
    err := service.Authenticate(user, "password", "192.168.1.1")
    
    // Assert
    assert.Error(t, err)
    assert.IsType(t, &LoginLimitExceededError{}, err)
    mockLoginLimit.AssertExpectations(t)
}
```

#### 6.4.2 集成测试

```go
// 应用服务集成测试
func TestUserApplicationService_CreateUser_Integration(t *testing.T) {
    // Arrange
    ctx := context.Background()
    service := setupTestApplicationService(t)
    
    cmd := CreateUserCommand{
        TenantID: NewTenantID(1),
        AppID:    NewAppID(1),
        Username: "testuser",
        Email:    "<EMAIL>",
        Password: "SecurePass123!",
        RealName: "Test User",
    }
    
    // Act
    result, err := service.CreateUser(ctx, cmd)
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, result)
    
    // 验证用户已保存
    user, err := service.userRepo.FindByID(ctx, result.UserID)
    assert.NoError(t, err)
    assert.Equal(t, cmd.Username, user.Username().Value())
    
    // 验证事件已发布
    events := service.eventBus.GetPublishedEvents()
    assert.Len(t, events, 1)
    assert.IsType(t, &UserCreatedEvent{}, events[0])
}
```

### 6.5 监控与可观测性

#### 6.5.1 业务指标监控

```go
// 业务指标收集
type UserMetricsCollector struct {
    registrationCounter prometheus.Counter
    loginCounter        prometheus.Counter
    loginFailureCounter prometheus.Counter
    activeUsersGauge    prometheus.Gauge
}

func (c *UserMetricsCollector) OnUserRegistered(event *UserCreatedEvent) {
    c.registrationCounter.Inc()
}

func (c *UserMetricsCollector) OnUserLoggedIn(event *UserLoggedInEvent) {
    c.loginCounter.Inc()
}

func (c *UserMetricsCollector) OnLoginFailed(event *LoginFailedEvent) {
    c.loginFailureCounter.Inc()
}
```

#### 6.5.2 分布式追踪

```go
// 追踪装饰器
type TracedUserRepository struct {
    base   UserRepository
    tracer trace.Tracer
}

func (r *TracedUserRepository) FindByID(
    ctx context.Context,
    userID UserID,
) (*User, error) {
    ctx, span := r.tracer.Start(ctx, "UserRepository.FindByID")
    defer span.End()
    
    span.SetAttributes(
        attribute.Int64("user.id", userID.Value()),
    )
    
    user, err := r.base.FindByID(ctx, userID)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, err.Error())
    }
    
    return user, err
}
```

## 总结

本文档基于DDD专家视角，对Users业务系统进行了全面的领域建模分析。通过严格遵循DDD原则，我们识别了六个核心限界上下文，设计了完整的聚合、实体、值对象和领域服务体系，并详细分析了典型业务场景的建模细节。

**核心成果**：
1. **清晰的领域边界**：通过限界上下文划分，明确了各个业务领域的职责和边界
2. **充血的领域模型**：实体和值对象承载了丰富的业务逻辑和规则
3. **合理的聚合设计**：保证了数据一致性和业务不变量
4. **灵活的协作机制**：通过事件驱动和应用服务协调实现跨上下文协作
5. **可扩展的架构**：支持插件化扩展和演进

**实施建议**：
1. 采用渐进式重构策略，逐步从贫血模型向充血模型转变
2. 建立完善的测试体系，保证重构质量
3. 实施监控和可观测性，及时发现和解决问题
4. 建立团队DDD知识体系，提升建模能力
5. 持续优化和演进，适应业务发展需要

通过本次领域建模分析，为Users业务系统构建了坚实的DDD基础，为后续的系统演进和扩展提供了清晰的指导方向。