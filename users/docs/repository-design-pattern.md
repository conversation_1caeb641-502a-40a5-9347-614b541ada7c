# 仓储层设计模式技术方案

## 📋 方案概述

本方案旨在重构 users 系统的仓储层，提供统一、高效、可维护的数据访问模式。方案基于 DDD 架构原则，支持按字段更新、批量写入、分页查询等核心功能。

## 🏗️ 架构设计

### 1. 核心组件

```go
// 查询构建器 - 核心组件
type QueryBuilder[T any] struct {
    db       *gorm.DB
    ctx      context.Context
    query    *gorm.DB
    model    T
    tenant   *TenantContext
    preloads []string
    orders   []OrderClause
    offset   int
    limit    int
    conditions []Condition
}

// 仓储基类 - 通用操作
type BaseRepository[T any] struct {
    db *gorm.DB
    logger logiface.Logger
}

// 查询规范 - 业务规则
type QuerySpecification interface {
    Apply(query *gorm.DB) *gorm.DB
    Priority() int
}
```

### 2. 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层 (Application)                    │
├─────────────────────────────────────────────────────────────┤
│                    领域服务层 (Domain)                       │
├─────────────────────────────────────────────────────────────┤
│                    仓储接口层 (Repository Interface)          │
├─────────────────────────────────────────────────────────────┤
│                    仓储实现层 (Repository Implementation)     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  查询构建器      │  │  更新构建器      │  │  批量操作    │ │
│  │  QueryBuilder   │  │  UpdateBuilder  │  │  BatchOps   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  连接管理        │  │  事务管理        │  │  缓存管理    │ │
│  │  Connection     │  │  Transaction    │  │  Cache      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心功能设计

### 1. 分页参数说明

查询构建器支持两种分页方式：

- **直接分页**: 使用 `pageNum` 和 `pageSize` 参数
  - `pageNum`: 页码，从 1 开始
  - `pageSize`: 每页大小，默认 10
  - 自动计算 `offset = (pageNum - 1) * pageSize`

- **偏移分页**: 使用 `offset` 和 `limit` 参数
  - `offset`: 偏移量，从 0 开始
  - `limit`: 限制数量

### 2. 手动标记功能

查询构建器支持手动标记功能，当某些关键参数无效时直接返回空结果，避免无效查询：

#### 支持的验证类型：
- **用户ID验证**: `userID <= 0`
- **应用ID验证**: `appID <= 0`
- **租户ID验证**: `tenantID <= 0`
- **字符串参数验证**: `value == ""`
- **数组参数验证**: `len(values) == 0`
- **权限验证**: 用户认证和租户匹配

#### 验证方法：
- `ValidateUserID(userID int64)` - 验证用户ID
- `ValidateAppID(appID int64)` - 验证应用ID
- `ValidateTenantID(tenantID int64)` - 验证租户ID
- `ValidateStringParam(value string, paramName string)` - 验证字符串参数
- `ValidateArrayParam(values []interface{}, paramName string)` - 验证数组参数
- `ValidateInt64ArrayParam(values []int64, paramName string)` - 验证int64数组参数
- `SkipQuery(reason string)` - 手动标记跳过查询

#### 使用场景：
- 参数为0或空时直接返回空结果
- 权限验证失败时跳过查询
- 租户ID不匹配时返回空结果
- 应用ID无效时跳过查询

### 3. 查询构建器 (QueryBuilder)

```go
// 查询构建器实现
type QueryBuilder[T any] struct {
    db         *gorm.DB
    ctx        context.Context
    query      *gorm.DB
    model      T
    tenant     *TenantContext
    preloads   []string
    orders     []OrderClause
    offset     int
    limit      int
    conditions []Condition
    groupBy    []string
    having     []Condition
    distinct   bool
    lockMode   string
    skipQuery  bool // 手动标记，跳过查询直接返回空
    skipReason string // 跳过查询的原因
}

// 查询条件
type Condition struct {
    Field    string
    Operator  string
    Value     interface{}
    Logic     string // AND, OR
}

// 排序子句
type OrderClause struct {
    Field     string
    Direction string // ASC, DESC
}

// 查询构建器方法
func (qb *QueryBuilder[T]) WithTenantFilter() *QueryBuilder[T] {
    tenantID, _ := usercontext.GetTenantID(qb.ctx)
    internalAppID := model.GetInternalAppIDFromContext(qb.ctx)
    
    // 检查关键参数有效性
    if tenantID <= 0 {
        qb.skipQuery = true
        qb.skipReason = "invalid tenant_id"
        return qb
    }
    
    if internalAppID <= 0 {
        qb.skipQuery = true
        qb.skipReason = "invalid internal_app_id"
        return qb
    }
    
    qb.conditions = append(qb.conditions, []Condition{
        {Field: "tenant_id", Operator: "=", Value: tenantID, Logic: "AND"},
        {Field: "internal_app_id", Operator: "=", Value: internalAppID, Logic: "AND"},
    }...)
    return qb
}

// 手动标记跳过查询
func (qb *QueryBuilder[T]) SkipQuery(reason string) *QueryBuilder[T] {
    qb.skipQuery = true
    qb.skipReason = reason
    return qb
}

// 检查参数有效性并标记
func (qb *QueryBuilder[T]) ValidateAndSkipIfInvalid(condition bool, reason string) *QueryBuilder[T] {
    if condition {
        qb.skipQuery = true
        qb.skipReason = reason
    }
    return qb
}

// 检查用户ID有效性
func (qb *QueryBuilder[T]) ValidateUserID(userID int64) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(userID <= 0, "invalid user_id")
}

// 检查应用ID有效性
func (qb *QueryBuilder[T]) ValidateAppID(appID int64) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(appID <= 0, "invalid app_id")
}

// 检查租户ID有效性
func (qb *QueryBuilder[T]) ValidateTenantID(tenantID int64) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(tenantID <= 0, "invalid tenant_id")
}

// 检查字符串参数有效性
func (qb *QueryBuilder[T]) ValidateStringParam(value string, paramName string) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(value == "", "invalid "+paramName)
}

// 检查数组参数有效性
func (qb *QueryBuilder[T]) ValidateArrayParam(values []interface{}, paramName string) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(len(values) == 0, "invalid "+paramName)
}

// 检查int64数组参数有效性
func (qb *QueryBuilder[T]) ValidateInt64ArrayParam(values []int64, paramName string) *QueryBuilder[T] {
    return qb.ValidateAndSkipIfInvalid(len(values) == 0, "invalid "+paramName)
}

// 参数验证工具类
type QueryValidator struct {
    skipQuery  bool
    skipReason string
    logger     logiface.Logger
    ctx        context.Context
}

func NewQueryValidator(ctx context.Context, logger logiface.Logger) *QueryValidator {
    return &QueryValidator{
        ctx:    ctx,
        logger: logger,
    }
}

// 验证用户ID
func (qv *QueryValidator) ValidateUserID(userID int64) *QueryValidator {
    if userID <= 0 {
        qv.skipQuery = true
        qv.skipReason = "invalid user_id"
        qv.logger.Debug(qv.ctx, "Invalid user ID", logiface.Int64("user_id", userID))
    }
    return qv
}

// 验证应用ID
func (qv *QueryValidator) ValidateAppID(appID int64) *QueryValidator {
    if appID <= 0 {
        qv.skipQuery = true
        qv.skipReason = "invalid app_id"
        qv.logger.Debug(qv.ctx, "Invalid app ID", logiface.Int64("app_id", appID))
    }
    return qv
}

// 验证租户ID
func (qv *QueryValidator) ValidateTenantID(tenantID int64) *QueryValidator {
    if tenantID <= 0 {
        qv.skipQuery = true
        qv.skipReason = "invalid tenant_id"
        qv.logger.Debug(qv.ctx, "Invalid tenant ID", logiface.Int64("tenant_id", tenantID))
    }
    return qv
}

// 验证字符串参数
func (qv *QueryValidator) ValidateStringParam(value string, paramName string) *QueryValidator {
    if value == "" {
        qv.skipQuery = true
        qv.skipReason = "invalid " + paramName
        qv.logger.Debug(qv.ctx, "Invalid string parameter", logiface.String("param_name", paramName))
    }
    return qv
}

// 验证数组参数
func (qv *QueryValidator) ValidateArrayParam(values []interface{}, paramName string) *QueryValidator {
    if len(values) == 0 {
        qv.skipQuery = true
        qv.skipReason = "invalid " + paramName
        qv.logger.Debug(qv.ctx, "Invalid array parameter", logiface.String("param_name", paramName))
    }
    return qv
}

// 验证int64数组参数
func (qv *QueryValidator) ValidateInt64ArrayParam(values []int64, paramName string) *QueryValidator {
    if len(values) == 0 {
        qv.skipQuery = true
        qv.skipReason = "invalid " + paramName
        qv.logger.Debug(qv.ctx, "Invalid int64 array parameter", logiface.String("param_name", paramName))
    }
    return qv
}

// 验证权限
func (qv *QueryValidator) ValidatePermission(userInfo *usercontext.UserInfo, requiredTenantID int64) *QueryValidator {
    if userInfo == nil {
        qv.skipQuery = true
        qv.skipReason = "user not authenticated"
        qv.logger.Debug(qv.ctx, "User not authenticated")
        return qv
    }
    
    if requiredTenantID > 0 && requiredTenantID != userInfo.TenantID {
        qv.skipQuery = true
        qv.skipReason = "tenant_id mismatch"
        qv.logger.Debug(qv.ctx, "Tenant ID mismatch", 
            logiface.Int64("required_tenant_id", requiredTenantID),
            logiface.Int64("user_tenant_id", userInfo.TenantID))
    }
    return qv
}

// 检查是否需要跳过查询
func (qv *QueryValidator) ShouldSkip() bool {
    return qv.skipQuery
}

// 获取跳过原因
func (qv *QueryValidator) GetSkipReason() string {
    return qv.skipReason
}

func (qb *QueryBuilder[T]) WithCondition(field, operator string, value interface{}) *QueryBuilder[T] {
    qb.conditions = append(qb.conditions, Condition{
        Field: field, Operator: operator, Value: value, Logic: "AND",
    })
    return qb
}

func (qb *QueryBuilder[T]) WithOrCondition(field, operator string, value interface{}) *QueryBuilder[T] {
    qb.conditions = append(qb.conditions, Condition{
        Field: field, Operator: operator, Value: value, Logic: "OR",
    })
    return qb
}

func (qb *QueryBuilder[T]) WithInCondition(field string, values []interface{}) *QueryBuilder[T] {
    qb.conditions = append(qb.conditions, Condition{
        Field: field, Operator: "IN", Value: values, Logic: "AND",
    })
    return qb
}

func (qb *QueryBuilder[T]) WithNotInCondition(field string, values []interface{}) *QueryBuilder[T] {
    qb.conditions = append(qb.conditions, Condition{
        Field: field, Operator: "NOT IN", Value: values, Logic: "AND",
    })
    return qb
}

func (qb *QueryBuilder[T]) WithInConditionInt64(field string, values []int64) *QueryBuilder[T] {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qb.WithInCondition(field, interfaceValues)
}

func (qb *QueryBuilder[T]) WithNotInConditionInt64(field string, values []int64) *QueryBuilder[T] {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qb.WithNotInCondition(field, interfaceValues)
}

func (qb *QueryBuilder[T]) WithInConditionString(field string, values []string) *QueryBuilder[T] {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qb.WithInCondition(field, interfaceValues)
}

func (qb *QueryBuilder[T]) WithNotInConditionString(field string, values []string) *QueryBuilder[T] {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qb.WithNotInCondition(field, interfaceValues)
}

func (qb *QueryBuilder[T]) WithLikeCondition(field, pattern string) *QueryBuilder[T] {
    qb.conditions = append(qb.conditions, Condition{
        Field: field, Operator: "LIKE", Value: "%"+pattern+"%", Logic: "AND",
    })
    return qb
}

func (qb *QueryBuilder[T]) WithPagination(pageNum, pageSize int) *QueryBuilder[T] {
    if pageNum <= 0 {
        pageNum = 1
    }
    if pageSize <= 0 {
        pageSize = 10
    }
    
    qb.offset = (pageNum - 1) * pageSize
    qb.limit = pageSize
    return qb
}

func (qb *QueryBuilder[T]) WithOrder(field, direction string) *QueryBuilder[T] {
    qb.orders = append(qb.orders, OrderClause{
        Field: field, Direction: direction,
    })
    return qb
}

func (qb *QueryBuilder[T]) WithPreloads(preloads ...string) *QueryBuilder[T] {
    qb.preloads = append(qb.preloads, preloads...)
    return qb
}

func (qb *QueryBuilder[T]) WithGroupBy(fields ...string) *QueryBuilder[T] {
    qb.groupBy = append(qb.groupBy, fields...)
    return qb
}

func (qb *QueryBuilder[T]) WithHaving(conditions ...Condition) *QueryBuilder[T] {
    qb.having = append(qb.having, conditions...)
    return qb
}

func (qb *QueryBuilder[T]) WithDistinct() *QueryBuilder[T] {
    qb.distinct = true
    return qb
}

func (qb *QueryBuilder[T]) WithLock(mode string) *QueryBuilder[T] {
    qb.lockMode = mode
    return qb
}

// 执行查询
func (qb *QueryBuilder[T]) buildQuery() *gorm.DB {
    query := qb.db.WithContext(qb.ctx).Model(qb.model)
    
    // 应用条件
    for _, condition := range qb.conditions {
        if condition.Logic == "OR" {
            query = query.Or(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
        } else {
            query = query.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
        }
    }
    
    // 应用预加载
    for _, preload := range qb.preloads {
        query = query.Preload(preload)
    }
    
    // 应用排序
    for _, order := range qb.orders {
        query = query.Order(fmt.Sprintf("%s %s", order.Field, order.Direction))
    }
    
    // 应用分组
    if len(qb.groupBy) > 0 {
        query = query.Group(strings.Join(qb.groupBy, ", "))
    }
    
    // 应用 HAVING
    for _, having := range qb.having {
        query = query.Having(fmt.Sprintf("%s %s ?", having.Field, having.Operator), having.Value)
    }
    
    // 应用 DISTINCT
    if qb.distinct {
        query = query.Distinct()
    }
    
    // 应用锁
    if qb.lockMode != "" {
        query = query.Set("gorm:query_option", qb.lockMode)
    }
    
    return query
}

func (qb *QueryBuilder[T]) Find() ([]T, error) {
    // 检查是否需要跳过查询
    if qb.skipQuery {
        qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
        return []T{}, nil
    }
    
    query := qb.buildQuery()
    
    if qb.offset > 0 || qb.limit > 0 {
        query = query.Offset(qb.offset).Limit(qb.limit)
    }
    
    var results []T
    err := query.Find(&results).Error
    return results, err
}

func (qb *QueryBuilder[T]) FindOne() (*T, error) {
    // 检查是否需要跳过查询
    if qb.skipQuery {
        qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
        return nil, nil
    }
    
    query := qb.buildQuery()
    
    var result T
    err := query.First(&result).Error
    if err == gorm.ErrRecordNotFound {
        return nil, nil
    }
    return &result, err
}

func (qb *QueryBuilder[T]) Count() (int64, error) {
    // 检查是否需要跳过查询
    if qb.skipQuery {
        qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
        return 0, nil
    }
    
    query := qb.buildQuery()
    
    var count int64
    err := query.Count(&count).Error
    return count, err
}

func (qb *QueryBuilder[T]) Exists() (bool, error) {
    // 检查是否需要跳过查询
    if qb.skipQuery {
        qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
        return false, nil
    }
    
    count, err := qb.Count()
    return count > 0, err
}
```

### 2. 更新构建器 (UpdateBuilder)

```go
// 更新构建器
type UpdateBuilder[T any] struct {
    db       *gorm.DB
    ctx      context.Context
    model    T
    updates  map[string]interface{}
    conditions []Condition
    tenant   *TenantContext
}

// 更新构建器方法
func NewUpdateBuilder[T any](db *gorm.DB, ctx context.Context, model T) *UpdateBuilder[T] {
    tenantID, _ := usercontext.GetTenantID(ctx)
    internalAppID := model.GetInternalAppIDFromContext(ctx)
    
    return &UpdateBuilder[T]{
        db:      db,
        ctx:     ctx,
        model:   model,
        updates: make(map[string]interface{}),
        tenant:  &TenantContext{TenantID: tenantID, InternalAppID: internalAppID},
    }
}

func (ub *UpdateBuilder[T]) Set(field string, value interface{}) *UpdateBuilder[T] {
    ub.updates[field] = value
    return ub
}

func (ub *UpdateBuilder[T]) SetMap(updates map[string]interface{}) *UpdateBuilder[T] {
    for field, value := range updates {
        ub.updates[field] = value
    }
    return ub
}

func (ub *UpdateBuilder[T]) Increment(field string, value interface{}) *UpdateBuilder[T] {
    ub.updates[field] = gorm.Expr(fmt.Sprintf("%s + ?", field), value)
    return ub
}

func (ub *UpdateBuilder[T]) Decrement(field string, value interface{}) *UpdateBuilder[T] {
    ub.updates[field] = gorm.Expr(fmt.Sprintf("%s - ?", field), value)
    return ub
}

func (ub *UpdateBuilder[T]) Where(field, operator string, value interface{}) *UpdateBuilder[T] {
    ub.conditions = append(ub.conditions, Condition{
        Field: field, Operator: operator, Value: value, Logic: "AND",
    })
    return ub
}

func (ub *UpdateBuilder[T]) WithTenantFilter() *UpdateBuilder[T] {
    ub.conditions = append(ub.conditions, []Condition{
        {Field: "tenant_id", Operator: "=", Value: ub.tenant.TenantID, Logic: "AND"},
        {Field: "internal_app_id", Operator: "=", Value: ub.tenant.InternalAppID, Logic: "AND"},
    }...)
    return ub
}

// 执行更新
func (ub *UpdateBuilder[T]) Update() error {
    query := ub.db.WithContext(ub.ctx).Model(ub.model)
    
    // 应用条件
    for _, condition := range ub.conditions {
        query = query.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
    }
    
    // 应用更新
    return query.Updates(ub.updates).Error
}

func (ub *UpdateBuilder[T]) UpdateWithResult() (int64, error) {
    query := ub.db.WithContext(ub.ctx).Model(ub.model)
    
    // 应用条件
    for _, condition := range ub.conditions {
        query = query.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
    }
    
    // 应用更新
    result := query.Updates(ub.updates)
    return result.RowsAffected, result.Error
}
```

### 3. 批量操作 (BatchOperations)

```go
// 批量操作构建器
type BatchOperations[T any] struct {
    db     *gorm.DB
    ctx    context.Context
    logger logiface.Logger
}

func NewBatchOperations[T any](db *gorm.DB, ctx context.Context, logger logiface.Logger) *BatchOperations[T] {
    return &BatchOperations[T]{
        db:     db,
        ctx:    ctx,
        logger: logger,
    }
}

// 批量创建
func (bo *BatchOperations[T]) BatchCreate(entities []T, batchSize int) error {
    if batchSize <= 0 {
        batchSize = 1000
    }
    
    tx := bo.db.WithContext(bo.ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    for i := 0; i < len(entities); i += batchSize {
        end := i + batchSize
        if end > len(entities) {
            end = len(entities)
        }
        
        batch := entities[i:end]
        if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
            tx.Rollback()
            return fmt.Errorf("batch create failed at index %d: %w", i, err)
        }
    }
    
    return tx.Commit().Error
}

// 批量更新
func (bo *BatchOperations[T]) BatchUpdate(entities []T, batchSize int) error {
    if batchSize <= 0 {
        batchSize = 1000
    }
    
    tx := bo.db.WithContext(bo.ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    for i := 0; i < len(entities); i += batchSize {
        end := i + batchSize
        if end > len(entities) {
            end = len(entities)
        }
        
        batch := entities[i:end]
        for _, entity := range batch {
            if err := tx.Save(&entity).Error; err != nil {
                tx.Rollback()
                return fmt.Errorf("batch update failed at index %d: %w", i, err)
            }
        }
    }
    
    return tx.Commit().Error
}

// 批量删除
func (bo *BatchOperations[T]) BatchDelete(ids []int64) error {
    tx := bo.db.WithContext(bo.ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    if err := tx.Delete(new(T), ids).Error; err != nil {
        tx.Rollback()
        return fmt.Errorf("batch delete failed: %w", err)
    }
    
    return tx.Commit().Error
}

// 批量软删除
func (bo *BatchOperations[T]) BatchSoftDelete(ids []int64) error {
    tx := bo.db.WithContext(bo.ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    if err := tx.Model(new(T)).Where("id IN ?", ids).Update("deleted_at", time.Now()).Error; err != nil {
        tx.Rollback()
        return fmt.Errorf("batch soft delete failed: %w", err)
    }
    
    return tx.Commit().Error
}
```

### 4. 仓储基类 (BaseRepository)

```go
// 仓储基类
type BaseRepository[T any] struct {
    db     *gorm.DB
    logger logiface.Logger
}

func NewBaseRepository[T any](db *gorm.DB, logger logiface.Logger) *BaseRepository[T] {
    return &BaseRepository[T]{
        db:     db,
        logger: logger,
    }
}

// 基础 CRUD 操作
func (r *BaseRepository[T]) Create(ctx context.Context, entity *T) error {
    return r.db.WithContext(ctx).Create(entity).Error
}

func (r *BaseRepository[T]) CreateInBatch(ctx context.Context, entities []T, batchSize int) error {
    return NewBatchOperations[T](r.db, ctx, r.logger).BatchCreate(entities, batchSize)
}

func (r *BaseRepository[T]) Update(ctx context.Context, entity *T) error {
    return r.db.WithContext(ctx).Save(entity).Error
}

func (r *BaseRepository[T]) UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error {
    return NewUpdateBuilder[T](r.db, ctx, new(T)).
        SetMap(updates).
        WithTenantFilter().
        Where("id", "=", id).
        Update()
}

func (r *BaseRepository[T]) Delete(ctx context.Context, id int64) error {
    return r.db.WithContext(ctx).Delete(new(T), id).Error
}

func (r *BaseRepository[T]) SoftDelete(ctx context.Context, id int64) error {
    return r.db.WithContext(ctx).Model(new(T)).Where("id = ?", id).
        Update("deleted_at", time.Now()).Error
}

func (r *BaseRepository[T]) FindByID(ctx context.Context, id int64) (*T, error) {
    return NewQueryBuilder[T](r.db, ctx, new(T)).
        WithTenantFilter().
        Where("id", "=", id).
        FindOne()
}

func (r *BaseRepository[T]) FindByIDs(ctx context.Context, ids []int64) ([]T, error) {
    return NewQueryBuilder[T](r.db, ctx, new(T)).
        WithTenantFilter().
        WhereIn("id", ids).
        Find()
}

func (r *BaseRepository[T]) List(ctx context.Context, params *QueryParams) ([]T, int64, error) {
    qb := NewQueryBuilder[T](r.db, ctx, new(T)).WithTenantFilter()
    
    // 应用查询参数
    if params != nil {
        qb.ApplyParams(params)
    }
    
    // 获取总数
    total, err := qb.Count()
    if err != nil {
        return nil, 0, err
    }
    
    // 应用分页 - 优先使用 Page/Size，如果没有则使用 Offset/Limit
    if params != nil {
        if params.Page > 0 && params.Size > 0 {
            qb.WithPagination(params.Page, params.Size)
        } else if params.Offset >= 0 && params.Limit > 0 {
            pageNum := params.Offset/params.Limit + 1
            qb.WithPagination(pageNum, params.Limit)
        }
    }
    
    // 执行查询
    items, err := qb.Find()
    return items, total, err
}

func (r *BaseRepository[T]) Count(ctx context.Context, params *QueryParams) (int64, error) {
    qb := NewQueryBuilder[T](r.db, ctx, new(T)).WithTenantFilter()
    
    if params != nil {
        qb.ApplyParams(params)
    }
    
    return qb.Count()
}

func (r *BaseRepository[T]) Exists(ctx context.Context, params *QueryParams) (bool, error) {
    return NewQueryBuilder[T](r.db, ctx, new(T)).
        WithTenantFilter().
        ApplyParams(params).
        Exists()
}
```

### 5. 查询参数 (QueryParams)

```go
// 查询参数
type QueryParams struct {
    // 分页参数 - 支持两种方式
    Page     int // 页码，从 1 开始
    Size      int // 每页大小
    Offset    int // 偏移量，从 0 开始
    Limit     int // 限制数量
    
    // 排序参数
    OrderBy   string
    OrderDir  string
    
    // 预加载
    Preloads []string
    
    // 条件参数
    Conditions []Condition
    
    // 分组参数
    GroupBy []string
    Having  []Condition
    
    // 其他参数
    Distinct bool
    LockMode string
}

func (qp *QueryParams) WithPagination(pageNum, pageSize int) *QueryParams {
    if pageNum <= 0 {
        pageNum = 1
    }
    if pageSize <= 0 {
        pageSize = 10
    }
    
    qp.Page = pageNum
    qp.Size = pageSize
    qp.Offset = (pageNum - 1) * pageSize
    qp.Limit = pageSize
    return qp
}

func (qp *QueryParams) WithOffsetLimit(offset, limit int) *QueryParams {
    if offset < 0 {
        offset = 0
    }
    if limit <= 0 {
        limit = 10
    }
    
    qp.Offset = offset
    qp.Limit = limit
    qp.Page = offset/limit + 1
    qp.Size = limit
    return qp
}

func (qp *QueryParams) WithOrder(orderBy, orderDir string) *QueryParams {
    qp.OrderBy = orderBy
    qp.OrderDir = orderDir
    return qp
}

func (qp *QueryParams) WithPreloads(preloads ...string) *QueryParams {
    qp.Preloads = append(qp.Preloads, preloads...)
    return qp
}

func (qp *QueryParams) WithCondition(field, operator string, value interface{}) *QueryParams {
    qp.Conditions = append(qp.Conditions, Condition{
        Field: field, Operator: operator, Value: value, Logic: "AND",
    })
    return qp
}

func (qp *QueryParams) WithInCondition(field string, values []interface{}) *QueryParams {
    qp.Conditions = append(qp.Conditions, Condition{
        Field: field, Operator: "IN", Value: values, Logic: "AND",
    })
    return qp
}

func (qp *QueryParams) WithNotInCondition(field string, values []interface{}) *QueryParams {
    qp.Conditions = append(qp.Conditions, Condition{
        Field: field, Operator: "NOT IN", Value: values, Logic: "AND",
    })
    return qp
}

func (qp *QueryParams) WithInConditionInt64(field string, values []int64) *QueryParams {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qp.WithInCondition(field, interfaceValues)
}

func (qp *QueryParams) WithNotInConditionInt64(field string, values []int64) *QueryParams {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qp.WithNotInCondition(field, interfaceValues)
}

func (qp *QueryParams) WithInConditionString(field string, values []string) *QueryParams {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qp.WithInCondition(field, interfaceValues)
}

func (qp *QueryParams) WithNotInConditionString(field string, values []string) *QueryParams {
    interfaceValues := make([]interface{}, len(values))
    for i, v := range values {
        interfaceValues[i] = v
    }
    return qp.WithNotInCondition(field, interfaceValues)
}

func (qp *QueryParams) WithGroupBy(fields ...string) *QueryParams {
    qp.GroupBy = append(qp.GroupBy, fields...)
    return qp
}

func (qp *QueryParams) WithDistinct() *QueryParams {
    qp.Distinct = true
    return qp
}

func (qp *QueryParams) WithLock(mode string) *QueryParams {
    qp.LockMode = mode
    return qp
}
```

## 📊 使用示例

### 1. 用户仓储实现

```go
// 用户仓储实现
type UserRepositoryImpl struct {
    *BaseRepository[entity.User]
}

func NewUserRepository(db *gorm.DB, logger logiface.Logger) repository.UserRepository {
    return &UserRepositoryImpl{
        BaseRepository: NewBaseRepository[entity.User](db, logger),
    }
}

// 自定义查询方法
func (r *UserRepositoryImpl) FindByUsername(ctx context.Context, username string) (*entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        ValidateStringParam(username, "username").
        WithCondition("username", "=", username).
        FindOne()
}

func (r *UserRepositoryImpl) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        ValidateStringParam(email, "email").
        WithCondition("email", "=", email).
        FindOne()
}

func (r *UserRepositoryImpl) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("phone", "=", phone).
        FindOne()
}

func (r *UserRepositoryImpl) ExistsByUsername(ctx context.Context, username string) (bool, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("username", "=", username).
        Exists()
}

func (r *UserRepositoryImpl) ExistsByEmail(ctx context.Context, email string) (bool, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("email", "=", email).
        Exists()
}

func (r *UserRepositoryImpl) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("phone", "=", phone).
        Exists()
}

// 复杂查询示例
func (r *UserRepositoryImpl) FindUsersByDepartment(ctx context.Context, departmentID int64, params *QueryParams) ([]entity.User, int64, error) {
    qb := NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("department_id", "=", departmentID).
        WithPreloads("Department", "Position", "Roles")
    
    if params != nil {
        qb.ApplyParams(params)
    }
    
    total, err := qb.Count()
    if err != nil {
        return nil, 0, err
    }
    
    if params != nil && (params.Offset > 0 || params.Limit > 0) {
        pageNum := params.Offset/params.Limit + 1
        qb.WithPagination(pageNum, params.Limit)
    }
    
    users, err := qb.Find()
    return users, total, err
}

// IN 查询示例
func (r *UserRepositoryImpl) FindUsersByIDs(ctx context.Context, userIDs []int64) ([]entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        ValidateInt64ArrayParam(userIDs, "user_ids").
        WithInConditionInt64("id", userIDs).
        WithPreloads("Department", "Position", "Roles").
        Find()
}

func (r *UserRepositoryImpl) FindUsersByStatuses(ctx context.Context, statuses []value_object.UserStatus) ([]entity.User, error) {
    interfaceStatuses := make([]interface{}, len(statuses))
    for i, status := range statuses {
        interfaceStatuses[i] = status
    }
    
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithInCondition("status", interfaceStatuses).
        WithPreloads("Department", "Position").
        Find()
}

func (r *UserRepositoryImpl) FindUsersByUsernames(ctx context.Context, usernames []string) ([]entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        WithInConditionString("username", usernames).
        Find()
}

func (r *UserRepositoryImpl) FindUsersNotInDepartment(ctx context.Context, departmentIDs []int64) ([]entity.User, error) {
    return NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter().
        ValidateInt64ArrayParam(departmentIDs, "department_ids").
        WithNotInConditionInt64("department_id", departmentIDs).
        WithPreloads("Department").
        Find()
}

// 复杂的手动标记示例
func (r *UserRepositoryImpl) FindUsersByComplexCriteria(ctx context.Context, req *dto.ComplexUserQueryRequest) ([]entity.User, error) {
    qb := NewQueryBuilder[entity.User](r.db, ctx, new(entity.User)).
        WithTenantFilter()
    
    // 检查用户ID有效性
    if req.UserID > 0 {
        qb.ValidateUserID(req.UserID)
    }
    
    // 检查应用ID有效性
    if req.AppID > 0 {
        qb.ValidateAppID(req.AppID)
    }
    
    // 检查租户ID有效性
    if req.TenantID > 0 {
        qb.ValidateTenantID(req.TenantID)
    }
    
    // 检查用户名有效性
    if req.Username != "" {
        qb.ValidateStringParam(req.Username, "username")
    }
    
    // 检查邮箱有效性
    if req.Email != "" {
        qb.ValidateStringParam(req.Email, "email")
    }
    
    // 检查部门ID数组有效性
    if len(req.DepartmentIDs) > 0 {
        qb.ValidateInt64ArrayParam(req.DepartmentIDs, "department_ids")
    }
    
    // 检查状态数组有效性
    if len(req.Statuses) > 0 {
        interfaceStatuses := make([]interface{}, len(req.Statuses))
        for i, status := range req.Statuses {
            interfaceStatuses[i] = status
        }
        qb.ValidateArrayParam(interfaceStatuses, "statuses")
    }
    
    // 如果所有参数都无效，手动标记跳过查询
    if req.UserID <= 0 && req.AppID <= 0 && req.TenantID <= 0 && 
       req.Username == "" && req.Email == "" && 
       len(req.DepartmentIDs) == 0 && len(req.Statuses) == 0 {
        qb.SkipQuery("no valid criteria provided")
    }
    
    // 应用查询条件
    if req.UserID > 0 {
        qb.WithCondition("id", "=", req.UserID)
    }
    
    if req.AppID > 0 {
        qb.WithCondition("app_id", "=", req.AppID)
    }
    
    if req.TenantID > 0 {
        qb.WithCondition("tenant_id", "=", req.TenantID)
    }
    
    if req.Username != "" {
        qb.WithCondition("username", "=", req.Username)
    }
    
    if req.Email != "" {
        qb.WithCondition("email", "=", req.Email)
    }
    
    if len(req.DepartmentIDs) > 0 {
        qb.WithInConditionInt64("department_id", req.DepartmentIDs)
    }
    
    if len(req.Statuses) > 0 {
        interfaceStatuses := make([]interface{}, len(req.Statuses))
        for i, status := range req.Statuses {
            interfaceStatuses[i] = status
        }
        qb.WithInCondition("status", interfaceStatuses)
    }
    
    qb.WithPreloads("Department", "Position", "Roles")
    
    return qb.Find()
}

// 批量操作示例
func (r *UserRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []int64, status value_object.UserStatus) error {
    return NewUpdateBuilder[entity.User](r.db, ctx, new(entity.User)).
        Set("status", status).
        Set("updated_at", time.Now()).
        WithTenantFilter().
        WhereIn("id", ids).
        Update()
}

func (r *UserRepositoryImpl) BatchAssignDepartment(ctx context.Context, ids []int64, departmentID int64) error {
    return NewUpdateBuilder[entity.User](r.db, ctx, new(entity.User)).
        Set("department_id", departmentID).
        Set("updated_at", time.Now()).
        WithTenantFilter().
        WhereIn("id", ids).
        Update()
}
```

### 2. 应用服务使用示例

```go
// 应用服务中的使用
func (s *UserApplicationService) ListUsers(ctx context.Context, req *dto.ListUsersRequest) (*dto.ListUsersResponse, error) {
    // 构建查询参数
    params := &QueryParams{}.
        WithPagination(req.Page, req.Size).
        WithOrder(req.OrderBy, req.OrderDir).
        WithPreloads("Department", "Position", "Roles")
    
    // 添加搜索条件
    if req.Keyword != "" {
        params.WithCondition("username", "LIKE", req.Keyword).
            WithOrCondition("real_name", "LIKE", req.Keyword).
            WithOrCondition("email", "LIKE", req.Keyword)
    }
    
    // 添加状态过滤
    if req.Status != "" {
        params.WithCondition("status", "=", req.Status)
    }
    
    // 添加部门过滤
    if req.DepartmentID > 0 {
        params.WithCondition("department_id", "=", req.DepartmentID)
    }
    
    // 执行查询
    users, total, err := s.userRepo.List(ctx, params)
    if err != nil {
        return nil, err
    }
    
    // 转换为响应
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return &dto.ListUsersResponse{
        Users: responses,
        Total: total,
        Page:  req.Page,
        Size:  req.Size,
    }, nil
}

// 使用 QueryParams 进行 IN 查询的示例
func (s *UserApplicationService) ListUsersByMultipleCriteria(ctx context.Context, req *dto.ListUsersByCriteriaRequest) (*dto.ListUsersResponse, error) {
    params := &QueryParams{}.
        WithPagination(req.Page, req.Size).
        WithOrder(req.OrderBy, req.OrderDir).
        WithPreloads("Department", "Position", "Roles")
    
    // 添加多个部门过滤 (IN 查询)
    if len(req.DepartmentIDs) > 0 {
        params.WithInConditionInt64("department_id", req.DepartmentIDs)
    }
    
    // 添加多个状态过滤 (IN 查询)
    if len(req.Statuses) > 0 {
        interfaceStatuses := make([]interface{}, len(req.Statuses))
        for i, status := range req.Statuses {
            interfaceStatuses[i] = status
        }
        params.WithInCondition("status", interfaceStatuses)
    }
    
    // 排除某些用户 (NOT IN 查询)
    if len(req.ExcludeUserIDs) > 0 {
        params.WithNotInConditionInt64("id", req.ExcludeUserIDs)
    }
    
    // 添加搜索条件
    if req.Keyword != "" {
        params.WithCondition("username", "LIKE", req.Keyword).
            WithOrCondition("real_name", "LIKE", req.Keyword).
            WithOrCondition("email", "LIKE", req.Keyword)
    }
    
    // 执行查询
    users, total, err := s.userRepo.List(ctx, params)
    if err != nil {
        return nil, err
    }
    
    // 转换为响应
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return &dto.ListUsersResponse{
        Users: responses,
        Total: total,
        Page:  req.Page,
        Size:  req.Size,
    }, nil
}

// 分页查询示例 - 直接使用 pageNum 和 pageSize
func (s *UserApplicationService) ListUsersWithPagination(ctx context.Context, pageNum, pageSize int, keyword string) (*dto.ListUsersResponse, error) {
    qb := NewQueryBuilder[entity.User](s.userRepo.(*UserRepositoryImpl).db, ctx, new(entity.User)).
        WithTenantFilter().
        WithPagination(pageNum, pageSize).
        WithOrder("created_at", "DESC").
        WithPreloads("Department", "Position")
    
    // 添加搜索条件
    if keyword != "" {
        qb.WithLikeCondition("username", keyword).
            WithOrCondition("real_name", "LIKE", "%"+keyword+"%").
            WithOrCondition("email", "LIKE", "%"+keyword+"%")
    }
    
    // 获取总数
    total, err := qb.Count()
    if err != nil {
        return nil, err
    }
    
    // 执行查询
    users, err := qb.Find()
    if err != nil {
        return nil, err
    }
    
    // 转换为响应
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return &dto.ListUsersResponse{
        Users: responses,
        Total: total,
        Page:  pageNum,
        Size:  pageSize,
    }, nil
}

// 分页查询示例 - 使用 QueryParams
func (s *UserApplicationService) ListUsersWithQueryParams(ctx context.Context, req *dto.ListUsersRequest) (*dto.ListUsersResponse, error) {
    // 方式1: 使用 Page/Size
    params := &QueryParams{}.
        WithPagination(req.Page, req.Size).
        WithOrder(req.OrderBy, req.OrderDir).
        WithPreloads("Department", "Position", "Roles")
    
    // 方式2: 使用 Offset/Limit (如果需要)
    // params := &QueryParams{}.
    //     WithOffsetLimit(req.Offset, req.Limit).
    //     WithOrder(req.OrderBy, req.OrderDir).
    //     WithPreloads("Department", "Position", "Roles")
    
    // 添加搜索条件
    if req.Keyword != "" {
        params.WithCondition("username", "LIKE", "%"+req.Keyword+"%").
            WithOrCondition("real_name", "LIKE", "%"+req.Keyword+"%").
            WithOrCondition("email", "LIKE", "%"+req.Keyword+"%")
    }
    
    // 添加状态过滤
    if req.Status != "" {
        params.WithCondition("status", "=", req.Status)
    }
    
    // 添加部门过滤
    if req.DepartmentID > 0 {
        params.WithCondition("department_id", "=", req.DepartmentID)
    }
    
    // 执行查询
    users, total, err := s.userRepo.List(ctx, params)
    if err != nil {
        return nil, err
    }
    
    // 转换为响应
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return &dto.ListUsersResponse{
        Users: responses,
        Total: total,
        Page:  req.Page,
        Size:  req.Size,
    }, nil
}

func (s *UserApplicationService) UpdateUser(ctx context.Context, id int64, req *dto.UpdateUserRequest) (*entity.User, error) {
    // 构建更新参数
    updates := make(map[string]interface{})
    
    if req.RealName != "" {
        updates["real_name"] = req.RealName
    }
    if req.Email != "" {
        updates["email"] = req.Email
    }
    if req.Phone != "" {
        updates["phone"] = req.Phone
    }
    if req.Status != "" {
        updates["status"] = req.Status
    }
    if req.DepartmentID > 0 {
        updates["department_id"] = req.DepartmentID
    }
    if req.PositionID > 0 {
        updates["position_id"] = req.PositionID
    }
    
    updates["updated_at"] = time.Now()
    
    // 执行更新
    err := s.userRepo.UpdateByID(ctx, id, updates)
    if err != nil {
        return nil, err
    }
    
    // 返回更新后的用户
    return s.userRepo.FindByID(ctx, id)
}

// IN 查询在应用服务中的使用示例
func (s *UserApplicationService) GetUsersByIDs(ctx context.Context, userIDs []int64) ([]*dto.UserResponse, error) {
    // 应用层也可以进行参数验证
    if len(userIDs) == 0 {
        s.logger.Debug(ctx, "Empty user IDs, returning empty result")
        return []*dto.UserResponse{}, nil
    }
    
    users, err := s.userRepo.FindUsersByIDs(ctx, userIDs)
    if err != nil {
        return nil, err
    }
    
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return responses, nil
}

// 应用服务层的手动标记示例
func (s *UserApplicationService) GetUsersByComplexCriteria(ctx context.Context, req *dto.ComplexUserQueryRequest) ([]*dto.UserResponse, error) {
    // 使用 QueryValidator 进行参数验证
    validator := NewQueryValidator(ctx, s.logger)
    
    // 验证请求对象
    if req == nil {
        s.logger.Debug(ctx, "Nil request, returning empty result")
        return []*dto.UserResponse{}, nil
    }
    
    // 验证关键参数
    validator.ValidateUserID(req.UserID).
        ValidateAppID(req.AppID).
        ValidateTenantID(req.TenantID).
        ValidateStringParam(req.Username, "username").
        ValidateStringParam(req.Email, "email").
        ValidateInt64ArrayParam(req.DepartmentIDs, "department_ids")
    
    // 检查用户权限
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        s.logger.Warn(ctx, "User not authenticated", logiface.Error(err))
        return []*dto.UserResponse{}, nil
    }
    
    // 验证权限
    validator.ValidatePermission(userInfo, req.TenantID)
    
    // 如果所有参数都无效，手动标记跳过查询
    if req.UserID <= 0 && req.AppID <= 0 && req.TenantID <= 0 && 
       req.Username == "" && req.Email == "" && 
       len(req.DepartmentIDs) == 0 && len(req.Statuses) == 0 {
        validator.skipQuery = true
        validator.skipReason = "no valid criteria provided"
    }
    
    // 检查是否需要跳过查询
    if validator.ShouldSkip() {
        s.logger.Debug(ctx, "Skipping query", logiface.String("reason", validator.GetSkipReason()))
        return []*dto.UserResponse{}, nil
    }
    
    users, err := s.userRepo.FindUsersByComplexCriteria(ctx, req)
    if err != nil {
        return nil, err
    }
    
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return responses, nil
}

// 使用 QueryValidator 的简化示例
func (s *UserApplicationService) GetUserByIDWithValidation(ctx context.Context, userID int64) (*dto.UserResponse, error) {
    validator := NewQueryValidator(ctx, s.logger)
    
    // 验证用户ID
    validator.ValidateUserID(userID)
    
    // 检查用户权限
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        s.logger.Warn(ctx, "User not authenticated", logiface.Error(err))
        return nil, nil
    }
    
    // 验证权限
    validator.ValidatePermission(userInfo, 0) // 0 表示不检查特定租户
    
    // 检查是否需要跳过查询
    if validator.ShouldSkip() {
        s.logger.Debug(ctx, "Skipping query", logiface.String("reason", validator.GetSkipReason()))
        return nil, nil
    }
    
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    if user == nil {
        return nil, nil
    }
    
    return s.toUserResponse(user), nil
}

func (s *UserApplicationService) GetUsersByStatuses(ctx context.Context, statuses []string) ([]*dto.UserResponse, error) {
    // 转换状态字符串为枚举值
    userStatuses := make([]value_object.UserStatus, len(statuses))
    for i, status := range statuses {
        userStatuses[i] = value_object.UserStatus(status)
    }
    
    users, err := s.userRepo.FindUsersByStatuses(ctx, userStatuses)
    if err != nil {
        return nil, err
    }
    
    responses := make([]*dto.UserResponse, len(users))
    for i, user := range users {
        responses[i] = s.toUserResponse(&user)
    }
    
    return responses, nil
}

func (s *UserApplicationService) BatchUpdateUserStatus(ctx context.Context, userIDs []int64, status value_object.UserStatus) error {
    return s.userRepo.BatchUpdateStatus(ctx, userIDs, status)
}

func (s *UserApplicationService) BatchAssignUsersToDepartment(ctx context.Context, userIDs []int64, departmentID int64) error {
    return s.userRepo.BatchAssignDepartment(ctx, userIDs, departmentID)
}
```

## 🚀 性能优化

### 1. 查询优化

```go
// 查询缓存
type QueryCache struct {
    cache map[string]interface{}
    mutex sync.RWMutex
    ttl   time.Duration
}

func (qc *QueryCache) Get(key string) (interface{}, bool) {
    qc.mutex.RLock()
    defer qc.mutex.RUnlock()
    
    if item, exists := qc.cache[key]; exists {
        return item, true
    }
    return nil, false
}

func (qc *QueryCache) Set(key string, value interface{}) {
    qc.mutex.Lock()
    defer qc.mutex.Unlock()
    
    qc.cache[key] = value
    
    // 设置过期时间
    time.AfterFunc(qc.ttl, func() {
        qc.mutex.Lock()
        delete(qc.cache, key)
        qc.mutex.Unlock()
    })
}

// 查询构建器缓存支持
func (qb *QueryBuilder[T]) WithCache(cache *QueryCache, key string) *QueryBuilder[T] {
    if cached, exists := cache.Get(key); exists {
        // 返回缓存结果
        return cached.(*QueryBuilder[T])
    }
    
    // 执行查询并缓存结果
    result := qb.Find()
    cache.Set(key, result)
    return result
}
```

### 2. 批量操作优化

```go
// 批量操作优化
func (bo *BatchOperations[T]) BatchUpsert(entities []T, batchSize int) error {
    if batchSize <= 0 {
        batchSize = 1000
    }
    
    tx := bo.db.WithContext(bo.ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    for i := 0; i < len(entities); i += batchSize {
        end := i + batchSize
        if end > len(entities) {
            end = len(entities)
        }
        
        batch := entities[i:end]
        for _, entity := range batch {
            // 使用 ON DUPLICATE KEY UPDATE
            if err := tx.Clauses(clause.OnConflict{
                UpdateAll: true,
            }).Create(&entity).Error; err != nil {
                tx.Rollback()
                return fmt.Errorf("batch upsert failed at index %d: %w", i, err)
            }
        }
    }
    
    return tx.Commit().Error
}
```

## 📈 监控和日志

### 1. 查询性能监控

```go
// 查询性能监控
type QueryMetrics struct {
    Duration    time.Duration
    RowsAffected int64
    Error       error
    Query       string
    Params      []interface{}
}

func (qb *QueryBuilder[T]) FindWithMetrics() ([]T, *QueryMetrics, error) {
    start := time.Now()
    
    query := qb.buildQuery()
    var results []T
    
    err := query.Find(&results).Error
    
    metrics := &QueryMetrics{
        Duration: time.Since(start),
        Error:    err,
        Query:    query.ToSQL(func(tx *gorm.DB) *gorm.DB {
            return tx.Find(&results)
        }),
    }
    
    return results, metrics, err
}
```

### 2. 慢查询日志

```go
// 慢查询日志
func (qb *QueryBuilder[T]) FindWithSlowQueryLog(threshold time.Duration) ([]T, error) {
    results, metrics, err := qb.FindWithMetrics()
    
    if metrics.Duration > threshold {
        qb.logger.Warn(qb.ctx, "Slow query detected",
            logiface.Duration("duration", metrics.Duration),
            logiface.String("query", metrics.Query),
            logiface.Any("params", metrics.Params),
        )
    }
    
    return results, err
}
```

## 🧪 测试策略

### 1. 单元测试

```go
// 查询构建器测试
func TestQueryBuilder_Find(t *testing.T) {
    db := setupTestDB()
    ctx := context.Background()
    
    // 创建测试数据
    user := &entity.User{
        Username: "test_user",
        Email:    "<EMAIL>",
        TenantID: 1,
    }
    db.Create(user)
    
    // 测试查询
    qb := NewQueryBuilder[entity.User](db, ctx, new(entity.User)).
        WithTenantFilter().
        WithCondition("username", "=", "test_user")
    
    result, err := qb.FindOne()
    
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, "test_user", result.Username)
}
```

### 2. 性能测试

```go
// 性能基准测试
func BenchmarkQueryBuilder_Find(b *testing.B) {
    db := setupTestDB()
    ctx := context.Background()
    
    // 创建测试数据
    for i := 0; i < 1000; i++ {
        user := &entity.User{
            Username: fmt.Sprintf("user_%d", i),
            Email:    fmt.Sprintf("<EMAIL>", i),
            TenantID: 1,
        }
        db.Create(user)
    }
    
    b.ResetTimer()
    
    for i := 0; i < b.N; i++ {
        qb := NewQueryBuilder[entity.User](db, ctx, new(entity.User)).
            WithTenantFilter().
            WithPagination(0, 100)
        
        _, err := qb.Find()
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## 📋 实施计划

### 阶段 1: 基础架构 (1-2 周)
- [ ] 实现 QueryBuilder 核心功能
- [ ] 实现 UpdateBuilder 核心功能
- [ ] 实现 BatchOperations 核心功能
- [ ] 实现 BaseRepository 基类

### 阶段 2: 仓储重构 (2-3 周)
- [ ] 重构 UserRepository 实现
- [ ] 重构 DepartmentRepository 实现
- [ ] 重构 RoleRepository 实现
- [ ] 重构 PermissionRepository 实现

### 阶段 3: 性能优化 (1-2 周)
- [ ] 实现查询缓存
- [ ] 优化批量操作
- [ ] 添加性能监控
- [ ] 实现慢查询日志

### 阶段 4: 测试和文档 (1 周)
- [ ] 编写单元测试
- [ ] 编写性能测试
- [ ] 完善技术文档
- [ ] 进行代码审查

## 🎯 预期收益

### 1. 开发效率提升
- 减少重复代码 70%
- 提高查询构建效率 50%
- 简化复杂查询实现

### 2. 性能提升
- 查询性能提升 30%
- 批量操作性能提升 50%
- 内存使用优化 20%

### 3. 可维护性提升
- 代码复用率提升 80%
- 测试覆盖率提升 90%
- 错误率降低 60%

### 4. 扩展性提升
- 支持新的查询模式
- 支持新的更新模式
- 支持新的批量操作

这个技术方案提供了一个完整、高效、可维护的仓储层设计模式，能够显著提升系统的开发效率和运行性能。
