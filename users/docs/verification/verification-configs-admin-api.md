# 验证配置管理接口文档

> **本文件为管理后台接口文档，所有接口遵循统一 HTTP API 设计规范**
> **注意：此文档已从策略管理重构为统一的配置管理系统**

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 配置结构说明](#2-配置结构说明)
- [3. 配置管理接口](#3-配置管理接口)
  - [3.1 配置列表（GET）](#31-配置列表get)
  - [3.2 有效配置查询（GET）](#32-有效配置查询get)
  - [3.3 创建静态配置（POST）](#33-创建静态配置post)
  - [3.4 创建动态配置（POST）](#34-创建动态配置post)
  - [3.5 更新静态配置（PUT）](#35-更新静态配置put)
  - [3.6 更新动态配置（PUT）](#36-更新动态配置put)
  - [3.7 删除配置（DELETE）](#37-删除配置delete)
  - [3.8 复制系统配置（POST）](#38-复制系统配置post)
  - [3.9 表达式校验与测试（POST）](#39-表达式校验与测试post)
- [4. 验证操作接口](#4-验证操作接口)
- [5. 错误码](#5-错误码)
- [6. 使用建议与安全性](#6-使用建议与安全性)

---

## 1. 系统概述

验证配置管理系统支持双模式配置：
- **静态配置**: 基于验证用途（purpose）和目标类型（target_type）的固定配置
- **动态配置**: 基于业务场景（business_scene）和条件表达式的智能配置

系统实现智能配置查找：动态配置 → 静态配置 → 系统默认配置

---

## 2. 配置结构说明

### 2.1 基础配置字段

| 字段                    | 类型     | 说明                                   |
|------------------------|----------|----------------------------------------|
| id                     | bigint   | 配置ID                                 |
| tenant_id              | bigint   | 租户ID                                 |
| config_mode            | string   | 配置模式：static/dynamic               |
| target_type            | int      | 目标类型（1=邮箱，2=短信，3=MFA）        |
| token_type             | int      | 令牌类型（1=链接，2=验证码）            |
| token_length           | int      | 令牌长度                               |
| expire_minutes         | int      | 有效期（分钟）                         |
| max_attempts           | int      | 最大尝试次数                           |
| rate_limit_per_minute  | int      | 每分钟限制                             |
| rate_limit_per_hour    | int      | 每小时限制                             |
| rate_limit_per_day     | int      | 每天限制                               |
| template_code          | string   | 模板代码                               |
| priority               | int      | 优先级，数值越小优先级越高              |
| description            | string   | 配置描述                               |
| is_active              | bool     | 是否启用                               |
| created_at             | string   | 创建时间                               |
| updated_at             | string   | 更新时间                               |

### 2.2 静态配置特有字段

| 字段           | 类型     | 说明                                   |
|----------------|----------|----------------------------------------|
| purpose        | int      | 验证用途（1=注册，2=登录，3=重置密码等） |
| purpose_name   | string   | 用途名称                               |

### 2.3 动态配置特有字段

| 字段                    | 类型     | 说明                                   |
|------------------------|----------|----------------------------------------|
| business_scene         | string   | 业务场景（如 login、register 等）       |
| judgment_dimension     | string   | 判定维度（如 ip、user、device）         |
| condition_expr         | string   | 判定条件表达式                         |
| verification_level     | int      | 验证级别（1-5）                        |
| require_verification   | bool     | 是否需要验证                           |

---

## 3. 配置管理接口

### 3.1 配置列表（GET）

```http
GET /api/users/verification/configs
```

**查询参数:**
- `config_mode` (string, 可选): 配置模式 static/dynamic
- `purpose` (int, 可选): 验证用途（静态配置）
- `business_scene` (string, 可选): 业务场景（动态配置）
- `target_type` (int, 可选): 目标类型
- `is_active` (bool, 可选): 是否启用
- `page` (int, 可选): 页码，默认1
- `size` (int, 可选): 页大小，默认20
- `order_by` (string, 可选): 排序字段，默认created_at
- `order_desc` (bool, 可选): 是否降序，默认true

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "config_mode": "static",
        "purpose": 1,
        "purpose_name": "注册验证",
        "target_type": 1,
        "token_type": 2,
        "is_active": true
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20,
    "total_pages": 1
  }
}
```

### 3.2 有效配置查询（GET）

```http
GET /api/users/verification/configs/effective
```

**查询参数:**
- `purpose` (int, 必填): 验证用途
- `target_type` (int, 必填): 目标类型  
- `business_scene` (string, 可选): 业务场景

**响应示例:**
```json
{
  "code": 0,
  "message": "success", 
  "data": {
    "id": 1,
    "config_mode": "static",
    "purpose": 1,
    "target_type": 1,
    "token_type": 2,
    "token_length": 6,
    "expire_minutes": 5,
    "max_attempts": 3,
    "template_code": "default_template",
    "is_active": true
  }
}
```

### 3.3 创建静态配置（POST）

```http
POST /api/users/verification/configs/static
```

**请求体:**
```json
{
  "purpose": 1,
  "target_type": 1,
  "token_type": 2,
  "token_length": 6,
  "expire_minutes": 5,
  "max_attempts": 3,
  "rate_limit_per_minute": 1,
  "rate_limit_per_hour": 10,
  "rate_limit_per_day": 100,
  "template_code": "default_template",
  "priority": 10,
  "description": "注册邮箱验证配置"
}
```

### 3.4 创建动态配置（POST）

```http
POST /api/users/verification/configs/dynamic
```

**请求体:**
```json
{
  "business_scene": "login",
  "target_type": 1,
  "judgment_dimension": "ip",
  "condition_expr": "fail_count > 5",
  "token_type": 2,
  "token_length": 6,
  "expire_minutes": 5,
  "max_attempts": 3,
  "rate_limit_per_minute": 1,
  "rate_limit_per_hour": 10,
  "rate_limit_per_day": 100,
  "template_code": "security_template",
  "priority": 5,
  "verification_level": 3,
  "require_verification": true,
  "description": "登录安全验证配置"
}
```

### 3.5 更新静态配置（PUT）

```http
PUT /api/users/verification/configs/static/{id}
```

### 3.6 更新动态配置（PUT）

```http
PUT /api/users/verification/configs/dynamic/{id}
```

### 3.7 删除配置（DELETE）

```http
DELETE /api/users/verification/configs/static/{id}
DELETE /api/users/verification/configs/dynamic/{id}
```

### 3.8 复制系统配置（POST）

```http
POST /api/users/verification/configs/copy
```

**请求体:**
```json
{
  "source_tenant_id": 0,
  "target_tenant_id": 1001,
  "config_types": ["static", "dynamic"],
  "overwrite": false
}
```

### 3.9 表达式校验与测试（POST）

```http
POST /api/users/verification/configs/validate-expr
```

**请求体:**
```json
{
  "condition_expr": "fail_count > 5 && is_new_device == true"
}
```

---

## 4. 验证操作接口

### 4.1 发送验证（POST）

```http
POST /api/users/verification/send
```

### 4.2 验证令牌（POST）

```http
POST /api/users/verification/verify
```

### 4.3 重新发送验证（POST）

```http
POST /api/users/verification/resend
```

### 4.4 检查令牌状态（GET）

```http
GET /api/users/verification/status
```

### 4.5 获取验证统计（GET）

```http
GET /api/users/verification/statistics
```

---

## 5. 错误码

| 错误码 | 说明                     |
|--------|--------------------------|
| 100200 | 验证配置不存在           |
| 100201 | 验证配置已禁用           |
| 100203 | 验证配置无效             |
| 100204 | 验证配置已存在           |
| 100604 | 表达式语法错误           |
| 100605 | 表达式测试失败           |

---

## 6. 使用建议与安全性

### 6.1 配置优先级
1. 动态配置优先级高于静态配置
2. 租户配置优先级高于系统默认配置
3. 同类配置按priority字段排序

### 6.2 安全建议
- 动态配置的condition_expr应经过严格校验
- 合理设置rate_limit防止滥用
- 定期审查配置的有效性和安全性

### 6.3 最佳实践
- 优先使用静态配置满足常规需求
- 仅在需要智能判断时使用动态配置
- 保持配置的简洁性和可维护性