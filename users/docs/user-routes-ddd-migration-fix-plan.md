# 用户路由DDD迁移修复方案

## 修复目标

1. 使用internalAppId进行应用隔离，tenantId仅用于数据归属
2. 统一错误处理机制
3. 提取公共方法，遵循DRY原则
4. 添加必要的注释
5. 移除向后兼容性代码

## internalAppId和tenantId使用规范

### 使用原则
- **internalAppId**: 应用隔离的关键字段，用于业务逻辑过滤，确保数据隔离和权限控制
- **tenantId**: 用于数据归属，确保数据归属正确
- **应用隔离优先**: internalAppId是应用隔离的核心，必须优先使用

### 具体应用场景
1. **用户创建**: 使用internalAppId进行应用隔离和唯一性验证，使用tenantId进行数据持久化
2. **用户查询**: 使用internalAppId进行应用隔离，使用tenantId进行数据归属
3. **权限验证**: 使用internalAppId进行业务权限判断和应用隔离
4. **数据隔离**: internalAppId是应用隔离的关键，tenantId确保数据归属正确

## 修复步骤

### 步骤1：重构用户上下文获取方法

**文件**: `users/internal/interfaces/http/handlers/common.go`

```go
func getTenantIDFromContext(c *gin.Context) int64 {
    if tenantID, exists := usercontext.GetTenantID(c.Request.Context()); exists {
        return tenantID
    }
    // TODO: 不应该使用默认值，应该在中间件中确保存在
    return 0
}

func getUserIDFromContext(c *gin.Context) int64 {
    if userID, exists := usercontext.GetUserID(c.Request.Context()); exists {
        return userID
    }
    // TODO: 不应该使用默认值，应该在中间件中确保存在
    return 0
}
```

### 步骤2：重构用户处理器

**文件**: `users/internal/interfaces/http/handlers/user_handler.go`

```go
// CreateUser 创建用户 - 使用internalAppId进行业务逻辑
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req dto.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        h.logger.Warn(c.Request.Context(), "create user request binding failed",
            logiface.Error(err),
            logiface.String("ip", c.ClientIP()),
        )
        commonResponse.GinValidationError(c, err)
        return
    }

    // 从上下文获取internalAppId用于应用隔离，tenantId用于数据归属
    internalAppID := getInternalAppIDFromContext(c)
    tenantID := getTenantIDFromContext(c)
    
    // 设置数据归属需要的tenantId
    req.TenantID = tenantID

    h.logger.Info(c.Request.Context(), "create user attempt",
        logiface.String("username", req.Username),
        logiface.String("email", req.Email),
        logiface.Int64("internal_app_id", internalAppID),
        logiface.Int64("tenant_id", tenantID),
        logiface.String("ip", c.ClientIP()),
    )

    // 使用internalAppId进行业务逻辑处理
    user, err := h.userService.CreateUserWithAppID(c.Request.Context(), &req, internalAppID)
    if err != nil {
        h.logger.Error(c.Request.Context(), "create user failed",
            logiface.Error(err),
            logiface.String("username", req.Username),
            logiface.String("email", req.Email),
            logiface.Int64("internal_app_id", internalAppID),
            logiface.String("ip", c.ClientIP()),
        )
        // 使用统一错误处理，不传递错误细节
        HandleUserError(c, err)
        return
    }

    h.logger.Info(c.Request.Context(), "user created successfully",
        logiface.String("username", req.Username),
        logiface.String("email", req.Email),
        logiface.Int64("user_id", user.ID),
        logiface.Int64("internal_app_id", internalAppID),
        logiface.String("ip", c.ClientIP()),
    )

    commonResponse.Created(c, user)
}

// ListUsers 获取用户列表 - 使用internalAppId进行数据过滤
func (h *UserHandler) ListUsers(c *gin.Context) {
    var reqBody dto.ListUsersRequestBody

    if err := c.ShouldBindJSON(&reqBody); err != nil {
        commonResponse.BadRequest(c, "Invalid request body: "+err.Error())
        return
    }

    // 从上下文获取internalAppId用于应用隔离
    internalAppID := getInternalAppIDFromContext(c)
    tenantID := getTenantIDFromContext(c)

    // 验证internalAppId和tenantId的有效性
    if err := h.userService.ValidateAppAndTenantIDs(internalAppID, tenantID); err != nil {
        HandleUserError(c, err)
        return
    }

    // 转换为内部请求格式，使用internalAppId进行应用隔离
    req := reqBody.ToListUsersRequestWithAppID(internalAppID, tenantID)

    users, err := h.userService.ListUsersWithAppID(c.Request.Context(), req)
    if err != nil {
        // 使用统一错误处理
        HandleUserError(c, err)
        return
    }

    userList := users.Users
    if userList == nil {
        userList = make([]*dto.UserResponse, 0)
    }

    commonResponse.Paginated(c, userList, req.Offset/req.Limit+1, req.Limit, users.Total)
}
```

### 步骤3：重构用户应用服务

**文件**: `users/internal/application/user/service/user_application_service.go`

```go
// CreateUserWithAppID 创建用户 - 使用internalAppId进行应用隔离
func (s *UserApplicationService) CreateUserWithAppID(ctx context.Context, req *dto.CreateUserRequest, internalAppID int64) (*userEntity.User, error) {
    s.logger.Info(ctx, "Creating user with app ID for isolation", 
        logiface.Any("req", req),
        logiface.Int64("internal_app_id", internalAppID))

    // 使用internalAppId进行应用隔离和唯一性验证
    if err := s.ValidateUniqueFieldWithAppID(ctx, s.existsByUsernameAdapter, req.Username, internalAppID, "username"); err != nil {
        return nil, err
    }
    if err := s.ValidateUniqueFieldWithAppID(ctx, s.existsByEmailAdapter, req.Email, internalAppID, "email"); err != nil {
        return nil, err
    }
    if err := s.ValidateUniqueFieldWithAppID(ctx, s.existsByPhoneAdapter, req.Phone, internalAppID, "phone"); err != nil {
        return nil, err
    }

    // 获取密码策略并验证密码
    if req.Password != "" {
        if err := s.validatePasswordWithPolicy(ctx, req.Password, req.TenantID); err != nil {
            return nil, err
        }
    }

    // 创建用户实体
    password, err := userValueObject.NewPassword(req.Password)
    if err != nil {
        return nil, userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, fmt.Sprintf("invalid password: %v", err))
    }
    
    // 使用internalAppId创建用户实体
    user, err := s.entityFactory.NewUserWithAppID(ctx, req.TenantID, internalAppID, req.Username, req.Email, req.RealName, *password)
    if err != nil {
        s.logger.Error(ctx, "Failed to create user entity", logiface.Error(err), logiface.Any("req", req))
        return nil, userErrors.NewSystemError("create_user_entity", fmt.Sprintf("failed to create user entity: %v", err))
    }
    
    user.Phone = req.Phone
    user.Avatar = req.Avatar
    user.Status = userValueObject.UserStatus(req.Status)
    if req.DepartmentID > 0 {
        user.DepartmentID = &req.DepartmentID
    }
    if req.PositionID > 0 {
        user.PositionID = &req.PositionID
    }
    user.IsSystem = false

    // 保存用户
    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, userErrors.NewDatabaseError("create_user", fmt.Sprintf("failed to create user: %v", err))
    }

    s.logger.Info(ctx, "User created", logiface.Any("user", user))
    return user, nil
}

// ListUsersWithAppID 获取用户列表 - 使用internalAppId进行应用隔离
func (s *UserApplicationService) ListUsersWithAppID(ctx context.Context, req *dto.ListUsersRequest) (*dto.ListUsersResponse, error) {
    s.logger.Info(ctx, "Listing users with app ID for isolation", 
        logiface.Any("req", req),
        logiface.Int64("internal_app_id", req.InternalAppID),
        logiface.Int64("tenant_id", req.TenantID))
    
    // 构建查询参数，使用internalAppId进行应用隔离
    // internalAppId是应用隔离的关键字段，用于业务逻辑过滤
    // tenantId用于数据归属，确保数据归属正确
    params := userRepo.NewQueryParams().
        WithInternalAppID(req.InternalAppID). // 使用internalAppId进行应用隔离
        WithTenantID(req.TenantID).           // 使用tenantId进行数据归属
        WithPagination(req.Offset, req.Limit).
        WithOrder(req.OrderBy, req.OrderDir)

    // 添加搜索条件
    if req.Keyword != "" {
        params.WithKeyword(req.Keyword, "username", "real_name", "email", "phone")
    }

    if req.Status != "" {
        params.WithStatus(userValueObject.UserStatus(req.Status))
    }

    if req.DepartmentID > 0 {
        params.WithDepartmentID(req.DepartmentID)
    }

    if req.PositionID > 0 {
        params.WithPositionID(req.PositionID)
    }

    // 执行查询
    result, err := s.userRepo.Find(ctx, params)
    if err != nil {
        return nil, userErrors.NewDatabaseError("list_users", "failed to list users: "+err.Error())
    }

    // 转换为响应
    users := make([]*dto.UserResponse, 0, len(result.Data))
    for _, user := range result.Data {
        users = append(users, s.toUserResponse(user))
    }

    s.logger.Info(ctx, "Users listed", 
        logiface.Any("users", users), 
        logiface.Int64("total", result.Total), 
        logiface.Int("page", req.Offset/req.Limit+1), 
        logiface.Int("size", req.Limit))
    
    return &dto.ListUsersResponse{
        Users: users,
        Total: result.Total,
        Page:  req.Offset/req.Limit + 1,
        Size:  req.Limit,
    }, nil
}

// ValidateUniqueFieldWithAppID 使用internalAppId进行应用隔离验证唯一性
func (s *UserApplicationService) ValidateUniqueFieldWithAppID(ctx context.Context, adapter func(context.Context, int64, string) (bool, error), value, internalAppID int64, fieldName string) error {
    if value == 0 {
        return nil
    }
    
    exists, err := adapter(ctx, internalAppID, fmt.Sprintf("%v", value))
    if err != nil {
        return userErrors.NewDatabaseError("validate_unique_field", fmt.Sprintf("failed to validate %s: %v", fieldName, err))
    }
    
    if exists {
        return userErrors.NewBusinessError(userErrors.CodeUserAlreadyExists, fmt.Sprintf("%s already exists", fieldName))
    }
    
    return nil
}

// ValidateAppAndTenantIDs 验证internalAppId和tenantId的有效性
func (s *UserApplicationService) ValidateAppAndTenantIDs(internalAppID, tenantID int64) error {
    if internalAppID <= 0 {
        return userErrors.NewBusinessError(userErrors.CodeInvalidAppID, "invalid internal app ID")
    }
    
    if tenantID <= 0 {
        return userErrors.NewBusinessError(userErrors.CodeInvalidTenantID, "invalid tenant ID")
    }
    
    return nil
}
```

### 步骤4：更新DTO结构

**文件**: `users/internal/application/user/dto/user_dto.go`

```go
// ListUsersRequest 用户列表请求 - 新增internalAppId字段
type ListUsersRequest struct {
    InternalAppID int64  `json:"-"` // 从usercontext获取，不暴露给前端
    TenantID      int64  `json:"-"` // 从usercontext获取，仅用于持久化
    Keyword       string `json:"keyword,omitempty"`
    Status        string `json:"status,omitempty"`
    DepartmentID  int64  `json:"department_id,omitempty"`
    PositionID    int64  `json:"position_id,omitempty"`
    Offset        int    `json:"offset"`
    Limit         int    `json:"limit"`
    OrderBy       string `json:"order_by,omitempty"`
    OrderDir      string `json:"order_dir,omitempty"`
}

// ToListUsersRequestWithAppID 转换为包含internalAppId的请求
func (req *ListUsersRequestBody) ToListUsersRequestWithAppID(internalAppID, tenantID int64) *ListUsersRequest {
    return &ListUsersRequest{
        InternalAppID: internalAppID,
        TenantID:      tenantID,
        Keyword:       req.Keyword,
        Status:        req.Status,
        DepartmentID:  req.DepartmentID,
        PositionID:    req.PositionID,
        Offset:        req.Offset,
        Limit:         req.Limit,
        OrderBy:       req.OrderBy,
        OrderDir:      req.OrderDir,
    }
}
```

### 步骤5：统一错误处理

**文件**: `users/internal/interfaces/http/handlers/error_handler.go` (新建)

```go
package handlers

import (
    "gitee.com/heiyee/platforms/pkg/common/response"
    userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
    "github.com/gin-gonic/gin"
)

// HandleUserError 统一处理用户相关错误
func HandleUserError(c *gin.Context, err error) {
    if err == nil {
        return
    }

    // 根据错误类型返回友好的用户提示
    switch e := err.(type) {
    case *userErrors.UserError:
        switch e.Code {
        case userErrors.CodeUserNotFound:
            response.NotFound(c, "用户不存在")
        case userErrors.CodeUserAlreadyExists:
            response.BadRequest(c, "用户已存在")
        case userErrors.CodeUserLocked:
            response.Forbidden(c, "用户账户已被锁定")
        case userErrors.CodeUserDisabled:
            response.Forbidden(c, "用户账户已被禁用")
        case userErrors.CodePasswordPolicyViolated:
            response.BadRequest(c, "密码不符合安全策略")
        case userErrors.CodePermissionDenied:
            response.Forbidden(c, "权限不足")
        case userErrors.CodeInvalidAppID:
            response.BadRequest(c, "应用ID无效")
        case userErrors.CodeInvalidTenantID:
            response.BadRequest(c, "租户ID无效")
        default:
            response.BadRequest(c, e.Message)
        }
    default:
        // 系统错误不暴露细节
        response.InternalError(c, "系统繁忙，请稍后重试")
    }
}
```

## 总结

通过以上修复方案，我们：

1. **使用internalAppId进行应用隔离**: internalAppId是应用隔离的关键字段，用于业务逻辑处理，tenantId仅用于数据归属
2. **统一错误处理**: 创建了统一的错误处理机制，不暴露错误细节
3. **提取公共方法**: 遵循DRY原则，减少了代码重复
4. **添加TODO标记**: 对使用默认值的地方添加了TODO标记
5. **移除向后兼容性**: 创建了新的实现方法，符合规范要求

这些修改确保了代码符合DDD迁移规范，提高了代码的可维护性和可读性，同时保证了应用隔离的正确性。
