# 组织架构增强功能设计

## 概述

组织架构增强功能旨在支持复杂的企业组织场景，提供灵活、可扩展的组织架构管理能力，包括多层级部门管理、职位体系、组织架构变更管理等功能。

## 🎯 设计目标

### 核心目标
- **复杂组织架构支持**：支持多层级、多分支的组织架构
- **灵活职位体系**：支持复杂的职位层级和权限体系
- **组织架构变更管理**：支持组织架构的动态调整和变更追踪
- **跨部门协作**：支持跨部门的权限和协作机制

### 业务目标
- **组织架构可视化**：提供直观的组织架构展示
- **权限继承机制**：支持基于组织架构的权限继承
- **组织架构审计**：记录组织架构变更的完整审计日志
- **组织架构导入导出**：支持组织架构的批量操作

## 🏗️ 架构设计

### 1. 组织架构层次结构

```
企业
├── 一级部门
│   ├── 二级部门
│   │   ├── 三级部门
│   │   └── 职位
│   └── 职位
├── 一级部门
│   ├── 二级部门
│   └── 职位
└── 职位
```

### 2. 组织架构数据模型

```go
// 部门实体
type Department struct {
    ID           int64     `json:"id" gorm:"primaryKey"`
    Name         string    `json:"name" gorm:"not null"`
    Code         string    `json:"code" gorm:"uniqueIndex"`
    ParentID     *int64    `json:"parent_id" gorm:"index"`
    Level        int       `json:"level" gorm:"not null"`
    Path         string    `json:"path" gorm:"not null"`
    Sort         int       `json:"sort" gorm:"default:0"`
    Status       string    `json:"status" gorm:"default:'active'"`
    ManagerID    *int64    `json:"manager_id"`
    Description  string    `json:"description"`
    TenantID     int64     `json:"tenant_id" gorm:"not null"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    DeletedAt    *time.Time `json:"deleted_at" gorm:"index"`
    
    // 关联关系
    Parent       *Department `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
    Children     []Department `json:"children,omitempty" gorm:"foreignKey:ParentID"`
    Manager      *User       `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
    Users        []User      `json:"users,omitempty" gorm:"many2many:user_departments;"`
    Positions    []Position  `json:"positions,omitempty" gorm:"foreignKey:DepartmentID"`
}

// 职位实体
type Position struct {
    ID           int64     `json:"id" gorm:"primaryKey"`
    Name         string    `json:"name" gorm:"not null"`
    Code         string    `json:"code" gorm:"uniqueIndex"`
    DepartmentID int64     `json:"department_id" gorm:"not null"`
    Level        int       `json:"level" gorm:"not null"`
    Sort         int       `json:"sort" gorm:"default:0"`
    Status       string    `json:"status" gorm:"default:'active'"`
    Description  string    `json:"description"`
    TenantID     int64     `json:"tenant_id" gorm:"not null"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    DeletedAt    *time.Time `json:"deleted_at" gorm:"index"`
    
    // 关联关系
    Department   *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
    Users        []User      `json:"users,omitempty" gorm:"many2many:user_positions;"`
}

// 组织架构变更记录
type OrganizationChange struct {
    ID           int64     `json:"id" gorm:"primaryKey"`
    ChangeType   string    `json:"change_type" gorm:"not null"` // create, update, delete, move
    EntityType   string    `json:"entity_type" gorm:"not null"` // department, position
    EntityID     int64     `json:"entity_id" gorm:"not null"`
    OldData      string    `json:"old_data"` // JSON格式的旧数据
    NewData      string    `json:"new_data"` // JSON格式的新数据
    Reason       string    `json:"reason"`
    OperatorID   int64     `json:"operator_id" gorm:"not null"`
    TenantID     int64     `json:"tenant_id" gorm:"not null"`
    CreatedAt    time.Time `json:"created_at"`
    
    // 关联关系
    Operator     *User     `json:"operator,omitempty" gorm:"foreignKey:OperatorID"`
}
```

## 🔧 核心功能设计

### 1. 组织架构管理

#### 1.1 部门管理功能
```go
// 部门应用服务
type DepartmentApplicationService struct {
    departmentRepo repository.DepartmentRepository
    userRepo       repository.UserRepository
    changeRepo     repository.OrganizationChangeRepository
    logger         *log.Logger
}

// 创建部门
func (s *DepartmentApplicationService) CreateDepartment(ctx context.Context, req *dto.CreateDepartmentRequest) (*dto.DepartmentResponse, error) {
    // 1. 验证请求参数
    if err := s.validateCreateRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 检查部门代码唯一性
    if exists, _ := s.departmentRepo.ExistsByCode(ctx, req.TenantID, req.Code); exists {
        return nil, errors.New("部门代码已存在")
    }
    
    // 3. 计算部门层级和路径
    level, path, err := s.calculateDepartmentLevel(ctx, req.TenantID, req.ParentID)
    if err != nil {
        return nil, err
    }
    
    // 4. 创建部门
    department := &entity.Department{
        Name:        req.Name,
        Code:        req.Code,
        ParentID:    req.ParentID,
        Level:       level,
        Path:        path,
        Sort:        req.Sort,
        ManagerID:   req.ManagerID,
        Description: req.Description,
        TenantID:    req.TenantID,
    }
    
    if err := s.departmentRepo.Create(ctx, department); err != nil {
        return nil, err
    }
    
    // 5. 记录变更日志
    s.recordChange(ctx, "create", "department", department.ID, nil, department)
    
    return s.buildDepartmentResponse(department), nil
}

// 更新部门
func (s *DepartmentApplicationService) UpdateDepartment(ctx context.Context, req *dto.UpdateDepartmentRequest) (*dto.DepartmentResponse, error) {
    // 1. 获取原部门信息
    oldDepartment, err := s.departmentRepo.FindByID(ctx, req.ID)
    if err != nil {
        return nil, err
    }
    
    // 2. 验证更新权限
    if err := s.validateUpdatePermission(ctx, req.ID); err != nil {
        return nil, err
    }
    
    // 3. 检查部门代码唯一性（排除自身）
    if req.Code != oldDepartment.Code {
        if exists, _ := s.departmentRepo.ExistsByCode(ctx, req.TenantID, req.Code); exists {
            return nil, errors.New("部门代码已存在")
        }
    }
    
    // 4. 更新部门信息
    oldData := *oldDepartment
    oldDepartment.Name = req.Name
    oldDepartment.Code = req.Code
    oldDepartment.ManagerID = req.ManagerID
    oldDepartment.Description = req.Description
    oldDepartment.Sort = req.Sort
    
    if err := s.departmentRepo.Update(ctx, oldDepartment); err != nil {
        return nil, err
    }
    
    // 5. 记录变更日志
    s.recordChange(ctx, "update", "department", oldDepartment.ID, &oldData, oldDepartment)
    
    return s.buildDepartmentResponse(oldDepartment), nil
}

// 移动部门
func (s *DepartmentApplicationService) MoveDepartment(ctx context.Context, req *dto.MoveDepartmentRequest) error {
    // 1. 获取部门信息
    department, err := s.departmentRepo.FindByID(ctx, req.DepartmentID)
    if err != nil {
        return err
    }
    
    // 2. 验证移动权限
    if err := s.validateMovePermission(ctx, req.DepartmentID, req.NewParentID); err != nil {
        return err
    }
    
    // 3. 检查是否形成循环引用
    if err := s.checkCircularReference(ctx, req.DepartmentID, req.NewParentID); err != nil {
        return err
    }
    
    // 4. 执行移动操作
    oldParentID := department.ParentID
    oldPath := department.Path
    
    if err := s.moveDepartmentTree(ctx, department, req.NewParentID); err != nil {
        return err
    }
    
    // 5. 记录变更日志
    s.recordChange(ctx, "move", "department", department.ID, 
                   map[string]interface{}{"old_parent_id": oldParentID, "old_path": oldPath},
                   map[string]interface{}{"new_parent_id": req.NewParentID, "new_path": department.Path})
    
    return nil
}
```

#### 1.2 职位管理功能
```go
// 职位应用服务
type PositionApplicationService struct {
    positionRepo repository.PositionRepository
    departmentRepo repository.DepartmentRepository
    changeRepo    repository.OrganizationChangeRepository
    logger        *log.Logger
}

// 创建职位
func (s *PositionApplicationService) CreatePosition(ctx context.Context, req *dto.CreatePositionRequest) (*dto.PositionResponse, error) {
    // 1. 验证部门是否存在
    if _, err := s.departmentRepo.FindByID(ctx, req.DepartmentID); err != nil {
        return nil, errors.New("部门不存在")
    }
    
    // 2. 检查职位代码唯一性
    if exists, _ := s.positionRepo.ExistsByCode(ctx, req.TenantID, req.Code); exists {
        return nil, errors.New("职位代码已存在")
    }
    
    // 3. 创建职位
    position := &entity.Position{
        Name:         req.Name,
        Code:         req.Code,
        DepartmentID: req.DepartmentID,
        Level:        req.Level,
        Sort:         req.Sort,
        Description:  req.Description,
        TenantID:     req.TenantID,
    }
    
    if err := s.positionRepo.Create(ctx, position); err != nil {
        return nil, err
    }
    
    // 4. 记录变更日志
    s.recordChange(ctx, "create", "position", position.ID, nil, position)
    
    return s.buildPositionResponse(position), nil
}
```

### 2. 组织架构查询

#### 2.1 组织架构树查询
```go
// 获取组织架构树
func (s *DepartmentApplicationService) GetOrganizationTree(ctx context.Context, req *dto.GetOrganizationTreeRequest) (*dto.OrganizationTreeResponse, error) {
    // 1. 获取所有部门
    departments, err := s.departmentRepo.FindByTenantID(ctx, req.TenantID)
    if err != nil {
        return nil, err
    }
    
    // 2. 构建部门树
    departmentTree := s.buildDepartmentTree(departments)
    
    // 3. 获取职位信息
    positions, err := s.positionRepo.FindByTenantID(ctx, req.TenantID)
    if err != nil {
        return nil, err
    }
    
    // 4. 将职位分配到部门
    s.assignPositionsToDepartments(departmentTree, positions)
    
    return &dto.OrganizationTreeResponse{
        Tree: departmentTree,
    }, nil
}

// 构建部门树
func (s *DepartmentApplicationService) buildDepartmentTree(departments []*entity.Department) []*dto.DepartmentTreeNode {
    // 创建部门映射
    deptMap := make(map[int64]*dto.DepartmentTreeNode)
    var rootNodes []*dto.DepartmentTreeNode
    
    // 初始化所有部门节点
    for _, dept := range departments {
        node := &dto.DepartmentTreeNode{
            ID:           dept.ID,
            Name:         dept.Name,
            Code:         dept.Code,
            Level:        dept.Level,
            Path:         dept.Path,
            Sort:         dept.Sort,
            Status:       dept.Status,
            ManagerID:    dept.ManagerID,
            Description:  dept.Description,
            Children:     []*dto.DepartmentTreeNode{},
            Positions:    []*dto.PositionNode{},
        }
        deptMap[dept.ID] = node
    }
    
    // 构建父子关系
    for _, dept := range departments {
        node := deptMap[dept.ID]
        if dept.ParentID == nil {
            rootNodes = append(rootNodes, node)
        } else {
            if parent, exists := deptMap[*dept.ParentID]; exists {
                parent.Children = append(parent.Children, node)
            }
        }
    }
    
    // 排序
    s.sortDepartmentTree(rootNodes)
    
    return rootNodes
}
```

#### 2.2 组织架构搜索
```go
// 搜索组织架构
func (s *DepartmentApplicationService) SearchOrganization(ctx context.Context, req *dto.SearchOrganizationRequest) (*dto.SearchOrganizationResponse, error) {
    // 1. 搜索部门
    departments, err := s.departmentRepo.Search(ctx, &repository.DepartmentSearchCriteria{
        TenantID:  req.TenantID,
        Keyword:   req.Keyword,
        Status:    req.Status,
        Level:     req.Level,
        ManagerID: req.ManagerID,
        Page:      req.Page,
        PageSize:  req.PageSize,
    })
    if err != nil {
        return nil, err
    }
    
    // 2. 搜索职位
    positions, err := s.positionRepo.Search(ctx, &repository.PositionSearchCriteria{
        TenantID:     req.TenantID,
        Keyword:      req.Keyword,
        Status:       req.Status,
        DepartmentID: req.DepartmentID,
        Level:        req.Level,
        Page:         req.Page,
        PageSize:     req.PageSize,
    })
    if err != nil {
        return nil, err
    }
    
    return &dto.SearchOrganizationResponse{
        Departments: s.buildDepartmentResponses(departments),
        Positions:   s.buildPositionResponses(positions),
    }, nil
}
```

### 3. 组织架构变更管理

#### 3.1 变更记录功能
```go
// 记录组织架构变更
func (s *DepartmentApplicationService) recordChange(ctx context.Context, changeType, entityType string, entityID int64, oldData, newData interface{}) {
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        s.logger.Warn(ctx, "Failed to get user info for change record", "error", err)
        return
    }
    
    change := &entity.OrganizationChange{
        ChangeType: changeType,
        EntityType: entityType,
        EntityID:   entityID,
        OperatorID: userInfo.UserID,
        TenantID:   userInfo.TenantID,
    }
    
    if oldData != nil {
        if oldDataBytes, err := json.Marshal(oldData); err == nil {
            change.OldData = string(oldDataBytes)
        }
    }
    
    if newData != nil {
        if newDataBytes, err := json.Marshal(newData); err == nil {
            change.NewData = string(newDataBytes)
        }
    }
    
    if err := s.changeRepo.Create(ctx, change); err != nil {
        s.logger.Error(ctx, "Failed to record organization change", "error", err)
    }
}
```

#### 3.2 变更历史查询
```go
// 获取组织架构变更历史
func (s *DepartmentApplicationService) GetChangeHistory(ctx context.Context, req *dto.GetChangeHistoryRequest) (*dto.ChangeHistoryResponse, error) {
    changes, err := s.changeRepo.FindByCriteria(ctx, &repository.ChangeSearchCriteria{
        TenantID:   req.TenantID,
        EntityType:  req.EntityType,
        EntityID:    req.EntityID,
        ChangeType:  req.ChangeType,
        OperatorID:  req.OperatorID,
        StartTime:   req.StartTime,
        EndTime:     req.EndTime,
        Page:        req.Page,
        PageSize:    req.PageSize,
    })
    if err != nil {
        return nil, err
    }
    
    return &dto.ChangeHistoryResponse{
        Changes: s.buildChangeResponses(changes),
    }, nil
}
```

### 4. 组织架构导入导出

#### 4.1 组织架构导出
```go
// 导出组织架构
func (s *DepartmentApplicationService) ExportOrganization(ctx context.Context, req *dto.ExportOrganizationRequest) (*dto.ExportOrganizationResponse, error) {
    // 1. 获取组织架构数据
    departments, err := s.departmentRepo.FindByTenantID(ctx, req.TenantID)
    if err != nil {
        return nil, err
    }
    
    positions, err := s.positionRepo.FindByTenantID(ctx, req.TenantID)
    if err != nil {
        return nil, err
    }
    
    // 2. 构建导出数据
    exportData := &dto.OrganizationExportData{
        Departments: s.buildDepartmentExportData(departments),
        Positions:   s.buildPositionExportData(positions),
        ExportTime:  time.Now(),
        TenantID:    req.TenantID,
    }
    
    // 3. 生成导出文件
    fileData, err := s.generateExportFile(exportData, req.Format)
    if err != nil {
        return nil, err
    }
    
    return &dto.ExportOrganizationResponse{
        FileName: fmt.Sprintf("organization_%d_%s.%s", req.TenantID, time.Now().Format("20060102"), req.Format),
        FileData: fileData,
        FileSize: len(fileData),
    }, nil
}
```

#### 4.2 组织架构导入
```go
// 导入组织架构
func (s *DepartmentApplicationService) ImportOrganization(ctx context.Context, req *dto.ImportOrganizationRequest) (*dto.ImportOrganizationResponse, error) {
    // 1. 解析导入文件
    importData, err := s.parseImportFile(req.FileData, req.Format)
    if err != nil {
        return nil, err
    }
    
    // 2. 验证导入数据
    if err := s.validateImportData(importData); err != nil {
        return nil, err
    }
    
    // 3. 执行导入操作
    result, err := s.executeImport(ctx, importData, req.TenantID, req.ImportMode)
    if err != nil {
        return nil, err
    }
    
    return &dto.ImportOrganizationResponse{
        SuccessCount: result.SuccessCount,
        ErrorCount:   result.ErrorCount,
        Errors:       result.Errors,
    }, nil
}
```

## 📋 权限继承机制

### 1. 基于组织架构的权限继承
```go
// 组织架构权限服务
type OrganizationPermissionService struct {
    departmentRepo repository.DepartmentRepository
    positionRepo   repository.PositionRepository
    userRepo       repository.UserRepository
    permissionRepo repository.PermissionRepository
}

// 获取用户的组织架构权限
func (s *OrganizationPermissionService) GetUserOrganizationPermissions(ctx context.Context, userID int64) ([]string, error) {
    // 1. 获取用户所属部门和职位
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取部门权限
    deptPermissions, err := s.getDepartmentPermissions(ctx, user.DepartmentID)
    if err != nil {
        return nil, err
    }
    
    // 3. 获取职位权限
    positionPermissions, err := s.getPositionPermissions(ctx, user.PositionID)
    if err != nil {
        return nil, err
    }
    
    // 4. 合并权限
    allPermissions := make(map[string]bool)
    for _, perm := range deptPermissions {
        allPermissions[perm] = true
    }
    for _, perm := range positionPermissions {
        allPermissions[perm] = true
    }
    
    // 5. 转换为切片
    var permissions []string
    for perm := range allPermissions {
        permissions = append(permissions, perm)
    }
    
    return permissions, nil
}

// 获取部门权限（包括上级部门权限）
func (s *OrganizationPermissionService) getDepartmentPermissions(ctx context.Context, departmentID int64) ([]string, error) {
    var permissions []string
    
    // 获取部门及其所有上级部门
    departments, err := s.getDepartmentHierarchy(ctx, departmentID)
    if err != nil {
        return nil, err
    }
    
    // 收集所有部门的权限
    for _, dept := range departments {
        deptPerms, err := s.permissionRepo.GetDepartmentPermissions(ctx, dept.ID)
        if err != nil {
            continue
        }
        permissions = append(permissions, deptPerms...)
    }
    
    return permissions, nil
}
```

## 🔄 路由配置

### 1. 组织架构管理路由
```go
// 组织架构管理路由
func SetupOrganizationRoutes(r *gin.Engine, handlers *Handlers) {
    org := r.Group("/api/organization")
    org.Use(middleware.RequirePermission("organization:read"))
    {
        // 部门管理
        dept := org.Group("/department")
        {
            dept.POST("/create", middleware.RequirePermission("department:create"), handlers.Department.CreateDepartment)
            dept.POST("/update", middleware.RequirePermission("department:update"), handlers.Department.UpdateDepartment)
            dept.POST("/delete", middleware.RequirePermission("department:delete"), handlers.Department.DeleteDepartment)
            dept.POST("/move", middleware.RequirePermission("department:move"), handlers.Department.MoveDepartment)
            dept.POST("/list", handlers.Department.ListDepartments)
            dept.POST("/tree", handlers.Department.GetDepartmentTree)
            dept.POST("/get", handlers.Department.GetDepartment)
        }
        
        // 职位管理
        pos := org.Group("/position")
        {
            pos.POST("/create", middleware.RequirePermission("position:create"), handlers.Position.CreatePosition)
            pos.POST("/update", middleware.RequirePermission("position:update"), handlers.Position.UpdatePosition)
            pos.POST("/delete", middleware.RequirePermission("position:delete"), handlers.Position.DeletePosition)
            pos.POST("/list", handlers.Position.ListPositions)
            pos.POST("/get", handlers.Position.GetPosition)
        }
        
        // 组织架构查询
        org.POST("/tree", handlers.Organization.GetOrganizationTree)
        org.POST("/search", handlers.Organization.SearchOrganization)
        
        // 变更管理
        org.POST("/changes", handlers.Organization.GetChangeHistory)
        
        // 导入导出
        org.POST("/export", middleware.RequirePermission("organization:export"), handlers.Organization.ExportOrganization)
        org.POST("/import", middleware.RequirePermission("organization:import"), handlers.Organization.ImportOrganization)
    }
}
```

## 📊 性能优化

### 1. 组织架构缓存
```go
// 组织架构缓存
type OrganizationCache struct {
    cache    *cache.Cache
    duration time.Duration
}

// 缓存组织架构树
func (oc *OrganizationCache) CacheOrganizationTree(tenantID int64, tree []*dto.DepartmentTreeNode) {
    key := fmt.Sprintf("org_tree_%d", tenantID)
    oc.cache.Set(key, tree, oc.duration)
}

// 获取缓存的组织架构树
func (oc *OrganizationCache) GetCachedOrganizationTree(tenantID int64) ([]*dto.DepartmentTreeNode, bool) {
    key := fmt.Sprintf("org_tree_%d", tenantID)
    if tree, found := oc.cache.Get(key); found {
        return tree.([]*dto.DepartmentTreeNode), true
    }
    return nil, false
}
```

### 2. 数据库索引优化
```sql
-- 部门表索引
CREATE INDEX idx_department_tenant_parent ON departments(tenant_id, parent_id);
CREATE INDEX idx_department_tenant_level ON departments(tenant_id, level);
CREATE INDEX idx_department_tenant_path ON departments(tenant_id, path);
CREATE INDEX idx_department_tenant_manager ON departments(tenant_id, manager_id);

-- 职位表索引
CREATE INDEX idx_position_tenant_dept ON positions(tenant_id, department_id);
CREATE INDEX idx_position_tenant_level ON positions(tenant_id, level);

-- 变更记录表索引
CREATE INDEX idx_change_tenant_entity ON organization_changes(tenant_id, entity_type, entity_id);
CREATE INDEX idx_change_tenant_time ON organization_changes(tenant_id, created_at);
```

## 🚀 实施计划

### 第一阶段：基础功能（2-3周）
1. 实现部门管理基础功能
2. 实现职位管理基础功能
3. 实现组织架构树查询
4. 添加基础权限控制

### 第二阶段：高级功能（2-3周）
1. 实现组织架构变更管理
2. 实现权限继承机制
3. 实现组织架构搜索
4. 添加变更审计功能

### 第三阶段：扩展功能（2-3周）
1. 实现组织架构导入导出
2. 实现组织架构可视化
3. 性能优化和缓存
4. 完善测试和文档

## 📝 测试策略

### 1. 单元测试
```go
func TestDepartmentApplicationService(t *testing.T) {
    // 测试部门创建、更新、删除
    // 测试部门树构建
    // 测试部门移动
}

func TestPositionApplicationService(t *testing.T) {
    // 测试职位创建、更新、删除
    // 测试职位分配
}
```

### 2. 集成测试
```go
func TestOrganizationIntegration(t *testing.T) {
    // 测试完整的组织架构管理流程
    // 测试权限继承机制
    // 测试变更管理
}
```

### 3. 性能测试
```go
func TestOrganizationPerformance(t *testing.T) {
    // 测试组织架构树查询性能
    // 测试大量数据的处理能力
    // 测试缓存机制效果
}
```

这个组织架构增强功能设计将提供灵活、可扩展的组织架构管理能力，支持复杂的企业组织场景。 