# 权限检查中间件功能设计

## 概述

权限检查中间件是用户系统的核心安全组件，用于防止越权操作，确保用户只能访问和操作其有权限的资源。

## 🎯 设计目标

### 核心目标
- **防止越权访问**：确保用户只能访问有权限的资源
- **统一权限控制**：提供一致的权限检查机制
- **高性能**：最小化权限检查的性能开销
- **灵活配置**：支持多种权限策略和规则

### 安全目标
- **最小权限原则**：用户只拥有完成任务所需的最小权限
- **权限分离**：不同角色拥有不同的权限集合
- **审计追踪**：记录所有权限相关的操作日志

## 🏗️ 架构设计

### 1. 中间件层次结构

```
HTTP请求 → 认证中间件 → 权限中间件 → 业务处理器
                ↓              ↓
            用户身份验证    权限检查验证
                ↓              ↓
            用户上下文      权限决策
```

### 2. 权限检查流程

```go
// 权限检查流程图
Request → Extract User Info → Get User Permissions → Check Resource Permission → Decision
   ↓              ↓                    ↓                      ↓              ↓
解析请求 → 提取用户信息 → 获取用户权限 → 检查资源权限 → 允许/拒绝
```

## 🔧 核心功能设计

### 1. 权限中间件基础功能

#### 1.1 权限检查中间件
```go
// 基础权限检查中间件
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 获取用户信息
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 2. 检查用户权限
        if !hasPermission(userInfo.UserID, permission) {
            commonErrors.Forbidden(c, "权限不足")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

#### 1.2 资源权限检查中间件
```go
// 资源级权限检查
func RequireResourcePermission(resourceType, action string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 获取资源ID
        resourceID := c.Param("id")
        
        // 检查资源权限
        if !hasResourcePermission(userInfo.UserID, resourceType, resourceID, action) {
            commonErrors.Forbidden(c, "无权访问此资源")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 2. 高级权限功能

#### 2.1 动态权限检查
```go
// 支持动态权限检查
func RequireDynamicPermission(permissionFunc func(c *gin.Context) string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 动态获取权限要求
        requiredPermission := permissionFunc(c)
        
        if !hasPermission(userInfo.UserID, requiredPermission) {
            commonErrors.Forbidden(c, "权限不足")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

#### 2.2 租户权限检查
```go
// 租户级权限检查
func RequireTenantPermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        tenantID, exists := usercontext.GetTenantID(c.Request.Context())
        if !exists {
            commonErrors.Forbidden(c, "租户信息缺失")
            c.Abort()
            return
        }
        
        // 检查租户内权限
        if !hasTenantPermission(userInfo.UserID, tenantID, permission) {
            commonErrors.Forbidden(c, "租户内权限不足")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 3. 权限缓存机制

#### 3.1 权限缓存设计
```go
type PermissionCache struct {
    cache    *cache.Cache
    duration time.Duration
}

// 缓存用户权限
func (pc *PermissionCache) CacheUserPermissions(userID int64, permissions []string) {
    key := fmt.Sprintf("user_permissions_%d", userID)
    pc.cache.Set(key, permissions, pc.duration)
}

// 获取缓存的权限
func (pc *PermissionCache) GetCachedPermissions(userID int64) ([]string, bool) {
    key := fmt.Sprintf("user_permissions_%d", userID)
    if permissions, found := pc.cache.Get(key); found {
        return permissions.([]string), true
    }
    return nil, false
}
```

### 4. 权限审计功能

#### 4.1 权限审计日志
```go
// 权限审计中间件
func AuditPermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        // 记录权限使用日志
        userInfo, _ := usercontext.GetUserInfo(c.Request.Context())
        duration := time.Since(start)
        
        logPermissionAccess(userInfo.UserID, permission, c.Request.URL.Path, 
                          c.Request.Method, duration, c.Writer.Status())
    }
}
```

## 📋 权限策略设计

### 1. 权限分类

#### 1.1 系统级权限
```go
const (
    // 用户管理权限
    PermissionUserCreate    = "user:create"
    PermissionUserRead      = "user:read"
    PermissionUserUpdate    = "user:update"
    PermissionUserDelete    = "user:delete"
    PermissionUserList      = "user:list"
    
    // 角色管理权限
    PermissionRoleCreate    = "role:create"
    PermissionRoleRead      = "role:read"
    PermissionRoleUpdate    = "role:update"
    PermissionRoleDelete    = "role:delete"
    PermissionRoleAssign    = "role:assign"
    
    // 租户管理权限
    PermissionTenantCreate  = "tenant:create"
    PermissionTenantRead    = "tenant:read"
    PermissionTenantUpdate  = "tenant:update"
    PermissionTenantDelete  = "tenant:delete"
)
```

#### 1.2 业务级权限
```go
const (
    // 部门管理权限
    PermissionDepartmentCreate = "department:create"
    PermissionDepartmentRead   = "department:read"
    PermissionDepartmentUpdate = "department:update"
    PermissionDepartmentDelete = "department:delete"
    
    // 文件管理权限
    PermissionFileUpload   = "file:upload"
    PermissionFileDownload = "file:download"
    PermissionFileDelete   = "file:delete"
    PermissionFileShare    = "file:share"
)
```

### 2. 权限组合策略

#### 2.1 权限组合中间件
```go
// 要求多个权限中的任意一个
func RequireAnyPermission(permissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 检查是否有任意一个权限
        for _, permission := range permissions {
            if hasPermission(userInfo.UserID, permission) {
                c.Next()
                return
            }
        }
        
        commonErrors.Forbidden(c, "权限不足")
        c.Abort()
    }
}

// 要求所有权限
func RequireAllPermissions(permissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userInfo, err := usercontext.RequireAuth(c.Request.Context())
        if err != nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 检查是否拥有所有权限
        for _, permission := range permissions {
            if !hasPermission(userInfo.UserID, permission) {
                commonErrors.Forbidden(c, "权限不足")
                c.Abort()
                return
            }
        }
        
        c.Next()
    }
}
```

## 🔄 路由配置示例

### 1. 基础权限路由
```go
// 用户管理路由
userRoutes := router.Group("/api/user")
{
    userRoutes.POST("/create", RequirePermission(PermissionUserCreate), userHandler.CreateUser)
    userRoutes.POST("/list", RequirePermission(PermissionUserRead), userHandler.ListUsers)
    userRoutes.POST("/get", RequirePermission(PermissionUserRead), userHandler.GetUser)
    userRoutes.POST("/update", RequirePermission(PermissionUserUpdate), userHandler.UpdateUser)
    userRoutes.POST("/delete", RequirePermission(PermissionUserDelete), userHandler.DeleteUser)
}
```

### 2. 资源权限路由
```go
// 文件管理路由
fileRoutes := router.Group("/api/file")
{
    fileRoutes.POST("/upload", RequirePermission(PermissionFileUpload), fileHandler.UploadFile)
    fileRoutes.GET("/download/:id", RequireResourcePermission("file", "download"), fileHandler.DownloadFile)
    fileRoutes.POST("/delete/:id", RequireResourcePermission("file", "delete"), fileHandler.DeleteFile)
    fileRoutes.POST("/share/:id", RequireResourcePermission("file", "share"), fileHandler.ShareFile)
}
```

### 3. 租户权限路由
```go
// 租户管理路由
tenantRoutes := router.Group("/api/tenant")
{
    tenantRoutes.POST("/create", RequireTenantPermission(PermissionTenantCreate), tenantHandler.CreateTenant)
    tenantRoutes.POST("/update", RequireTenantPermission(PermissionTenantUpdate), tenantHandler.UpdateTenant)
    tenantRoutes.POST("/delete", RequireTenantPermission(PermissionTenantDelete), tenantHandler.DeleteTenant)
}
```

## 📊 性能优化

### 1. 权限缓存策略
```go
// 权限缓存配置
type PermissionCacheConfig struct {
    Enabled     bool          `json:"enabled"`
    Duration    time.Duration `json:"duration"`
    MaxSize     int           `json:"max_size"`
    CleanupInterval time.Duration `json:"cleanup_interval"`
}

// 默认配置
var DefaultPermissionCacheConfig = PermissionCacheConfig{
    Enabled:         true,
    Duration:        5 * time.Minute,
    MaxSize:         1000,
    CleanupInterval: 10 * time.Minute,
}
```

### 2. 权限预加载
```go
// 用户登录时预加载权限
func PreloadUserPermissions(userID int64) {
    permissions := getUserPermissions(userID)
    permissionCache.CacheUserPermissions(userID, permissions)
}
```

## 🔍 监控和告警

### 1. 权限使用统计
```go
// 权限使用统计
type PermissionUsageStats struct {
    Permission    string    `json:"permission"`
    UserID        int64     `json:"user_id"`
    AccessCount   int       `json:"access_count"`
    LastAccess    time.Time `json:"last_access"`
    DeniedCount   int       `json:"denied_count"`
}
```

### 2. 异常权限告警
```go
// 权限异常检测
func DetectPermissionAnomalies() {
    // 检测频繁的权限拒绝
    // 检测异常时间段的权限访问
    // 检测跨租户的权限访问
}
```

## 🚀 实施计划

### 第一阶段：基础权限中间件
1. 实现基础权限检查中间件
2. 添加权限缓存机制
3. 集成到现有路由

### 第二阶段：高级权限功能
1. 实现资源级权限检查
2. 添加租户权限隔离
3. 实现权限审计功能

### 第三阶段：优化和监控
1. 性能优化和缓存策略
2. 权限使用监控
3. 异常检测和告警

## 📝 测试策略

### 1. 单元测试
```go
func TestRequirePermission(t *testing.T) {
    // 测试权限检查中间件
    // 测试权限缓存机制
    // 测试权限审计功能
}
```

### 2. 集成测试
```go
func TestPermissionIntegration(t *testing.T) {
    // 测试完整的权限检查流程
    // 测试权限与业务逻辑的集成
    // 测试权限缓存的有效性
}
```

### 3. 性能测试
```go
func TestPermissionPerformance(t *testing.T) {
    // 测试权限检查的性能
    // 测试缓存机制的效果
    // 测试并发权限检查
}
```

这个权限检查中间件设计将有效防止越权操作，提供统一、高效、安全的权限控制机制。 