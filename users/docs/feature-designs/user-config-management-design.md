# 用户配置管理功能设计

## 概述

用户配置管理功能旨在提供个性化的用户体验，允许用户自定义界面设置、通知偏好、隐私设置等，提升用户满意度和系统可用性。

## 🎯 设计目标

### 核心目标
- **个性化体验**：支持用户自定义界面和功能设置
- **隐私保护**：提供细粒度的隐私控制选项
- **通知管理**：支持灵活的通知偏好设置
- **数据安全**：确保用户配置数据的安全存储和传输

### 用户体验目标
- **直观易用**：提供简单直观的配置界面
- **响应迅速**：配置更改立即生效
- **跨设备同步**：支持多设备间的配置同步
- **默认合理**：提供合理的默认配置选项

## 🏗️ 架构设计

### 1. 用户配置层次结构

```
用户配置
├── 界面设置
│   ├── 主题模式（深色/浅色/自动）
│   ├── 语言设置
│   ├── 时区设置
│   └── 界面布局
├── 通知设置
│   ├── 邮件通知
│   ├── 短信通知
│   ├── 推送通知
│   └── 通知频率
├── 隐私设置
│   ├── 个人信息可见性
│   ├── 活动记录可见性
│   └── 数据共享设置
└── 功能设置
    ├── 安全设置
    ├── 偏好设置
    └── 高级选项
```

### 2. 用户配置数据模型

```go
// 用户配置实体
type UserConfig struct {
    ID          int64     `json:"id" gorm:"primaryKey"`
    UserID      int64     `json:"user_id" gorm:"not null;uniqueIndex"`
    TenantID    int64     `json:"tenant_id" gorm:"not null"`
    
    // 界面设置
    Theme       string    `json:"theme" gorm:"default:'light'"` // light, dark, auto
    Language    string    `json:"language" gorm:"default:'zh-CN'"`
    Timezone    string    `json:"timezone" gorm:"default:'Asia/Shanghai'"`
    Layout      string    `json:"layout" gorm:"default:'default'"`
    
    // 通知设置
    EmailNotification    bool `json:"email_notification" gorm:"default:true"`
    SMSNotification     bool `json:"sms_notification" gorm:"default:false"`
    PushNotification    bool `json:"push_notification" gorm:"default:true"`
    NotificationFrequency string `json:"notification_frequency" gorm:"default:'immediate'"` // immediate, daily, weekly
    
    // 隐私设置
    ProfileVisibility    string `json:"profile_visibility" gorm:"default:'public'"` // public, friends, private
    ActivityVisibility   string `json:"activity_visibility" gorm:"default:'friends'"` // public, friends, private
    DataSharing         bool   `json:"data_sharing" gorm:"default:true"`
    
    // 安全设置
    TwoFactorAuth       bool   `json:"two_factor_auth" gorm:"default:false"`
    LoginNotification   bool   `json:"login_notification" gorm:"default:true"`
    SessionTimeout      int    `json:"session_timeout" gorm:"default:3600"` // 秒
    
    // 功能设置
    AutoSave            bool   `json:"auto_save" gorm:"default:true"`
    AdvancedMode        bool   `json:"advanced_mode" gorm:"default:false"`
    DebugMode           bool   `json:"debug_mode" gorm:"default:false"`
    
    // 自定义设置（JSON格式）
    CustomSettings      string `json:"custom_settings" gorm:"type:text"`
    
    CreatedAt           time.Time `json:"created_at"`
    UpdatedAt           time.Time `json:"updated_at"`
    
    // 关联关系
    User                *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// 用户配置历史记录
type UserConfigHistory struct {
    ID          int64     `json:"id" gorm:"primaryKey"`
    UserID      int64     `json:"user_id" gorm:"not null"`
    ConfigID    int64     `json:"config_id" gorm:"not null"`
    ChangeType  string    `json:"change_type" gorm:"not null"` // create, update, delete
    OldValue    string    `json:"old_value"` // JSON格式
    NewValue    string    `json:"new_value"` // JSON格式
    ChangedBy   int64     `json:"changed_by" gorm:"not null"`
    Reason      string    `json:"reason"`
    CreatedAt   time.Time `json:"created_at"`
    
    // 关联关系
    User        *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
    Config      *UserConfig `json:"config,omitempty" gorm:"foreignKey:ConfigID"`
    ChangedByUser *User   `json:"changed_by_user,omitempty" gorm:"foreignKey:ChangedBy"`
}
```

## 🔧 核心功能设计

### 1. 用户配置管理

#### 1.1 配置应用服务
```go
// 用户配置应用服务
type UserConfigApplicationService struct {
    configRepo    repository.UserConfigRepository
    historyRepo   repository.UserConfigHistoryRepository
    userRepo      repository.UserRepository
    logger        *log.Logger
}

// 获取用户配置
func (s *UserConfigApplicationService) GetUserConfig(ctx context.Context, req *dto.GetUserConfigRequest) (*dto.UserConfigResponse, error) {
    // 1. 获取用户信息
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return nil, err
    }
    
    // 2. 查询用户配置
    config, err := s.configRepo.FindByUserID(ctx, userInfo.UserID)
    if err != nil {
        // 如果配置不存在，创建默认配置
        if errors.Is(err, repository.ErrNotFound) {
            config, err = s.createDefaultConfig(ctx, userInfo.UserID, userInfo.TenantID)
            if err != nil {
                return nil, err
            }
        } else {
            return nil, err
        }
    }
    
    return s.buildConfigResponse(config), nil
}

// 更新用户配置
func (s *UserConfigApplicationService) UpdateUserConfig(ctx context.Context, req *dto.UpdateUserConfigRequest) (*dto.UserConfigResponse, error) {
    // 1. 获取用户信息
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取当前配置
    config, err := s.configRepo.FindByUserID(ctx, userInfo.UserID)
    if err != nil {
        if errors.Is(err, repository.ErrNotFound) {
            config, err = s.createDefaultConfig(ctx, userInfo.UserID, userInfo.TenantID)
            if err != nil {
                return nil, err
            }
        } else {
            return nil, err
        }
    }
    
    // 3. 保存旧配置用于历史记录
    oldConfig := *config
    
    // 4. 更新配置
    if req.Theme != nil {
        config.Theme = *req.Theme
    }
    if req.Language != nil {
        config.Language = *req.Language
    }
    if req.Timezone != nil {
        config.Timezone = *req.Timezone
    }
    if req.Layout != nil {
        config.Layout = *req.Layout
    }
    if req.EmailNotification != nil {
        config.EmailNotification = *req.EmailNotification
    }
    if req.SMSNotification != nil {
        config.SMSNotification = *req.SMSNotification
    }
    if req.PushNotification != nil {
        config.PushNotification = *req.PushNotification
    }
    if req.NotificationFrequency != nil {
        config.NotificationFrequency = *req.NotificationFrequency
    }
    if req.ProfileVisibility != nil {
        config.ProfileVisibility = *req.ProfileVisibility
    }
    if req.ActivityVisibility != nil {
        config.ActivityVisibility = *req.ActivityVisibility
    }
    if req.DataSharing != nil {
        config.DataSharing = *req.DataSharing
    }
    if req.TwoFactorAuth != nil {
        config.TwoFactorAuth = *req.TwoFactorAuth
    }
    if req.LoginNotification != nil {
        config.LoginNotification = *req.LoginNotification
    }
    if req.SessionTimeout != nil {
        config.SessionTimeout = *req.SessionTimeout
    }
    if req.AutoSave != nil {
        config.AutoSave = *req.AutoSave
    }
    if req.AdvancedMode != nil {
        config.AdvancedMode = *req.AdvancedMode
    }
    if req.DebugMode != nil {
        config.DebugMode = *req.DebugMode
    }
    if req.CustomSettings != nil {
        config.CustomSettings = *req.CustomSettings
    }
    
    // 5. 保存配置
    if err := s.configRepo.Update(ctx, config); err != nil {
        return nil, err
    }
    
    // 6. 记录配置变更历史
    s.recordConfigChange(ctx, config.ID, &oldConfig, config, req.Reason)
    
    return s.buildConfigResponse(config), nil
}

// 重置用户配置
func (s *UserConfigApplicationService) ResetUserConfig(ctx context.Context, req *dto.ResetUserConfigRequest) (*dto.UserConfigResponse, error) {
    // 1. 获取用户信息
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取当前配置
    config, err := s.configRepo.FindByUserID(ctx, userInfo.UserID)
    if err != nil {
        return nil, err
    }
    
    // 3. 保存旧配置用于历史记录
    oldConfig := *config
    
    // 4. 重置为默认配置
    defaultConfig := s.getDefaultConfig(userInfo.TenantID)
    config.Theme = defaultConfig.Theme
    config.Language = defaultConfig.Language
    config.Timezone = defaultConfig.Timezone
    config.Layout = defaultConfig.Layout
    config.EmailNotification = defaultConfig.EmailNotification
    config.SMSNotification = defaultConfig.SMSNotification
    config.PushNotification = defaultConfig.PushNotification
    config.NotificationFrequency = defaultConfig.NotificationFrequency
    config.ProfileVisibility = defaultConfig.ProfileVisibility
    config.ActivityVisibility = defaultConfig.ActivityVisibility
    config.DataSharing = defaultConfig.DataSharing
    config.TwoFactorAuth = defaultConfig.TwoFactorAuth
    config.LoginNotification = defaultConfig.LoginNotification
    config.SessionTimeout = defaultConfig.SessionTimeout
    config.AutoSave = defaultConfig.AutoSave
    config.AdvancedMode = defaultConfig.AdvancedMode
    config.DebugMode = defaultConfig.DebugMode
    config.CustomSettings = defaultConfig.CustomSettings
    
    // 5. 保存配置
    if err := s.configRepo.Update(ctx, config); err != nil {
        return nil, err
    }
    
    // 6. 记录配置变更历史
    s.recordConfigChange(ctx, config.ID, &oldConfig, config, "配置重置")
    
    return s.buildConfigResponse(config), nil
}
```

## 📋 配置分类管理

### 1. 界面设置配置
```go
// 界面设置DTO
type InterfaceSettings struct {
    Theme    string `json:"theme"`    // 主题模式
    Language string `json:"language"` // 语言设置
    Timezone string `json:"timezone"` // 时区设置
    Layout   string `json:"layout"`   // 界面布局
}

// 更新界面设置
func (s *UserConfigApplicationService) UpdateInterfaceSettings(ctx context.Context, req *dto.UpdateInterfaceSettingsRequest) error {
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return err
    }
    
    config, err := s.configRepo.FindByUserID(ctx, userInfo.UserID)
    if err != nil {
        return err
    }
    
    // 更新界面设置
    if req.Theme != nil {
        config.Theme = *req.Theme
    }
    if req.Language != nil {
        config.Language = *req.Language
    }
    if req.Timezone != nil {
        config.Timezone = *req.Timezone
    }
    if req.Layout != nil {
        config.Layout = *req.Layout
    }
    
    return s.configRepo.Update(ctx, config)
}
```

### 2. 通知设置配置
```go
// 通知设置DTO
type NotificationSettings struct {
    EmailNotification    bool   `json:"email_notification"`
    SMSNotification     bool   `json:"sms_notification"`
    PushNotification    bool   `json:"push_notification"`
    NotificationFrequency string `json:"notification_frequency"`
}

// 更新通知设置
func (s *UserConfigApplicationService) UpdateNotificationSettings(ctx context.Context, req *dto.UpdateNotificationSettingsRequest) error {
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return err
    }
    
    config, err := s.configRepo.FindByUserID(ctx, userInfo.UserID)
    if err != nil {
        return err
    }
    
    // 更新通知设置
    if req.EmailNotification != nil {
        config.EmailNotification = *req.EmailNotification
    }
    if req.SMSNotification != nil {
        config.SMSNotification = *req.SMSNotification
    }
    if req.PushNotification != nil {
        config.PushNotification = *req.PushNotification
    }
    if req.NotificationFrequency != nil {
        config.NotificationFrequency = *req.NotificationFrequency
    }
    
    return s.configRepo.Update(ctx, config)
}
```

## 🔄 路由配置

### 1. 用户配置管理路由
```go
// 用户配置管理路由
func SetupUserConfigRoutes(r *gin.Engine, handlers *Handlers) {
    config := r.Group("/api/user/config")
    config.Use(middleware.RequireAuth())
    {
        // 基础配置管理
        config.POST("/get", handlers.UserConfig.GetUserConfig)
        config.POST("/update", handlers.UserConfig.UpdateUserConfig)
        config.POST("/reset", handlers.UserConfig.ResetUserConfig)
        
        // 分类配置管理
        config.POST("/interface/update", handlers.UserConfig.UpdateInterfaceSettings)
        config.POST("/notification/update", handlers.UserConfig.UpdateNotificationSettings)
        config.POST("/privacy/update", handlers.UserConfig.UpdatePrivacySettings)
        config.POST("/security/update", handlers.UserConfig.UpdateSecuritySettings)
        
        // 配置历史
        config.POST("/history", handlers.UserConfig.GetConfigHistory)
        
        // 配置同步
        config.POST("/sync", handlers.UserConfig.SyncUserConfig)
        config.POST("/sync/get", handlers.UserConfig.GetDeviceConfig)
    }
}
```

## 🚀 实施计划

### 第一阶段：基础功能（1-2周）
1. 实现用户配置数据模型
2. 实现基础配置管理功能
3. 实现配置验证和默认值
4. 添加基础权限控制

### 第二阶段：高级功能（1-2周）
1. 实现配置分类管理
2. 实现配置历史记录
3. 实现配置同步功能
4. 添加配置缓存机制

### 第三阶段：扩展功能（1周）
1. 实现配置导入导出
2. 实现配置模板功能
3. 性能优化和监控
4. 完善测试和文档

这个用户配置管理功能设计将提供个性化、安全、高效的用户配置管理能力，显著提升用户体验。 