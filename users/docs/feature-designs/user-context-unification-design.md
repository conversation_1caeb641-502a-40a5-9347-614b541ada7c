# 用户上下文统一架构功能设计

## 概述

用户上下文统一架构是确保系统内所有模块使用一致的用户认证和上下文管理机制的核心组件，提供统一的用户身份验证、权限管理和上下文传递功能。

## 🎯 设计目标

### 核心目标
- **统一认证机制**：所有模块使用相同的用户认证方式
- **一致上下文传递**：确保用户信息在整个请求生命周期中正确传递
- **简化集成**：为其他模块提供简单易用的用户上下文接口
- **高性能**：最小化上下文获取和传递的性能开销

### 架构目标
- **解耦合**：用户上下文与具体业务逻辑解耦
- **可扩展**：支持未来新的认证方式和上下文信息
- **向后兼容**：保持与现有代码的兼容性

## 🏗️ 架构设计

### 1. 用户上下文层次结构

```
HTTP请求 → 认证中间件 → 用户上下文中间件 → 业务处理器
                ↓              ↓
            用户身份验证    上下文信息注入
                ↓              ↓
            用户信息提取    上下文传递
```

### 2. 上下文信息模型

```go
// 用户上下文信息
type UserContext struct {
    UserID      int64  `json:"user_id"`
    Username    string `json:"username"`
    Email       string `json:"email"`
    Phone       string `json:"phone"`
    TenantID    int64  `json:"tenant_id"`
    TenantCode  string `json:"tenant_code"`
    Roles       []string `json:"roles"`
    Permissions []string `json:"permissions"`
    LoginTime   time.Time `json:"login_time"`
    SessionID   string `json:"session_id"`
    IPAddress   string `json:"ip_address"`
    UserAgent   string `json:"user_agent"`
}
```

## 🔧 核心功能设计

### 1. 用户上下文中间件

#### 1.1 基础用户上下文中间件
```go
// 用户上下文中间件
func UserContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 从请求中提取用户信息
        userInfo, err := extractUserFromRequest(c)
        if err != nil {
            // 对于不需要认证的接口，继续处理
            if isPublicEndpoint(c.Request.URL.Path) {
                c.Next()
                return
            }
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 2. 验证用户信息有效性
        if !isValidUser(userInfo) {
            commonErrors.Unauthorized(c, "用户信息无效")
            c.Abort()
            return
        }
        
        // 3. 将用户信息注入到上下文中
        ctx := context.WithValue(c.Request.Context(), "user_context", userInfo)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}
```

#### 1.2 用户上下文工具函数
```go
// 获取用户上下文
func GetUserContext(ctx context.Context) (*UserContext, error) {
    userCtx, ok := ctx.Value("user_context").(*UserContext)
    if !ok {
        return nil, errors.New("用户上下文不存在")
    }
    return userCtx, nil
}

// 获取用户ID
func GetUserID(ctx context.Context) (int64, error) {
    userCtx, err := GetUserContext(ctx)
    if err != nil {
        return 0, err
    }
    return userCtx.UserID, nil
}

// 获取租户ID
func GetTenantID(ctx context.Context) (int64, error) {
    userCtx, err := GetUserContext(ctx)
    if err != nil {
        return 0, err
    }
    return userCtx.TenantID, nil
}

// 检查用户是否有指定权限
func HasPermission(ctx context.Context, permission string) bool {
    userCtx, err := GetUserContext(ctx)
    if err != nil {
        return false
    }
    
    for _, p := range userCtx.Permissions {
        if p == permission {
            return true
        }
    }
    return false
}
```

### 2. 认证方式统一

#### 2.1 JWT认证支持
```go
// JWT认证中间件
func JWTAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 从请求头获取JWT token
        token := extractJWTFromRequest(c)
        if token == "" {
            commonErrors.Unauthorized(c, "缺少认证令牌")
            c.Abort()
            return
        }
        
        // 2. 验证JWT token
        claims, err := validateJWT(token)
        if err != nil {
            commonErrors.Unauthorized(c, "认证令牌无效")
            c.Abort()
            return
        }
        
        // 3. 构建用户上下文
        userContext := &UserContext{
            UserID:     claims.UserID,
            Username:   claims.Username,
            Email:      claims.Email,
            TenantID:   claims.TenantID,
            LoginTime:  claims.IssuedAt,
            SessionID:  claims.SessionID,
        }
        
        // 4. 注入到请求上下文
        ctx := context.WithValue(c.Request.Context(), "user_context", userContext)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}
```

#### 2.2 Session认证支持
```go
// Session认证中间件
func SessionAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 从session中获取用户信息
        session := sessions.Default(c)
        userID := session.Get("user_id")
        if userID == nil {
            commonErrors.Unauthorized(c, "用户未登录")
            c.Abort()
            return
        }
        
        // 2. 从数据库获取用户详细信息
        user, err := getUserByID(userID.(int64))
        if err != nil {
            commonErrors.Unauthorized(c, "用户信息无效")
            c.Abort()
            return
        }
        
        // 3. 构建用户上下文
        userContext := &UserContext{
            UserID:     user.ID,
            Username:   user.Username,
            Email:      user.Email,
            TenantID:   user.TenantID,
            LoginTime:  time.Now(),
            SessionID:  session.ID(),
        }
        
        // 4. 注入到请求上下文
        ctx := context.WithValue(c.Request.Context(), "user_context", userContext)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}
```

### 3. 上下文传递机制

#### 3.1 gRPC上下文传递
```go
// gRPC用户上下文拦截器
func UserContextInterceptor() grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
        // 1. 从gRPC元数据中提取用户信息
        md, ok := metadata.FromIncomingContext(ctx)
        if !ok {
            return handler(ctx, req)
        }
        
        // 2. 提取用户ID和租户ID
        userIDStrs := md.Get("user_id")
        tenantIDStrs := md.Get("tenant_id")
        
        if len(userIDStrs) > 0 && len(tenantIDStrs) > 0 {
            userID, _ := strconv.ParseInt(userIDStrs[0], 10, 64)
            tenantID, _ := strconv.ParseInt(tenantIDStrs[0], 10, 64)
            
            // 3. 构建用户上下文
            userContext := &UserContext{
                UserID:   userID,
                TenantID: tenantID,
            }
            
            // 4. 注入到gRPC上下文
            ctx = context.WithValue(ctx, "user_context", userContext)
        }
        
        return handler(ctx, req)
    }
}
```

#### 3.2 微服务间上下文传递
```go
// 微服务间用户上下文传递
func PropagateUserContext(ctx context.Context, headers map[string]string) context.Context {
    userCtx, err := GetUserContext(ctx)
    if err != nil {
        return ctx
    }
    
    // 将用户信息添加到请求头
    headers["X-User-ID"] = strconv.FormatInt(userCtx.UserID, 10)
    headers["X-Tenant-ID"] = strconv.FormatInt(userCtx.TenantID, 10)
    headers["X-Username"] = userCtx.Username
    
    return ctx
}
```

### 4. 上下文缓存机制

#### 4.1 用户信息缓存
```go
// 用户上下文缓存
type UserContextCache struct {
    cache    *cache.Cache
    duration time.Duration
}

// 缓存用户上下文
func (ucc *UserContextCache) CacheUserContext(userID int64, userContext *UserContext) {
    key := fmt.Sprintf("user_context_%d", userID)
    ucc.cache.Set(key, userContext, ucc.duration)
}

// 获取缓存的用户上下文
func (ucc *UserContextCache) GetCachedUserContext(userID int64) (*UserContext, bool) {
    key := fmt.Sprintf("user_context_%d", userID)
    if userCtx, found := ucc.cache.Get(key); found {
        return userCtx.(*UserContext), true
    }
    return nil, false
}
```

## 📋 迁移策略

### 1. 现有代码迁移

#### 1.1 Handler迁移示例
```go
// 迁移前
func (h *UserHandler) GetUser(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        commonErrors.Unauthorized(c, "用户未登录")
        return
    }
    // 业务逻辑...
}

// 迁移后
func (h *UserHandler) GetUser(c *gin.Context) {
    userInfo, err := usercontext.RequireAuth(c.Request.Context())
    if err != nil {
        commonErrors.Unauthorized(c, "用户未登录")
        return
    }
    // 业务逻辑...
}
```

#### 1.2 服务层迁移示例
```go
// 迁移前
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    // 从ctx中手动提取用户信息
    userID := ctx.Value("user_id").(int64)
    // 业务逻辑...
}

// 迁移后
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    userInfo, err := usercontext.RequireAuth(ctx)
    if err != nil {
        return err
    }
    // 业务逻辑...
}
```

### 2. 渐进式迁移计划

#### 2.1 第一阶段：基础设施
1. 实现用户上下文中间件
2. 添加用户上下文工具函数
3. 创建迁移指南和示例

#### 2.2 第二阶段：核心模块迁移
1. 迁移用户管理模块
2. 迁移认证模块
3. 迁移权限模块

#### 2.3 第三阶段：业务模块迁移
1. 迁移其他业务模块
2. 更新测试用例
3. 性能优化

## 🔄 路由配置

### 1. 全局中间件配置
```go
// 主路由配置
func SetupRoutes(r *gin.Engine) {
    // 全局中间件
    r.Use(middleware.UserContextMiddleware())
    r.Use(middleware.JWTAuthMiddleware())
    
    // API路由
    api := r.Group("/api")
    {
        // 用户管理路由
        userRoutes := api.Group("/user")
        {
            userRoutes.POST("/create", userHandler.CreateUser)
            userRoutes.POST("/list", userHandler.ListUsers)
            userRoutes.POST("/get", userHandler.GetUser)
            userRoutes.POST("/update", userHandler.UpdateUser)
            userRoutes.POST("/delete", userHandler.DeleteUser)
        }
        
        // 其他路由...
    }
}
```

### 2. 条件认证路由
```go
// 支持公开和私有接口的路由配置
func SetupConditionalAuthRoutes(r *gin.Engine) {
    // 公开接口（不需要认证）
    public := r.Group("/api/public")
    {
        public.POST("/login", authHandler.Login)
        public.POST("/register", authHandler.Register)
        public.GET("/health", healthHandler.HealthCheck)
    }
    
    // 私有接口（需要认证）
    private := r.Group("/api")
    private.Use(middleware.UserContextMiddleware())
    {
        private.POST("/user/profile", userHandler.GetProfile)
        private.POST("/user/update", userHandler.UpdateProfile)
    }
}
```

## 📊 性能优化

### 1. 上下文缓存策略
```go
// 用户上下文缓存配置
type UserContextCacheConfig struct {
    Enabled     bool          `json:"enabled"`
    Duration    time.Duration `json:"duration"`
    MaxSize     int           `json:"max_size"`
    CleanupInterval time.Duration `json:"cleanup_interval"`
}

// 默认配置
var DefaultUserContextCacheConfig = UserContextCacheConfig{
    Enabled:         true,
    Duration:        10 * time.Minute,
    MaxSize:         1000,
    CleanupInterval: 15 * time.Minute,
}
```

### 2. 上下文预加载
```go
// 用户登录时预加载上下文
func PreloadUserContext(userID int64) {
    userContext := buildUserContext(userID)
    userContextCache.CacheUserContext(userID, userContext)
}
```

## 🔍 监控和调试

### 1. 上下文使用统计
```go
// 用户上下文使用统计
type UserContextUsageStats struct {
    UserID        int64     `json:"user_id"`
    AccessCount   int       `json:"access_count"`
    LastAccess    time.Time `json:"last_access"`
    CacheHitRate  float64   `json:"cache_hit_rate"`
    ErrorCount    int       `json:"error_count"`
}
```

### 2. 上下文调试工具
```go
// 调试中间件
func DebugUserContext() gin.HandlerFunc {
    return func(c *gin.Context) {
        userCtx, err := GetUserContext(c.Request.Context())
        if err == nil {
            log.Printf("User Context: %+v", userCtx)
        }
        c.Next()
    }
}
```

## 🚀 实施计划

### 第一阶段：基础架构（1-2周）
1. 实现用户上下文中间件
2. 添加用户上下文工具函数
3. 创建JWT和Session认证支持
4. 实现基础缓存机制

### 第二阶段：核心模块迁移（2-3周）
1. 迁移用户管理模块
2. 迁移认证模块
3. 迁移权限模块
4. 更新路由配置

### 第三阶段：业务模块迁移（2-3周）
1. 迁移其他业务模块
2. 更新测试用例
3. 性能优化和监控
4. 文档更新

## 📝 测试策略

### 1. 单元测试
```go
func TestUserContextMiddleware(t *testing.T) {
    // 测试用户上下文中间件
    // 测试用户上下文工具函数
    // 测试认证方式
}
```

### 2. 集成测试
```go
func TestUserContextIntegration(t *testing.T) {
    // 测试完整的用户上下文流程
    // 测试上下文传递机制
    // 测试缓存机制
}
```

### 3. 性能测试
```go
func TestUserContextPerformance(t *testing.T) {
    // 测试用户上下文获取性能
    // 测试缓存机制效果
    // 测试并发访问
}
```

这个用户上下文统一架构设计将确保系统内所有模块使用一致的用户认证和上下文管理机制，提供统一、高效、安全的用户上下文服务。 