# Users业务系统领域对象设计

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025年1月
- **文档类型**: DDD领域对象设计文档
- **适用范围**: Users业务系统聚合、实体、值对象设计

---

## 1. 用户身份认证上下文

### 1.1 User聚合（聚合根）

#### 聚合结构
```
User聚合根
├── 实体
│   └── User（聚合根实体）
├── 值对象
│   ├── UserID
│   ├── Username
│   ├── Email
│   ├── Password
│   ├── UserStatus
│   ├── RealName
│   ├── Phone
│   ├── Avatar
│   ├── TenantID
│   └── AppID
└── 领域服务
    ├── UserAuthenticationService
    ├── PasswordPolicyService
    ├── LoginLimitService
    └── MFAService
```

#### 核心实体定义

```go
// User聚合根 - 用户身份管理的核心
type User struct {
    // 基础标识
    id              UserID            // 用户唯一标识
    tenantID        TenantID          // 租户标识
    internalAppID   ApplicationID     // 应用标识
    
    // 身份信息
    username        Username          // 用户名(值对象)
    email           Email            // 邮箱(值对象) 
    phone           PhoneNumber      // 手机号(值对象)
    realName        string           // 真实姓名
    avatar          AvatarURL        // 头像URL
    
    // 账户状态
    status          UserStatus       // 用户状态(值对象)
    accountStatus   AccountStatus    // 账户状态(值对象)
    
    // 安全相关
    password        Password         // 密码(值对象)
    mfaConfig       MFAConfig       // 多因子认证配置(值对象)
    
    // 登录控制
    failedLoginAttempts  int         // 失败登录次数
    lastLoginAt         *time.Time   // 上次登录时间
    lastLoginIP         IPAddress    // 上次登录IP
    accountLockedUntil  *time.Time   // 账户锁定到期时间
    
    // 组织关系 (引用其他聚合)
    departmentID    *DepartmentID    // 部门ID(可选)
    positionID      *PositionID      // 职位ID(可选)
    managerID       *UserID          // 直接主管ID(可选)
    
    // 审计信息
    createdAt       time.Time
    createdBy       UserID
    updatedAt       time.Time
    updatedBy       UserID
    deletedAt       *time.Time
    
    // 领域事件
    domainEvents    []DomainEvent
}

// 核心业务方法
func (u *User) Activate() error {
    if u.status == UserStatusActive {
        return ErrUserAlreadyActive
    }
    
    u.status = UserStatusActive
    u.recordEvent(NewUserActivatedEvent(u.id, u.tenantID))
    return nil
}

func (u *User) Authenticate(password string, policy PasswordPolicy) error {
    if !u.status.CanLogin() {
        return NewUserCannotLoginError(u.id, u.status)
    }
    
    if u.IsLocked() {
        return NewUserLockedError(u.id, u.lockReason)
    }
    
    if !u.password.Verify(password) {
        u.failedLoginAttempts++
        u.recordEvent(NewLoginFailedEvent(u.id, u.failedLoginAttempts))
        
        if u.failedLoginAttempts >= policy.MaxFailedAttempts() {
            u.Lock("Too many failed login attempts", policy.LockDuration())
            u.recordEvent(NewUserLockedEvent(u.id, u.lockReason))
        }
        
        return NewInvalidPasswordError()
    }
    
    // 认证成功
    u.failedLoginAttempts = 0
    u.lastLoginAt = &time.Time{}
    u.recordEvent(NewUserLoggedInEvent(u.id, u.tenantID))
    
    return nil
}
```

#### 值对象定义

```go
// UserID - 用户唯一标识
type UserID struct {
    value int64
}

func NewUserID(value int64) (UserID, error) {
    if value <= 0 {
        return UserID{}, ErrInvalidUserID
    }
    return UserID{value: value}, nil
}

func (id UserID) Value() int64 {
    return id.value
}

// Username - 用户名值对象
type Username struct {
    value string
}

func NewUsername(value string) (Username, error) {
    if len(value) < 3 || len(value) > 50 {
        return Username{}, ErrInvalidUsername
    }
    
    if !usernameRegex.MatchString(value) {
        return Username{}, ErrInvalidUsernameFormat
    }
    
    return Username{value: strings.ToLower(value)}, nil
}

// Email - 邮箱值对象
type Email struct {
    value string
}

func NewEmail(value string) (Email, error) {
    if !emailRegex.MatchString(value) {
        return Email{}, ErrInvalidEmail
    }
    
    return Email{value: strings.ToLower(value)}, nil
}

// Password - 密码值对象
type Password struct {
    hashedValue string
    salt        string
    algorithm   string
}

func NewPassword(plainPassword string, policy PasswordPolicy) (Password, error) {
    if !policy.Validate(plainPassword) {
        return Password{}, NewPasswordPolicyViolationError()
    }
    
    salt := generateSalt()
    hashedValue := hashPassword(plainPassword, salt)
    
    return Password{
        hashedValue: hashedValue,
        salt:        salt,
        algorithm:   "bcrypt",
    }, nil
}

func (p Password) Verify(plainPassword string) bool {
    return verifyPassword(plainPassword, p.hashedValue, p.salt)
}
```

### 1.2 领域服务

```go
// UserAuthenticationService - 用户认证领域服务
type UserAuthenticationService struct {
    userRepo        UserRepository
    passwordPolicy  PasswordPolicy
    loginLimit      LoginLimitPolicy
    mfaService      MFAService
    eventBus        EventBus
}

func (s *UserAuthenticationService) AuthenticateUser(ctx context.Context, username string, password string, mfaCode *string) (*User, error) {
    // 1. 查找用户
    user, err := s.userRepo.FindByUsername(ctx, username)
    if err != nil {
        return nil, err
    }
    
    // 2. 验证密码
    if err := user.Authenticate(password, s.passwordPolicy); err != nil {
        return nil, err
    }
    
    // 3. 验证MFA（如果启用）
    if user.HasMFAEnabled() {
        if mfaCode == nil {
            return nil, NewMFARequiredError()
        }
        
        if !s.mfaService.Verify(user.GetMFAConfig(), *mfaCode) {
            return nil, NewInvalidMFACodeError()
        }
    }
    
    // 4. 检查登录限制
    if s.loginLimit.IsExceeded(user.ID) {
        return nil, NewLoginLimitExceededError()
    }
    
    // 5. 更新登录信息
    user.UpdateLastLogin(getClientIP(ctx))
    
    // 6. 保存用户状态
    if err := s.userRepo.Update(ctx, user); err != nil {
        return nil, err
    }
    
    // 7. 发布登录事件
    s.eventBus.Publish(NewUserLoggedInEvent(user.ID, user.TenantID))
    
    return user, nil
}
```

---

## 2. 组织架构管理上下文

### 2.1 Department聚合（聚合根）

#### 聚合结构
```
Department聚合根
├── 实体
│   └── Department（聚合根实体）
├── 值对象
│   ├── DepartmentID
│   ├── DepartmentName
│   ├── DepartmentCode
│   ├── DepartmentLevel
│   └── DepartmentPath
└── 领域服务
    ├── DepartmentHierarchyService
    └── DepartmentValidationService
```

#### 核心实体定义

```go
// Department聚合根
type Department struct {
    // 基础标识
    id          DepartmentID
    tenantID    TenantID
    appID       ApplicationID
    
    // 基本信息
    name        DepartmentName
    code        DepartmentCode
    description string
    
    // 层级关系
    parentID    *DepartmentID
    level       DepartmentLevel
    path        DepartmentPath
    
    // 组织信息
    managerID   *UserID
    sortOrder   int
    
    // 状态信息
    status      DepartmentStatus
    isSystem    bool
    
    // 审计信息
    createdAt   time.Time
    createdBy   UserID
    updatedAt   time.Time
    updatedBy   UserID
    deletedAt   *time.Time
    
    // 领域事件
    domainEvents []DomainEvent
}

// 核心业务方法
func (d *Department) MoveToParent(newParentID *DepartmentID, hierarchyService DepartmentHierarchyService) error {
    // 验证新父部门存在
    if newParentID != nil {
        if !hierarchyService.DepartmentExists(*newParentID) {
            return NewDepartmentNotFoundError(*newParentID)
        }
        
        // 检查是否形成循环引用
        if hierarchyService.WouldCreateCycle(d.id, *newParentID) {
            return NewCircularReferenceError()
        }
    }
    
    // 更新父部门
    oldParentID := d.parentID
    d.parentID = newParentID
    
    // 重新计算层级和路径
    d.recalculateHierarchy(hierarchyService)
    
    // 记录事件
    d.recordEvent(NewDepartmentMovedEvent(d.id, oldParentID, newParentID))
    
    return nil
}
```

---

## 3. 权限访问控制上下文

### 3.1 Role聚合（聚合根）

#### 聚合结构
```
Role聚合根
├── 实体
│   ├── Role（聚合根实体）
│   └── Permission（实体）
├── 值对象
│   ├── RoleID
│   ├── RoleName
│   ├── RoleCode
│   ├── PermissionID
│   ├── PermissionName
│   └── ResourcePath
└── 领域服务
    ├── RolePermissionService
    └── PermissionValidationService
```

#### 核心实体定义

```go
// Role聚合根
type Role struct {
    // 基础标识
    id          RoleID
    tenantID    TenantID
    appID       ApplicationID
    
    // 基本信息
    name        RoleName
    code        RoleCode
    description string
    
    // 权限信息
    permissions []Permission
    
    // 继承关系
    parentRoleID *RoleID
    
    // 状态信息
    status      RoleStatus
    isSystem    bool
    
    // 审计信息
    createdAt   time.Time
    createdBy   UserID
    updatedAt   time.Time
    updatedBy   UserID
    deletedAt   *time.Time
    
    // 领域事件
    domainEvents []DomainEvent
}

// 核心业务方法
func (r *Role) AssignPermission(permission Permission) error {
    // 检查权限是否已存在
    for _, existing := range r.permissions {
        if existing.ID().Equals(permission.ID()) {
            return NewPermissionAlreadyAssignedError(permission.ID())
        }
    }
    
    r.permissions = append(r.permissions, permission)
    r.recordEvent(NewPermissionAssignedEvent(r.id, permission.ID()))
    
    return nil
}

func (r *Role) HasPermission(resourcePath ResourcePath, action Action) bool {
    for _, permission := range r.permissions {
        if permission.Matches(resourcePath, action) {
            return true
        }
    }
    
    return false
}
```

---

## 4. 资源管理上下文

### 4.1 Resource聚合（聚合根）

#### 聚合结构
```
Resource聚合根
├── 实体
│   └── Resource（聚合根实体）
├── 值对象
│   ├── ResourceID
│   ├── ResourceName
│   ├── ResourceType
│   ├── ResourcePath
│   └── ResourceLevel
└── 领域服务
    ├── ResourceHierarchyService
    └── ResourcePermissionService
```

#### 核心实体定义

```go
// Resource聚合根
type Resource struct {
    // 基础标识
    id          ResourceID
    tenantID    TenantID
    appID       ApplicationID
    
    // 基本信息
    name        ResourceName
    displayName string
    description string
    
    // 资源信息
    resourceType ResourceType
    path         ResourcePath
    method       HTTPMethod
    
    // 层级关系
    parentID    *ResourceID
    level       ResourceLevel
    path        ResourcePath
    
    // 权限信息
    isPublic    bool
    publicLevel PublicLevel
    
    // 状态信息
    status      ResourceStatus
    isSystem    bool
    isUniversal bool
    
    // 审计信息
    createdAt   time.Time
    createdBy   UserID
    updatedAt   time.Time
    updatedBy   UserID
    deletedAt   *time.Time
    
    // 领域事件
    domainEvents []DomainEvent
}

// 核心业务方法
func (r *Resource) SetPublicAccess(isPublic bool, level PublicLevel) {
    r.isPublic = isPublic
    r.publicLevel = level
    r.recordEvent(NewResourcePublicAccessChangedEvent(r.id, isPublic, level))
}
```

---

## 5. 租户管理上下文

### 5.1 Tenant聚合（聚合根）

#### 聚合结构
```
Tenant聚合根
├── 实体
│   └── Tenant（聚合根实体）
├── 值对象
│   ├── TenantID
│   ├── TenantCode
│   ├── TenantName
│   └── SubscriptionPlan
└── 领域服务
    ├── TenantConfigurationService
    └── TenantQuotaService
```

#### 核心实体定义

```go
// Tenant聚合根
type Tenant struct {
    // 基础标识
    id          TenantID
    code        TenantCode
    name        TenantName
    
    // 基本信息
    description string
    domain      string
    
    // 订阅信息
    subscriptionPlan SubscriptionPlan
    subscriptionStatus SubscriptionStatus
    validFrom        time.Time
    validUntil       *time.Time
    
    // 配置信息
    config      TenantConfig
    
    // 配额信息
    quotas      ResourceQuotas
    
    // 状态信息
    status      TenantStatus
    isActive    bool
    
    // 审计信息
    createdAt   time.Time
    createdBy   UserID
    updatedAt   time.Time
    updatedBy   UserID
    deletedAt   *time.Time
    
    // 领域事件
    domainEvents []DomainEvent
}

// 核心业务方法
func (t *Tenant) Activate() error {
    if t.status == TenantStatusActive {
        return ErrTenantAlreadyActive
    }
    
    t.status = TenantStatusActive
    t.isActive = true
    t.recordEvent(NewTenantActivatedEvent(t.id))
    
    return nil
}

func (t *Tenant) CheckQuota(resourceType ResourceType, amount int) bool {
    quota := t.quotas.GetQuota(resourceType)
    return quota.HasCapacity(amount)
}
```

---

## 6. 总结

通过领域对象设计，我们为Users业务系统的每个限界上下文定义了清晰的聚合、实体和值对象。这种设计具有以下特点：

1. **聚合边界清晰**：每个聚合都有明确的边界和职责
2. **业务规则封装**：业务规则封装在聚合内部，确保一致性
3. **值对象不可变**：值对象保证数据的一致性和不可变性
4. **领域事件驱动**：通过领域事件实现聚合间的松耦合通信
5. **领域服务协调**：复杂的业务逻辑通过领域服务协调

这种设计为后续的应用服务层和基础设施层提供了坚实的基础，确保系统的可维护性、可扩展性和业务一致性。
