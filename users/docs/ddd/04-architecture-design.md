# Users业务系统架构设计

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025年1月
- **文档类型**: DDD架构设计文档
- **适用范围**: Users业务系统技术架构和实施方案

---

## 1. 整体架构设计

### 1.1 DDD分层架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                             表现层 (Presentation Layer)                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│  HTTP API Controllers │ gRPC Services │ Web Interface │ CLI Tools │ Event Bus │
├─────────────────────────────────────────────────────────────────────────────────┤
│                             应用层 (Application Layer)                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│  Application Services │ DTOs │ Command/Query Handlers │ Event Handlers │      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                             领域层 (Domain Layer)                             │
├─────────────────────────────────────────────────────────────────────────────────┤
│  Aggregates │ Entities │ Value Objects │ Domain Services │ Repository Interfaces │
├─────────────────────────────────────────────────────────────────────────────────┤
│                          基础设施层 (Infrastructure Layer)                     │
├─────────────────────────────────────────────────────────────────────────────────┤
│  Repository Impl │ External APIs │ Message Queue │ Database │ Cache │ Storage │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 限界上下文架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Users 业务域                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                   │
│  │  用户身份认证    │  │   组织架构管理   │  │   权限访问控制   │                   │
│  │     上下文      │  │     上下文      │  │     上下文      │                   │
│  │                │  │                │  │                │                   │
│  │ • User聚合      │  │ • Department聚合│  │ • Role聚合      │                   │
│  │ • Auth服务      │  │ • Position聚合  │  │ • Permission聚合│                   │
│  │ • 认证服务      │  │ • 组织服务      │  │ • 权限服务      │                   │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                   │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   资源管理      │  │    租户管理     │  │   第三方集成    │                   │
│  │     上下文      │  │     上下文      │  │     上下文      │                   │
│  │                │  │                │  │                │                   │
│  │ • Resource聚合  │  │ • Tenant聚合    │  │ • OAuth服务     │                   │
│  │ • 资源服务      │  │ • 配置服务      │  │ • 身份映射      │                   │
│  │ • 权限控制      │  │ • 配额服务      │  │ • 安全策略      │                   │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                   │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   应用管理      │  │   验证通知      │  │   文件上传      │                   │
│  │     上下文      │  │     上下文      │  │     上下文      │                   │
│  │                │  │                │  │                │                   │
│  │ • Application聚合│  │ • 验证服务      │  │ • File聚合      │                   │
│  │ • 应用服务      │  │ • 通知服务      │  │ • 存储服务      │                   │
│  │ • 隔离服务      │  │ • 模板服务      │  │ • 权限控制      │                   │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                   │
│                                                                                 │
│  ┌─────────────────┐                                                           │
│  │   ID生成器      │                                                           │
│  │     上下文      │                                                           │
│  │                │                                                           │
│  │ • ID生成服务    │                                                           │
│  │ • 序列管理      │                                                           │
│  │ • 分布式协调    │                                                           │
│  └─────────────────┘                                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

---

## 2. 技术架构设计

### 2.1 技术栈选择

#### 后端技术栈
```yaml
编程语言: Go 1.21+
框架: 
  - Gin (HTTP框架)
  - gRPC (微服务通信)
  - GORM (ORM框架)
  - Wire (依赖注入)

数据库:
  - MySQL 8.0+ (主数据库)
  - Redis 7.0+ (缓存和会话)
  - MongoDB (可选，日志存储)

消息队列:
  - RabbitMQ (事件总线)
  - Redis Streams (轻量级消息)

监控和日志:
  - Prometheus (指标监控)
  - Jaeger (链路追踪)
  - ELK Stack (日志管理)

安全:
  - JWT (身份认证)
  - bcrypt (密码加密)
  - HTTPS/TLS (传输加密)
```

#### 前端技术栈
```yaml
框架: React 18+ + TypeScript
UI库: Ant Design 5.x
状态管理: Zustand
路由: React Router v6
HTTP客户端: Axios
构建工具: Vite
```

### 2.2 数据库设计

#### 数据库架构
```sql
-- 用户身份认证上下文
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    real_name VARCHAR(100),
    avatar VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    password VARCHAR(255) NOT NULL,
    password_changed_at TIMESTAMP,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    login_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    locked_at TIMESTAMP,
    locked_until TIMESTAMP,
    lock_reason VARCHAR(200),
    department_id BIGINT,
    position_id BIGINT,
    employee_id VARCHAR(50),
    hire_date DATE,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_department (department_id),
    INDEX idx_position (position_id)
);

-- 组织架构管理上下文
CREATE TABLE departments (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    level INT NOT NULL DEFAULT 1,
    path VARCHAR(500),
    manager_id BIGINT,
    sort_order INT DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_parent (parent_id),
    INDEX idx_path (path),
    UNIQUE KEY uk_tenant_code (tenant_id, code)
);

CREATE TABLE positions (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    level INT NOT NULL DEFAULT 1,
    category VARCHAR(50),
    default_role_ids JSON,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    UNIQUE KEY uk_tenant_code (tenant_id, code)
);

-- 权限访问控制上下文
CREATE TABLE roles (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    parent_role_id BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_parent_role (parent_role_id),
    UNIQUE KEY uk_tenant_code (tenant_id, code)
);

CREATE TABLE permissions (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    resource_path VARCHAR(500) NOT NULL,
    action VARCHAR(50) NOT NULL,
    scope VARCHAR(20) NOT NULL DEFAULT 'self',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_resource_action (resource_path, action),
    UNIQUE KEY uk_tenant_code (tenant_id, code)
);

CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_role (role_id),
    INDEX idx_permission (permission_id),
    UNIQUE KEY uk_role_permission (role_id, permission_id)
);

CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    scope VARCHAR(20) NOT NULL DEFAULT 'self',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT NOT NULL,
    valid_from TIMESTAMP NULL,
    valid_until TIMESTAMP NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_user (user_id),
    INDEX idx_role (role_id),
    INDEX idx_validity (valid_from, valid_until),
    UNIQUE KEY uk_user_role (user_id, role_id)
);

-- 资源管理上下文
CREATE TABLE resources (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    internal_app_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    resource_type VARCHAR(20) NOT NULL,
    path VARCHAR(500) NOT NULL,
    method VARCHAR(10),
    parent_id BIGINT,
    level INT NOT NULL DEFAULT 1,
    full_path VARCHAR(500),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    is_universal BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    public_level VARCHAR(20),
    assignable BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_tenant_app (tenant_id, internal_app_id),
    INDEX idx_parent (parent_id),
    INDEX idx_path (path),
    INDEX idx_type (resource_type),
    UNIQUE KEY uk_tenant_path (tenant_id, path)
);

-- 租户管理上下文
CREATE TABLE tenants (
    id BIGINT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    domain VARCHAR(100),
    subscription_plan VARCHAR(50) NOT NULL,
    subscription_status VARCHAR(20) NOT NULL DEFAULT 'active',
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NULL,
    config JSON,
    quotas JSON,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_code (code),
    INDEX idx_status (status)
);
```

### 2.3 缓存策略

#### Redis缓存设计
```yaml
缓存层级:
  L1 - 本地缓存 (内存):
    - 用户会话信息
    - 权限验证结果
    - 配置信息
  
  L2 - Redis缓存:
    - 用户信息缓存
    - 权限缓存
    - 组织架构缓存
    - 资源缓存
    - 会话存储

缓存策略:
  用户信息缓存:
    - 键格式: user:{tenant_id}:{user_id}
    - TTL: 30分钟
    - 更新策略: 写时更新
  
  权限缓存:
    - 键格式: permissions:{tenant_id}:{user_id}
    - TTL: 15分钟
    - 更新策略: 权限变更时清除
  
  组织架构缓存:
    - 键格式: dept_tree:{tenant_id}
    - TTL: 1小时
    - 更新策略: 组织变更时清除
  
  会话缓存:
    - 键格式: session:{session_id}
    - TTL: 24小时
    - 更新策略: 滑动过期
```

### 2.4 消息队列设计

#### 事件总线架构
```yaml
事件类型:
  用户事件:
    - UserCreatedEvent
    - UserUpdatedEvent
    - UserDeletedEvent
    - UserLoggedInEvent
    - UserLockedEvent
  
  权限事件:
    - PermissionAssignedEvent
    - PermissionRevokedEvent
    - RoleCreatedEvent
    - RoleUpdatedEvent
  
  组织事件:
    - DepartmentCreatedEvent
    - DepartmentMovedEvent
    - UserDepartmentChangedEvent
  
  租户事件:
    - TenantCreatedEvent
    - TenantConfigUpdatedEvent
    - TenantStatusChangedEvent

消息队列配置:
  交换机类型: Topic
  路由键格式: {context}.{event_type}
  持久化: 启用
  死信队列: 启用
  重试策略: 指数退避
```

---

## 3. 安全架构设计

### 3.1 认证架构

#### JWT认证流程
```yaml
认证流程:
  1. 用户登录:
     - 验证用户名密码
     - 验证MFA (如果启用)
     - 生成JWT令牌
     - 返回令牌和用户信息
  
  2. 令牌验证:
     - 解析JWT令牌
     - 验证签名
     - 检查过期时间
     - 验证用户状态
  
  3. 令牌刷新:
     - 验证刷新令牌
     - 生成新的访问令牌
     - 更新令牌过期时间

JWT配置:
  访问令牌:
    - 算法: RS256
    - 过期时间: 2小时
    - 包含信息: 用户ID、租户ID、角色列表
  
  刷新令牌:
    - 算法: RS256
    - 过期时间: 7天
    - 包含信息: 用户ID、令牌类型
```

#### 多因子认证
```yaml
MFA配置:
  支持方式:
    - TOTP (基于时间的一次性密码)
    - SMS验证码
    - 邮件验证码
  
  配置选项:
    - 强制启用MFA
    - MFA信任设备
    - 备用验证方式
  
  安全策略:
    - 验证码有效期: 5分钟
    - 重试次数限制: 3次
    - 锁定时间: 15分钟
```

### 3.2 授权架构

#### RBAC权限模型
```yaml
权限模型:
  角色 (Role):
    - 系统角色: 预定义的系统角色
    - 自定义角色: 用户创建的角色
    - 继承关系: 支持角色继承
  
  权限 (Permission):
    - 资源权限: 对特定资源的操作权限
    - 数据权限: 对数据范围的访问权限
    - 功能权限: 对特定功能的访问权限
  
  用户角色分配:
    - 直接分配: 直接为用户分配角色
    - 继承分配: 通过组织架构继承角色
    - 临时分配: 有时间限制的角色分配

权限验证:
  验证流程:
    1. 获取用户角色
    2. 计算有效权限
    3. 检查资源权限
    4. 验证数据权限
    5. 返回验证结果
  
  缓存策略:
    - 权限缓存: 15分钟
    - 角色缓存: 30分钟
    - 用户权限缓存: 10分钟
```

### 3.3 数据安全

#### 数据加密
```yaml
加密策略:
  传输加密:
    - HTTPS/TLS 1.3
    - 证书管理
    - 强制HTTPS重定向
  
  存储加密:
    - 密码哈希: bcrypt
    - 敏感数据: AES-256
    - 数据库加密: 透明数据加密
  
  密钥管理:
    - 密钥轮换策略
    - 密钥存储: HSM或安全存储
    - 密钥备份和恢复
```

#### 数据隔离
```yaml
多租户隔离:
  数据隔离策略:
    - 数据库级别: 每个租户独立数据库
    - Schema级别: 每个租户独立Schema
    - 行级别: 通过tenant_id字段隔离
  
  资源隔离:
    - 计算资源: 容器或虚拟机隔离
    - 存储资源: 独立存储空间
    - 网络资源: 网络隔离
  
  应用隔离:
    - 应用实例: 独立部署
    - 配置隔离: 租户特定配置
    - 缓存隔离: 租户特定缓存
```

---

## 4. 性能架构设计

### 4.1 性能优化策略

#### 数据库优化
```yaml
索引策略:
  主键索引:
    - 所有表使用BIGINT主键
    - 自增或雪花算法生成
  
  复合索引:
    - (tenant_id, internal_app_id): 多租户查询
    - (parent_id, sort_order): 层级查询
    - (resource_path, method): 资源查询
  
  覆盖索引:
    - 常用查询字段组合
    - 减少回表查询

查询优化:
  分页查询:
    - 使用LIMIT和OFFSET
    - 大偏移量使用游标分页
  
  批量操作:
    - 批量插入和更新
    - 减少数据库连接数
  
  读写分离:
    - 主库写操作
    - 从库读操作
    - 读写一致性保证
```

#### 缓存优化
```yaml
缓存策略:
  热点数据缓存:
    - 用户信息: 30分钟TTL
    - 权限信息: 15分钟TTL
    - 组织架构: 1小时TTL
  
  缓存更新:
    - 写时更新: 数据变更时更新缓存
    - 定时刷新: 定期刷新缓存数据
    - 失效策略: 缓存失效时重新加载
  
  缓存穿透防护:
    - 布隆过滤器: 防止无效查询
    - 空值缓存: 缓存空结果
    - 限流保护: 防止恶意查询
```

### 4.2 扩展性设计

#### 水平扩展
```yaml
服务扩展:
  无状态设计:
    - 服务实例无状态
    - 会话信息外部存储
    - 配置信息集中管理
  
  负载均衡:
    - 应用层负载均衡
    - 数据库读写分离
    - 缓存集群部署
  
  自动扩缩容:
    - 基于CPU/内存指标
    - 基于请求量指标
    - 基于业务指标
```

#### 数据库扩展
```yaml
分库分表:
  分库策略:
    - 按租户分库: 大租户独立数据库
    - 按业务分库: 不同业务独立数据库
  
  分表策略:
    - 按时间分表: 历史数据分表
    - 按ID分表: 大表按ID范围分表
  
  数据迁移:
    - 在线迁移: 不停机数据迁移
    - 增量同步: 实时数据同步
    - 一致性保证: 迁移过程数据一致性
```

---

## 5. 监控和运维

### 5.1 监控体系

#### 应用监控
```yaml
指标监控:
  业务指标:
    - 用户注册数
    - 登录成功率
    - 权限验证次数
    - 系统响应时间
  
  技术指标:
    - CPU使用率
    - 内存使用率
    - 数据库连接数
    - 缓存命中率
  
  告警规则:
    - 错误率 > 5%
    - 响应时间 > 2秒
    - 可用性 < 99.9%
    - 磁盘使用率 > 80%
```

#### 日志管理
```yaml
日志策略:
  日志级别:
    - ERROR: 错误信息
    - WARN: 警告信息
    - INFO: 一般信息
    - DEBUG: 调试信息
  
  日志格式:
    - 结构化JSON格式
    - 包含时间戳、级别、消息
    - 包含请求ID、用户ID、租户ID
  
  日志存储:
    - 本地日志文件
    - 集中日志系统
    - 日志分析和告警
```

### 5.2 部署架构

#### 容器化部署
```yaml
Docker配置:
  基础镜像:
    - Go应用: golang:1.21-alpine
    - 数据库: mysql:8.0
    - 缓存: redis:7.0-alpine
  
  容器编排:
    - Kubernetes集群
    - 服务发现和负载均衡
    - 自动扩缩容
    - 滚动更新
  
  环境配置:
    - 开发环境: 单机部署
    - 测试环境: 小规模集群
    - 生产环境: 大规模集群
```

#### CI/CD流程
```yaml
持续集成:
  代码检查:
    - 静态代码分析
    - 单元测试覆盖
    - 安全漏洞扫描
  
  构建流程:
    - 代码编译
    - 镜像构建
    - 自动化测试
  
  部署流程:
    - 蓝绿部署
    - 金丝雀发布
    - 回滚机制
```

---

## 6. 总结

通过架构设计，我们为Users业务系统建立了完整的技术架构体系：

1. **分层架构清晰**：DDD四层架构，职责明确
2. **限界上下文隔离**：10个业务上下文，独立演进
3. **技术栈现代化**：Go + React + MySQL + Redis
4. **安全架构完善**：JWT + RBAC + 数据加密
5. **性能优化全面**：缓存 + 索引 + 读写分离
6. **监控运维完善**：指标监控 + 日志管理 + 容器化部署

这种架构设计确保了系统的：
- **可扩展性**：支持业务快速扩展
- **可维护性**：模块化设计，便于维护
- **高性能**：多层优化，性能优异
- **高可用性**：容错设计，服务稳定
- **安全性**：多层次安全防护
- **可观测性**：完善的监控和日志体系

这种架构为系统的长期发展奠定了坚实的技术基础。
