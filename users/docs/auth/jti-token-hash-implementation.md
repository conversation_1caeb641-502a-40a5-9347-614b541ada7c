# JTI + 令牌哈希混合方案实现说明

## 概述

本文档描述了在用户认证系统中实现的 JTI (JWT ID) + 令牌哈希混合方案，该方案在保持 JWT 无状态特性的同时，提供了会话管理和令牌撤销能力。

## 架构设计

### 核心组件

1. **JWT Service**: 负责生成包含 JTI 的 JWT 令牌
2. **Auth Session Entity**: 存储 JTI 和令牌哈希的会话实体
3. **Auth Repository**: 提供基于 JTI 和令牌哈希的查询方法
4. **Database Schema**: 支持 JTI 和令牌哈希的数据库表结构

### 数据流程

```
用户登录 → 生成JWT(含JTI) → 计算令牌哈希 → 存储会话(JTI+哈希) → 返回令牌
令牌验证 → 解析JTI → 查询会话 → 验证哈希 → 检查状态
令牌刷新 → 验证JTI → 生成新令牌 → 更新会话 → 返回新令牌
```

## 实现细节

### 1. 数据库表结构

```sql
CREATE TABLE `auth_sessions` (
  `id` varchar(36) NOT NULL COMMENT '会话ID，UUID格式',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `access_token` text COMMENT '访问令牌（向后兼容）',
  `refresh_token` text COMMENT '刷新令牌（向后兼容）',
  `jti` varchar(64) NOT NULL COMMENT 'JWT ID，用于令牌撤销',
  `access_token_hash` varchar(64) COMMENT '访问令牌哈希',
  `refresh_token_hash` varchar(64) COMMENT '刷新令牌哈希',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '会话状态',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_auth_sessions_jti` (`jti`),
  KEY `idx_auth_sessions_access_token_hash` (`access_token_hash`),
  KEY `idx_auth_sessions_refresh_token_hash` (`refresh_token_hash`)
);
```

### 2. JWT Service 增强

#### 新增方法

```go
// 生成访问令牌并返回完整信息
func (j *JWTService) GenerateAccessTokenWithInfo(userID, tenantID int64, username string) (*TokenInfo, error)

// 生成刷新令牌并返回完整信息
func (j *JWTService) GenerateRefreshTokenWithInfo(userID, tenantID int64) (*TokenInfo, error)

// 计算令牌哈希
func (j *JWTService) CalculateTokenHash(token string) string

// 获取令牌的JTI
func (j *JWTService) GetTokenJTI(tokenString string) (string, error)
```

#### TokenInfo 结构

```go
type TokenInfo struct {
    Token     string    `json:"token"`      // JWT令牌字符串
    JTI       string    `json:"jti"`        // JWT ID
    TokenHash string    `json:"token_hash"` // 令牌哈希
    ExpiresAt time.Time `json:"expires_at"` // 过期时间
}
```

### 3. Auth Session Entity 增强

#### 新增字段

```go
type AuthSession struct {
    // 现有字段...
    JTI              string `json:"jti" gorm:"type:varchar(64);not null;uniqueIndex"`
    AccessTokenHash  string `json:"access_token_hash" gorm:"type:varchar(64);index"`
    RefreshTokenHash string `json:"refresh_token_hash" gorm:"type:varchar(64);index"`
}
```

#### 新增方法

```go
// 创建使用JTI的会话
func NewAuthSessionWithJTI(userID, tenantID int64, jti, accessTokenHash, refreshTokenHash string, deviceInfo *value_object.DeviceInfo, expiresAt time.Time) *AuthSession

// 使用JTI刷新会话
func (s *AuthSession) RefreshWithJTI(jti, accessTokenHash, refreshTokenHash string, expiresAt time.Time)

// JTI相关方法
func (s *AuthSession) GetJTI() string
func (s *AuthSession) SetJTI(jti string)
func (s *AuthSession) GetAccessTokenHash() string
func (s *AuthSession) SetAccessTokenHash(hash string)
func (s *AuthSession) GetRefreshTokenHash() string
func (s *AuthSession) SetRefreshTokenHash(hash string)
```

### 4. Auth Repository 增强

#### 新增查询方法

```go
// 根据JTI查找会话
func (r *AuthRepositoryImpl) FindSessionByJTI(ctx context.Context, jti string) (*entity.AuthSession, error)

// 根据访问令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByAccessTokenHash(ctx context.Context, accessTokenHash string) (*entity.AuthSession, error)

// 根据刷新令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByRefreshTokenHash(ctx context.Context, refreshTokenHash string) (*entity.AuthSession, error)

// 根据JTI撤销会话
func (r *AuthRepositoryImpl) RevokeSessionByJTI(ctx context.Context, jti string) error
```

### 5. 认证应用服务更新

#### 登录流程

```go
func (s *AuthApplicationService) loginCoreHandlerWithJTI(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, int64, error) {
    // 1. 验证用户凭据
    // 2. 生成JWT令牌（包含JTI）
    accessTokenInfo, err := s.jwtService.GenerateAccessTokenWithInfo(user.ID, tenantID, user.Username)
    refreshTokenInfo, err := s.jwtService.GenerateRefreshTokenWithInfo(user.ID, tenantID)
    
    // 3. 创建会话（存储JTI和令牌哈希）
    session := authEntity.NewAuthSessionWithJTI(
        user.ID, tenantID, 
        accessTokenInfo.JTI,
        accessTokenInfo.TokenHash,
        refreshTokenInfo.TokenHash,
        deviceInfo, expiresAt,
    )
    
    // 4. 保存会话
    s.authRepo.CreateSession(ctx, session)
    
    // 5. 返回令牌
    return &dto.LoginResponseDTO{
        AccessToken:  accessTokenInfo.Token,
        RefreshToken: refreshTokenInfo.Token,
        // ...
    }, user.ID, nil
}
```

#### 令牌刷新流程

```go
func (s *AuthApplicationService) RefreshToken(ctx context.Context, refreshDTO *dto.RefreshTokenDTO) (*dto.LoginResponseDTO, error) {
    // 1. 解析刷新令牌，获取JTI
    claims, err := s.jwtService.ParseToken(refreshDTO.RefreshToken)
    
    // 2. 根据JTI查找会话
    session, err := s.authRepo.FindSessionByJTI(ctx, claims.ID)
    
    // 3. 验证会话状态
    if !session.IsActive() {
        return nil, userErrors.NewBusinessError(userErrors.CodeUserOperationNotAllowed, "session is not active")
    }
    
    // 4. 生成新令牌
    accessTokenInfo, err := s.jwtService.GenerateAccessTokenWithInfo(user.ID, user.TenantID, user.Username)
    refreshTokenInfo, err := s.jwtService.GenerateRefreshTokenWithInfo(user.ID, user.TenantID)
    
    // 5. 更新会话
    session.RefreshWithJTI(accessTokenInfo.JTI, accessTokenInfo.TokenHash, refreshTokenInfo.TokenHash, expiresAt)
    s.authRepo.UpdateSession(ctx, session)
    
    // 6. 返回新令牌
    return &dto.LoginResponseDTO{
        AccessToken:  accessTokenInfo.Token,
        RefreshToken: refreshTokenInfo.Token,
        // ...
    }, nil
}
```

## 安全特性

### 1. 令牌撤销

- **JTI唯一性**: 每个JWT令牌都有唯一的JTI，确保令牌可追踪
- **会话状态管理**: 通过数据库中的会话状态控制令牌有效性
- **实时撤销**: 支持立即撤销特定会话，无需等待令牌过期

### 2. 令牌验证

- **哈希验证**: 使用SHA256哈希验证令牌完整性
- **状态检查**: 验证会话状态和过期时间
- **JTI匹配**: 确保令牌JTI与数据库中的会话JTI一致

### 3. 安全审计

- **完整日志**: 记录所有令牌操作，包括生成、验证、刷新、撤销
- **会话追踪**: 通过JTI追踪令牌使用情况
- **异常检测**: 检测异常登录模式和令牌使用

## 性能优化

### 1. 数据库索引

```sql
-- JTI唯一索引，用于快速查找和撤销
UNIQUE KEY `uk_auth_sessions_jti` (`jti`)

-- 令牌哈希索引，用于快速验证
KEY `idx_auth_sessions_access_token_hash` (`access_token_hash`)
KEY `idx_auth_sessions_refresh_token_hash` (`refresh_token_hash`)

-- 状态和过期时间索引，用于清理过期会话
KEY `idx_auth_sessions_status_expires` (`status`, `expires_at`)
```

### 2. 缓存策略

- **会话缓存**: 将活跃会话缓存到Redis，减少数据库查询
- **JTI黑名单**: 缓存已撤销的JTI，快速拒绝无效令牌
- **令牌哈希缓存**: 缓存已验证的令牌哈希，避免重复计算

### 3. 清理机制

```sql
-- 定期清理过期会话的存储过程
CREATE PROCEDURE `cleanup_expired_sessions`()
BEGIN
    DELETE FROM `auth_sessions` 
    WHERE `expires_at` < NOW() 
       OR `status` IN ('expired', 'revoked', 'logged_out');
END
```

## 向后兼容性

### 1. 数据库兼容

- 保留原有的 `access_token` 和 `refresh_token` 字段
- 新增的 JTI 和哈希字段为可选，支持渐进式迁移
- 提供数据迁移脚本，将现有令牌转换为哈希

### 2. API兼容

- 保持现有的登录和刷新API接口不变
- 内部实现使用新的JTI方案，对外透明
- 支持新旧两种会话创建方式

### 3. 渐进式迁移

1. **第一阶段**: 部署新代码，支持JTI方案
2. **第二阶段**: 运行数据迁移，为现有会话生成JTI和哈希
3. **第三阶段**: 完全切换到JTI方案，移除旧字段

## 监控和运维

### 1. 关键指标

- **会话创建率**: 每分钟创建的会话数量
- **令牌验证成功率**: 令牌验证的成功率
- **会话撤销率**: 主动撤销的会话比例
- **过期会话清理**: 自动清理的过期会话数量

### 2. 告警规则

- **高失败率**: 令牌验证失败率超过阈值
- **异常撤销**: 短时间内大量会话被撤销
- **数据库性能**: 会话查询响应时间过长
- **存储空间**: 会话表占用空间过大

### 3. 日志分析

```go
// 关键日志字段
logiface.String("jti", jti)                    // JWT ID
logiface.String("token_hash", tokenHash)       // 令牌哈希
logiface.String("session_id", sessionID)       // 会话ID
logiface.String("token_type", "access|refresh") // 令牌类型
logiface.String("action", "create|verify|refresh|revoke") // 操作类型
```

## 最佳实践

### 1. 安全建议

- **定期轮换密钥**: 定期更换JWT签名密钥
- **限制令牌生命周期**: 设置合理的令牌过期时间
- **监控异常活动**: 监控异常的令牌使用模式
- **实施速率限制**: 限制令牌生成和验证的速率

### 2. 性能建议

- **合理设置索引**: 根据查询模式优化数据库索引
- **实施缓存策略**: 使用Redis缓存活跃会话
- **定期清理**: 定期清理过期和无效会话
- **监控资源使用**: 监控数据库和缓存资源使用情况

### 3. 运维建议

- **备份策略**: 定期备份会话数据
- **监控告警**: 设置完善的监控和告警机制
- **文档维护**: 保持文档的及时更新
- **测试验证**: 定期进行安全性和性能测试

## 总结

JTI + 令牌哈希混合方案成功解决了JWT无状态特性与会话管理需求之间的矛盾，提供了：

1. **安全性**: 支持令牌撤销和会话管理
2. **性能**: 通过哈希验证和索引优化提升性能
3. **可扩展性**: 支持多租户和分布式部署
4. **可维护性**: 完整的日志记录和监控机制
5. **向后兼容**: 支持渐进式迁移，不影响现有功能

该方案为现代微服务架构提供了安全、高效、可扩展的认证解决方案。 