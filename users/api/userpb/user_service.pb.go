// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/user_service.proto

package userpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求：token 查询用户信息
type GetUserInfoByTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`              // JWT 或 access token
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` // 应用ID（必须）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByTokenRequest) Reset() {
	*x = GetUserInfoByTokenRequest{}
	mi := &file_api_user_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByTokenRequest) ProtoMessage() {}

func (x *GetUserInfoByTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByTokenRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoByTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserInfoByTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetUserInfoByTokenRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

// 响应：用户信息
type GetUserInfoByTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *UserInfo              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByTokenResponse) Reset() {
	*x = GetUserInfoByTokenResponse{}
	mi := &file_api_user_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByTokenResponse) ProtoMessage() {}

func (x *GetUserInfoByTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByTokenResponse.ProtoReflect.Descriptor instead.
func (*GetUserInfoByTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserInfoByTokenResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserInfoByTokenResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetUserInfoByTokenResponse) GetData() *UserInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 用户基础信息（不返回 roles/status）
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	RealName      string                 `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	TenantId      int64                  `protobuf:"varint,5,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	InternalAppId int64                  `protobuf:"varint,6,opt,name=internal_app_id,json=internalAppId,proto3" json:"internal_app_id,omitempty"` // 内部应用ID，bigint类型提升性能
	AppId         string                 `protobuf:"bytes,7,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                            // 外部应用ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_api_user_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfo) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInfo) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *UserInfo) GetInternalAppId() int64 {
	if x != nil {
		return x.InternalAppId
	}
	return 0
}

func (x *UserInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

// 批量权限检查请求
type CheckUserPermissionsRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PermissionCodes []string               `protobuf:"bytes,2,rep,name=permission_codes,json=permissionCodes,proto3" json:"permission_codes,omitempty"` // 支持单个或多个权限
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CheckUserPermissionsRequest) Reset() {
	*x = CheckUserPermissionsRequest{}
	mi := &file_api_user_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPermissionsRequest) ProtoMessage() {}

func (x *CheckUserPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPermissionsRequest.ProtoReflect.Descriptor instead.
func (*CheckUserPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckUserPermissionsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckUserPermissionsRequest) GetPermissionCodes() []string {
	if x != nil {
		return x.PermissionCodes
	}
	return nil
}

// 批量权限检查响应
type CheckUserPermissionsResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Results       []*PermissionCheckResult `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserPermissionsResponse) Reset() {
	*x = CheckUserPermissionsResponse{}
	mi := &file_api_user_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPermissionsResponse) ProtoMessage() {}

func (x *CheckUserPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPermissionsResponse.ProtoReflect.Descriptor instead.
func (*CheckUserPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckUserPermissionsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUserPermissionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckUserPermissionsResponse) GetResults() []*PermissionCheckResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// 权限检查结果
type PermissionCheckResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PermissionCode string                 `protobuf:"bytes,1,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	HasPermission  bool                   `protobuf:"varint,2,opt,name=has_permission,json=hasPermission,proto3" json:"has_permission,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PermissionCheckResult) Reset() {
	*x = PermissionCheckResult{}
	mi := &file_api_user_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionCheckResult) ProtoMessage() {}

func (x *PermissionCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionCheckResult.ProtoReflect.Descriptor instead.
func (*PermissionCheckResult) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{5}
}

func (x *PermissionCheckResult) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *PermissionCheckResult) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

// 根据应用ID查询应用信息请求
type GetAppInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InternalAppId int64                  `protobuf:"varint,1,opt,name=internal_app_id,json=internalAppId,proto3" json:"internal_app_id,omitempty"` // 内部应用ID（可选）
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                            // 外部应用ID（可选）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppInfoRequest) Reset() {
	*x = GetAppInfoRequest{}
	mi := &file_api_user_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoRequest) ProtoMessage() {}

func (x *GetAppInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoRequest.ProtoReflect.Descriptor instead.
func (*GetAppInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAppInfoRequest) GetInternalAppId() int64 {
	if x != nil {
		return x.InternalAppId
	}
	return 0
}

func (x *GetAppInfoRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

// 根据应用ID查询应用信息响应
type GetAppInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *AppInfo               `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppInfoResponse) Reset() {
	*x = GetAppInfoResponse{}
	mi := &file_api_user_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoResponse) ProtoMessage() {}

func (x *GetAppInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoResponse.ProtoReflect.Descriptor instead.
func (*GetAppInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAppInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAppInfoResponse) GetData() *AppInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 应用基本信息
type AppInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InternalAppId int64                  `protobuf:"varint,1,opt,name=internal_app_id,json=internalAppId,proto3" json:"internal_app_id,omitempty"` // 内部应用ID
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                            // 外部应用ID
	AppName       string                 `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                      // 应用名称
	AppType       string                 `protobuf:"bytes,4,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`                      // 应用类型
	IsSystem      bool                   `protobuf:"varint,5,opt,name=is_system,json=isSystem,proto3" json:"is_system,omitempty"`                  // 是否系统应用
	Status        string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`                                       // 应用状态
	TenantId      int64                  `protobuf:"varint,7,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`                  // 租户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	mi := &file_api_user_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{8}
}

func (x *AppInfo) GetInternalAppId() int64 {
	if x != nil {
		return x.InternalAppId
	}
	return 0
}

func (x *AppInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppInfo) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *AppInfo) GetIsSystem() bool {
	if x != nil {
		return x.IsSystem
	}
	return false
}

func (x *AppInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AppInfo) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

// 根据租户ID查询租户信息请求
type GetTenantInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantInfoRequest) Reset() {
	*x = GetTenantInfoRequest{}
	mi := &file_api_user_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantInfoRequest) ProtoMessage() {}

func (x *GetTenantInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTenantInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetTenantInfoRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

// 根据租户ID查询租户信息响应
type GetTenantInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TenantInfo            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantInfoResponse) Reset() {
	*x = GetTenantInfoResponse{}
	mi := &file_api_user_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantInfoResponse) ProtoMessage() {}

func (x *GetTenantInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantInfoResponse.ProtoReflect.Descriptor instead.
func (*GetTenantInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetTenantInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetTenantInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetTenantInfoResponse) GetData() *TenantInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 租户信息
type TenantInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TenantId         int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`                        // 租户ID
	TenantCode       string                 `protobuf:"bytes,2,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`                   // 租户代码
	TenantName       string                 `protobuf:"bytes,3,opt,name=tenant_name,json=tenantName,proto3" json:"tenant_name,omitempty"`                   // 租户名称
	SubscriptionPlan string                 `protobuf:"bytes,4,opt,name=subscription_plan,json=subscriptionPlan,proto3" json:"subscription_plan,omitempty"` // 订阅计划
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TenantInfo) Reset() {
	*x = TenantInfo{}
	mi := &file_api_user_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TenantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantInfo) ProtoMessage() {}

func (x *TenantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantInfo.ProtoReflect.Descriptor instead.
func (*TenantInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{11}
}

func (x *TenantInfo) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *TenantInfo) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *TenantInfo) GetTenantName() string {
	if x != nil {
		return x.TenantName
	}
	return ""
}

func (x *TenantInfo) GetSubscriptionPlan() string {
	if x != nil {
		return x.SubscriptionPlan
	}
	return ""
}

var File_api_user_service_proto protoreflect.FileDescriptor

const file_api_user_service_proto_rawDesc = "" +
	"\n" +
	"\x16api/user_service.proto\x12\x04user\"H\n" +
	"\x19GetUserInfoByTokenRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x15\n" +
	"\x06app_id\x18\x02 \x01(\tR\x05appId\"n\n" +
	"\x1aGetUserInfoByTokenResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04data\"\xce\x01\n" +
	"\bUserInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1b\n" +
	"\treal_name\x18\x03 \x01(\tR\brealName\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x1b\n" +
	"\ttenant_id\x18\x05 \x01(\x03R\btenantId\x12&\n" +
	"\x0finternal_app_id\x18\x06 \x01(\x03R\rinternalAppId\x12\x15\n" +
	"\x06app_id\x18\a \x01(\tR\x05appId\"a\n" +
	"\x1bCheckUserPermissionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12)\n" +
	"\x10permission_codes\x18\x02 \x03(\tR\x0fpermissionCodes\"\x83\x01\n" +
	"\x1cCheckUserPermissionsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x125\n" +
	"\aresults\x18\x03 \x03(\v2\x1b.user.PermissionCheckResultR\aresults\"g\n" +
	"\x15PermissionCheckResult\x12'\n" +
	"\x0fpermission_code\x18\x01 \x01(\tR\x0epermissionCode\x12%\n" +
	"\x0ehas_permission\x18\x02 \x01(\bR\rhasPermission\"R\n" +
	"\x11GetAppInfoRequest\x12&\n" +
	"\x0finternal_app_id\x18\x01 \x01(\x03R\rinternalAppId\x12\x15\n" +
	"\x06app_id\x18\x02 \x01(\tR\x05appId\"e\n" +
	"\x12GetAppInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\x04data\x18\x03 \x01(\v2\r.user.AppInfoR\x04data\"\xd0\x01\n" +
	"\aAppInfo\x12&\n" +
	"\x0finternal_app_id\x18\x01 \x01(\x03R\rinternalAppId\x12\x15\n" +
	"\x06app_id\x18\x02 \x01(\tR\x05appId\x12\x19\n" +
	"\bapp_name\x18\x03 \x01(\tR\aappName\x12\x19\n" +
	"\bapp_type\x18\x04 \x01(\tR\aappType\x12\x1b\n" +
	"\tis_system\x18\x05 \x01(\bR\bisSystem\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x12\x1b\n" +
	"\ttenant_id\x18\a \x01(\x03R\btenantId\"3\n" +
	"\x14GetTenantInfoRequest\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\"k\n" +
	"\x15GetTenantInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12$\n" +
	"\x04data\x18\x03 \x01(\v2\x10.user.TenantInfoR\x04data\"\x98\x01\n" +
	"\n" +
	"TenantInfo\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12\x1f\n" +
	"\vtenant_code\x18\x02 \x01(\tR\n" +
	"tenantCode\x12\x1f\n" +
	"\vtenant_name\x18\x03 \x01(\tR\n" +
	"tenantName\x12+\n" +
	"\x11subscription_plan\x18\x04 \x01(\tR\x10subscriptionPlan2\xd0\x02\n" +
	"\vUserService\x12W\n" +
	"\x12GetUserInfoByToken\x12\x1f.user.GetUserInfoByTokenRequest\x1a .user.GetUserInfoByTokenResponse\x12]\n" +
	"\x14CheckUserPermissions\x12!.user.CheckUserPermissionsRequest\x1a\".user.CheckUserPermissionsResponse\x12?\n" +
	"\n" +
	"GetAppInfo\x12\x17.user.GetAppInfoRequest\x1a\x18.user.GetAppInfoResponse\x12H\n" +
	"\rGetTenantInfo\x12\x1a.user.GetTenantInfoRequest\x1a\x1b.user.GetTenantInfoResponseB-Z+gitee.com/heiyee/platforms/users/api/userpbb\x06proto3"

var (
	file_api_user_service_proto_rawDescOnce sync.Once
	file_api_user_service_proto_rawDescData []byte
)

func file_api_user_service_proto_rawDescGZIP() []byte {
	file_api_user_service_proto_rawDescOnce.Do(func() {
		file_api_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_user_service_proto_rawDesc), len(file_api_user_service_proto_rawDesc)))
	})
	return file_api_user_service_proto_rawDescData
}

var file_api_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_user_service_proto_goTypes = []any{
	(*GetUserInfoByTokenRequest)(nil),    // 0: user.GetUserInfoByTokenRequest
	(*GetUserInfoByTokenResponse)(nil),   // 1: user.GetUserInfoByTokenResponse
	(*UserInfo)(nil),                     // 2: user.UserInfo
	(*CheckUserPermissionsRequest)(nil),  // 3: user.CheckUserPermissionsRequest
	(*CheckUserPermissionsResponse)(nil), // 4: user.CheckUserPermissionsResponse
	(*PermissionCheckResult)(nil),        // 5: user.PermissionCheckResult
	(*GetAppInfoRequest)(nil),            // 6: user.GetAppInfoRequest
	(*GetAppInfoResponse)(nil),           // 7: user.GetAppInfoResponse
	(*AppInfo)(nil),                      // 8: user.AppInfo
	(*GetTenantInfoRequest)(nil),         // 9: user.GetTenantInfoRequest
	(*GetTenantInfoResponse)(nil),        // 10: user.GetTenantInfoResponse
	(*TenantInfo)(nil),                   // 11: user.TenantInfo
}
var file_api_user_service_proto_depIdxs = []int32{
	2,  // 0: user.GetUserInfoByTokenResponse.data:type_name -> user.UserInfo
	5,  // 1: user.CheckUserPermissionsResponse.results:type_name -> user.PermissionCheckResult
	8,  // 2: user.GetAppInfoResponse.data:type_name -> user.AppInfo
	11, // 3: user.GetTenantInfoResponse.data:type_name -> user.TenantInfo
	0,  // 4: user.UserService.GetUserInfoByToken:input_type -> user.GetUserInfoByTokenRequest
	3,  // 5: user.UserService.CheckUserPermissions:input_type -> user.CheckUserPermissionsRequest
	6,  // 6: user.UserService.GetAppInfo:input_type -> user.GetAppInfoRequest
	9,  // 7: user.UserService.GetTenantInfo:input_type -> user.GetTenantInfoRequest
	1,  // 8: user.UserService.GetUserInfoByToken:output_type -> user.GetUserInfoByTokenResponse
	4,  // 9: user.UserService.CheckUserPermissions:output_type -> user.CheckUserPermissionsResponse
	7,  // 10: user.UserService.GetAppInfo:output_type -> user.GetAppInfoResponse
	10, // 11: user.UserService.GetTenantInfo:output_type -> user.GetTenantInfoResponse
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_api_user_service_proto_init() }
func file_api_user_service_proto_init() {
	if File_api_user_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_user_service_proto_rawDesc), len(file_api_user_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_user_service_proto_goTypes,
		DependencyIndexes: file_api_user_service_proto_depIdxs,
		MessageInfos:      file_api_user_service_proto_msgTypes,
	}.Build()
	File_api_user_service_proto = out.File
	file_api_user_service_proto_goTypes = nil
	file_api_user_service_proto_depIdxs = nil
}
