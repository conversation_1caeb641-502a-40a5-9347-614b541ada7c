// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/user_service.proto

package userpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserService_GetUserInfoByToken_FullMethodName   = "/user.UserService/GetUserInfoByToken"
	UserService_CheckUserPermissions_FullMethodName = "/user.UserService/CheckUserPermissions"
	UserService_GetAppInfo_FullMethodName           = "/user.UserService/GetAppInfo"
	UserService_GetTenantInfo_FullMethodName        = "/user.UserService/GetTenantInfo"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 用户服务
type UserServiceClient interface {
	// 1. 根据 token 查询当前用户信息
	GetUserInfoByToken(ctx context.Context, in *GetUserInfoByTokenRequest, opts ...grpc.CallOption) (*GetUserInfoByTokenResponse, error)
	// 2. 根据用户ID和权限code判断是否有权限（支持批量）
	CheckUserPermissions(ctx context.Context, in *CheckUserPermissionsRequest, opts ...grpc.CallOption) (*CheckUserPermissionsResponse, error)
	// 3. 根据内部应用ID或外部应用ID查询应用基本信息
	GetAppInfo(ctx context.Context, in *GetAppInfoRequest, opts ...grpc.CallOption) (*GetAppInfoResponse, error)
	// 4. 根据租户ID查询租户信息
	GetTenantInfo(ctx context.Context, in *GetTenantInfoRequest, opts ...grpc.CallOption) (*GetTenantInfoResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) GetUserInfoByToken(ctx context.Context, in *GetUserInfoByTokenRequest, opts ...grpc.CallOption) (*GetUserInfoByTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoByTokenResponse)
	err := c.cc.Invoke(ctx, UserService_GetUserInfoByToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserPermissions(ctx context.Context, in *CheckUserPermissionsRequest, opts ...grpc.CallOption) (*CheckUserPermissionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUserPermissionsResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserPermissions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetAppInfo(ctx context.Context, in *GetAppInfoRequest, opts ...grpc.CallOption) (*GetAppInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAppInfoResponse)
	err := c.cc.Invoke(ctx, UserService_GetAppInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetTenantInfo(ctx context.Context, in *GetTenantInfoRequest, opts ...grpc.CallOption) (*GetTenantInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTenantInfoResponse)
	err := c.cc.Invoke(ctx, UserService_GetTenantInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility.
//
// 用户服务
type UserServiceServer interface {
	// 1. 根据 token 查询当前用户信息
	GetUserInfoByToken(context.Context, *GetUserInfoByTokenRequest) (*GetUserInfoByTokenResponse, error)
	// 2. 根据用户ID和权限code判断是否有权限（支持批量）
	CheckUserPermissions(context.Context, *CheckUserPermissionsRequest) (*CheckUserPermissionsResponse, error)
	// 3. 根据内部应用ID或外部应用ID查询应用基本信息
	GetAppInfo(context.Context, *GetAppInfoRequest) (*GetAppInfoResponse, error)
	// 4. 根据租户ID查询租户信息
	GetTenantInfo(context.Context, *GetTenantInfoRequest) (*GetTenantInfoResponse, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServiceServer struct{}

func (UnimplementedUserServiceServer) GetUserInfoByToken(context.Context, *GetUserInfoByTokenRequest) (*GetUserInfoByTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfoByToken not implemented")
}
func (UnimplementedUserServiceServer) CheckUserPermissions(context.Context, *CheckUserPermissionsRequest) (*CheckUserPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserPermissions not implemented")
}
func (UnimplementedUserServiceServer) GetAppInfo(context.Context, *GetAppInfoRequest) (*GetAppInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppInfo not implemented")
}
func (UnimplementedUserServiceServer) GetTenantInfo(context.Context, *GetTenantInfoRequest) (*GetTenantInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTenantInfo not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}
func (UnimplementedUserServiceServer) testEmbeddedByValue()                     {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_GetUserInfoByToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoByTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserInfoByToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserInfoByToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserInfoByToken(ctx, req.(*GetUserInfoByTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserPermissions(ctx, req.(*CheckUserPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetAppInfo(ctx, req.(*GetAppInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetTenantInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTenantInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetTenantInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetTenantInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetTenantInfo(ctx, req.(*GetTenantInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserInfoByToken",
			Handler:    _UserService_GetUserInfoByToken_Handler,
		},
		{
			MethodName: "CheckUserPermissions",
			Handler:    _UserService_CheckUserPermissions_Handler,
		},
		{
			MethodName: "GetAppInfo",
			Handler:    _UserService_GetAppInfo_Handler,
		},
		{
			MethodName: "GetTenantInfo",
			Handler:    _UserService_GetTenantInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/user_service.proto",
}
