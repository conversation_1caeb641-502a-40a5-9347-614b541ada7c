// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/file_upload_service.proto

package fileuploadpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FileUploadService_MarkFilePermanent_FullMethodName       = "/fileupload.FileUploadService/MarkFilePermanent"
	FileUploadService_BatchMarkFilesPermanent_FullMethodName = "/fileupload.FileUploadService/BatchMarkFilesPermanent"
)

// FileUploadServiceClient is the client API for FileUploadService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 文件上传服务
type FileUploadServiceClient interface {
	// 标记文件为永久存储
	MarkFilePermanent(ctx context.Context, in *MarkFilePermanentRequest, opts ...grpc.CallOption) (*MarkFilePermanentResponse, error)
	// 批量标记文件为永久存储
	BatchMarkFilesPermanent(ctx context.Context, in *BatchMarkFilesPermanentRequest, opts ...grpc.CallOption) (*BatchMarkFilesPermanentResponse, error)
}

type fileUploadServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileUploadServiceClient(cc grpc.ClientConnInterface) FileUploadServiceClient {
	return &fileUploadServiceClient{cc}
}

func (c *fileUploadServiceClient) MarkFilePermanent(ctx context.Context, in *MarkFilePermanentRequest, opts ...grpc.CallOption) (*MarkFilePermanentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkFilePermanentResponse)
	err := c.cc.Invoke(ctx, FileUploadService_MarkFilePermanent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileUploadServiceClient) BatchMarkFilesPermanent(ctx context.Context, in *BatchMarkFilesPermanentRequest, opts ...grpc.CallOption) (*BatchMarkFilesPermanentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchMarkFilesPermanentResponse)
	err := c.cc.Invoke(ctx, FileUploadService_BatchMarkFilesPermanent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileUploadServiceServer is the server API for FileUploadService service.
// All implementations must embed UnimplementedFileUploadServiceServer
// for forward compatibility.
//
// 文件上传服务
type FileUploadServiceServer interface {
	// 标记文件为永久存储
	MarkFilePermanent(context.Context, *MarkFilePermanentRequest) (*MarkFilePermanentResponse, error)
	// 批量标记文件为永久存储
	BatchMarkFilesPermanent(context.Context, *BatchMarkFilesPermanentRequest) (*BatchMarkFilesPermanentResponse, error)
	mustEmbedUnimplementedFileUploadServiceServer()
}

// UnimplementedFileUploadServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFileUploadServiceServer struct{}

func (UnimplementedFileUploadServiceServer) MarkFilePermanent(context.Context, *MarkFilePermanentRequest) (*MarkFilePermanentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkFilePermanent not implemented")
}
func (UnimplementedFileUploadServiceServer) BatchMarkFilesPermanent(context.Context, *BatchMarkFilesPermanentRequest) (*BatchMarkFilesPermanentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMarkFilesPermanent not implemented")
}
func (UnimplementedFileUploadServiceServer) mustEmbedUnimplementedFileUploadServiceServer() {}
func (UnimplementedFileUploadServiceServer) testEmbeddedByValue()                           {}

// UnsafeFileUploadServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileUploadServiceServer will
// result in compilation errors.
type UnsafeFileUploadServiceServer interface {
	mustEmbedUnimplementedFileUploadServiceServer()
}

func RegisterFileUploadServiceServer(s grpc.ServiceRegistrar, srv FileUploadServiceServer) {
	// If the following call pancis, it indicates UnimplementedFileUploadServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FileUploadService_ServiceDesc, srv)
}

func _FileUploadService_MarkFilePermanent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkFilePermanentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileUploadServiceServer).MarkFilePermanent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileUploadService_MarkFilePermanent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileUploadServiceServer).MarkFilePermanent(ctx, req.(*MarkFilePermanentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileUploadService_BatchMarkFilesPermanent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMarkFilesPermanentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileUploadServiceServer).BatchMarkFilesPermanent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileUploadService_BatchMarkFilesPermanent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileUploadServiceServer).BatchMarkFilesPermanent(ctx, req.(*BatchMarkFilesPermanentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileUploadService_ServiceDesc is the grpc.ServiceDesc for FileUploadService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileUploadService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "fileupload.FileUploadService",
	HandlerType: (*FileUploadServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MarkFilePermanent",
			Handler:    _FileUploadService_MarkFilePermanent_Handler,
		},
		{
			MethodName: "BatchMarkFilesPermanent",
			Handler:    _FileUploadService_BatchMarkFilesPermanent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/file_upload_service.proto",
}
