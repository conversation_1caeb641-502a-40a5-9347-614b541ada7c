// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/idgenerator_service.proto

package idgeneratorpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 生成ID请求
type GenerateIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessType  string                 `protobuf:"bytes,1,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"` // 业务类型
	SequenceName  string                 `protobuf:"bytes,2,opt,name=sequence_name,json=sequenceName,proto3" json:"sequence_name,omitempty"` // 序列名称
	TenantId      int64                  `protobuf:"varint,3,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`            // 租户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateIdRequest) Reset() {
	*x = GenerateIdRequest{}
	mi := &file_api_idgenerator_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateIdRequest) ProtoMessage() {}

func (x *GenerateIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_idgenerator_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateIdRequest.ProtoReflect.Descriptor instead.
func (*GenerateIdRequest) Descriptor() ([]byte, []int) {
	return file_api_idgenerator_service_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateIdRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *GenerateIdRequest) GetSequenceName() string {
	if x != nil {
		return x.SequenceName
	}
	return ""
}

func (x *GenerateIdRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

// 生成ID响应
type GenerateIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateIdResponse) Reset() {
	*x = GenerateIdResponse{}
	mi := &file_api_idgenerator_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateIdResponse) ProtoMessage() {}

func (x *GenerateIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_idgenerator_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateIdResponse.ProtoReflect.Descriptor instead.
func (*GenerateIdResponse) Descriptor() ([]byte, []int) {
	return file_api_idgenerator_service_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateIdResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GenerateIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GenerateIdResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 批量生成ID请求
type GenerateBatchIdsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BusinessType  string                 `protobuf:"bytes,1,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	SequenceName  string                 `protobuf:"bytes,2,opt,name=sequence_name,json=sequenceName,proto3" json:"sequence_name,omitempty"`
	TenantId      int64                  `protobuf:"varint,3,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	Count         int32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"` // 生成数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateBatchIdsRequest) Reset() {
	*x = GenerateBatchIdsRequest{}
	mi := &file_api_idgenerator_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateBatchIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateBatchIdsRequest) ProtoMessage() {}

func (x *GenerateBatchIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_idgenerator_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateBatchIdsRequest.ProtoReflect.Descriptor instead.
func (*GenerateBatchIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_idgenerator_service_proto_rawDescGZIP(), []int{2}
}

func (x *GenerateBatchIdsRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *GenerateBatchIdsRequest) GetSequenceName() string {
	if x != nil {
		return x.SequenceName
	}
	return ""
}

func (x *GenerateBatchIdsRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *GenerateBatchIdsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 批量生成ID响应
type GenerateBatchIdsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Ids           []int64                `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateBatchIdsResponse) Reset() {
	*x = GenerateBatchIdsResponse{}
	mi := &file_api_idgenerator_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateBatchIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateBatchIdsResponse) ProtoMessage() {}

func (x *GenerateBatchIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_idgenerator_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateBatchIdsResponse.ProtoReflect.Descriptor instead.
func (*GenerateBatchIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_idgenerator_service_proto_rawDescGZIP(), []int{3}
}

func (x *GenerateBatchIdsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GenerateBatchIdsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GenerateBatchIdsResponse) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_api_idgenerator_service_proto protoreflect.FileDescriptor

const file_api_idgenerator_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/idgenerator_service.proto\x12\vidgenerator\"z\n" +
	"\x11GenerateIdRequest\x12#\n" +
	"\rbusiness_type\x18\x01 \x01(\tR\fbusinessType\x12#\n" +
	"\rsequence_name\x18\x02 \x01(\tR\fsequenceName\x12\x1b\n" +
	"\ttenant_id\x18\x03 \x01(\x03R\btenantId\"R\n" +
	"\x12GenerateIdResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\"\x96\x01\n" +
	"\x17GenerateBatchIdsRequest\x12#\n" +
	"\rbusiness_type\x18\x01 \x01(\tR\fbusinessType\x12#\n" +
	"\rsequence_name\x18\x02 \x01(\tR\fsequenceName\x12\x1b\n" +
	"\ttenant_id\x18\x03 \x01(\x03R\btenantId\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\"Z\n" +
	"\x18GenerateBatchIdsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x10\n" +
	"\x03ids\x18\x03 \x03(\x03R\x03ids2\xc4\x01\n" +
	"\x12IdGeneratorService\x12M\n" +
	"\n" +
	"GenerateId\x12\x1e.idgenerator.GenerateIdRequest\x1a\x1f.idgenerator.GenerateIdResponse\x12_\n" +
	"\x10GenerateBatchIds\x12$.idgenerator.GenerateBatchIdsRequest\x1a%.idgenerator.GenerateBatchIdsResponseB4Z2gitee.com/heiyee/platforms/users/api/idgeneratorpbb\x06proto3"

var (
	file_api_idgenerator_service_proto_rawDescOnce sync.Once
	file_api_idgenerator_service_proto_rawDescData []byte
)

func file_api_idgenerator_service_proto_rawDescGZIP() []byte {
	file_api_idgenerator_service_proto_rawDescOnce.Do(func() {
		file_api_idgenerator_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_idgenerator_service_proto_rawDesc), len(file_api_idgenerator_service_proto_rawDesc)))
	})
	return file_api_idgenerator_service_proto_rawDescData
}

var file_api_idgenerator_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_idgenerator_service_proto_goTypes = []any{
	(*GenerateIdRequest)(nil),        // 0: idgenerator.GenerateIdRequest
	(*GenerateIdResponse)(nil),       // 1: idgenerator.GenerateIdResponse
	(*GenerateBatchIdsRequest)(nil),  // 2: idgenerator.GenerateBatchIdsRequest
	(*GenerateBatchIdsResponse)(nil), // 3: idgenerator.GenerateBatchIdsResponse
}
var file_api_idgenerator_service_proto_depIdxs = []int32{
	0, // 0: idgenerator.IdGeneratorService.GenerateId:input_type -> idgenerator.GenerateIdRequest
	2, // 1: idgenerator.IdGeneratorService.GenerateBatchIds:input_type -> idgenerator.GenerateBatchIdsRequest
	1, // 2: idgenerator.IdGeneratorService.GenerateId:output_type -> idgenerator.GenerateIdResponse
	3, // 3: idgenerator.IdGeneratorService.GenerateBatchIds:output_type -> idgenerator.GenerateBatchIdsResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_idgenerator_service_proto_init() }
func file_api_idgenerator_service_proto_init() {
	if File_api_idgenerator_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_idgenerator_service_proto_rawDesc), len(file_api_idgenerator_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_idgenerator_service_proto_goTypes,
		DependencyIndexes: file_api_idgenerator_service_proto_depIdxs,
		MessageInfos:      file_api_idgenerator_service_proto_msgTypes,
	}.Build()
	File_api_idgenerator_service_proto = out.File
	file_api_idgenerator_service_proto_goTypes = nil
	file_api_idgenerator_service_proto_depIdxs = nil
}
