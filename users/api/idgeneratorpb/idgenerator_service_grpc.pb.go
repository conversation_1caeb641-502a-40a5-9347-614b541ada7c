// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/idgenerator_service.proto

package idgeneratorpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	IdGeneratorService_GenerateId_FullMethodName       = "/idgenerator.IdGeneratorService/GenerateId"
	IdGeneratorService_GenerateBatchIds_FullMethodName = "/idgenerator.IdGeneratorService/GenerateBatchIds"
)

// IdGeneratorServiceClient is the client API for IdGeneratorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ID生成器服务
type IdGeneratorServiceClient interface {
	// 生成单个ID
	GenerateId(ctx context.Context, in *GenerateIdRequest, opts ...grpc.CallOption) (*GenerateIdResponse, error)
	// 批量生成ID
	GenerateBatchIds(ctx context.Context, in *GenerateBatchIdsRequest, opts ...grpc.CallOption) (*GenerateBatchIdsResponse, error)
}

type idGeneratorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewIdGeneratorServiceClient(cc grpc.ClientConnInterface) IdGeneratorServiceClient {
	return &idGeneratorServiceClient{cc}
}

func (c *idGeneratorServiceClient) GenerateId(ctx context.Context, in *GenerateIdRequest, opts ...grpc.CallOption) (*GenerateIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateIdResponse)
	err := c.cc.Invoke(ctx, IdGeneratorService_GenerateId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *idGeneratorServiceClient) GenerateBatchIds(ctx context.Context, in *GenerateBatchIdsRequest, opts ...grpc.CallOption) (*GenerateBatchIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateBatchIdsResponse)
	err := c.cc.Invoke(ctx, IdGeneratorService_GenerateBatchIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IdGeneratorServiceServer is the server API for IdGeneratorService service.
// All implementations must embed UnimplementedIdGeneratorServiceServer
// for forward compatibility.
//
// ID生成器服务
type IdGeneratorServiceServer interface {
	// 生成单个ID
	GenerateId(context.Context, *GenerateIdRequest) (*GenerateIdResponse, error)
	// 批量生成ID
	GenerateBatchIds(context.Context, *GenerateBatchIdsRequest) (*GenerateBatchIdsResponse, error)
	mustEmbedUnimplementedIdGeneratorServiceServer()
}

// UnimplementedIdGeneratorServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedIdGeneratorServiceServer struct{}

func (UnimplementedIdGeneratorServiceServer) GenerateId(context.Context, *GenerateIdRequest) (*GenerateIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateId not implemented")
}
func (UnimplementedIdGeneratorServiceServer) GenerateBatchIds(context.Context, *GenerateBatchIdsRequest) (*GenerateBatchIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateBatchIds not implemented")
}
func (UnimplementedIdGeneratorServiceServer) mustEmbedUnimplementedIdGeneratorServiceServer() {}
func (UnimplementedIdGeneratorServiceServer) testEmbeddedByValue()                            {}

// UnsafeIdGeneratorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IdGeneratorServiceServer will
// result in compilation errors.
type UnsafeIdGeneratorServiceServer interface {
	mustEmbedUnimplementedIdGeneratorServiceServer()
}

func RegisterIdGeneratorServiceServer(s grpc.ServiceRegistrar, srv IdGeneratorServiceServer) {
	// If the following call pancis, it indicates UnimplementedIdGeneratorServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&IdGeneratorService_ServiceDesc, srv)
}

func _IdGeneratorService_GenerateId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IdGeneratorServiceServer).GenerateId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IdGeneratorService_GenerateId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IdGeneratorServiceServer).GenerateId(ctx, req.(*GenerateIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IdGeneratorService_GenerateBatchIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateBatchIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IdGeneratorServiceServer).GenerateBatchIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IdGeneratorService_GenerateBatchIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IdGeneratorServiceServer).GenerateBatchIds(ctx, req.(*GenerateBatchIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// IdGeneratorService_ServiceDesc is the grpc.ServiceDesc for IdGeneratorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IdGeneratorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "idgenerator.IdGeneratorService",
	HandlerType: (*IdGeneratorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateId",
			Handler:    _IdGeneratorService_GenerateId_Handler,
		},
		{
			MethodName: "GenerateBatchIds",
			Handler:    _IdGeneratorService_GenerateBatchIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/idgenerator_service.proto",
}
