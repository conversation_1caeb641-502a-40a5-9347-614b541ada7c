package routes

import (
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/pkg/config"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupAppConfigRoutes 设置应用配置路由（原租户配置迁移到应用维度）
func SetupAppConfigRoutes(engine *gin.RouterGroup, appConfigHandler *handlers.AppConfigHandler, jwtService *jwt.JWTService) {
	// 应用配置API路由组
	appConfigGroup := engine.Group(config.GlobalAPIPrefix + "/app-config")
	appConfigGroup.Use(httpmiddleware.RequireAuthedMiddleware())

	// 密码策略配置
	appConfigGroup.GET("/password-policy", appConfigHandler.GetPasswordPolicy)     // 获取密码策略
	appConfigGroup.POST("/password-policy", appConfigHandler.UpdatePasswordPolicy) // 更新密码策略

	// 注册方式配置
	appConfigGroup.GET("/registration-methods", appConfigHandler.GetRegistrationMethods)     // 获取注册方式
	appConfigGroup.POST("/registration-methods", appConfigHandler.UpdateRegistrationMethods) // 更新注册方式

	// 租户信息配置
	appConfigGroup.GET("/tenant-info", appConfigHandler.GetTenantInfo)     // 获取租户信息
	appConfigGroup.POST("/tenant-info", appConfigHandler.UpdateTenantInfo) // 更新租户信息

	// 通用配置管理
	appConfigGroup.GET("/configs", appConfigHandler.GetTenantConfigs)               // 获取应用所有配置
	appConfigGroup.POST("/config", appConfigHandler.UpdateTenantConfig)             // 更新应用配置
	appConfigGroup.POST("/copy-system", appConfigHandler.CopySystemConfigsToTenant) // 复制系统配置到应用
}
