package routes

import (
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/pkg/config"

	"github.com/gin-gonic/gin"
)

// SetupApplicationRoutes 设置应用相关路由
func SetupApplicationRoutes(engine *gin.RouterGroup, applicationHandler *handlers.ApplicationHandler, jwtService interface{}) {
	// 应用管理路由组 - 使用 /api/user/admin/application 前缀
	applicationGroup := engine.Group(config.GlobalAPIPrefix + "/admin/application")
	applicationGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 获取应用列表
		applicationGroup.POST("/list", applicationHandler.ListApplications)

		// 获取应用详情
		applicationGroup.POST("/get", applicationHandler.GetApplication)

		// 根据应用ID获取应用详情
		applicationGroup.POST("/get-by-app-id", applicationHandler.GetApplicationByAppID)

		// 创建应用
		applicationGroup.POST("/create", applicationHandler.CreateApplication)

		// 更新应用
		applicationGroup.POST("/update", applicationHandler.UpdateApplication)

		// 删除应用
		applicationGroup.POST("/delete", applicationHandler.DeleteApplication)

		// 启用应用
		applicationGroup.POST("/enable", applicationHandler.EnableApplication)

		// 禁用应用
		applicationGroup.POST("/disable", applicationHandler.DisableApplication)

		// 获取应用统计信息
		applicationGroup.POST("/stats", applicationHandler.GetApplicationStats)
	}
}
