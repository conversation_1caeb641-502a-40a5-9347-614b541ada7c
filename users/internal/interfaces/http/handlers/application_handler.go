package handlers

import (
	"strconv"

	common_response "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/application/application/dto"
	"gitee.com/heiyee/platforms/users/internal/application/application/service"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"

	"github.com/gin-gonic/gin"
)

// ApplicationHandler 应用处理器
type ApplicationHandler struct {
	appService *service.ApplicationApplicationService
}

// NewApplicationHandler 创建应用处理器
func NewApplicationHandler(appService *service.ApplicationApplicationService) *ApplicationHandler {
	return &ApplicationHandler{
		appService: appService,
	}
}

// ListApplications 获取应用列表
func (h *ApplicationHandler) ListApplications(c *gin.Context) {
	// 获取查询参数
	var req dto.ListApplicationsRequest

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	// 获取当前用户租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}
	tenantID := userInfo.TenantID

	req.TenantID = tenantID
	req.Page = page
	req.Size = size

	// 搜索关键词
	if keyword := c.Query("keyword"); keyword != "" {
		req.Keyword = &keyword
	}

	// 应用类型过滤
	if appTypeStr := c.Query("app_type"); appTypeStr != "" {
		appType := entity.ApplicationType(appTypeStr)
		req.AppType = &appType
	}

	// 状态过滤
	if statusStr := c.Query("status"); statusStr != "" {
		status := entity.ApplicationStatus(statusStr)
		req.Status = &status
	}

	// 是否系统应用过滤
	if isSystemStr := c.Query("is_system"); isSystemStr != "" {
		if isSystem, err := strconv.ParseBool(isSystemStr); err == nil {
			req.IsSystem = &isSystem
		}
	}

	// 时间范围过滤
	if createdAtStart := c.Query("created_at_start"); createdAtStart != "" {
		req.CreatedAtStart = &createdAtStart
	}
	if createdAtEnd := c.Query("created_at_end"); createdAtEnd != "" {
		req.CreatedAtEnd = &createdAtEnd
	}

	// 调用服务层
	result, err := h.appService.ListApplications(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	// 构建分页响应
	common_response.Paginated(c, result.List, result.Page, result.Size, result.Total)
}

// GetApplication 获取应用详情
func (h *ApplicationHandler) GetApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.GetApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.GetApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// GetApplicationByAppID 根据应用ID获取应用详情
func (h *ApplicationHandler) GetApplicationByAppID(c *gin.Context) {
	// 从请求体解析参数
	var req dto.GetApplicationByAppIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.GetApplicationByAppID(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// CreateApplication 创建应用
func (h *ApplicationHandler) CreateApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 设置租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}
	req.TenantID = userInfo.TenantID

	// 设置默认值
	if req.Status == "" {
		req.Status = entity.ApplicationStatusActive
	}
	if req.AppType == "" {
		req.AppType = entity.ApplicationTypeWeb
	}
	if req.RateLimit == 0 {
		req.RateLimit = 1000
	}
	req.IsActive = true

	// 调用服务层
	result, err := h.appService.CreateApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// UpdateApplication 更新应用
func (h *ApplicationHandler) UpdateApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.UpdateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.UpdateApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// DeleteApplication 删除应用
func (h *ApplicationHandler) DeleteApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.DeleteApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	err := h.appService.DeleteApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用删除成功"})
}

// EnableApplication 启用应用
func (h *ApplicationHandler) EnableApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.EnableApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, err.Error())
		return
	}

	// 调用服务层
	err := h.appService.EnableApplication(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用启用成功"})
}

// DisableApplication 禁用应用
func (h *ApplicationHandler) DisableApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.DisableApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, err.Error())
		return
	}

	// 调用服务层
	err := h.appService.DisableApplication(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用禁用成功"})
}

// GetApplicationStats 获取应用统计信息
func (h *ApplicationHandler) GetApplicationStats(c *gin.Context) {
	// 获取租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}
	tenantID := userInfo.TenantID

	// 调用服务层
	stats, err := h.appService.GetStats(c.Request.Context(), tenantID)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, stats)
}
