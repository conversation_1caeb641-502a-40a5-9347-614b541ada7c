package handlers

import (
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	appDto "gitee.com/heiyee/platforms/users/internal/application/application/dto"
	appService "gitee.com/heiyee/platforms/users/internal/application/application/service"
	"gitee.com/heiyee/platforms/users/internal/application/fileupload/service"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userService "gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/pkg/validator"

	"github.com/gin-gonic/gin"
)

// AppConfigHandler 应用配置处理器
type AppConfigHandler struct {
	logger            logiface.Logger
	appConfigService  *userService.AppConfigService
	fileUploadService *service.FileUploadApplicationService
	appService        *appService.ApplicationApplicationService
}

// NewAppConfigHandler 创建应用配置处理器
func NewAppConfigHandler(logger logiface.Logger, appConfigService *userService.AppConfigService, fileUploadService *service.FileUploadApplicationService, appService *appService.ApplicationApplicationService) *AppConfigHandler {
	return &AppConfigHandler{
		logger:            logger,
		appConfigService:  appConfigService,
		fileUploadService: fileUploadService,
		appService:        appService,
	}
}

// GetPasswordPolicy 获取密码策略（按internalAppId粒度）
func (h *AppConfigHandler) GetPasswordPolicy(c *gin.Context) {
	appID := c.Query("app_id")
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	req := &dto.GetPasswordPolicyRequest{
		AppID: appID,
	}

	policy, err := h.appConfigService.GetPasswordPolicy(c.Request.Context(), req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get password policy", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", app.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, policy)
}

// UpdatePasswordPolicy 更新密码策略（按internalAppId粒度）
func (h *AppConfigHandler) UpdatePasswordPolicy(c *gin.Context) {
	var req dto.UpdatePasswordPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	if req.Policy == nil {
		commonResponse.BadRequest(c, "密码策略不能为空")
		return
	}

	// 从请求体中获取app_id
	appID := req.AppID
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	// 设置正确的tenantId
	req.TenantID = app.TenantID

	if err := h.appConfigService.UpdatePasswordPolicy(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update password policy", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", req.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "密码策略更新成功"})
}

// GetRegistrationMethods 获取注册方式配置（按internalAppId粒度）
func (h *AppConfigHandler) GetRegistrationMethods(c *gin.Context) {
	appID := c.Query("app_id")
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	req := &dto.GetRegistrationMethodsRequest{
		AppID: appID,
	}

	methods, err := h.appConfigService.GetRegistrationMethods(c.Request.Context(), req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get registration methods", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", app.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, methods)
}

// UpdateRegistrationMethods 更新注册方式配置（按internalAppId粒度）
func (h *AppConfigHandler) UpdateRegistrationMethods(c *gin.Context) {
	var req dto.UpdateRegistrationMethodsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	if req.Methods == nil {
		commonResponse.BadRequest(c, "注册方式配置不能为空")
		return
	}

	// 从请求体中获取app_id
	appID := req.AppID
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	// 设置正确的tenantId
	req.TenantID = app.TenantID

	if err := h.appConfigService.UpdateRegistrationMethods(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update registration methods", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", req.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "注册方式配置更新成功"})
}

// GetTenantConfigs 获取应用（原租户）所有配置（按internalAppId粒度）
func (h *AppConfigHandler) GetTenantConfigs(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	configs, err := h.appConfigService.GetTenantConfigs(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get tenant configs", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, configs)
}

// UpdateTenantConfig 更新应用配置（原租户配置，按internalAppId粒度）
func (h *AppConfigHandler) UpdateTenantConfig(c *gin.Context) {
	var req dto.UpdateTenantConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	v := validator.NewValidator()
	v.Required(req.ConfigKey, "config_key")
	v.Required(req.ConfigValue, "config_value")

	if err := v.Validate(); err != nil {
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	if err := h.appConfigService.UpdateTenantConfig(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update tenant config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "租户配置更新成功"})
}

// CopySystemConfigsToTenant 复制系统配置到应用（按internalAppId粒度）
func (h *AppConfigHandler) CopySystemConfigsToTenant(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	if err := h.appConfigService.CopySystemConfigsToTenant(c.Request.Context(), tenantID); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to copy system configs to tenant", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "系统配置复制成功"})
}

// GetTenantInfo 获取应用信息配置（原租户信息）
func (h *AppConfigHandler) GetTenantInfo(c *gin.Context) {
	appID := c.Query("app_id")
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	// 确保应用图标场景配置存在
	if err := h.fileUploadService.EnsureAppIconSceneConfig(c.Request.Context(), app.TenantID, app.ID); err != nil {
		h.logger.Warn(c.Request.Context(), "Failed to ensure app icon scene config", logiface.Error(err), logiface.Int64("tenant_id", app.TenantID))
		// 不阻断流程，只记录警告
	}

	req := &dto.GetTenantInfoRequest{
		AppID: appID,
	}

	info, err := h.appConfigService.GetTenantInfo(c.Request.Context(), req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get tenant info", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", app.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, info)
}

// UpdateTenantInfo 更新应用信息配置（原租户信息）
func (h *AppConfigHandler) UpdateTenantInfo(c *gin.Context) {
	var req dto.UpdateTenantInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	if req.Info == nil {
		commonResponse.BadRequest(c, "租户信息不能为空")
		return
	}

	// 从请求体中获取app_id
	appID := req.AppID
	if appID == "" {
		commonResponse.BadRequest(c, "app_id不能为空")
		return
	}

	// 通过appId获取应用信息，从而获取tenantId
	appReq := &appDto.GetApplicationByAppIDRequest{
		AppID: appID,
	}
	app, err := h.appService.GetApplicationByAppID(c.Request.Context(), appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		commonResponse.InternalError(c, err)
		return
	}
	if app == nil {
		commonResponse.NotFound(c, "应用不存在")
		return
	}

	// 设置正确的tenantId
	req.TenantID = app.TenantID

	if err := h.appConfigService.UpdateTenantInfo(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update tenant info", logiface.Error(err), logiface.String("app_id", appID), logiface.Int64("tenant_id", req.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "租户信息更新成功"})
}
