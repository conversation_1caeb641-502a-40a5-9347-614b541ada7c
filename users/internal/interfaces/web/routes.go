package web

import (
	"gitee.com/heiyee/platforms/pkg/logiface"
	userService "gitee.com/heiyee/platforms/users/internal/application/user/service"
	verificationService "gitee.com/heiyee/platforms/users/internal/application/verification/service"
	userRepo "gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	passwordService "gitee.com/heiyee/platforms/users/internal/domain/user/service"
	verificationRepo "gitee.com/heiyee/platforms/users/internal/domain/verification/repository"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置Web路由
func SetupRoutes(r *gin.Engine, handlers *Handlers) {
	// 密码重置相关路由
	passwordReset := r.Group("/password-reset")
	{
		passwordReset.GET("/", handlers.PasswordReset.ShowResetFormGin)
		passwordReset.POST("/", handlers.PasswordReset.ProcessResetGin)
	}

	// 其他路由...
}

// Handlers 处理器集合
type Handlers struct {
	PasswordReset *PasswordResetHandler
	// 其他处理器...
}

// NewHandlers 创建处理器集合
func NewHandlers(
	verificationService *verificationService.VerificationApplicationService,
	userApplicationService *userService.UserApplicationService,
	appConfigService *userService.AppConfigService,
	passwordValidator *passwordService.PasswordPolicyValidator,
	userRepo userRepo.UserRepository,
	tenantRepo userRepo.TenantRepository,
	tokenRepo verificationRepo.VerificationTokenRepository,
	logger logiface.Logger,
) *Handlers {
	return &Handlers{
		PasswordReset: NewPasswordResetHandler(
			verificationService,
			userApplicationService,
			appConfigService,
			passwordValidator,
			userRepo,
			tenantRepo,
			tokenRepo,
			logger,
		),
	}
}
