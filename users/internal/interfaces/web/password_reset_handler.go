package web

import (
	"context"
	"html/template"
	"net/http"

	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userService "gitee.com/heiyee/platforms/users/internal/application/user/service"
	verificationService "gitee.com/heiyee/platforms/users/internal/application/verification/service"
	userEntity "gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	userRepo "gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	passwordService "gitee.com/heiyee/platforms/users/internal/domain/user/service"
	verificationEntity "gitee.com/heiyee/platforms/users/internal/domain/verification/entity"
	verificationRepo "gitee.com/heiyee/platforms/users/internal/domain/verification/repository"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// PasswordResetHandler 密码重置处理器
type PasswordResetHandler struct {
	verificationService    *verificationService.VerificationApplicationService
	userApplicationService *userService.UserApplicationService
	appConfigService       *userService.AppConfigService
	passwordValidator      *passwordService.PasswordPolicyValidator
	userRepo               userRepo.UserRepository
	tenantRepo             userRepo.TenantRepository
	tokenRepo              verificationRepo.VerificationTokenRepository
	templates              *template.Template
	logger                 logiface.Logger
}

// NewPasswordResetHandler 创建密码重置处理器
func NewPasswordResetHandler(
	verificationService *verificationService.VerificationApplicationService,
	userApplicationService *userService.UserApplicationService,
	appConfigService *userService.AppConfigService,
	passwordValidator *passwordService.PasswordPolicyValidator,
	userRepo userRepo.UserRepository,
	tenantRepo userRepo.TenantRepository,
	tokenRepo verificationRepo.VerificationTokenRepository,
	logger logiface.Logger,
) *PasswordResetHandler {
	// 解析HTML模板
	tmpl := template.Must(template.ParseGlob("templates/password_reset/*.html"))

	return &PasswordResetHandler{
		verificationService:    verificationService,
		userApplicationService: userApplicationService,
		appConfigService:       appConfigService,
		passwordValidator:      passwordValidator,
		userRepo:               userRepo,
		tenantRepo:             tenantRepo,
		tokenRepo:              tokenRepo,
		templates:              tmpl,
		logger:                 logger,
	}
}

// ShowResetForm 显示密码重置表单
func (h *PasswordResetHandler) ShowResetForm(w http.ResponseWriter, r *http.Request) {
	// 获取token参数
	token := r.URL.Query().Get("token")
	if token == "" {
		h.renderErrorPage(w, "重置链接无效", "重置链接已失效，请重新申请密码重置")
		return
	}

	ctx := r.Context()
	// 先尝试根据token查询verification_tokens，不限制租户
	tokenEntity, err := h.verificationService.GetTokenByTokenString(ctx, token)
	if err != nil || tokenEntity == nil {
		h.renderErrorPage(w, "重置链接无效", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 如果tokenEntity.TenantID为0，认定是没有查询到token
	if tokenEntity.TenantID == 0 {
		h.renderErrorPage(w, "重置链接无效", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 检查token是否有效
	if !tokenEntity.IsValid() {
		var message string
		var isPasswordExpired bool
		switch tokenEntity.Status {
		case verificationEntity.TokenStatusUsed:
			message = "重置链接已被使用，请重新申请密码重置"
		case verificationEntity.TokenStatusExpired:
			message = "重置链接已过期，请重新申请密码重置"
			isPasswordExpired = true
		case verificationEntity.TokenStatusRevoked:
			message = "重置链接已被撤销，请重新申请密码重置"
		default:
			message = "重置链接已失效，请重新申请密码重置"
		}
		h.renderErrorPageWithConfig(w, "重置链接无效", message, tokenEntity.TenantID, isPasswordExpired)
		return
	}

	// 检查token用途
	if tokenEntity.Purpose != verificationEntity.PurposePasswordReset {
		h.renderErrorPage(w, "重置链接无效", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 获取租户信息（用于模板显示）
	var tenantName string = "系统"
	var passwordResetURL string = "http://localhost:8084/password-reset" // 默认URL
	if tokenEntity.TenantID > 0 {
		tenant, err := h.tenantRepo.FindByID(ctx, tokenEntity.TenantID)
		if err == nil && tenant != nil {
			tenantName = tenant.TenantName
		}

		// 获取租户的配置信息，包括密码重置URL
		appID, hasAppID := usercontext.GetAppId(ctx)
		if !hasAppID || appID == "" {
			h.logger.Error(ctx, "AppID not found in context, cannot get tenant config",
				logiface.String("token", token))
			// 如果没有AppID，使用默认配置，不阻断流程
		} else {
			tenantInfoReq := &dto.GetTenantInfoRequest{
				AppID: appID,
			}
			tenantInfoResp, err := h.appConfigService.GetTenantInfo(ctx, tenantInfoReq)
			if err == nil && tenantInfoResp != nil && tenantInfoResp.Info != nil && tenantInfoResp.Info.PasswordResetURL != "" {
				passwordResetURL = tenantInfoResp.Info.PasswordResetURL
			}
		}
	}

	// 渲染重置表单
	data := map[string]interface{}{
		"Token":            token,
		"Target":           tokenEntity.Target,
		"ExpiresAt":        tokenEntity.ExpiresAt.Format("2006-01-02 15:04:05"),
		"TenantName":       tenantName,
		"PasswordResetURL": passwordResetURL,
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := h.templates.ExecuteTemplate(w, "reset_form.html", data); err != nil {
		http.Error(w, "页面加载失败", http.StatusInternalServerError)
	}
}

// ProcessReset 处理密码重置
func (h *PasswordResetHandler) ProcessReset(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}

	// 解析表单数据
	if err := r.ParseForm(); err != nil {
		h.renderErrorPage(w, "请求处理失败", "请重新提交")
		return
	}

	token := r.FormValue("token")
	newPassword := r.FormValue("new_password")
	confirmPassword := r.FormValue("confirm_password")

	// 基本验证
	if token == "" || newPassword == "" || confirmPassword == "" {
		h.renderErrorPage(w, "参数不完整", "请填写所有必填字段")
		return
	}

	if newPassword != confirmPassword {
		h.renderErrorPage(w, "密码不匹配", "两次输入的密码不一致")
		return
	}

	ctx := r.Context()

	// 验证token，先不限制租户获取
	tokenEntity, err := h.verificationService.GetTokenByTokenString(ctx, token)
	if err != nil || tokenEntity == nil {
		h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 如果tokenEntity.TenantID为0，认定是没有查询到token
	if tokenEntity.TenantID == 0 {
		h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 检查token是否有效
	if !tokenEntity.IsValid() {
		h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 获取租户的密码策略
	var passwordPolicy *userEntity.PasswordPolicy
	if tokenEntity.TenantID > 0 {
		// 有租户信息，根据租户ID查询密码策略
		appID, hasAppID := usercontext.GetAppId(ctx)
		if !hasAppID || appID == "" {
			h.logger.Error(ctx, "AppID not found in context, using default password policy",
				logiface.String("token", token))
			passwordPolicy = userEntity.NewDefaultPasswordPolicy()
		} else {
			policyReq := &dto.GetPasswordPolicyRequest{
				AppID: appID,
			}
			policyResp, err := h.appConfigService.GetPasswordPolicy(ctx, policyReq)
			if err != nil {
				// 如果获取失败，使用默认策略并记录错误（不阻断流程）
				h.logger.Warn(ctx, "Failed to get password policy, using default",
					logiface.Error(err), logiface.String("app_id", appID))
				passwordPolicy = userEntity.NewDefaultPasswordPolicy()
			} else {
				passwordPolicy = policyResp.Policy
			}
		}
	} else {
		// 没有租户信息，使用默认密码策略
		passwordPolicy = userEntity.NewDefaultPasswordPolicy()
	}

	// 验证密码是否符合策略
	if err := h.passwordValidator.ValidatePassword(newPassword, passwordPolicy); err != nil {
		h.renderErrorPage(w, "密码强度不符合要求", err.Error())
		return
	}

	// 查找用户（根据token中的target信息）
	var user *userEntity.User
	if tokenEntity.UserID != nil {
		// 如果token中有用户ID，直接根据用户ID查找
		user, err = h.userRepo.FindByID(ctx, *tokenEntity.UserID)
		if err != nil || user == nil {
			h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
			return
		}
	} else {
		// 如果没有用户ID，根据target（邮箱）查找用户
		user, err = h.userRepo.FindByEmail(ctx, tokenEntity.Target)
		if err != nil || user == nil {
			h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
			return
		}
	}

	if user == nil {
		h.renderErrorPage(w, "重置链接已过期", "重置链接已失效，请重新申请密码重置")
		return
	}

	// 执行密码重置
	resetReq := &dto.ResetPasswordDTO{
		UserID:      user.ID,
		NewPassword: newPassword,
	}

	if err := h.userApplicationService.ResetPassword(ctx, resetReq); err != nil {
		h.renderErrorPage(w, "密码重置失败", "请稍后重试或联系客服")
		return
	}

	// 标记token为已使用（确保token失效） - 只有密码重置成功后才执行
	tokenEntity.MarkAsUsed()
	if err := h.tokenRepo.Update(ctx, tokenEntity); err != nil {
		// 记录错误但不影响用户流程，因为密码已经重置成功
		// 即使token更新失败，密码重置也已经成功了
		// TODO: 添加日志记录token更新失败
	}

	// 渲染成功页面
	h.renderSuccessPageWithConfig(w, "密码重置成功", "您的密码已成功重置，请使用新密码登录", tokenEntity.TenantID)
}

// ShowRequestPage 显示重新申请重置页面
func (h *PasswordResetHandler) ShowRequestPage(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := h.templates.ExecuteTemplate(w, "request.html", map[string]interface{}{}); err != nil {
		http.Error(w, "页面加载失败", http.StatusInternalServerError)
	}
}

// renderErrorPage 渲染错误页面
func (h *PasswordResetHandler) renderErrorPage(w http.ResponseWriter, title, message string) {
	h.renderErrorPageWithConfig(w, title, message, 0, false)
}

// renderErrorPageWithConfig 渲染带配置的错误页面
func (h *PasswordResetHandler) renderErrorPageWithConfig(w http.ResponseWriter, title, message string, tenantID int64, isPasswordExpired bool) {
	var passwordResetRequestURL string
	var loginURL string

	// 获取租户配置
	if tenantID > 0 {
		// 这里没有请求上下文，无法获取AppID，使用默认配置
		// TODO: 重构方法签名，传入上下文参数以获取AppID
		h.logger.Warn(context.Background(), "Cannot get AppID for tenant config, using default values",
			logiface.Int64("tenant_id", tenantID))
	}

	data := map[string]interface{}{
		"Title":                   title,
		"Message":                 message,
		"Type":                    "error",
		"ShowResetRequestButton":  isPasswordExpired && passwordResetRequestURL != "",
		"PasswordResetRequestURL": passwordResetRequestURL,
		"ShowLoginButton":         loginURL != "",
		"LoginURL":                loginURL,
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := h.templates.ExecuteTemplate(w, "result.html", data); err != nil {
		http.Error(w, "模板渲染失败", http.StatusInternalServerError)
	}
}

// renderSuccessPage 渲染成功页面
func (h *PasswordResetHandler) renderSuccessPage(w http.ResponseWriter, title, message string) {
	h.renderSuccessPageWithConfig(w, title, message, 0)
}

// renderSuccessPageWithConfig 渲染带配置的成功页面
func (h *PasswordResetHandler) renderSuccessPageWithConfig(w http.ResponseWriter, title, message string, tenantID int64) {
	var loginURL string

	// 获取租户配置
	if tenantID > 0 {
		// 这里没有请求上下文，无法获取AppID，使用默认配置
		// TODO: 重构方法签名，传入上下文参数以获取AppID
		h.logger.Warn(context.Background(), "Cannot get AppID for tenant config, using default values",
			logiface.Int64("tenant_id", tenantID))
	}

	data := map[string]interface{}{
		"Title":           title,
		"Message":         message,
		"Type":            "success",
		"ShowLoginButton": loginURL != "",
		"LoginURL":        loginURL,
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := h.templates.ExecuteTemplate(w, "result.html", data); err != nil {
		http.Error(w, "模板渲染失败", http.StatusInternalServerError)
	}
}

// ShowResetFormGin Gin适配器方法
func (h *PasswordResetHandler) ShowResetFormGin(c *gin.Context) {
	h.ShowResetForm(c.Writer, c.Request)
}

// ProcessResetGin Gin适配器方法
func (h *PasswordResetHandler) ProcessResetGin(c *gin.Context) {
	h.ProcessReset(c.Writer, c.Request)
}

// ShowRequestPageGin Gin适配器方法
func (h *PasswordResetHandler) ShowRequestPageGin(c *gin.Context) {
	h.ShowRequestPage(c.Writer, c.Request)
}
