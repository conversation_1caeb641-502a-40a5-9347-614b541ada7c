# User Errors 替换指南

## 问题分析

### 1. 错误码冲突问题

**问题描述**：用户模块的错误码与通用错误码存在冲突，导致代码中可能出现歧义。

**冲突的错误码**：
- `CodeResourceNotFound` (110309) vs 通用 `CodeResourceNotFound` (40001)
- `CodeAccountDisabled` (110015) vs 通用 `CodeAccountDisabled` (20013)
- `CodeOperationNotAllowed` (110311) vs 通用 `CodeOperationNotAllowed` (40011)

**解决方案**：将用户模块特有的错误码重命名，避免与通用错误码冲突。

### 2. 错误使用场景问题

**问题描述**：某些错误码的使用场景不够明确，可能导致错误处理不当。

## 替换建议

### 已完成的替换

#### 1. 错误码重命名

| 原错误码 | 新错误码 | 说明 |
|---------|---------|------|
| `CodeAccountLocked` | `CodeUserAccountLocked` | 用户账户被锁定 |
| `CodeAccountDisabled` | `CodeUserAccountDisabled` | 用户账户被禁用 |
| `CodeResourceNotFound` | `CodeUserResourceNotFound` | 用户资源不存在 |
| `CodeOperationNotAllowed` | `CodeUserOperationNotAllowed` | 用户操作不被允许 |

#### 2. 错误消息映射更新

已更新 `errorMessages` 映射，确保所有重命名的错误码都有对应的错误消息。

#### 3. 便捷函数更新

已更新 `NewAccountLockedError` 函数，使用新的错误码 `CodeUserAccountLocked`。

### 需要继续替换的地方

#### 1. 业务逻辑层替换

**文件**：`internal/application/auth/service/auth_application_service.go`

**替换内容**：
```go
// 替换前
return nil, 0, userErrors.NewUserError(userErrors.CodeAccountDisabled, fmt.Sprintf("reason: %s", user.LockReason))
return nil, userErrors.NewBusinessError(userErrors.CodeResourceNotFound, fmt.Sprintf("session not found: %v", err))
return nil, userErrors.NewBusinessError(userErrors.CodeOperationNotAllowed, "session is not active")
return nil, userErrors.NewBusinessError(userErrors.CodeAccountDisabled, "user account is not active")
return userErrors.NewBusinessError(userErrors.CodeOperationNotAllowed, "session does not belong to user")

// 替换后
return nil, 0, userErrors.NewUserError(userErrors.CodeUserAccountDisabled, fmt.Sprintf("reason: %s", user.LockReason))
return nil, userErrors.NewBusinessError(userErrors.CodeSessionNotFound, fmt.Sprintf("session not found: %v", err))
return nil, userErrors.NewBusinessError(userErrors.CodeUserOperationNotAllowed, "session is not active")
return nil, userErrors.NewBusinessError(userErrors.CodeUserAccountDisabled, "user account is not active")
return userErrors.NewBusinessError(userErrors.CodeUserOperationNotAllowed, "session does not belong to user")
```

**文件**：`internal/application/user/service/base_service.go`

**替换内容**：
```go
// 替换前
return nil, errors.NewUserError(errors.CodeResourceNotFound, fmt.Sprintf("%s不存在", entityName))
return errors.NewUserError(errors.CodeOperationNotAllowed, fmt.Sprintf("不能删除系统%s", entityName))
return errors.NewUserError(errors.CodeOperationNotAllowed, fmt.Sprintf("不能删除有关联用户的%s", entityName))
return errors.NewUserError(errors.CodeOperationNotAllowed, fmt.Sprintf("不能删除有子实体的%s", entityName))

// 替换后
return nil, errors.NewUserError(errors.CodeUserResourceNotFound, fmt.Sprintf("%s不存在", entityName))
return errors.NewUserError(errors.CodeUserOperationNotAllowed, fmt.Sprintf("不能删除系统%s", entityName))
return errors.NewUserError(errors.CodeUserOperationNotAllowed, fmt.Sprintf("不能删除有关联用户的%s", entityName))
return errors.NewUserError(errors.CodeUserOperationNotAllowed, fmt.Sprintf("不能删除有子实体的%s", entityName))
```

#### 2. 接口层替换

**文件**：`internal/interfaces/http/handlers/error_handler.go`

**替换内容**：
```go
// 替换前
case userErrors.CodeAccountLocked:
case userErrors.CodeAccountDisabled:
case userErrors.CodeResourceNotFound:
case userErrors.CodeOperationNotAllowed:

// 替换后
case userErrors.CodeUserAccountLocked:
case userErrors.CodeUserAccountDisabled:
case userErrors.CodeUserResourceNotFound:
case userErrors.CodeUserOperationNotAllowed:
```

**文件**：`internal/interfaces/http/handlers/resource_handler.go`

**替换内容**：
```go
// 替换前
if userErr, ok := err.(*errors.UserError); ok && userErr.GetCode() == errors.CodeResourceNotFound {

// 替换后
if userErr, ok := err.(*errors.UserError); ok && userErr.GetCode() == errors.CodeUserResourceNotFound {
```

### 3. 其他需要检查的文件

以下文件可能需要类似的替换：

1. `internal/application/auth/service/register_application_service.go`
2. `internal/application/user/service/department_application_service.go`
3. `internal/application/user/service/position_application_service.go`
4. `internal/application/user/service/tenant_application_service.go`
5. `internal/application/fileupload/service/file_upload_application_service.go`
6. `internal/infrastructure/thirdparty/config_service.go`
7. `internal/infrastructure/external/email_service_client.go`

### 4. 测试文件更新

**文件**：`internal/domain/errors/user_errors_test.go`

需要更新测试用例中的错误码引用，确保测试能够通过。

## 替换原则

### 1. 错误码命名原则

- 用户模块特有的错误码应该以 `User` 前缀或后缀标识
- 避免与通用错误码冲突
- 保持错误码的语义清晰

### 2. 错误处理原则

- 系统级错误使用通用错误码
- 业务逻辑错误使用模块特定错误码
- 确保错误消息对用户友好

### 3. 向后兼容性

- 保持错误码的数值不变
- 只修改错误码的常量名称
- 确保错误处理逻辑的一致性

## 验证步骤

1. **编译检查**：确保所有文件能够正常编译
2. **测试运行**：运行相关测试用例，确保功能正常
3. **错误处理验证**：测试各种错误场景，确保错误处理正确
4. **API响应验证**：确保API返回的错误码和消息正确

## 注意事项

1. **错误码范围**：用户模块错误码范围保持在 110000-119999
2. **错误消息**：确保错误消息对用户友好，不暴露系统内部信息
3. **日志记录**：在错误处理中记录详细的错误信息用于调试
4. **文档更新**：更新相关文档，说明错误码的使用场景

## 总结

通过这次替换，我们解决了以下问题：

1. **消除了错误码冲突**：用户模块错误码不再与通用错误码冲突
2. **提高了代码可读性**：错误码名称更加明确，便于理解
3. **增强了错误处理的一致性**：统一的错误处理模式
4. **改善了维护性**：清晰的错误码分类和命名

这些改进将有助于提高代码质量和系统的可维护性。 