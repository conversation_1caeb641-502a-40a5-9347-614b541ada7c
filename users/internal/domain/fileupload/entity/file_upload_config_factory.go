package entity

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// FileUploadConfigFactory 文件上传配置工厂
type FileUploadConfigFactory struct {
	idGenerator entity.IDGenerator
}

// NewFileUploadConfigFactory 创建文件上传配置工厂
func NewFileUploadConfigFactory(idGenerator entity.IDGenerator) *FileUploadConfigFactory {
	if idGenerator == nil {
		panic("ID generator cannot be nil")
	}
	return &FileUploadConfigFactory{
		idGenerator: idGenerator,
	}
}

// CreateFileUploadConfig 创建新的文件上传配置
func (f *FileUploadConfigFactory) CreateFileUploadConfig(ctx context.Context, tenantID, appID int64, sceneCode, sceneName string, allowedTypes []string, maxFileSize int64) (*FileUploadConfig, error) {
	// 生成分布式ID
	configID, err := f.idGenerator.GenerateID(ctx, "file_upload_config")
	if err != nil {
		return nil, fmt.Errorf("failed to generate file upload config ID: %w", err)
	}

	// 创建配置实体
	config := NewFileUploadConfig(tenantID, appID, sceneCode, sceneName, allowedTypes, maxFileSize)
	config.SetID(configID)

	return config, nil
}

// CreateFileUploadConfigWithContext 使用分布式ID创建新的文件上传配置
func (f *FileUploadConfigFactory) CreateFileUploadConfigWithContext(ctx context.Context, tenantID, appID int64, sceneCode, sceneName string, allowedTypes []string, maxFileSize int64) (*FileUploadConfig, error) {
	// 生成分布式ID
	configID, err := f.idGenerator.GenerateID(ctx, "file_upload_config")
	if err != nil {
		return nil, fmt.Errorf("failed to generate file upload config ID: %w", err)
	}

	// 创建配置实体
	config := NewFileUploadConfig(tenantID, appID, sceneCode, sceneName, allowedTypes, maxFileSize)
	config.SetID(configID)

	return config, nil
}
