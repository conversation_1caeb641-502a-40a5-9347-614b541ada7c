package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// TenantConfigRepository 租户配置仓储接口
type TenantConfigRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, config *entity.TenantConfig) error
	Update(ctx context.Context, config *entity.TenantConfig) error
	Delete(ctx context.Context, configKey string) error
	FindByKey(ctx context.Context, configKey string) (*entity.TenantConfig, error)

	// 配置查询
	FindByTenantID(ctx context.Context) ([]*entity.TenantConfig, error)
	FindSystemConfig(ctx context.Context, configKey string) (*entity.TenantConfig, error)
	FindAllSystemConfigs(ctx context.Context) ([]*entity.TenantConfig, error)

	// 配置管理
	UpsertConfig(ctx context.Context, req *dto.UpsertConfigRequest) error
	CopySystemConfigsToTenant(ctx context.Context) error
	DeleteTenantConfigs(ctx context.Context) error

	// 配置检查
	Exists(ctx context.Context, configKey string) (bool, error)
	GetEffectiveConfig(ctx context.Context, configKey string) (*entity.TenantConfig, error)
}
