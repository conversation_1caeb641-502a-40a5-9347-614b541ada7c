package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// ApplicationRepository 应用仓储接口
type ApplicationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, application *entity.Application) error
	GetByID(ctx context.Context, id int64) (*entity.Application, error)
	GetByInternalAppID(ctx context.Context, internalAppID, tenantID int64) (*entity.Application, error)
	GetByAppID(ctx context.Context, appID string) (*entity.Application, error)
	Update(ctx context.Context, application *entity.Application) error
	Delete(ctx context.Context, id int64) error
	SoftDelete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, params *ApplicationQueryParams) ([]*entity.Application, int64, error)
	Count(ctx context.Context, params *ApplicationQueryParams) (int64, error)

	// 状态管理
	Enable(ctx context.Context, internalAppID int64, updatedBy int64) error
	Disable(ctx context.Context, internalAppID int64, updatedBy int64) error

	// 唯一性检查
	ExistsByAppID(ctx context.Context, appID string, excludeID ...int64) (bool, error)
	ExistsByInternalAppID(ctx context.Context, internalAppID int64, excludeID ...int64) (bool, error)

	// 统计操作
	GetStats(ctx context.Context) (*ApplicationStats, error)
}

// ApplicationQueryParams 应用查询参数
type ApplicationQueryParams struct {
	// 基础查询条件
	IDs            []int64  `json:"ids,omitempty"`
	InternalAppIDs []int64  `json:"internal_app_ids,omitempty"`
	AppIDs         []string `json:"app_ids,omitempty"`

	// 应用信息过滤
	AppName  *string                   `json:"app_name,omitempty"`
	AppType  *entity.ApplicationType   `json:"app_type,omitempty"`
	Status   *entity.ApplicationStatus `json:"status,omitempty"`
	IsSystem *bool                     `json:"is_system,omitempty"`

	// 搜索条件
	Keyword      *string  `json:"keyword,omitempty"`       // 关键词搜索(应用名称、描述、app_id)
	SearchFields []string `json:"search_fields,omitempty"` // 搜索字段列表

	// 分页和排序
	Page     int    `json:"page"`                // 页码(从1开始)
	Size     int    `json:"size"`                // 每页大小
	OrderBy  string `json:"order_by,omitempty"`  // 排序字段
	OrderDir string `json:"order_dir,omitempty"` // 排序方向: asc, desc

	// 时间范围
	CreatedAtStart *string `json:"created_at_start,omitempty"` // 创建时间开始
	CreatedAtEnd   *string `json:"created_at_end,omitempty"`   // 创建时间结束

	// 软删除处理
	WithDeleted bool `json:"with_deleted,omitempty"` // 是否包含已删除记录
}

// NewApplicationQueryParams 创建新的应用查询参数
func NewApplicationQueryParams() *ApplicationQueryParams {
	return &ApplicationQueryParams{
		Page:     1,
		Size:     10,
		OrderBy:  "created_at",
		OrderDir: "desc",
	}
}

// WithKeyword 设置关键词搜索
func (q *ApplicationQueryParams) WithKeyword(keyword string) *ApplicationQueryParams {
	q.Keyword = &keyword
	q.SearchFields = []string{"app_name", "description", "app_id"}
	return q
}

// WithAppType 设置应用类型过滤
func (q *ApplicationQueryParams) WithAppType(appType entity.ApplicationType) *ApplicationQueryParams {
	q.AppType = &appType
	return q
}

// WithStatus 设置状态过滤
func (q *ApplicationQueryParams) WithStatus(status entity.ApplicationStatus) *ApplicationQueryParams {
	q.Status = &status
	return q
}

// WithIsSystem 设置是否系统应用过滤
func (q *ApplicationQueryParams) WithIsSystem(isSystem bool) *ApplicationQueryParams {
	q.IsSystem = &isSystem
	return q
}

// WithPagination 设置分页参数
func (q *ApplicationQueryParams) WithPagination(page, size int) *ApplicationQueryParams {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	q.Page = page
	q.Size = size
	return q
}

// WithOrder 设置排序参数
func (q *ApplicationQueryParams) WithOrder(orderBy, orderDir string) *ApplicationQueryParams {
	q.OrderBy = orderBy
	q.OrderDir = orderDir
	return q
}

// WithTimeRange 设置时间范围
func (q *ApplicationQueryParams) WithTimeRange(start, end *string) *ApplicationQueryParams {
	q.CreatedAtStart = start
	q.CreatedAtEnd = end
	return q
}

// GetOffset 获取偏移量
func (q *ApplicationQueryParams) GetOffset() int {
	return (q.Page - 1) * q.Size
}

// ApplicationStats 应用统计信息
type ApplicationStats struct {
	TotalCount   int64                              `json:"total_count"`   // 总应用数
	ActiveCount  int64                              `json:"active_count"`  // 活跃应用数
	SystemCount  int64                              `json:"system_count"`  // 系统应用数
	TypeCounts   map[entity.ApplicationType]int64   `json:"type_counts"`   // 各类型应用数量
	StatusCounts map[entity.ApplicationStatus]int64 `json:"status_counts"` // 各状态应用数量
}
