package value_object

// TenantStatus 租户状态值对象
type TenantStatus string

const (
	// TenantStatusActive 激活状态
	TenantStatusActive TenantStatus = "active"
	// TenantStatusDisabled 禁用状态
	TenantStatusDisabled TenantStatus = "disabled"
	// TenantStatusExpired 过期状态
	TenantStatusExpired TenantStatus = "expired"
)

// IsActive 检查是否为激活状态
func (s TenantStatus) IsActive() bool {
	return s == TenantStatusActive
}

// IsDisabled 检查是否为禁用状态
func (s TenantStatus) IsDisabled() bool {
	return s == TenantStatusDisabled
}

// IsExpired 检查是否为过期状态
func (s TenantStatus) IsExpired() bool {
	return s == TenantStatusExpired
}

// CanLogin 检查是否可以登录
func (s TenantStatus) CanLogin() bool {
	return s == TenantStatusActive
}

// CanCreateUser 检查是否可以创建用户
func (s TenantStatus) CanCreateUser() bool {
	return s == TenantStatusActive
}

// CanAccessSystem 检查是否可以访问系统
func (s TenantStatus) CanAccessSystem() bool {
	return s == TenantStatusActive
}

// GetDisplayName 获取状态的显示名称
func (s TenantStatus) GetDisplayName() string {
	switch s {
	case TenantStatusActive:
		return "激活"
	case TenantStatusDisabled:
		return "禁用"
	case TenantStatusExpired:
		return "过期"
	default:
		return "未知"
	}
}

// GetDisplayColor 获取状态显示颜色（用于前端显示）
func (s TenantStatus) GetDisplayColor() string {
	switch s {
	case TenantStatusActive:
		return "green"
	case TenantStatusDisabled:
		return "red"
	case TenantStatusExpired:
		return "orange"
	default:
		return "gray"
	}
}

// String 实现Stringer接口
func (s TenantStatus) String() string {
	return string(s)
}

// IsValid 验证状态是否有效
func (s TenantStatus) IsValid() bool {
	switch s {
	case TenantStatusActive, TenantStatusDisabled, TenantStatusExpired:
		return true
	default:
		return false
	}
}

// ToDBValue 转换为数据库存储值
func (s TenantStatus) ToDBValue() string {
	return string(s)
}

// FromDBValue 从数据库值创建状态
func FromDBValueToTenantStatus(value string) TenantStatus {
	status := TenantStatus(value)
	if !status.IsValid() {
		return TenantStatusActive // 默认为激活状态
	}
	return status
}

// GetAllTenantStatuses 获取所有可用的租户状态
func GetAllTenantStatuses() []TenantStatus {
	return []TenantStatus{
		TenantStatusActive,
		TenantStatusDisabled,
		TenantStatusExpired,
	}
}

// GetTenantStatusOptions 获取租户状态选项（用于前端下拉框）
func GetTenantStatusOptions() []map[string]interface{} {
	statuses := GetAllTenantStatuses()
	options := make([]map[string]interface{}, len(statuses))

	for i, status := range statuses {
		options[i] = map[string]interface{}{
			"value":      status.String(),
			"label":      status.GetDisplayName(),
			"color":      status.GetDisplayColor(),
			"can_login":  status.CanLogin(),
			"can_access": status.CanAccessSystem(),
		}
	}

	return options
}
