package value_object

import (
	"encoding/json"
	"fmt"
)

// ApplicationConfig 应用配置值对象
type ApplicationConfig struct {
	CallbackURLs      []string               `json:"callback_urls"`
	AllowedOrigins    []string               `json:"allowed_origins"`
	Scopes            []string               `json:"scopes"`
	LogoURL           string                 `json:"logo_url"`
	HomepageURL       string                 `json:"homepage_url"`
	PrivacyPolicyURL  string                 `json:"privacy_policy_url"`
	TermsOfServiceURL string                 `json:"terms_of_service_url"`
	ContactEmail      string                 `json:"contact_email"`
	Extra             map[string]interface{} `json:"extra"`
}

// NewApplicationConfig 创建新的应用配置
func NewApplicationConfig() *ApplicationConfig {
	return &ApplicationConfig{
		CallbackURLs:   make([]string, 0),
		AllowedOrigins: make([]string, 0),
		Scopes:         make([]string, 0),
		Extra:          make(map[string]interface{}),
	}
}

// JSONMap 与entity中的JSONMap类型兼容
type JSONMap map[string]interface{}

// ToJSONMap 将配置转换为JSONMap
func (c *ApplicationConfig) ToJSONMap() JSONMap {
	result := make(JSONMap)

	if len(c.CallbackURLs) > 0 {
		result["callback_urls"] = c.CallbackURLs
	}
	if len(c.AllowedOrigins) > 0 {
		result["allowed_origins"] = c.AllowedOrigins
	}
	if len(c.Scopes) > 0 {
		result["scopes"] = c.Scopes
	}
	if c.LogoURL != "" {
		result["logo_url"] = c.LogoURL
	}
	if c.HomepageURL != "" {
		result["homepage_url"] = c.HomepageURL
	}
	if c.PrivacyPolicyURL != "" {
		result["privacy_policy_url"] = c.PrivacyPolicyURL
	}
	if c.TermsOfServiceURL != "" {
		result["terms_of_service_url"] = c.TermsOfServiceURL
	}
	if c.ContactEmail != "" {
		result["contact_email"] = c.ContactEmail
	}

	// 添加额外配置
	for k, v := range c.Extra {
		result[k] = v
	}

	return result
}

// FromJSONMap 从JSONMap构建配置
func (c *ApplicationConfig) FromJSONMap(data JSONMap) {
	if data == nil {
		return
	}

	// 解析callback_urls
	if callbackURLs, ok := data["callback_urls"].([]interface{}); ok {
		c.CallbackURLs = make([]string, len(callbackURLs))
		for i, url := range callbackURLs {
			if s, ok := url.(string); ok {
				c.CallbackURLs[i] = s
			}
		}
	}

	// 解析allowed_origins
	if allowedOrigins, ok := data["allowed_origins"].([]interface{}); ok {
		c.AllowedOrigins = make([]string, len(allowedOrigins))
		for i, origin := range allowedOrigins {
			if s, ok := origin.(string); ok {
				c.AllowedOrigins[i] = s
			}
		}
	}

	// 解析scopes
	if scopes, ok := data["scopes"].([]interface{}); ok {
		c.Scopes = make([]string, len(scopes))
		for i, scope := range scopes {
			if s, ok := scope.(string); ok {
				c.Scopes[i] = s
			}
		}
	}

	// 解析字符串字段
	if logoURL, ok := data["logo_url"].(string); ok {
		c.LogoURL = logoURL
	}

	if homepageURL, ok := data["homepage_url"].(string); ok {
		c.HomepageURL = homepageURL
	}

	if privacyPolicyURL, ok := data["privacy_policy_url"].(string); ok {
		c.PrivacyPolicyURL = privacyPolicyURL
	}

	if termsOfServiceURL, ok := data["terms_of_service_url"].(string); ok {
		c.TermsOfServiceURL = termsOfServiceURL
	}

	if contactEmail, ok := data["contact_email"].(string); ok {
		c.ContactEmail = contactEmail
	}

	// 存储其他额外字段
	knownFields := map[string]bool{
		"callback_urls":        true,
		"allowed_origins":      true,
		"scopes":               true,
		"logo_url":             true,
		"homepage_url":         true,
		"privacy_policy_url":   true,
		"terms_of_service_url": true,
		"contact_email":        true,
	}

	for k, v := range data {
		if !knownFields[k] {
			c.Extra[k] = v
		}
	}
}

// AddCallbackURL 添加回调URL
func (c *ApplicationConfig) AddCallbackURL(url string) {
	if url != "" && !c.HasCallbackURL(url) {
		c.CallbackURLs = append(c.CallbackURLs, url)
	}
}

// RemoveCallbackURL 移除回调URL
func (c *ApplicationConfig) RemoveCallbackURL(url string) {
	for i, existingURL := range c.CallbackURLs {
		if existingURL == url {
			c.CallbackURLs = append(c.CallbackURLs[:i], c.CallbackURLs[i+1:]...)
			break
		}
	}
}

// HasCallbackURL 检查是否包含指定的回调URL
func (c *ApplicationConfig) HasCallbackURL(url string) bool {
	for _, existingURL := range c.CallbackURLs {
		if existingURL == url {
			return true
		}
	}
	return false
}

// AddAllowedOrigin 添加允许的来源
func (c *ApplicationConfig) AddAllowedOrigin(origin string) {
	if origin != "" && !c.HasAllowedOrigin(origin) {
		c.AllowedOrigins = append(c.AllowedOrigins, origin)
	}
}

// RemoveAllowedOrigin 移除允许的来源
func (c *ApplicationConfig) RemoveAllowedOrigin(origin string) {
	for i, existingOrigin := range c.AllowedOrigins {
		if existingOrigin == origin {
			c.AllowedOrigins = append(c.AllowedOrigins[:i], c.AllowedOrigins[i+1:]...)
			break
		}
	}
}

// HasAllowedOrigin 检查是否包含指定的来源
func (c *ApplicationConfig) HasAllowedOrigin(origin string) bool {
	for _, existingOrigin := range c.AllowedOrigins {
		if existingOrigin == origin {
			return true
		}
	}
	return false
}

// AddScope 添加作用域
func (c *ApplicationConfig) AddScope(scope string) {
	if scope != "" && !c.HasScope(scope) {
		c.Scopes = append(c.Scopes, scope)
	}
}

// RemoveScope 移除作用域
func (c *ApplicationConfig) RemoveScope(scope string) {
	for i, existingScope := range c.Scopes {
		if existingScope == scope {
			c.Scopes = append(c.Scopes[:i], c.Scopes[i+1:]...)
			break
		}
	}
}

// HasScope 检查是否包含指定的作用域
func (c *ApplicationConfig) HasScope(scope string) bool {
	for _, existingScope := range c.Scopes {
		if existingScope == scope {
			return true
		}
	}
	return false
}

// SetExtraField 设置额外字段
func (c *ApplicationConfig) SetExtraField(key string, value interface{}) {
	c.Extra[key] = value
}

// GetExtraField 获取额外字段
func (c *ApplicationConfig) GetExtraField(key string) interface{} {
	return c.Extra[key]
}

// RemoveExtraField 移除额外字段
func (c *ApplicationConfig) RemoveExtraField(key string) {
	delete(c.Extra, key)
}

// Validate 验证配置
func (c *ApplicationConfig) Validate() error {
	// 验证回调URL格式
	for _, url := range c.CallbackURLs {
		if url == "" {
			return fmt.Errorf("callback URL cannot be empty")
		}
	}

	// 验证来源格式
	for _, origin := range c.AllowedOrigins {
		if origin == "" {
			return fmt.Errorf("allowed origin cannot be empty")
		}
	}

	// 验证作用域格式
	for _, scope := range c.Scopes {
		if scope == "" {
			return fmt.Errorf("scope cannot be empty")
		}
	}

	return nil
}

// Clone 克隆配置
func (c *ApplicationConfig) Clone() *ApplicationConfig {
	clone := NewApplicationConfig()

	// 克隆切片
	if len(c.CallbackURLs) > 0 {
		clone.CallbackURLs = make([]string, len(c.CallbackURLs))
		copy(clone.CallbackURLs, c.CallbackURLs)
	}

	if len(c.AllowedOrigins) > 0 {
		clone.AllowedOrigins = make([]string, len(c.AllowedOrigins))
		copy(clone.AllowedOrigins, c.AllowedOrigins)
	}

	if len(c.Scopes) > 0 {
		clone.Scopes = make([]string, len(c.Scopes))
		copy(clone.Scopes, c.Scopes)
	}

	// 克隆字符串字段
	clone.LogoURL = c.LogoURL
	clone.HomepageURL = c.HomepageURL
	clone.PrivacyPolicyURL = c.PrivacyPolicyURL
	clone.TermsOfServiceURL = c.TermsOfServiceURL
	clone.ContactEmail = c.ContactEmail

	// 克隆额外字段
	for k, v := range c.Extra {
		clone.Extra[k] = v
	}

	return clone
}

// ToJSON 转换为JSON字符串
func (c *ApplicationConfig) ToJSON() ([]byte, error) {
	return json.Marshal(c)
}

// FromJSON 从JSON字符串构建配置
func (c *ApplicationConfig) FromJSON(data []byte) error {
	return json.Unmarshal(data, c)
}
