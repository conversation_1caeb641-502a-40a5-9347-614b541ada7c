package value_object

// DepartmentStatus 部门状态值对象
type DepartmentStatus string

const (
	// DepartmentStatusActive 激活状态
	DepartmentStatusActive DepartmentStatus = "active"
	// DepartmentStatusDisabled 禁用状态
	DepartmentStatusDisabled DepartmentStatus = "disabled"
)

// IsActive 检查是否为激活状态
func (s DepartmentStatus) IsActive() bool {
	return s == DepartmentStatusActive
}

// IsDisabled 检查是否为禁用状态
func (s DepartmentStatus) IsDisabled() bool {
	return s == DepartmentStatusDisabled
}

// CanAssignUser 检查是否可以分配用户
func (s DepartmentStatus) CanAssignUser() bool {
	return s == DepartmentStatusActive
}

// CanCreateSubDepartment 检查是否可以创建子部门
func (s DepartmentStatus) CanCreateSubDepartment() bool {
	return s == DepartmentStatusActive
}

// CanManage 检查是否可以管理（修改、删除等操作）
func (s DepartmentStatus) CanManage() bool {
	return s == DepartmentStatusActive
}

// GetDisplayName 获取状态的显示名称
func (s DepartmentStatus) GetDisplayName() string {
	switch s {
	case DepartmentStatusActive:
		return "激活"
	case DepartmentStatusDisabled:
		return "禁用"
	default:
		return "未知"
	}
}

// GetDisplayColor 获取状态显示颜色（用于前端显示）
func (s DepartmentStatus) GetDisplayColor() string {
	switch s {
	case DepartmentStatusActive:
		return "green"
	case DepartmentStatusDisabled:
		return "red"
	default:
		return "gray"
	}
}

// String 实现Stringer接口
func (s DepartmentStatus) String() string {
	return string(s)
}

// IsValid 验证状态是否有效
func (s DepartmentStatus) IsValid() bool {
	switch s {
	case DepartmentStatusActive, DepartmentStatusDisabled:
		return true
	default:
		return false
	}
}

// ToDBValue 转换为数据库存储值
func (s DepartmentStatus) ToDBValue() string {
	return string(s)
}

// FromDBValueToDepartmentStatus 从数据库值创建状态
func FromDBValueToDepartmentStatus(value string) DepartmentStatus {
	status := DepartmentStatus(value)
	if !status.IsValid() {
		return DepartmentStatusActive // 默认为激活状态
	}
	return status
}

// GetAllDepartmentStatuses 获取所有可用的部门状态
func GetAllDepartmentStatuses() []DepartmentStatus {
	return []DepartmentStatus{
		DepartmentStatusActive,
		DepartmentStatusDisabled,
	}
}

// GetDepartmentStatusOptions 获取部门状态选项（用于前端下拉框）
func GetDepartmentStatusOptions() []map[string]interface{} {
	statuses := GetAllDepartmentStatuses()
	options := make([]map[string]interface{}, len(statuses))

	for i, status := range statuses {
		options[i] = map[string]interface{}{
			"value":           status.String(),
			"label":           status.GetDisplayName(),
			"color":           status.GetDisplayColor(),
			"can_assign_user": status.CanAssignUser(),
			"can_create_sub":  status.CanCreateSubDepartment(),
			"can_manage":      status.CanManage(),
		}
	}

	return options
}
