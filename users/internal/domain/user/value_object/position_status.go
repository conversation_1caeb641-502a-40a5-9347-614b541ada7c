package value_object

// PositionStatus 职位状态值对象
type PositionStatus string

const (
	// PositionStatusActive 激活状态
	PositionStatusActive PositionStatus = "active"
	// PositionStatusDisabled 禁用状态
	PositionStatusDisabled PositionStatus = "disabled"
)

// IsActive 检查是否为激活状态
func (s PositionStatus) IsActive() bool {
	return s == PositionStatusActive
}

// IsDisabled 检查是否为禁用状态
func (s PositionStatus) IsDisabled() bool {
	return s == PositionStatusDisabled
}

// CanAssignUser 检查是否可以分配用户
func (s PositionStatus) CanAssignUser() bool {
	return s == PositionStatusActive
}

// CanSetPermissions 检查是否可以设置权限
func (s PositionStatus) CanSetPermissions() bool {
	return s == PositionStatusActive
}

// CanManage 检查是否可以管理（修改、删除等操作）
func (s PositionStatus) CanManage() bool {
	return s == PositionStatusActive
}

// CanPromote 检查是否可以晋升
func (s PositionStatus) CanPromote() bool {
	return s == PositionStatusActive
}

// CanDemote 检查是否可以降级
func (s PositionStatus) CanDemote() bool {
	return s == PositionStatusActive
}

// GetDisplayName 获取状态的显示名称
func (s PositionStatus) GetDisplayName() string {
	switch s {
	case PositionStatusActive:
		return "激活"
	case PositionStatusDisabled:
		return "禁用"
	default:
		return "未知"
	}
}

// GetDisplayColor 获取状态显示颜色（用于前端显示）
func (s PositionStatus) GetDisplayColor() string {
	switch s {
	case PositionStatusActive:
		return "green"
	case PositionStatusDisabled:
		return "red"
	default:
		return "gray"
	}
}

// String 实现Stringer接口
func (s PositionStatus) String() string {
	return string(s)
}

// IsValid 验证状态是否有效
func (s PositionStatus) IsValid() bool {
	switch s {
	case PositionStatusActive, PositionStatusDisabled:
		return true
	default:
		return false
	}
}

// ToDBValue 转换为数据库存储值
func (s PositionStatus) ToDBValue() string {
	return string(s)
}

// FromDBValueToPositionStatus 从数据库值创建状态
func FromDBValueToPositionStatus(value string) PositionStatus {
	status := PositionStatus(value)
	if !status.IsValid() {
		return PositionStatusActive // 默认为激活状态
	}
	return status
}

// GetAllPositionStatuses 获取所有可用的职位状态
func GetAllPositionStatuses() []PositionStatus {
	return []PositionStatus{
		PositionStatusActive,
		PositionStatusDisabled,
	}
}

// GetPositionStatusOptions 获取职位状态选项（用于前端下拉框）
func GetPositionStatusOptions() []map[string]interface{} {
	statuses := GetAllPositionStatuses()
	options := make([]map[string]interface{}, len(statuses))

	for i, status := range statuses {
		options[i] = map[string]interface{}{
			"value":               status.String(),
			"label":               status.GetDisplayName(),
			"color":               status.GetDisplayColor(),
			"can_assign_user":     status.CanAssignUser(),
			"can_set_permissions": status.CanSetPermissions(),
			"can_manage":          status.CanManage(),
			"can_promote":         status.CanPromote(),
			"can_demote":          status.CanDemote(),
		}
	}

	return options
}
