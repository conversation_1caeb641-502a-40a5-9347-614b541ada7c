package value_object

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// ProfileData 用户配置文件数据值对象
type ProfileData map[string]interface{}

// NewProfileData 创建新的配置文件数据
func NewProfileData() ProfileData {
	return make(ProfileData)
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (p *ProfileData) Scan(value interface{}) error {
	if value == nil {
		*p = make(ProfileData)
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, p)
	case string:
		return json.Unmarshal([]byte(v), p)
	default:
		return fmt.Errorf("cannot scan %T into ProfileData", value)
	}
}

// Value 实现driver.Valuer接口，用于数据库写入
func (p ProfileData) Value() (driver.Value, error) {
	if len(p) == 0 {
		return "{}", nil
	}
	return json.Marshal(p)
}

// GetString 获取字符串类型的值
func (p ProfileData) GetString(key string) string {
	if value, exists := p[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetInt 获取整数类型的值
func (p ProfileData) GetInt(key string) int {
	if value, exists := p[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case float64:
			return int(v)
		case string:
			// 尝试解析字符串为整数
			var i int
			if _, err := fmt.Sscanf(v, "%d", &i); err == nil {
				return i
			}
		}
	}
	return 0
}

// GetInt64 获取int64类型的值
func (p ProfileData) GetInt64(key string) int64 {
	if value, exists := p[key]; exists {
		switch v := value.(type) {
		case int64:
			return v
		case int:
			return int64(v)
		case float64:
			return int64(v)
		case string:
			// 尝试解析字符串为整数
			var i int64
			if _, err := fmt.Sscanf(v, "%d", &i); err == nil {
				return i
			}
		}
	}
	return 0
}

// GetFloat64 获取float64类型的值
func (p ProfileData) GetFloat64(key string) float64 {
	if value, exists := p[key]; exists {
		switch v := value.(type) {
		case float64:
			return v
		case int:
			return float64(v)
		case int64:
			return float64(v)
		case string:
			// 尝试解析字符串为浮点数
			var f float64
			if _, err := fmt.Sscanf(v, "%f", &f); err == nil {
				return f
			}
		}
	}
	return 0.0
}

// GetBool 获取布尔类型的值
func (p ProfileData) GetBool(key string) bool {
	if value, exists := p[key]; exists {
		switch v := value.(type) {
		case bool:
			return v
		case string:
			return v == "true" || v == "1"
		case int:
			return v != 0
		case float64:
			return v != 0
		}
	}
	return false
}

// GetStringSlice 获取字符串切片类型的值
func (p ProfileData) GetStringSlice(key string) []string {
	if value, exists := p[key]; exists {
		if slice, ok := value.([]interface{}); ok {
			result := make([]string, len(slice))
			for i, item := range slice {
				if str, ok := item.(string); ok {
					result[i] = str
				}
			}
			return result
		}
		// 如果是[]string类型
		if slice, ok := value.([]string); ok {
			return slice
		}
	}
	return make([]string, 0)
}

// Set 设置指定键的值
func (p ProfileData) Set(key string, value interface{}) {
	p[key] = value
}

// Remove 移除指定键
func (p ProfileData) Remove(key string) {
	delete(p, key)
}

// Has 检查是否包含指定键
func (p ProfileData) Has(key string) bool {
	_, exists := p[key]
	return exists
}

// IsEmpty 检查是否为空
func (p ProfileData) IsEmpty() bool {
	return len(p) == 0
}

// Keys 获取所有键
func (p ProfileData) Keys() []string {
	keys := make([]string, 0, len(p))
	for key := range p {
		keys = append(keys, key)
	}
	return keys
}

// Values 获取所有值
func (p ProfileData) Values() []interface{} {
	values := make([]interface{}, 0, len(p))
	for _, value := range p {
		values = append(values, value)
	}
	return values
}

// Clone 克隆数据
func (p ProfileData) Clone() ProfileData {
	clone := make(ProfileData, len(p))
	for key, value := range p {
		clone[key] = value
	}
	return clone
}

// Merge 合并另一个ProfileData，新数据会覆盖已有字段
func (p ProfileData) Merge(other ProfileData) {
	for key, value := range other {
		p[key] = value
	}
}

// Filter 过滤数据，只保留指定的键
func (p ProfileData) Filter(keys ...string) ProfileData {
	keySet := make(map[string]bool)
	for _, key := range keys {
		keySet[key] = true
	}

	result := make(ProfileData)
	for key, value := range p {
		if keySet[key] {
			result[key] = value
		}
	}
	return result
}

// Exclude 排除指定的键
func (p ProfileData) Exclude(keys ...string) ProfileData {
	keySet := make(map[string]bool)
	for _, key := range keys {
		keySet[key] = true
	}

	result := make(ProfileData)
	for key, value := range p {
		if !keySet[key] {
			result[key] = value
		}
	}
	return result
}

// ToJSON 转换为JSON字符串
func (p ProfileData) ToJSON() ([]byte, error) {
	return json.Marshal(p)
}

// FromJSON 从JSON字符串加载数据
func (p ProfileData) FromJSON(data []byte) error {
	return json.Unmarshal(data, &p)
}

// ToMap 转换为普通的map
func (p ProfileData) ToMap() map[string]interface{} {
	return map[string]interface{}(p)
}

// FromMap 从普通的map加载数据
func (p ProfileData) FromMap(data map[string]interface{}) {
	for key, value := range data {
		p[key] = value
	}
}

// Validate 验证数据（可以根据具体需求实现）
func (p ProfileData) Validate() error {
	// 基础验证：检查是否包含非法字符或数据
	for key := range p {
		if key == "" {
			return fmt.Errorf("profile data key cannot be empty")
		}
	}
	return nil
}

// Size 获取数据条目数量
func (p ProfileData) Size() int {
	return len(p)
}

// Clear 清空所有数据
func (p ProfileData) Clear() {
	for key := range p {
		delete(p, key)
	}
}
