package entity

import (
	"encoding/json"
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// AppConfig 应用配置实体
type AppConfig struct {
	ID            int64     `json:"id" gorm:"primaryKey"`
	TenantID      int64     `json:"tenant_id" gorm:"not null;index"`
	InternalAppID int64     `json:"internal_app_id" gorm:"not null;default:1;index;comment:应用ID，bigint类型提升性能"`
	ConfigKey     string    `json:"config_key" gorm:"not null;size:100"`
	ConfigValue   string    `json:"config_value" gorm:"type:text"`
	ConfigType    string    `json:"config_type" gorm:"type:varchar(20);default:'json'"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (AppConfig) TableName() string {
	return "system_config"
}

// 配置键常量
const (
	ConfigKeyPasswordPolicy      = "password_policy"
	ConfigKeyRegistrationMethods = "registration_methods"
	ConfigKeyTenantInfo          = "tenant_info"
)

// EmailRegistrationConfig 邮箱注册配置
type EmailRegistrationConfig struct {
	Enabled                  bool   `json:"enabled"`                    // 是否启用
	RequireVerification      bool   `json:"require_verification"`       // 是否需要验证
	ManualActivation         bool   `json:"manual_activation"`          // 是否需要手动激活
	VerificationTemplateCode string `json:"verification_template_code"` // 验证模板代码
}

// PhoneRegistrationConfig 手机注册配置
type PhoneRegistrationConfig struct {
	Enabled                  bool   `json:"enabled"`                    // 是否启用
	RequireVerification      bool   `json:"require_verification"`       // 是否需要验证
	ManualActivation         bool   `json:"manual_activation"`          // 是否需要手动激活
	VerificationTemplateCode string `json:"verification_template_code"` // 验证码模板
}

// OAuthRegistrationConfig OAuth注册配置
type OAuthRegistrationConfig struct {
	Enabled          bool `json:"enabled"`           // 是否启用
	ManualActivation bool `json:"manual_activation"` // 是否需要手动激活
}

// AdminCreationConfig 管理员创建配置
type AdminCreationConfig struct {
	Enabled         bool `json:"enabled"`          // 是否启用
	RequireApproval bool `json:"require_approval"` // 是否需要审批
}

// RegistrationMethods 注册方式配置
type RegistrationMethods struct {
	Email         EmailRegistrationConfig `json:"email"`          // 邮箱注册详细配置
	Phone         PhoneRegistrationConfig `json:"phone"`          // 手机注册详细配置
	OAuth         OAuthRegistrationConfig `json:"oauth"`          // OAuth注册详细配置
	AdminCreation AdminCreationConfig     `json:"admin_creation"` // 管理员创建配置
}

// NewDefaultRegistrationMethods 创建默认注册方式配置
func NewDefaultRegistrationMethods() *RegistrationMethods {
	return &RegistrationMethods{
		Email: EmailRegistrationConfig{
			Enabled:                  true,
			RequireVerification:      true,
			ManualActivation:         false,
			VerificationTemplateCode: "email_verification",
		},
		Phone: PhoneRegistrationConfig{
			Enabled:                  true,
			RequireVerification:      true,
			ManualActivation:         false,
			VerificationTemplateCode: "sms_verification",
		},
		OAuth: OAuthRegistrationConfig{
			Enabled:          true,
			ManualActivation: false,
		},
		AdminCreation: AdminCreationConfig{
			Enabled:         true,
			RequireApproval: false,
		},
	}
}

// FromJSON 从JSON字符串反序列化
func (r *RegistrationMethods) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), r)
}

// ToJSON 序列化为JSON字符串
func (r *RegistrationMethods) ToJSON() (string, error) {
	data, err := json.Marshal(r)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// TenantInfo 租户信息配置
type TenantInfo struct {
	Name                    string `json:"name"`                       // 租户名称
	DisplayName             string `json:"display_name"`               // 显示名称
	Description             string `json:"description"`                // 描述
	Logo                    string `json:"logo"`                       // Logo URL
	Favicon                 string `json:"favicon"`                    // 网站图标
	Theme                   string `json:"theme"`                      // 主题
	Language                string `json:"language"`                   // 语言
	Timezone                string `json:"timezone"`                   // 时区
	Currency                string `json:"currency"`                   // 货币
	DateFormat              string `json:"date_format"`                // 日期格式
	TimeFormat              string `json:"time_format"`                // 时间格式
	Type                    string `json:"type"`                       // 租户类型
	ServiceEmail            string `json:"service_email"`              // 服务邮箱
	Address                 string `json:"address"`                    // 地址
	ContactPerson           string `json:"contact_person"`             // 联系人
	ContactPhone            string `json:"contact_phone"`              // 联系电话
	Website                 string `json:"website"`                    // 网站
	CustomDomain            string `json:"custom_domain"`              // 自定义域名
	PasswordResetURL        string `json:"password_reset_url"`         // 密码重置URL
	PasswordResetRequestURL string `json:"password_reset_request_url"` // 密码重置请求URL
	LoginURL                string `json:"login_url"`                  // 登录URL
}

// NewDefaultTenantInfo 创建默认租户信息配置
func NewDefaultTenantInfo() *TenantInfo {
	return &TenantInfo{
		Name:                    "Default Tenant",
		DisplayName:             "默认租户",
		Description:             "系统默认租户",
		Logo:                    "",
		Favicon:                 "",
		Theme:                   "default",
		Language:                "zh-CN",
		Timezone:                "Asia/Shanghai",
		Currency:                "CNY",
		DateFormat:              "YYYY-MM-DD",
		TimeFormat:              "HH:mm:ss",
		Type:                    "standard",
		ServiceEmail:            "",
		Address:                 "",
		ContactPerson:           "",
		ContactPhone:            "",
		Website:                 "",
		CustomDomain:            "",
		PasswordResetURL:        "",
		PasswordResetRequestURL: "",
		LoginURL:                "",
	}
}

// FromJSON 从JSON字符串反序列化
func (t *TenantInfo) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), t)
}

// ToJSON 序列化为JSON字符串
func (t *TenantInfo) ToJSON() (string, error) {
	data, err := json.Marshal(t)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// 类型别名，使用value_object包中的PasswordPolicy
type PasswordPolicy = value_object.PasswordPolicy

// NewDefaultPasswordPolicy 创建默认密码策略
func NewDefaultPasswordPolicy() *PasswordPolicy {
	return value_object.DefaultPasswordPolicy()
}
