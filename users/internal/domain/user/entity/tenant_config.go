package entity

import (
	"time"
)

// TenantConfig 租户配置实体
type TenantConfig struct {
	ID            int64     `json:"id" gorm:"primaryKey"`
	TenantID      int64     `json:"tenant_id" gorm:"not null;index"`
	InternalAppID int64     `json:"internal_app_id" gorm:"not null;default:1;index;comment:应用ID，bigint类型提升性能"`
	ConfigKey     string    `json:"config_key" gorm:"not null;size:100"`
	ConfigValue   string    `json:"config_value" gorm:"type:text"`
	ConfigType    string    `json:"config_type" gorm:"type:varchar(20);default:'json'"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (TenantConfig) TableName() string {
	return "system_config"
}
