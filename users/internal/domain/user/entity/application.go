package entity

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// ApplicationStatus 应用状态
type ApplicationStatus string

const (
	ApplicationStatusActive   ApplicationStatus = "active"   // 启用
	ApplicationStatusDisabled ApplicationStatus = "disabled" // 禁用
	ApplicationStatusPending  ApplicationStatus = "pending"  // 待审核
	ApplicationStatusExpired  ApplicationStatus = "expired"  // 已过期
)

// ApplicationType 应用类型
type ApplicationType string

const (
	ApplicationTypeAdmin       ApplicationType = "admin"
	ApplicationTypeMobile      ApplicationType = "mobile"
	ApplicationTypeWeb         ApplicationType = "web"
	ApplicationTypeAPI         ApplicationType = "api"
	ApplicationTypeMiniProgram ApplicationType = "miniprogram"
)

// JSONStringSlice 字符串切片的JSON类型
type JSONStringSlice []string

// Scan implements the Scanner interface for database deserialization
func (j *JSONStringSlice) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONStringSlice", value)
	}

	return json.Unmarshal(bytes, j)
}

// Value implements the driver Valuer interface for database serialization
func (j JSONStringSlice) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// JSONMap JSON对象类型
type JSONMap map[string]interface{}

// Scan implements the Scanner interface for database deserialization
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	return json.Unmarshal(bytes, j)
}

// Value implements the driver Valuer interface for database serialization
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Application 应用实体
type Application struct {
	ID                   int64             `json:"id" gorm:"primaryKey"`
	TenantID             int64             `json:"tenant_id" gorm:"not null;index"`
	AppID                string            `json:"app_id" gorm:"size:64;not null"`
	InternalAppID        int64             `json:"internal_app_id" gorm:"not null"`
	AppName              string            `json:"app_name" gorm:"size:128;not null"`
	AppType              ApplicationType   `json:"app_type" gorm:"size:32;not null"`
	Description          string            `json:"description" gorm:"size:256"`
	PublicConfig         JSONMap           `json:"public_config" gorm:"type:json"`
	EncryptedSecret      string            `json:"-" gorm:"type:text"`
	EncryptedConfig      JSONMap           `json:"-" gorm:"type:json"`
	Status               ApplicationStatus `json:"status" gorm:"size:20;not null;default:active"`
	MaxUsers             int               `json:"max_users" gorm:"default:0"`
	MaxRequestsPerMinute int               `json:"max_requests_per_minute" gorm:"default:1000"`
	IsSystem             bool              `json:"is_system" gorm:"default:false"`
	SortOrder            int               `json:"sort_order" gorm:"default:0"`
	CreatedAt            time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt            time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt            *time.Time        `json:"deleted_at" gorm:"index"`
	CreatedBy            *int64            `json:"created_by"`
	UpdatedBy            *int64            `json:"updated_by"`
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// IsValidStatus 检查状态是否有效
func (a *Application) IsValidStatus() bool {
	switch a.Status {
	case ApplicationStatusActive, ApplicationStatusDisabled, ApplicationStatusPending, ApplicationStatusExpired:
		return true
	default:
		return false
	}
}

// IsValidType 检查类型是否有效
func (a *Application) IsValidType() bool {
	switch a.AppType {
	case ApplicationTypeAdmin, ApplicationTypeMobile, ApplicationTypeWeb, ApplicationTypeAPI, ApplicationTypeMiniProgram:
		return true
	default:
		return false
	}
}

// Enable 启用应用
func (a *Application) Enable() {
	a.Status = ApplicationStatusActive
}

// Disable 禁用应用
func (a *Application) Disable() {
	a.Status = ApplicationStatusDisabled
}

// IsActive 检查应用是否激活
func (a *Application) IsActive() bool {
	return a.Status == ApplicationStatusActive
}
