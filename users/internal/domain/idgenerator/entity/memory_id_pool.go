package entity

import (
	"sync/atomic"
)

// MemoryIDPool 内存ID池，直接基于id_sequence申请连续的ID范围
type MemoryIDPool struct {
	// ID范围 [startValue, endValue]
	startValue   int64 // 起始值（包含）
	endValue     int64 // 结束值（包含）
	currentValue int64 // 当前值（atomic）
	
	// 配置信息
	businessType string // 业务类型
	tenantID     int64  // 租户ID
	step         int64  // 步长
}

// NewMemoryIDPool 创建新的内存ID池
// startValue: 起始ID值，endValue: 结束ID值（包含），step: 步长
func NewMemoryIDPool(businessType string, tenantID int64, startValue, endValue, step int64) *MemoryIDPool {
	return &MemoryIDPool{
		startValue:   startValue,
		endValue:     endValue,
		currentValue: startValue - step, // 初始化为起始值-step，这样第一次NextID()会返回startValue
		businessType: businessType,
		tenantID:     tenantID,
		step:         step,
	}
}

// NextID 获取下一个ID（atomic操作）
func (p *MemoryIDPool) NextID() (int64, error) {
	// 使用CAS确保原子性
	for {
		current := atomic.LoadInt64(&p.currentValue)
		nextValue := current + p.step
		
		// 检查是否超出范围
		if nextValue > p.endValue {
			return 0, ErrPoolExhausted
		}
		
		// 使用CAS操作原子性地更新值
		if atomic.CompareAndSwapInt64(&p.currentValue, current, nextValue) {
			return nextValue, nil
		}
		
		// CAS失败，说明有其他goroutine修改了值，重试
	}
}

// NextIDs 批量获取ID（atomic操作）
func (p *MemoryIDPool) NextIDs(count int) ([]int64, error) {
	if count <= 0 {
		return nil, ErrInvalidIncrementStep
	}
	
	ids := make([]int64, 0, count)
	
	for i := 0; i < count; i++ {
		id, err := p.NextID()
		if err != nil {
			// 如果已经获取了部分ID，返回已获取的ID
			if len(ids) > 0 {
				return ids, nil
			}
			return nil, err
		}
		ids = append(ids, id)
	}
	
	return ids, nil
}

// IsExhausted 检查是否已耗尽
func (p *MemoryIDPool) IsExhausted() bool {
	current := atomic.LoadInt64(&p.currentValue)
	return current >= p.endValue
}

// RemainingCount 剩余数量
func (p *MemoryIDPool) RemainingCount() int64 {
	current := atomic.LoadInt64(&p.currentValue)
	if current >= p.endValue {
		return 0
	}
	// 计算剩余的ID数量 = (endValue - current) / step
	return (p.endValue - current) / p.step
}

// UsageRate 使用率 (0.0 - 1.0)
func (p *MemoryIDPool) UsageRate() float64 {
	current := atomic.LoadInt64(&p.currentValue)
	totalSize := (p.endValue - p.startValue + p.step) / p.step // 总的ID数量
	used := (current - p.startValue + p.step) / p.step         // 已使用的ID数量
	
	if totalSize <= 0 {
		return 0.0
	}
	
	rate := float64(used) / float64(totalSize)
	if rate > 1.0 {
		return 1.0
	}
	if rate < 0.0 {
		return 0.0
	}
	return rate
}

// GetCurrentValue 获取当前值
func (p *MemoryIDPool) GetCurrentValue() int64 {
	return atomic.LoadInt64(&p.currentValue)
}

// GetStartValue 获取起始值
func (p *MemoryIDPool) GetStartValue() int64 {
	return p.startValue
}

// GetEndValue 获取结束值
func (p *MemoryIDPool) GetEndValue() int64 {
	return p.endValue
}

// GetBusinessType 获取业务类型
func (p *MemoryIDPool) GetBusinessType() string {
	return p.businessType
}

// GetTenantID 获取租户ID
func (p *MemoryIDPool) GetTenantID() int64 {
	return p.tenantID
}

// GetStep 获取步长
func (p *MemoryIDPool) GetStep() int64 {
	return p.step
}

// GetPoolSize 获取池大小
func (p *MemoryIDPool) GetPoolSize() int64 {
	return (p.endValue - p.startValue + p.step) / p.step
}