package entity

import "errors"

var (
	ErrInvalidBusinessType  = errors.New("invalid business type")
	ErrInvalidSequenceName  = errors.New("invalid sequence name")
	ErrSequenceNotFound     = errors.New("sequence not found")
	ErrPoolExhausted        = errors.New("pool exhausted")
	ErrSequenceReachMax     = errors.New("sequence reach max value")
	ErrInvalidTenantID      = errors.New("invalid tenant id")
	ErrSequenceExists       = errors.New("sequence already exists")
	ErrInvalidIncrementStep = errors.New("invalid increment step")
)
