package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	idGeneratorPkg "gitee.com/heiyee/platforms/users/pkg/id_generator"
)

// AppIDFactory 应用ID生成器工厂
type AppIDFactory struct {
	appIDGenerator *AppIDGenerator
	idGenerator    *idGeneratorPkg.IDGenerator
	logger         logiface.Logger
}

// NewAppIDFactory 创建应用ID生成器工厂
func NewAppIDFactory(
	appIDGenerator *AppIDGenerator,
	idGenerator *idGeneratorPkg.IDGenerator,
	logger logiface.Logger,
) *AppIDFactory {
	return &AppIDFactory{
		appIDGenerator: appIDGenerator,
		idGenerator:    idGenerator,
		logger:         logger,
	}
}

// GenerateAppIDs 生成应用的所有ID
func (f *AppIDFactory) GenerateAppIDs(ctx context.Context, tenantID int64) (*AppIDs, error) {
	f.logger.Info(ctx, "generating app IDs", logiface.Int64("tenant_id", tenantID))

	// 生成应用ID
	appID, err := f.appIDGenerator.GenerateAppID()
	if err != nil {
		f.logger.Error(ctx, "failed to generate app ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to generate app ID: %w", err)
	}

	// 生成内部应用ID
	internalAppID, err := f.idGenerator.GenerateApplicationID(ctx, tenantID)
	if err != nil {
		f.logger.Error(ctx, "failed to generate internal app ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to generate internal app ID: %w", err)
	}

	appIDs := &AppIDs{
		AppID:         appID,
		InternalAppID: internalAppID,
	}

	f.logger.Info(ctx, "generated app IDs",
		logiface.String("app_id", appID),
		logiface.Int64("internal_app_id", internalAppID),
		logiface.Int64("tenant_id", tenantID))

	return appIDs, nil
}

// ValidateAppIDs 验证应用ID
func (f *AppIDFactory) ValidateAppIDs(appIDs *AppIDs) error {
	// 验证应用ID
	if err := f.appIDGenerator.ValidateAppID(appIDs.AppID); err != nil {
		return fmt.Errorf("invalid app_id: %w", err)
	}

	// 验证内部应用ID
	if appIDs.InternalAppID <= 0 {
		return fmt.Errorf("internal_app_id must be positive, got %d", appIDs.InternalAppID)
	}

	return nil
}

// AppIDs 应用ID集合
type AppIDs struct {
	AppID         string `json:"app_id"`          // 对外公开的应用ID
	InternalAppID int64  `json:"internal_app_id"` // 内部应用ID
}
