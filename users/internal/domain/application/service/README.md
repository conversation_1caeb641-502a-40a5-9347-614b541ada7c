# 应用ID生成器服务

## 概述

本服务遵循DDD（领域驱动设计）规范，实现了应用ID的生成和管理功能。包含两种类型的ID：

1. **app_id**: 对外公开的应用ID，使用base64.RawURLEncoding编码的22字符字符串
2. **internal_app_id**: 内部应用ID，使用分布式ID生成器生成的int64类型

## 核心组件

### 1. AppIDGenerator
- **功能**: 生成和验证对外公开的应用ID
- **实现**: 使用crypto/rand生成16字节随机数，然后用base64.RawURLEncoding编码
- **格式**: 22字符，无=号，URL安全
- **示例**: `abcdefghijklmnopqrstuv`

### 2. AppIDFactory
- **功能**: 应用ID生成器工厂，统一管理两种ID的生成
- **职责**: 
  - 同时生成app_id和internal_app_id
  - 验证生成的ID组合
  - 提供统一的ID生成接口
- **实现**: 直接使用 `users/pkg/id_generator/factory.go` 中的 `GenerateApplicationID` 方法

## 使用方式

### 在应用服务中使用

```go
// 通过依赖注入获取AppIDFactory
appIDFactory := container.GetAppIDFactory()

// 生成应用ID
appIDs, err := appIDFactory.GenerateAppIDs(ctx, tenantID)
if err != nil {
    return nil, err
}

// 使用生成的ID
application := &entity.Application{
    ID:            appIDs.InternalAppID,
    AppID:         appIDs.AppID,
    InternalAppID: appIDs.InternalAppID,
    // ... 其他字段
}
```

### 直接使用生成器

```go
// 生成对外ID
appIDGenerator := NewAppIDGenerator()
appID, err := appIDGenerator.GenerateAppID()

// 直接使用工厂中的方法
idGenerator := container.GetIDGenerator()
internalAppID, err := idGenerator.GenerateApplicationID(ctx, tenantID)
```

## 设计原则

### 1. DDD规范
- **领域层**: 所有ID生成逻辑位于domain层
- **依赖倒置**: 通过接口定义依赖关系
- **单一职责**: 每个生成器只负责一种类型的ID
- **工厂模式**: 使用AppIDFactory统一管理ID生成

### 2. 安全性
- **随机性**: 使用crypto/rand确保随机性
- **唯一性**: 通过分布式ID生成器确保全局唯一
- **验证**: 提供ID格式验证功能

### 3. 可扩展性
- **接口设计**: 通过接口定义，支持不同的ID生成策略
- **工厂模式**: 便于添加新的ID类型
- **统一管理**: 所有业务ID生成都通过工厂统一管理

## 架构优势

### 简化设计
- **减少组件**: 移除了InternalAppIDGenerator，直接使用工厂中的ID生成器
- **简化依赖**: 减少了不必要的中间层
- **统一管理**: 所有ID生成都通过工厂统一管理

### 性能优化
- **减少调用链**: 直接调用工厂方法，减少中间层调用
- **内存优化**: 减少了对象创建和内存占用

## 测试

运行测试：
```bash
go test ./internal/domain/application/service/ -v
```

测试覆盖：
- ID生成功能
- ID格式验证
- ID唯一性检查
- 错误处理

## 配置

### 分布式ID生成器配置
- 业务类型: "application"
- 租户隔离: 支持多租户ID生成
- 缓存策略: 使用内存池提高性能

### 应用ID生成器配置
- 长度: 22字符
- 编码: base64.RawURLEncoding
- 字符集: A-Z, a-z, 0-9, -, _ 