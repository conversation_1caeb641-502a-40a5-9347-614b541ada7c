package service

import (
	"testing"
)

func TestAppIDGenerator_GenerateAppID(t *testing.T) {
	generator := NewAppIDGenerator()

	// 生成多个ID并验证
	for i := 0; i < 100; i++ {
		appID, err := generator.GenerateAppID()
		if err != nil {
			t.Fatalf("Failed to generate app ID: %v", err)
		}

		// 验证长度
		if len(appID) != 22 {
			t.<PERSON><PERSON><PERSON>("Expected app ID length 22, got %d: %s", len(appID), appID)
		}

		// 验证格式
		if err := generator.ValidateAppID(appID); err != nil {
			t.Errorf("Generated app ID is invalid: %s, error: %v", appID, err)
		}
	}
}

func TestAppIDGenerator_ValidateAppID(t *testing.T) {
	generator := NewAppIDGenerator()

	tests := []struct {
		name    string
		appID   string
		wantErr bool
	}{
		{
			name:    "valid app ID",
			appID:   "abcdefghijklmnopqrstuv", // 22字符的base64.RawURLEncoding
			wantErr: false,
		},
		{
			name:    "too short",
			appID:   "abc",
			wantErr: true,
		},
		{
			name:    "too long",
			appID:   "abcdefghijklmnopqrstuvwxyz",
			wantErr: true,
		},
		{
			name:    "invalid characters",
			appID:   "abcdefghijklmnopqrstu+", // + 不是base64.RawURLEncoding字符
			wantErr: true,
		},
		{
			name:    "empty string",
			appID:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := generator.ValidateAppID(tt.appID)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateAppID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAppIDGenerator_Uniqueness(t *testing.T) {
	generator := NewAppIDGenerator()
	generated := make(map[string]bool)

	// 生成1000个ID并检查唯一性
	for i := 0; i < 1000; i++ {
		appID, err := generator.GenerateAppID()
		if err != nil {
			t.Fatalf("Failed to generate app ID: %v", err)
		}

		if generated[appID] {
			t.Errorf("Duplicate app ID generated: %s", appID)
		}
		generated[appID] = true
	}

	if len(generated) != 1000 {
		t.Errorf("Expected 1000 unique IDs, got %d", len(generated))
	}
}
