package service

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
)

// AppIDGenerator 应用ID生成器服务
type AppIDGenerator struct{}

// NewAppIDGenerator 创建应用ID生成器
func NewAppIDGenerator() *AppIDGenerator {
	return &AppIDGenerator{}
}

// GenerateAppID 生成应用ID
// 使用crypto/rand生成16字节随机数，然后用base64.RawURLEncoding编码为22字符
func (g *AppIDGenerator) GenerateAppID() (string, error) {
	b := make([]byte, 16) // 128位
	_, err := rand.Read(b)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	return base64.RawURLEncoding.EncodeToString(b), nil // 22字符，无=号
}

// ValidateAppID 验证应用ID格式
func (g *AppIDGenerator) ValidateAppID(appID string) error {
	if len(appID) != 22 {
		return fmt.Erro<PERSON>("app_id must be exactly 22 characters long, got %d", len(appID))
	}

	// 验证是否为有效的base64.RawURLEncoding字符串
	_, err := base64.RawURLEncoding.DecodeString(appID)
	if err != nil {
		return fmt.Errorf("invalid app_id format: %w", err)
	}

	return nil
}
