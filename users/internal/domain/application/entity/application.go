package entity

import (
	"time"
)

// ApplicationType 应用类型
type ApplicationType string

const (
	ApplicationTypeWeb         ApplicationType = "web"
	ApplicationTypeMobile      ApplicationType = "mobile"
	ApplicationTypeAPI         ApplicationType = "api"
	ApplicationTypeDesktop     ApplicationType = "desktop"
	ApplicationTypeSystem      ApplicationType = "system"
	ApplicationTypeMiniprogram ApplicationType = "miniprogram"
)

// ApplicationStatus 应用状态
type ApplicationStatus string

const (
	ApplicationStatusActive    ApplicationStatus = "active"    // 启用
	ApplicationStatusInactive  ApplicationStatus = "inactive"  // 禁用
	ApplicationStatusPending   ApplicationStatus = "pending"   // 待审核
	ApplicationStatusSuspended ApplicationStatus = "suspended" // 暂停
)

// Application 应用实体
type Application struct {
	InternalAppID     int64             `json:"internal_app_id" gorm:"primaryKey;column:internal_app_id"`
	AppID             string            `json:"app_id" gorm:"uniqueIndex;not null;size:64"`
	TenantID          int64             `json:"tenant_id" gorm:"not null;index"`
	AppName           string            `json:"app_name" gorm:"not null;size:100"`
	AppCode           string            `json:"app_code" gorm:"not null;size:50"`
	Description       string            `json:"description" gorm:"type:text"`
	AppType           ApplicationType   `json:"app_type" gorm:"type:varchar(20);not null;default:'web'"`
	Status            ApplicationStatus `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	AppSecret         string            `json:"app_secret" gorm:"not null;size:128"`
	CallbackURLs      JSONArray         `json:"callback_urls" gorm:"type:json"`
	AllowedOrigins    JSONArray         `json:"allowed_origins" gorm:"type:json"`
	Scopes            JSONArray         `json:"scopes" gorm:"type:json"`
	RateLimit         int               `json:"rate_limit" gorm:"default:1000"`
	LogoURL           string            `json:"logo_url" gorm:"size:255"`
	HomepageURL       string            `json:"homepage_url" gorm:"size:255"`
	PrivacyPolicyURL  string            `json:"privacy_policy_url" gorm:"size:255"`
	TermsOfServiceURL string            `json:"terms_of_service_url" gorm:"size:255"`
	ContactEmail      string            `json:"contact_email" gorm:"size:100"`
	IsSystem          bool              `json:"is_system" gorm:"type:tinyint(1);not null;default:0"`
	Active            bool              `json:"is_active" gorm:"type:tinyint(1);not null;default:1"`
	Config            JSONMap           `json:"config" gorm:"type:json"`
	CreatedBy         int64             `json:"created_by" gorm:"not null"`
	UpdatedBy         *int64            `json:"updated_by"`
	CreatedAt         time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt         *time.Time        `json:"deleted_at" gorm:"index"`

	// 关联关系 - 暂时注释掉，避免循环依赖
	// Tenant *Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// JSONArray JSON数组类型
type JSONArray []string

// JSONMap JSON对象类型
type JSONMap map[string]interface{}

// IsActive 检查应用是否激活
func (a *Application) IsActive() bool {
	return a.Status == ApplicationStatusActive && a.Active
}

// IsSystemApp 检查是否为系统应用
func (a *Application) IsSystemApp() bool {
	return a.IsSystem
}

// CanAccessOrigin 检查是否允许访问指定源
func (a *Application) CanAccessOrigin(origin string) bool {
	if len(a.AllowedOrigins) == 0 {
		return true // 如果没有设置允许的源，则允许所有
	}

	for _, allowedOrigin := range a.AllowedOrigins {
		if allowedOrigin == origin || allowedOrigin == "*" {
			return true
		}
	}
	return false
}

// HasScope 检查是否具有指定权限范围
func (a *Application) HasScope(scope string) bool {
	if len(a.Scopes) == 0 {
		return true // 如果没有设置权限范围，则允许所有
	}

	for _, appScope := range a.Scopes {
		if appScope == scope || appScope == "*" {
			return true
		}
	}
	return false
}

// Activate 激活应用
func (a *Application) Activate() {
	a.Status = ApplicationStatusActive
	a.Active = true
}

// Deactivate 停用应用
func (a *Application) Deactivate() {
	a.Status = ApplicationStatusInactive
	a.Active = false
}

// Suspend 暂停应用
func (a *Application) Suspend() {
	a.Status = ApplicationStatusSuspended
	a.Active = false
}

// UpdateConfig 更新应用配置
func (a *Application) UpdateConfig(config JSONMap) {
	a.Config = config
}

// UpdateScopes 更新应用权限范围
func (a *Application) UpdateScopes(scopes []string) {
	a.Scopes = scopes
}

// UpdateAllowedOrigins 更新允许的源域名
func (a *Application) UpdateAllowedOrigins(origins []string) {
	a.AllowedOrigins = origins
}

// UpdateCallbackURLs 更新回调URL列表
func (a *Application) UpdateCallbackURLs(urls []string) {
	a.CallbackURLs = urls
}

// SetRateLimit 设置频率限制
func (a *Application) SetRateLimit(limit int) {
	a.RateLimit = limit
}

// Validate 验证应用数据
func (a *Application) Validate() error {
	if a.AppName == "" {
		return NewValidationError("app_name", "应用名称不能为空")
	}
	if a.AppCode == "" {
		return NewValidationError("app_code", "应用代码不能为空")
	}
	if a.AppID == "" {
		return NewValidationError("app_id", "应用ID不能为空")
	}
	if a.AppSecret == "" {
		return NewValidationError("app_secret", "应用密钥不能为空")
	}
	if a.TenantID <= 0 {
		return NewValidationError("tenant_id", "租户ID不能为空")
	}
	return nil
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}
