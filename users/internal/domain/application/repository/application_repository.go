package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/application/entity"
)

// ApplicationRepository 应用仓储接口
type ApplicationRepository interface {
	// Create 创建应用
	Create(ctx context.Context, application *entity.Application) error

	// Update 更新应用
	Update(ctx context.Context, application *entity.Application) error

	// Delete 删除应用（软删除）
	Delete(ctx context.Context, internalAppID int64) error

	// FindByInternalAppID 根据内部应用ID查找应用
	FindByInternalAppID(ctx context.Context, internalAppID int64) (*entity.Application, error)

	// FindByAppID 根据对外应用ID查找应用
	FindByAppID(ctx context.Context, appID string) (*entity.Application, error) // 保留用于外部API调用

	// FindByTenantID 根据租户ID查找应用列表
	FindByTenantID(ctx context.Context, tenantID int64) ([]*entity.Application, error)

	// FindByTenantIDAndAppCode 根据租户ID和应用代码查找应用
	FindByTenantIDAndAppCode(ctx context.Context, tenantID int64, appCode string) (*entity.Application, error)

	// FindActiveByTenantID 根据租户ID查找激活的应用列表
	FindActiveByTenantID(ctx context.Context, tenantID int64) ([]*entity.Application, error)

	// FindSystemApps 查找系统应用列表
	FindSystemApps(ctx context.Context) ([]*entity.Application, error)

	// ExistsByAppID 检查应用ID是否存在
	ExistsByAppID(ctx context.Context, appID string) (bool, error) // 保留用于外部API调用

	// ExistsByTenantIDAndAppCode 检查租户下的应用代码是否存在
	ExistsByTenantIDAndAppCode(ctx context.Context, tenantID int64, appCode string) (bool, error)

	// CountByTenantID 统计租户下的应用数量
	CountByTenantID(ctx context.Context, tenantID int64) (int64, error)

	// FindByInternalAppIDs 根据内部应用ID列表批量查找
	FindByInternalAppIDs(ctx context.Context, internalAppIDs []int64) ([]*entity.Application, error)

	// FindByAppIDs 根据对外应用ID列表批量查找
	FindByAppIDs(ctx context.Context, appIDs []string) ([]*entity.Application, error) // 保留用于外部API调用
}
