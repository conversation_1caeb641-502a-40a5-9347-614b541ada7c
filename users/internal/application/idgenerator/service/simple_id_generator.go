package service

import (
	"context"
	"sync"
	"sync/atomic"

	"gitee.com/heiyee/platforms/pkg/common"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/idgenerator/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/idgenerator/repository"
)

// SimpleIDGenerator 简化的ID生成器，直接基于id_sequence
type SimpleIDGenerator struct {
	// 业务类型到ID池的映射
	pools map[string]*entity.MemoryIDPool // key: businessType

	// 依赖
	sequenceRepo repository.SequenceRepository
	logger       logiface.Logger

	// 同步控制
	mutex sync.RWMutex

	// 预加载状态控制，防止重复申请
	preloadingFlags map[string]*int32 // key: businessType, value: atomic flag (0=idle, 1=preloading)
}

// NewSimpleIDGenerator 创建新的简化ID生成器
func NewSimpleIDGenerator(
	sequenceRepo repository.SequenceRepository,
	logger logiface.Logger,
) *SimpleIDGenerator {
	return &SimpleIDGenerator{
		pools:           make(map[string]*entity.MemoryIDPool),
		preloadingFlags: make(map[string]*int32),
		sequenceRepo:    sequenceRepo,
		logger:          logger,
	}
}

// NextID 获取指定businessType的下一个ID
func (g *SimpleIDGenerator) NextID(ctx context.Context, businessType string, tenantID int64) (int64, error) {
	// 单个ID生成调用批量方法以获得更好性能
	ids, err := g.NextIDs(ctx, businessType, tenantID, 1)
	if err != nil {
		return 0, err
	}
	return ids[0], nil
}

// NextIDs 批量获取指定businessType的ID
func (g *SimpleIDGenerator) NextIDs(ctx context.Context, businessType string, tenantID int64, count int) ([]int64, error) {
	g.logger.Info(ctx, "generating ID for business type", logiface.String("business_type", businessType), logiface.Int64("tenant_id", tenantID))
	// 获取或创建ID池
	pool, err := g.getOrCreatePool(ctx, businessType, common.SystemTenantID)
	if err != nil {
		return nil, err
	}

	// 尝试从池中获取ID
	ids, err := pool.NextIDs(count)
	if err != nil {
		// 如果池已耗尽，重新申请新的ID范围
		if err == entity.ErrPoolExhausted {
			newPool, err := g.refreshPool(ctx, businessType, common.SystemTenantID)
			if err != nil {
				return nil, err
			}
			return newPool.NextIDs(count)
		}
		return nil, err
	}

	// 检查是否需要预申请新的ID范围
	g.checkPreload(ctx, businessType, common.SystemTenantID, pool)

	return ids, nil
}

// getOrCreatePool 获取或创建ID池
func (g *SimpleIDGenerator) getOrCreatePool(ctx context.Context, businessType string, tenantID int64) (*entity.MemoryIDPool, error) {
	g.mutex.RLock()
	if pool, exists := g.pools[businessType]; exists && !pool.IsExhausted() {
		g.mutex.RUnlock()
		return pool, nil
	}
	g.mutex.RUnlock()

	// 需要创建新的ID池
	return g.createNewPool(ctx, businessType, tenantID)
}

// createNewPool 创建新的ID池
func (g *SimpleIDGenerator) createNewPool(ctx context.Context, businessType string, tenantID int64) (*entity.MemoryIDPool, error) {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// 双重检查
	if pool, exists := g.pools[businessType]; exists && !pool.IsExhausted() {
		return pool, nil
	}

	// 查找或创建序列
	sequence, err := g.sequenceRepo.FindByBusinessType(ctx, businessType, tenantID)
	if err != nil {
		g.logger.Error(ctx, "failed to find sequence for ID pool creation",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	// 申请一个cache_size大小的ID范围
	cacheSize := int64(sequence.CacheSize)
	if cacheSize <= 0 {
		cacheSize = 100 // 默认缓存大小
	}

	startValue, endValue, err := g.sequenceRepo.GetNextRangeWithLock(ctx, sequence.ID, cacheSize)
	if err != nil {
		g.logger.Error(ctx, "failed to get next range for ID pool creation",
			logiface.Error(err),
			logiface.Int64("sequence_id", sequence.ID),
			logiface.String("business_type", businessType),
			logiface.Int64("cache_size", cacheSize))
		return nil, err
	}

	// 创建新的ID池
	step := int64(sequence.IncrementStep)
	pool := entity.NewMemoryIDPool(businessType, tenantID, startValue, endValue, step)
	g.pools[businessType] = pool

	// 初始化预加载标志
	if g.preloadingFlags[businessType] == nil {
		flag := int32(0)
		g.preloadingFlags[businessType] = &flag
	}

	g.logger.Info(ctx, "created new memory ID pool",
		logiface.String("business_type", businessType),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("sequence_id", sequence.ID),
		logiface.Int64("start_value", startValue),
		logiface.Int64("end_value", endValue),
		logiface.Int64("cache_size", cacheSize),
		logiface.Int("increment_step", sequence.IncrementStep))

	return pool, nil
}

// refreshPool 刷新ID池
func (g *SimpleIDGenerator) refreshPool(ctx context.Context, businessType string, tenantID int64) (*entity.MemoryIDPool, error) {
	// 直接创建新的ID池，会自动替换旧的
	return g.createNewPool(ctx, businessType, tenantID)
}

// checkPreload 检查是否需要预申请新的ID范围
func (g *SimpleIDGenerator) checkPreload(ctx context.Context, businessType string, tenantID int64, pool *entity.MemoryIDPool) {
	// 检查使用率是否达到预申请阈值（80%）
	if pool.UsageRate() >= 0.8 {
		// 确保预加载标志存在
		g.mutex.RLock()
		flag := g.preloadingFlags[businessType]
		g.mutex.RUnlock()

		if flag == nil {
			// 如果标志不存在，创建一个
			g.mutex.Lock()
			if g.preloadingFlags[businessType] == nil {
				newFlag := int32(0)
				g.preloadingFlags[businessType] = &newFlag
				flag = &newFlag
			} else {
				flag = g.preloadingFlags[businessType]
			}
			g.mutex.Unlock()
		}

		// 使用原子操作检查并设置预加载标志，防止重复申请
		if atomic.CompareAndSwapInt32(flag, 0, 1) {
			// 异步预申请新的ID范围
			go func() {
				defer atomic.StoreInt32(flag, 0) // 完成后重置标志

				// 检查当前池是否仍然需要预申请
				g.mutex.RLock()
				currentPool := g.pools[businessType]
				g.mutex.RUnlock()

				if currentPool != nil && currentPool.UsageRate() >= 0.8 {
					newPool, err := g.createNewPool(ctx, businessType, tenantID)
					if err != nil {
						g.logger.Error(ctx, "failed to preload ID pool",
							logiface.Error(err),
							logiface.String("business_type", businessType),
							logiface.Int64("tenant_id", tenantID))
						return
					}

					g.logger.Info(ctx, "preloaded new ID pool",
						logiface.String("business_type", businessType),
						logiface.Int64("tenant_id", tenantID),
						logiface.Int64("start_value", newPool.GetStartValue()),
						logiface.Int64("end_value", newPool.GetEndValue()),
						logiface.Int64("pool_size", newPool.GetPoolSize()))
				}
			}()
		}
	}
}
