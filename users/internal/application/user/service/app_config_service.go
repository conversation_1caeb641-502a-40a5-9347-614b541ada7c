package service

import (
	"context"
	"regexp"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
)

// 密码策略常量
const (
	appMinPasswordLength = 6
	appMaxPasswordLength = 128
)

// AppConfigService 应用配置服务
type AppConfigService struct {
	appConfigRepo repository.AppConfigRepository
	appRepo       repository.ApplicationRepository
	logger        logiface.Logger
}

// Ensure AppConfigService implements AppConfigServiceInterface
var _ AppConfigServiceInterface = (*AppConfigService)(nil)

func NewAppConfigService(appConfigRepo repository.AppConfigRepository, appRepo repository.ApplicationRepository, logger logiface.Logger) *AppConfigService {
	return &AppConfigService{
		appConfigRepo: appConfigRepo,
		appRepo:       appRepo,
		logger:        logger,
	}
}

// getTenantIDFromAppID 从AppID获取TenantID
func (s *AppConfigService) getTenantIDFromAppID(ctx context.Context, appID string) (int64, error) {
	app, err := s.appRepo.GetByAppID(ctx, appID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get application by app_id", logiface.Error(err), logiface.String("app_id", appID))
		return 0, userErrors.NewDatabaseError("获取应用信息失败", err.Error())
	}
	if app == nil {
		return 0, userErrors.NewUserError(userErrors.CodeUserResourceNotFound, "应用不存在")
	}
	return app.TenantID, nil
}

// GetPasswordPolicy 获取密码策略
func (s *AppConfigService) GetPasswordPolicy(ctx context.Context, req *dto.GetPasswordPolicyRequest) (*dto.TenantPasswordPolicyResponse, error) {
	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return nil, err
	}

	s.logger.Info(ctx, "Getting password policy", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, tenantID, entity.ConfigKeyPasswordPolicy)
	if err != nil {
		return nil, err
	}

	var policy *entity.PasswordPolicy
	if config != nil {
		// 解析租户配置
		policy = &entity.PasswordPolicy{}
		if err := policy.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse password policy config", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
			return nil, userErrors.NewSystemError("解析密码策略配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		policy = entity.NewDefaultPasswordPolicy()
	}

	return &dto.TenantPasswordPolicyResponse{
		TenantID: tenantID,
		Policy:   policy,
	}, nil
}

// UpdatePasswordPolicy 更新密码策略
func (s *AppConfigService) UpdatePasswordPolicy(ctx context.Context, req *dto.UpdatePasswordPolicyRequest) error {
	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return err
	}

	s.logger.Info(ctx, "Updating password policy", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 验证密码策略
	if err := s.validatePasswordPolicy(req.Policy); err != nil {
		s.logger.Warn(ctx, "Password policy validation failed", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码策略验证失败: "+err.Error())
	}

	// 转换为JSON
	configValue, err := req.Policy.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize password policy", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewSystemError("序列化密码策略失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    tenantID,
		ConfigKey:   entity.ConfigKeyPasswordPolicy,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// GetRegistrationMethods 获取注册方式配置
func (s *AppConfigService) GetRegistrationMethods(ctx context.Context, req *dto.GetRegistrationMethodsRequest) (*dto.RegistrationMethodsResponse, error) {
	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return nil, err
	}

	s.logger.Info(ctx, "Getting registration methods", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, tenantID, entity.ConfigKeyRegistrationMethods)
	if err != nil {
		return nil, err
	}

	var methods *entity.RegistrationMethods
	if config != nil {
		// 解析租户配置
		methods = &entity.RegistrationMethods{}
		if err := methods.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse registration methods config", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
			return nil, userErrors.NewSystemError("解析注册方式配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		methods = entity.NewDefaultRegistrationMethods()
	}

	return &dto.RegistrationMethodsResponse{
		TenantID: tenantID,
		Methods:  methods,
	}, nil
}

// UpdateRegistrationMethods 更新注册方式配置
func (s *AppConfigService) UpdateRegistrationMethods(ctx context.Context, req *dto.UpdateRegistrationMethodsRequest) error {
	// 添加调试日志
	s.logger.Info(ctx, "AppConfigService.UpdateRegistrationMethods called",
		logiface.String("app_id", req.AppID),
		logiface.Any("methods", req.Methods))

	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return err
	}

	s.logger.Info(ctx, "Updating registration methods", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 转换为JSON
	configValue, err := req.Methods.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize registration methods", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewSystemError("序列化注册方式配置失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    tenantID,
		ConfigKey:   entity.ConfigKeyRegistrationMethods,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// GetTenantConfigs 获取应用所有配置（按internalAppId）
func (s *AppConfigService) GetTenantConfigs(ctx context.Context, tenantID int64) (*dto.TenantConfigListResponse, error) {
	s.logger.Info(ctx, "Getting tenant configs", logiface.Int64("tenant_id", tenantID))

	configs, err := s.appConfigRepo.FindByTenantID(ctx)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tenant configs", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return nil, userErrors.NewDatabaseError("获取租户配置失败", err.Error())
	}

	// 转换为响应格式
	configResponses := make([]*dto.TenantConfigResponse, len(configs))
	for i, config := range configs {
		configResponses[i] = &dto.TenantConfigResponse{
			TenantID:    config.TenantID,
			ConfigKey:   config.ConfigKey,
			ConfigValue: config.ConfigValue,
			ConfigType:  config.ConfigType,
		}
	}

	return &dto.TenantConfigListResponse{
		Configs: configResponses,
		Total:   int64(len(configResponses)),
	}, nil
}

// GetConfig 获取应用的单个配置项（按internalAppId）
func (s *AppConfigService) GetConfig(ctx context.Context, tenantID int64, configKey string) (*entity.AppConfig, error) {
	s.logger.Debug(ctx, "Getting tenant config",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("config_key", configKey))

	config, err := s.appConfigRepo.GetEffectiveConfig(ctx, configKey)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tenant config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("config_key", configKey))
		return nil, userErrors.NewDatabaseError("获取租户配置失败", err.Error())
	}

	return config, nil
}

// UpdateTenantConfig 更新应用配置（按internalAppId）
func (s *AppConfigService) UpdateTenantConfig(ctx context.Context, req *dto.UpdateTenantConfigRequest) error {
	s.logger.Info(ctx, "Updating tenant config", logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))

	// 创建UpsertConfigRequest对象
	upsertReq := &dto.UpsertConfigRequest{
		TenantID:    req.TenantID,
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		ConfigType:  "json",
	}

	// 保存配置（按tenant_id + internal_app_id + config_key 唯一）
	if err := s.appConfigRepo.UpsertConfig(ctx, upsertReq); err != nil {
		s.logger.Error(ctx, "Failed to update tenant config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
		return userErrors.NewDatabaseError("更新租户配置失败", err.Error())
	}

	s.logger.Info(ctx, "Tenant config updated successfully", logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
	return nil
}

// CopySystemConfigsToTenant 复制系统配置到租户
func (s *AppConfigService) CopySystemConfigsToTenant(ctx context.Context, tenantID int64) error {
	s.logger.Info(ctx, "Copying system configs to tenant", logiface.Int64("tenant_id", tenantID))

	if err := s.appConfigRepo.CopySystemConfigsToTenant(ctx); err != nil {
		s.logger.Error(ctx, "Failed to copy system configs to tenant", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewDatabaseError("复制系统配置到租户失败", err.Error())
	}

	s.logger.Info(ctx, "System configs copied to tenant successfully", logiface.Int64("tenant_id", tenantID))
	return nil
}

// validatePasswordPolicy 验证密码策略
func (s *AppConfigService) validatePasswordPolicy(policy *entity.PasswordPolicy) error {
	if policy.MinLength < appMinPasswordLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最小长度不能小于6位")
	}
	if policy.MaxLength > appMaxPasswordLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最大长度不能超过128位")
	}
	if policy.MinLength > policy.MaxLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最小长度不能大于最大长度")
	}
	if policy.PasswordHistoryCount < 0 {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码历史记录数不能为负数")
	}
	if policy.ExpireDays < 0 {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码过期天数不能为负数")
	}
	return nil
}

// GetTenantInfo 获取租户信息配置
func (s *AppConfigService) GetTenantInfo(ctx context.Context, req *dto.GetTenantInfoRequest) (*dto.TenantInfoResponse, error) {
	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return nil, err
	}

	s.logger.Info(ctx, "Getting tenant info", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, tenantID, entity.ConfigKeyTenantInfo)
	if err != nil {
		return nil, err
	}

	var info *entity.TenantInfo
	if config != nil {
		// 解析租户配置
		info = &entity.TenantInfo{}
		if err := info.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse tenant info config", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
			return nil, userErrors.NewSystemError("解析租户信息配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		info = entity.NewDefaultTenantInfo()
	}

	return &dto.TenantInfoResponse{
		TenantID: tenantID,
		Info:     info,
	}, nil
}

// UpdateTenantInfo 更新租户信息配置
func (s *AppConfigService) UpdateTenantInfo(ctx context.Context, req *dto.UpdateTenantInfoRequest) error {
	// 从AppID获取TenantID
	tenantID, err := s.getTenantIDFromAppID(ctx, req.AppID)
	if err != nil {
		return err
	}

	s.logger.Info(ctx, "Updating tenant info", logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))

	// 验证租户信息
	if err := s.validateTenantInfo(req.Info); err != nil {
		s.logger.Warn(ctx, "Tenant info validation failed", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
		return err // 直接返回验证错误，因为validateTenantInfo已经使用了NewParameterValidationFailedError
	}

	// 转换为JSON
	configValue, err := req.Info.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize tenant info", logiface.Error(err), logiface.String("app_id", req.AppID), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewSystemError("序列化租户信息失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    tenantID,
		ConfigKey:   entity.ConfigKeyTenantInfo,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// validateTenantInfo 验证租户信息
func (s *AppConfigService) validateTenantInfo(info *entity.TenantInfo) error {
	if info.Type != "enterprise" && info.Type != "personal" {
		return userErrors.NewParameterValidationFailedError("type", "租户类型必须是 enterprise 或 personal")
	}
	// 系统名称字段已迁移至前端/其他表，不在此强校验
	if info.ServiceEmail != "" && len(info.ServiceEmail) > 100 {
		return userErrors.NewParameterValidationFailedError("service_email", "客服邮箱长度不能超过100个字符")
	}
	if len(info.Address) > 500 {
		return userErrors.NewParameterValidationFailedError("address", "地址长度不能超过500个字符")
	}
	if len(info.ContactPerson) > 50 {
		return userErrors.NewParameterValidationFailedError("contact_person", "联系人长度不能超过50个字符")
	}
	if len(info.ContactPhone) > 20 {
		return userErrors.NewParameterValidationFailedError("contact_phone", "联系电话长度不能超过20个字符")
	}
	if len(info.Website) > 200 {
		return userErrors.NewParameterValidationFailedError("website", "网站地址长度不能超过200个字符")
	}
	if len(info.CustomDomain) > 255 {
		return userErrors.NewParameterValidationFailedError("custom_domain", "自定义域名长度不能超过255个字符")
	}
	if info.CustomDomain != "" {
		// 验证域名格式
		domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
		if !domainRegex.MatchString(info.CustomDomain) {
			return userErrors.NewParameterValidationFailedError("custom_domain", "自定义域名格式无效，请输入有效的域名格式")
		}
	}
	if len(info.Description) > 1000 {
		return userErrors.NewParameterValidationFailedError("description", "描述长度不能超过1000个字符")
	}
	return nil
}
