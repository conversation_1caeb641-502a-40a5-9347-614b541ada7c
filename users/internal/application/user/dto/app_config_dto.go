package dto

import (
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// GetAppConfigRequest 获取应用配置请求
type GetAppConfigRequest struct {
	TenantID int64 `json:"tenant_id" binding:"required"`
}

// UpdateAppConfigRequest 更新应用配置请求
type UpdateAppConfigRequest struct {
	TenantID    int64  `json:"tenant_id" binding:"required"`
	ConfigKey   string `json:"config_key" binding:"required"`
	ConfigValue string `json:"config_value" binding:"required"`
}

// UpsertConfigRequest 更新或插入配置请求 - 用于内部调用，封装超过3个参数的场景
type UpsertConfigRequest struct {
	TenantID    int64  `json:"tenant_id"`
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	ConfigType  string `json:"config_type"`
}

// GetPasswordPolicyRequest 获取密码策略请求
type GetPasswordPolicyRequest struct {
	AppID string `json:"app_id" binding:"required"`
}

// UpdatePasswordPolicyRequest 更新密码策略请求
type UpdatePasswordPolicyRequest struct {
	AppID    string                 `json:"app_id" binding:"required"`
	TenantID int64                  `json:"tenant_id,omitempty"`
	Policy   *entity.PasswordPolicy `json:"policy" binding:"required"`
}

// GetRegistrationMethodsRequest 获取注册方式请求
type GetRegistrationMethodsRequest struct {
	AppID string `json:"app_id" binding:"required"`
}

// UpdateRegistrationMethodsRequest 更新注册方式请求
type UpdateRegistrationMethodsRequest struct {
	AppID    string                      `json:"app_id" binding:"required"`
	TenantID int64                       `json:"tenant_id,omitempty"`
	Methods  *entity.RegistrationMethods `json:"methods" binding:"required"`
}

// GetTenantInfoRequest 获取租户信息请求
type GetTenantInfoRequest struct {
	AppID string `json:"app_id" binding:"required"`
}

// UpdateTenantInfoRequest 更新租户信息请求
type UpdateTenantInfoRequest struct {
	AppID    string             `json:"app_id" binding:"required"`
	TenantID int64              `json:"tenant_id,omitempty"`
	Info     *entity.TenantInfo `json:"info" binding:"required"`
}

// AppConfigResponse 应用配置响应
type AppConfigResponse struct {
	TenantID    int64  `json:"tenant_id"`
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	ConfigType  string `json:"config_type"`
}

// TenantPasswordPolicyResponse 租户密码策略响应
type TenantPasswordPolicyResponse struct {
	TenantID int64                  `json:"tenant_id"`
	Policy   *entity.PasswordPolicy `json:"policy"`
}

// RegistrationMethodsResponse 注册方式响应
type RegistrationMethodsResponse struct {
	TenantID int64                       `json:"tenant_id"`
	Methods  *entity.RegistrationMethods `json:"methods"`
}

// TenantInfoResponse 租户信息响应
type TenantInfoResponse struct {
	TenantID int64              `json:"tenant_id"`
	Info     *entity.TenantInfo `json:"info"`
}

// AppConfigListResponse 应用配置列表响应
type AppConfigListResponse struct {
	Configs []*AppConfigResponse `json:"configs"`
	Total   int64                `json:"total"`
}

// TenantConfigResponse 租户配置响应
type TenantConfigResponse struct {
	TenantID    int64  `json:"tenant_id"`
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	ConfigType  string `json:"config_type"`
}

// TenantConfigListResponse 租户配置列表响应
type TenantConfigListResponse struct {
	Configs []*TenantConfigResponse `json:"configs"`
	Total   int64                   `json:"total"`
}

// UpdateTenantConfigRequest 更新租户配置请求
type UpdateTenantConfigRequest struct {
	TenantID    int64  `json:"tenant_id" binding:"required"`
	ConfigKey   string `json:"config_key" binding:"required"`
	ConfigValue string `json:"config_value" binding:"required"`
}
