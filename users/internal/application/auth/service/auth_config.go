package service

import (
	"time"
)

// AuthServiceConfig 认证服务配置
type AuthServiceConfig struct {
	// 登录限制配置
	LoginLimitConfig LoginLimitConfig
	// 密码策略配置
	PasswordPolicyConfig PasswordPolicyConfig
	// 会话配置
	SessionConfig SessionConfig
	// 安全配置
	SecurityConfig SecurityConfig
}

// PasswordPolicyConfig 密码策略配置
type PasswordPolicyConfig struct {
	// 是否允许使用默认密码策略
	AllowDefaultPolicy bool
	// 默认密码策略回退时的行为
	DefaultPolicyFallbackBehavior string // "error" | "warn" | "allow"
}

// SessionConfig 会话配置
type SessionConfig struct {
	// 默认会话过期时间
	DefaultExpiration time.Duration
	// 是否允许创建无应用ID的会话
	AllowSessionWithoutAppID bool
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 是否允许使用默认内部应用ID
	AllowDefaultInternalAppID bool
	// 默认内部应用ID回退时的行为
	DefaultInternalAppIDFallbackBehavior string // "error" | "warn" | "use_zero"
}

// NewDefaultAuthServiceConfig 创建默认认证服务配置
func NewDefaultAuthServiceConfig() *AuthServiceConfig {
	return &AuthServiceConfig{
		LoginLimitConfig: DefaultLoginLimitConfig,
		PasswordPolicyConfig: PasswordPolicyConfig{
			AllowDefaultPolicy:            false,   // 不允许使用默认密码策略
			DefaultPolicyFallbackBehavior: "error", // 回退时返回错误
		},
		SessionConfig: SessionConfig{
			DefaultExpiration:        24 * time.Hour, // 24小时
			AllowSessionWithoutAppID: false,          // 不允许创建无应用ID的会话
		},
		SecurityConfig: SecurityConfig{
			AllowDefaultInternalAppID:            false,   // 不允许使用默认内部应用ID
			DefaultInternalAppIDFallbackBehavior: "error", // 回退时返回错误
		},
	}
}

// NewStrictAuthServiceConfig 创建严格模式的认证服务配置
func NewStrictAuthServiceConfig() *AuthServiceConfig {
	return &AuthServiceConfig{
		LoginLimitConfig: DefaultLoginLimitConfig,
		PasswordPolicyConfig: PasswordPolicyConfig{
			AllowDefaultPolicy:            false,
			DefaultPolicyFallbackBehavior: "error",
		},
		SessionConfig: SessionConfig{
			DefaultExpiration:        24 * time.Hour,
			AllowSessionWithoutAppID: false,
		},
		SecurityConfig: SecurityConfig{
			AllowDefaultInternalAppID:            false,
			DefaultInternalAppIDFallbackBehavior: "error",
		},
	}
}

// NewPermissiveAuthServiceConfig 创建宽松模式的认证服务配置（仅用于开发/测试环境）
func NewPermissiveAuthServiceConfig() *AuthServiceConfig {
	return &AuthServiceConfig{
		LoginLimitConfig: DefaultLoginLimitConfig,
		PasswordPolicyConfig: PasswordPolicyConfig{
			AllowDefaultPolicy:            true,   // 允许使用默认密码策略
			DefaultPolicyFallbackBehavior: "warn", // 回退时记录警告
		},
		SessionConfig: SessionConfig{
			DefaultExpiration:        24 * time.Hour,
			AllowSessionWithoutAppID: true, // 允许创建无应用ID的会话
		},
		SecurityConfig: SecurityConfig{
			AllowDefaultInternalAppID:            true,   // 允许使用默认内部应用ID
			DefaultInternalAppIDFallbackBehavior: "warn", // 回退时记录警告
		},
	}
}
