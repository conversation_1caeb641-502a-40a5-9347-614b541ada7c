package service

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/application/verification/dto"
	verificationEntity "gitee.com/heiyee/platforms/users/internal/domain/verification/entity"
)

// VerificationService 验证服务接口
// 定义注册服务需要的验证功能
type VerificationService interface {
	// SendVerification 发送验证码
	SendVerification(ctx context.Context, internalAppID, tenantID int64, req *dto.SendVerificationRequest) (*dto.SendVerificationResponse, error)

	// SendEmailVerification 发送邮件验证（优化版本）
	// 使用EmailVerificationRequest对象传递参数，提高代码可读性和维护性
	SendEmailVerification(ctx context.Context, tenantID int64, req *dto.EmailVerificationRequest) (*dto.SendVerificationResponse, error)

	// VerifyToken 验证令牌
	VerifyToken(ctx context.Context, tenantID int64, req *dto.VerifyTokenRequest) (*dto.VerifyTokenResponse, error)

	// ResendVerification 重新发送验证码
	ResendVerification(ctx context.Context, tenantID int64, req *dto.ResendVerificationRequest) (*dto.SendVerificationResponse, error)

	// CheckTokenStatus 检查令牌状态
	CheckTokenStatus(ctx context.Context, tenantID int64, req *dto.CheckTokenStatusRequest) (*dto.CheckTokenStatusResponse, error)

	// GetTokenByTokenString 通过token字符串获取完整令牌信息
	GetTokenByTokenString(ctx context.Context, tokenString string) (*verificationEntity.VerificationToken, error)

	// SaveToken 保存验证令牌
	SaveToken(ctx context.Context, token *verificationEntity.VerificationToken) error
}
