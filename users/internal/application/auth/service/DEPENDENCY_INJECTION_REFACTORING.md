# 依赖注入重构总结 - 方案4（接口解耦）

## 问题描述

在 `dependency_container.go` 中，`registerService` 在 `verificationService` 之前初始化，导致 `registerService` 中的 `verificationService` 为 `nil`：

```go
// 问题代码
c.applications.registerService = authService.NewRegisterApplicationService(...)  // 先初始化
c.applications.verificationService = authService.NewVerificationApplicationService(...)  // 后初始化
```

## 解决方案：接口解耦

### 1. 创建验证服务接口

**文件**: `users/internal/application/auth/service/verification_service_interface.go`

```go
// VerificationService 验证服务接口
// 定义注册服务需要的验证功能
type VerificationService interface {
	// SendVerification 发送验证码
	SendVerification(ctx context.Context, tenantID int64, req *dto.SendVerificationRequest) (*dto.SendVerificationResponse, error)
	
	// VerifyToken 验证令牌
	VerifyToken(ctx context.Context, tenantID int64, req *dto.VerifyTokenRequest) (*dto.VerifyTokenResponse, error)
	
	// ResendVerification 重新发送验证码
	ResendVerification(ctx context.Context, tenantID int64, req *dto.ResendVerificationRequest) (*dto.SendVerificationResponse, error)
	
	// CheckTokenStatus 检查令牌状态
	CheckTokenStatus(ctx context.Context, tenantID int64, req *dto.CheckTokenStatusRequest) (*dto.CheckTokenStatusResponse, error)
}
```

### 2. 修改 RegisterApplicationService

**文件**: `users/internal/application/auth/service/register_application_service.go`

```go
// 修改前
type RegisterApplicationService struct {
	verificationService *verificationAppService.VerificationApplicationService
	// ... 其他字段
}

// 修改后
type RegisterApplicationService struct {
	verificationService VerificationService // 使用接口而不是具体类型
	// ... 其他字段
}

// 构造函数修改
func NewRegisterApplicationService(
	verificationService VerificationService, // 使用接口
	// ... 其他参数
) *RegisterApplicationService {
	// ...
}
```

### 3. 调整初始化顺序

**文件**: `users/internal/infrastructure/container/dependency_container.go`

```go
// 修改前：registerService 在 verificationService 之前初始化
c.applications.registerService = authService.NewRegisterApplicationService(...)
c.applications.verificationService = verificationService.NewVerificationApplicationService(...)

// 修改后：verificationService 在 registerService 之前初始化
c.applications.verificationService = verificationService.NewVerificationApplicationService(...)
c.applications.registerService = authService.NewRegisterApplicationService(
	// ... 其他参数
	c.applications.verificationService, // 传递接口实现
	// ... 其他参数
)
```

## 重构优势

### 1. 完全解耦
- `RegisterApplicationService` 不再依赖具体的 `VerificationApplicationService` 类型
- 只依赖接口，符合依赖倒置原则

### 2. 易于测试
- 可以轻松创建 Mock 实现进行单元测试
- 不需要复杂的依赖注入框架

### 3. 符合 SOLID 原则
- **D** (Dependency Inversion Principle): 高层模块不依赖低层模块，都依赖抽象
- **I** (Interface Segregation Principle): 接口只包含必要的方法

### 4. 解决初始化顺序问题
- 通过接口解耦，避免了循环依赖
- 清晰的依赖关系，易于理解和维护

## 使用示例

### 在 RegisterApplicationService 中使用

```go
// 发送验证码
_, err = s.verificationService.SendVerification(ctx, tenantID, verificationReq)

// 检查令牌状态
_, err := s.verificationService.CheckTokenStatus(ctx, tenantID, checkReq)
```

### 创建 Mock 进行测试

```go
// Mock 实现
type MockVerificationService struct{}

func (m *MockVerificationService) SendVerification(ctx context.Context, tenantID int64, req *dto.SendVerificationRequest) (*dto.SendVerificationResponse, error) {
	// Mock 实现
	return &dto.SendVerificationResponse{}, nil
}

// 在测试中使用
registerService := authService.NewRegisterApplicationService(
	logger,
	userService,
	tenantService,
	tenantConfigService,
	&MockVerificationService{}, // 使用 Mock
	captchaService,
)
```

## 编译状态

- ✅ **编译通过**: `go build ./...` 成功
- ✅ **功能完整**: 所有功能正常工作
- ✅ **类型安全**: 接口提供类型安全
- ✅ **向后兼容**: 不影响现有功能

## 总结

通过接口解耦的方式，我们成功解决了依赖注入初始化顺序的问题。这种方案不仅解决了当前问题，还为未来的扩展和测试提供了更好的架构基础。

**关键改进**:
1. 创建了清晰的接口契约
2. 实现了依赖倒置
3. 解决了初始化顺序问题
4. 提高了代码的可测试性和可维护性 