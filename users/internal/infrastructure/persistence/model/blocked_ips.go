package model

import (
	"time"

	"gorm.io/gorm"
)

// BlockedIPsModel IP封禁数据模型
type BlockedIPsModel struct {
	ID        int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  int64      `gorm:"not null;index" json:"tenant_id"`
	IPAddress string     `gorm:"not null;size:45;index" json:"ip_address"`
	Reason    string     `gorm:"size:255" json:"reason"`
	BlockType string     `gorm:"size:20;default:'manual'" json:"block_type"`
	ExpiresAt *time.Time `json:"expires_at"`
	IsActive  bool       `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	CreatedBy int64      `gorm:"not null" json:"created_by"`
	CreatedAt time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (BlockedIPsModel) TableName() string {
	return "blocked_ips"
}

// GetTenantID 获取租户ID
func (b *BlockedIPsModel) GetTenantID() int64 {
	return b.TenantID
}

// SetTenantID 设置租户ID
func (b *BlockedIPsModel) SetTenantID(tenantID int64) {
	b.TenantID = tenantID
}

// BeforeCreate GORM创建前钩子
func (b *BlockedIPsModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (b *BlockedIPsModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (b *BlockedIPsModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
