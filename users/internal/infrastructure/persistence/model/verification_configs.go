package model

import (
	"time"

	"gorm.io/gorm"
)

// VerificationConfigsModel 验证配置数据模型
type VerificationConfigsModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	ConfigType    string    `gorm:"not null;size:50;index" json:"config_type"`
	ConfigKey     string    `gorm:"not null;size:100;index" json:"config_key"`
	ConfigValue   string    `gorm:"type:text" json:"config_value"`
	IsEnabled     bool      `gorm:"type:tinyint(1);not null;default:1" json:"is_enabled"`
	Description   string    `gorm:"size:255" json:"description"`
	CreatedBy     int64     `gorm:"not null" json:"created_by"`
	UpdatedBy     *int64    `json:"updated_by"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (VerificationConfigsModel) TableName() string {
	return "verification_configs"
}

// GetTenantID 获取租户ID
func (v *VerificationConfigsModel) GetTenantID() int64 {
	return v.TenantID
}

// SetTenantID 设置租户ID
func (v *VerificationConfigsModel) SetTenantID(tenantID int64) {
	v.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (v *VerificationConfigsModel) GetInternalAppID() int64 {
	return v.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (v *VerificationConfigsModel) SetInternalAppID(appID int64) {
	v.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (v *VerificationConfigsModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (v *VerificationConfigsModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (v *VerificationConfigsModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
