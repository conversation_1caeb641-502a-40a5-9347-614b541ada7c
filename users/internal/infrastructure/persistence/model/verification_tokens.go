package model

import (
	"time"

	"gorm.io/gorm"
)

// VerificationTokensModel 验证令牌数据模型
type VerificationTokensModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64      `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	UserID        int64      `gorm:"not null;index" json:"user_id"`
	TokenType     string     `gorm:"not null;size:50;index" json:"token_type"`
	TokenValue    string     `gorm:"not null;size:255;index" json:"token_value"`
	TargetType    string     `gorm:"not null;size:20;index" json:"target_type"`
	TargetValue   string     `gorm:"not null;size:255" json:"target_value"`
	Status        string     `gorm:"not null;size:20;default:'pending'" json:"status"`
	ExpiresAt     time.Time  `gorm:"not null;index" json:"expires_at"`
	UsedAt        *time.Time `json:"used_at"`
	CreatedAt     time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (VerificationTokensModel) TableName() string {
	return "verification_tokens"
}

// GetTenantID 获取租户ID
func (v *VerificationTokensModel) GetTenantID() int64 {
	return v.TenantID
}

// SetTenantID 设置租户ID
func (v *VerificationTokensModel) SetTenantID(tenantID int64) {
	v.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (v *VerificationTokensModel) GetInternalAppID() int64 {
	return v.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (v *VerificationTokensModel) SetInternalAppID(appID int64) {
	v.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (v *VerificationTokensModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (v *VerificationTokensModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (v *VerificationTokensModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
