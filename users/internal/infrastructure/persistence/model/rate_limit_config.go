package model

import (
	"time"

	"gorm.io/gorm"
)

// RateLimitConfigModel 限流配置数据模型
type RateLimitConfigModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	ResourceType  string    `gorm:"not null;size:50;index" json:"resource_type"`
	ResourceID    int64     `gorm:"not null;index" json:"resource_id"`
	LimitType     string    `gorm:"not null;size:20;default:'rate'" json:"limit_type"`
	LimitValue    int       `gorm:"not null;default:1000" json:"limit_value"`
	TimeWindow    int       `gorm:"not null;default:3600" json:"time_window"`
	IsActive      bool      `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	Description   string    `gorm:"size:255" json:"description"`
	CreatedBy     int64     `gorm:"not null" json:"created_by"`
	UpdatedBy     *int64    `json:"updated_by"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (RateLimitConfigModel) TableName() string {
	return "rate_limit_config"
}

// GetTenantID 获取租户ID
func (r *RateLimitConfigModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *RateLimitConfigModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *RateLimitConfigModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *RateLimitConfigModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (r *RateLimitConfigModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *RateLimitConfigModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *RateLimitConfigModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
