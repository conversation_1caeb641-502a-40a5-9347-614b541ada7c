package model

import (
	"time"

	"gorm.io/gorm"
)

// RolePermissionsModel 角色权限关联数据模型
type RolePermissionsModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	RoleID        int64     `gorm:"not null;index" json:"role_id"`
	PermissionID  int64     `gorm:"not null;index" json:"permission_id"`
	IsActive      bool      `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	CreatedBy     int64     `gorm:"not null" json:"created_by"`
	UpdatedBy     *int64    `json:"updated_by"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (RolePermissionsModel) TableName() string {
	return "role_permissions"
}

// GetTenantID 获取租户ID
func (r *RolePermissionsModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *RolePermissionsModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *RolePermissionsModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *RolePermissionsModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (r *RolePermissionsModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *RolePermissionsModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *RolePermissionsModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
