package model

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// TenantAware 租户感知接口
type TenantAware interface {
	GetTenantID() int64
	SetTenantID(tenantID int64)
}

// AppAware 应用感知接口
type AppAware interface {
	GetInternalAppID() int64
	SetInternalAppID(appID int64)
}

// BaseModel 基础模型结构体，包含通用字段
type BaseModel struct {
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// SoftDeleteModel 软删除模型结构体，包含DeletedAt字段
type SoftDeleteModel struct {
	BaseModel
	DeletedAt *time.Time `gorm:"index" json:"deleted_at"`
}

// GetTenantID 获取租户ID
func (b *BaseModel) GetTenantID() int64 {
	return b.TenantID
}

// SetTenantID 设置租户ID
func (b *BaseModel) SetTenantID(tenantID int64) {
	b.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (b *BaseModel) GetInternalAppID() int64 {
	return b.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (b *BaseModel) SetInternalAppID(appID int64) {
	b.InternalAppID = appID
}

// TenantAwareModel 租户感知模型接口
type TenantAwareModel interface {
	TenantAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// AppAwareModel 应用感知模型接口
type AppAwareModel interface {
	AppAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// FullAwareModel 完整感知模型接口（同时支持租户和应用）
type FullAwareModel interface {
	TenantAware
	AppAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// GetTenantIDFromContext 从上游context获取租户ID
func GetTenantIDFromContext(ctx context.Context) int64 {
	if ctx == nil {
		return 0
	}
	tenantID, _ := usercontext.GetTenantID(ctx)
	return tenantID
}

// GetInternalAppIDFromContext 从上游context获取内部应用ID
func GetInternalAppIDFromContext(ctx context.Context) int64 {
	if ctx == nil {
		return 0
	}
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	return internalAppID
}

// AutoFillTenantAndApp 自动填充租户和应用信息的钩子函数
func AutoFillTenantAndApp(tx *gorm.DB) {
	ctx := tx.Statement.Context
	if ctx == nil {
		return
	}

	// 获取当前模型
	model := tx.Statement.Model
	if model == nil {
		return
	}

	// 从上游context获取租户ID
	tenantID := GetTenantIDFromContext(ctx)
	if tenantID > 0 {
		if tenantAware, ok := model.(TenantAware); ok {
			tenantAware.SetTenantID(tenantID)
		}
	}

	// 从上游context获取应用ID
	internalAppID := GetInternalAppIDFromContext(ctx)
	if internalAppID > 0 {
		if appAware, ok := model.(AppAware); ok {
			appAware.SetInternalAppID(internalAppID)
		}
	}
}

// AutoFilterTenantAndApp 自动过滤租户和应用信息的钩子函数
func AutoFilterTenantAndApp(tx *gorm.DB) {
	ctx := tx.Statement.Context
	if ctx == nil {
		return
	}

	// 从上游context获取租户ID和应用ID
	tenantID := GetTenantIDFromContext(ctx)
	internalAppID := GetInternalAppIDFromContext(ctx)

	// 添加租户过滤条件
	if tenantID > 0 {
		tx.Statement.AddClause(clause.Where{Exprs: []clause.Expression{
			clause.Eq{Column: "tenant_id", Value: tenantID},
		}})
	}

	// 添加应用过滤条件
	if internalAppID > 0 {
		tx.Statement.AddClause(clause.Where{Exprs: []clause.Expression{
			clause.Eq{Column: "internal_app_id", Value: internalAppID},
		}})
	}
}

// BeforeCreateHook 创建前的钩子函数
func BeforeCreateHook(tx *gorm.DB) {
	AutoFillTenantAndApp(tx)
}

// BeforeUpdateHook 更新前的钩子函数
func BeforeUpdateHook(tx *gorm.DB) {
	AutoFillTenantAndApp(tx)
}

// BeforeQueryHook 查询前的钩子函数
func BeforeQueryHook(tx *gorm.DB) {
	AutoFilterTenantAndApp(tx)
}

// RegisterHooks 注册GORM钩子
func RegisterHooks(db *gorm.DB) {
	// 注册全局钩子
	db.Callback().Create().Before("gorm:create").Register("auto_fill_tenant_app", BeforeCreateHook)
	db.Callback().Update().Before("gorm:update").Register("auto_fill_tenant_app", BeforeUpdateHook)
	db.Callback().Query().Before("gorm:query").Register("auto_filter_tenant_app", BeforeQueryHook)
}

// IsTenantAware 检查模型是否支持租户感知
func IsTenantAware(model interface{}) bool {
	_, ok := model.(TenantAware)
	return ok
}

// IsAppAware 检查模型是否支持应用感知
func IsAppAware(model interface{}) bool {
	_, ok := model.(AppAware)
	return ok
}

// IsFullAware 检查模型是否同时支持租户和应用感知
func IsFullAware(model interface{}) bool {
	_, ok := model.(FullAwareModel)
	return ok
}

// GetModelAwareness 获取模型的感知类型
func GetModelAwareness(model interface{}) string {
	if IsFullAware(model) {
		return "full"
	} else if IsTenantAware(model) && IsAppAware(model) {
		return "both"
	} else if IsTenantAware(model) {
		return "tenant"
	} else if IsAppAware(model) {
		return "app"
	}
	return "none"
}
