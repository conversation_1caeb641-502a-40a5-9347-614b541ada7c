package model

import (
	"time"

	"gorm.io/gorm"
)

// ResourceRelationsModel 资源关系数据模型
type ResourceRelationsModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	ParentType    string    `gorm:"not null;size:50;index" json:"parent_type"`
	ParentID      int64     `gorm:"not null;index" json:"parent_id"`
	ChildType     string    `gorm:"not null;size:50;index" json:"child_type"`
	ChildID       int64     `gorm:"not null;index" json:"child_id"`
	RelationType  string    `gorm:"not null;size:50;index" json:"relation_type"`
	IsActive      bool      `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	CreatedBy     int64     `gorm:"not null" json:"created_by"`
	UpdatedBy     *int64    `json:"updated_by"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (ResourceRelationsModel) TableName() string {
	return "resource_relations"
}

// GetTenantID 获取租户ID
func (r *ResourceRelationsModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *ResourceRelationsModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *ResourceRelationsModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *ResourceRelationsModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (r *ResourceRelationsModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *ResourceRelationsModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *ResourceRelationsModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
