package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ApplicationModel 应用数据模型
type ApplicationModel struct {
	InternalAppID     int64          `gorm:"primaryKey;column:internal_app_id" json:"internal_app_id"`
	AppID             string         `gorm:"uniqueIndex;not null;size:64" json:"app_id"`
	TenantID          int64          `gorm:"not null;index" json:"tenant_id"`
	AppName           string         `gorm:"not null;size:100" json:"app_name"`
	AppCode           string         `gorm:"not null;size:50" json:"app_code"`
	Description       string         `gorm:"type:text" json:"description"`
	AppType           string         `gorm:"type:varchar(20);not null;default:'web'" json:"app_type"`
	Status            string         `gorm:"type:varchar(20);not null;default:'active'" json:"status"`
	AppSecret         string         `gorm:"not null;size:128" json:"app_secret"`
	CallbackURLs      JSONArray      `gorm:"type:json" json:"callback_urls"`
	AllowedOrigins    JSONArray      `gorm:"type:json" json:"allowed_origins"`
	Scopes            JSONArray      `gorm:"type:json" json:"scopes"`
	RateLimit         int            `gorm:"default:1000" json:"rate_limit"`
	LogoURL           string         `gorm:"size:255" json:"logo_url"`
	HomepageURL       string         `gorm:"size:255" json:"homepage_url"`
	PrivacyPolicyURL  string         `gorm:"size:255" json:"privacy_policy_url"`
	TermsOfServiceURL string         `gorm:"size:255" json:"terms_of_service_url"`
	ContactEmail      string         `gorm:"size:100" json:"contact_email"`
	IsSystem          bool           `gorm:"type:tinyint(1);not null;default:0" json:"is_system"`
	Active            bool           `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	Config            JSONMap        `gorm:"type:json" json:"config"`
	CreatedBy         int64          `gorm:"not null" json:"created_by"`
	UpdatedBy         *int64         `json:"updated_by"`
	CreatedAt         time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

// JSONArray JSON数组类型
type JSONArray []string

// Value 实现driver.Valuer接口
func (j JSONArray) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONArray) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSONArray", value)
	}

	return json.Unmarshal(bytes, j)
}

// TableName 指定表名
func (ApplicationModel) TableName() string {
	return "applications"
}

// GetTenantID 获取租户ID
func (a *ApplicationModel) GetTenantID() int64 {
	return a.TenantID
}

// SetTenantID 设置租户ID
func (a *ApplicationModel) SetTenantID(tenantID int64) {
	a.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (a *ApplicationModel) GetInternalAppID() int64 {
	return a.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (a *ApplicationModel) SetInternalAppID(appID int64) {
	a.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (a *ApplicationModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (a *ApplicationModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (a *ApplicationModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
