package model

import (
	"time"

	"gorm.io/gorm"
)

// MFAConfigsModel MFA配置数据模型
type MFAConfigsModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64      `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	UserID        int64      `gorm:"not null;index" json:"user_id"`
	MFAType       string     `gorm:"not null;size:20;index" json:"mfa_type"`
	SecretKey     string     `gorm:"not null;size:255" json:"secret_key"`
	BackupCodes   string     `gorm:"type:text" json:"backup_codes"`
	IsEnabled     bool       `gorm:"type:tinyint(1);not null;default:0" json:"is_enabled"`
	LastUsedAt    *time.Time `json:"last_used_at"`
	CreatedAt     time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (MFAConfigsModel) TableName() string {
	return "mfa_configs"
}

// GetTenantID 获取租户ID
func (m *MFAConfigsModel) GetTenantID() int64 {
	return m.TenantID
}

// SetTenantID 设置租户ID
func (m *MFAConfigsModel) SetTenantID(tenantID int64) {
	m.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (m *MFAConfigsModel) GetInternalAppID() int64 {
	return m.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (m *MFAConfigsModel) SetInternalAppID(appID int64) {
	m.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (m *MFAConfigsModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (m *MFAConfigsModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (m *MFAConfigsModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
