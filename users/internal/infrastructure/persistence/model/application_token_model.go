package model

import (
	"time"

	"gorm.io/gorm"
)

// ApplicationTokenModel 应用Token数据模型
type ApplicationTokenModel struct {
	ID            int64      `gorm:"primaryKey" json:"id"`
	InternalAppID int64      `gorm:"not null;index" json:"internal_app_id"`
	TokenType     string     `gorm:"type:varchar(20);not null" json:"token_type"`
	TokenValue    string     `gorm:"uniqueIndex;not null;size:255" json:"token_value"`
	TokenName     string     `gorm:"size:100" json:"token_name"`
	Permissions   JSONArray  `gorm:"type:json" json:"permissions"`
	ExpiresAt     *time.Time `json:"expires_at"`
	LastUsedAt    *time.Time `json:"last_used_at"`
	UsageCount    int        `gorm:"default:0" json:"usage_count"`
	IsActive      bool       `gorm:"type:tinyint(1);not null;default:1" json:"is_active"`
	CreatedBy     int64      `gorm:"not null" json:"created_by"`
	CreatedAt     time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (ApplicationTokenModel) TableName() string {
	return "application_tokens"
}

// GetTenantID 获取租户ID
func (a *ApplicationTokenModel) GetTenantID() int64 {
	return 0 // ApplicationTokenModel 没有 TenantID 字段
}

// SetTenantID 设置租户ID
func (a *ApplicationTokenModel) SetTenantID(tenantID int64) {
	// ApplicationTokenModel 没有 TenantID 字段，忽略设置
}

// GetInternalAppID 获取内部应用ID
func (a *ApplicationTokenModel) GetInternalAppID() int64 {
	return a.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (a *ApplicationTokenModel) SetInternalAppID(appID int64) {
	a.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (a *ApplicationTokenModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (a *ApplicationTokenModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (a *ApplicationTokenModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
