package model

import (
	"time"

	"gorm.io/gorm"
)

// SystemConfigModel 系统配置数据模型
type SystemConfigModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index" json:"internal_app_id"`
	ConfigKey     string    `gorm:"not null;size:100;index" json:"config_key"`
	ConfigValue   string    `gorm:"type:text" json:"config_value"`
	ConfigType    string    `gorm:"size:20;default:'string'" json:"config_type"`
	Description   string    `gorm:"size:255" json:"description"`
	IsSystem      bool      `gorm:"type:tinyint(1);not null;default:0" json:"is_system"`
	IsEncrypted   bool      `gorm:"type:tinyint(1);not null;default:0" json:"is_encrypted"`
	CreatedBy     int64     `gorm:"not null" json:"created_by"`
	UpdatedBy     *int64    `json:"updated_by"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (SystemConfigModel) TableName() string {
	return "system_config"
}

// GetTenantID 获取租户ID
func (s *SystemConfigModel) GetTenantID() int64 {
	return s.TenantID
}

// SetTenantID 设置租户ID
func (s *SystemConfigModel) SetTenantID(tenantID int64) {
	s.TenantID = tenantID
}

// BeforeCreate GORM创建前钩子
func (s *SystemConfigModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (s *SystemConfigModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (s *SystemConfigModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
