package model

import (
	"time"

	"gorm.io/gorm"
)

// AuditLogModel 审计日志数据模型
type AuditLogModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	UserID        int64     `gorm:"not null;index" json:"user_id"`
	Action        string    `gorm:"not null;size:50;index" json:"action"`
	ResourceType  string    `gorm:"not null;size:50;index" json:"resource_type"`
	ResourceID    int64     `gorm:"not null;index" json:"resource_id"`
	Details       string    `gorm:"type:text" json:"details"`
	IPAddress     string    `gorm:"size:45" json:"ip_address"`
	UserAgent     string    `gorm:"size:500" json:"user_agent"`
	Status        string    `gorm:"size:20;default:'success'" json:"status"`
	ErrorMessage  string    `gorm:"type:text" json:"error_message"`
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (AuditLogModel) TableName() string {
	return "audit_log"
}

// GetTenantID 获取租户ID
func (a *AuditLogModel) GetTenantID() int64 {
	return a.TenantID
}

// SetTenantID 设置租户ID
func (a *AuditLogModel) SetTenantID(tenantID int64) {
	a.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (a *AuditLogModel) GetInternalAppID() int64 {
	return a.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (a *AuditLogModel) SetInternalAppID(appID int64) {
	a.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (a *AuditLogModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (a *AuditLogModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (a *AuditLogModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
