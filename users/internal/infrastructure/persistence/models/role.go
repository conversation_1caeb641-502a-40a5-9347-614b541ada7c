package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// RoleModel 角色数据模型 - 仅用于数据库操作
type RoleModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	Name          string     `gorm:"not null;size:50"`
	Code          string     `gorm:"not null;size:50"`
	DisplayName   string     `gorm:"not null;size:100"`
	Description   string     `gorm:"size:500"`
	Status        string     `gorm:"type:varchar(20);not null;default:'active'"`
	IsSystem      bool       `gorm:"default:false"`
	Sort          int        `gorm:"not null;default:0"` // 新增：排序字段
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	DeletedAt     *time.Time `gorm:"index"`
}

// TableName 指定表名
func (RoleModel) TableName() string {
	return "roles"
}

// GetTenantID 获取租户ID
func (r *RoleModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *RoleModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *RoleModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *RoleModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (r *RoleModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *RoleModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *RoleModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (r *RoleModel) ToDomainEntity() *entity.Role {
	role := &entity.Role{
		ID:            r.ID,
		TenantID:      r.TenantID,
		InternalAppID: r.InternalAppID,
		Name:          r.Name,
		Code:          r.Code,
		DisplayName:   r.DisplayName,
		Description:   r.Description,
		Status:        r.Status,
		IsSystem:      r.IsSystem,
		Sort:          r.Sort, // 新增字段
		CreatedAt:     r.CreatedAt,
		UpdatedAt:     r.UpdatedAt,
		DeletedAt:     r.DeletedAt,
	}

	return role
}

// FromDomainEntity 从领域实体创建数据模型
func (r *RoleModel) FromDomainEntity(role *entity.Role) {
	r.ID = role.ID
	r.TenantID = role.TenantID
	r.InternalAppID = role.InternalAppID
	r.Name = role.Name
	r.Code = role.Code
	r.DisplayName = role.DisplayName
	r.Description = role.Description
	r.Status = role.Status
	r.IsSystem = role.IsSystem
	r.Sort = role.Sort // 新增字段
	r.CreatedAt = role.CreatedAt
	r.UpdatedAt = role.UpdatedAt
	r.DeletedAt = role.DeletedAt
}

// NewRoleModelFromDomain 从领域实体创建数据模型
func NewRoleModelFromDomain(role *entity.Role) *RoleModel {
	model := &RoleModel{}
	model.FromDomainEntity(role)
	return model
}
