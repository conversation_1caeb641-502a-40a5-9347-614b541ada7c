package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// DepartmentModel 部门数据模型 - 仅用于数据库操作
type DepartmentModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	ParentID      *int64     `gorm:"index"` // 父部门ID，支持层级结构
	Name          string     `gorm:"not null;size:100"`
	Code          string     `gorm:"not null;size:50;uniqueIndex"`
	Description   string     `gorm:"size:500"`
	Level         int        `gorm:"not null;default:1"` // 部门层级
	Sort          int        `gorm:"not null;default:0"` // 排序
	Status        string     `gorm:"type:varchar(20);not null;default:'active'"`
	ManagerID     *int64     `gorm:"index"` // 部门负责人ID
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	DeletedAt     *time.Time `gorm:"index"`
}

// TableName 指定表名
func (DepartmentModel) TableName() string {
	return "departments"
}

// GetTenantID 获取租户ID
func (d *DepartmentModel) GetTenantID() int64 {
	return d.TenantID
}

// SetTenantID 设置租户ID
func (d *DepartmentModel) SetTenantID(tenantID int64) {
	d.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (d *DepartmentModel) GetInternalAppID() int64 {
	return d.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (d *DepartmentModel) SetInternalAppID(appID int64) {
	d.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (d *DepartmentModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (d *DepartmentModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (d *DepartmentModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (d *DepartmentModel) ToDomainEntity() *entity.Department {
	department := &entity.Department{
		ID:            d.ID,
		TenantID:      d.TenantID,
		InternalAppID: d.InternalAppID,
		ParentID:      d.ParentID,
		Name:          d.Name,
		Code:          d.Code,
		Description:   d.Description,
		Level:         d.Level,
		Sort:          d.Sort,
		Status:        value_object.DepartmentStatus(d.Status),
		ManagerID:     d.ManagerID,
		CreatedAt:     d.CreatedAt,
		UpdatedAt:     d.UpdatedAt,
		DeletedAt:     d.DeletedAt,
	}

	return department
}

// FromDomainEntity 从领域实体创建数据模型
func (d *DepartmentModel) FromDomainEntity(department *entity.Department) {
	d.ID = department.ID
	d.TenantID = department.TenantID
	d.InternalAppID = department.InternalAppID
	d.ParentID = department.ParentID
	d.Name = department.Name
	d.Code = department.Code
	d.Description = department.Description
	d.Level = department.Level
	d.Sort = department.Sort
	d.Status = department.Status.String()
	d.ManagerID = department.ManagerID
	d.CreatedAt = department.CreatedAt
	d.UpdatedAt = department.UpdatedAt
	d.DeletedAt = department.DeletedAt
}

// NewDepartmentModelFromDomain 从领域实体创建数据模型
func NewDepartmentModelFromDomain(department *entity.Department) *DepartmentModel {
	model := &DepartmentModel{}
	model.FromDomainEntity(department)
	return model
}
