package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// PositionModel 职位数据模型 - 仅用于数据库操作
type PositionModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	DepartmentID  *int64     `gorm:"index"` // 所属部门ID
	Name          string     `gorm:"not null;size:100"`
	Code          string     `gorm:"not null;size:50;uniqueIndex"`
	Description   string     `gorm:"size:500"`
	Level         int        `gorm:"not null;default:1"` // 职位级别
	Sort          int        `gorm:"not null;default:0"` // 排序
	Status        string     `gorm:"type:varchar(20);not null;default:'active'"`
	IsSystem      bool       `gorm:"default:false"`
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	DeletedAt     *time.Time `gorm:"index"`
}

// TableName 指定表名
func (PositionModel) TableName() string {
	return "positions"
}

// GetTenantID 获取租户ID
func (p *PositionModel) GetTenantID() int64 {
	return p.TenantID
}

// SetTenantID 设置租户ID
func (p *PositionModel) SetTenantID(tenantID int64) {
	p.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (p *PositionModel) GetInternalAppID() int64 {
	return p.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (p *PositionModel) SetInternalAppID(appID int64) {
	p.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (p *PositionModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (p *PositionModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (p *PositionModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (p *PositionModel) ToDomainEntity() *entity.Position {
	position := &entity.Position{
		ID:            p.ID,
		TenantID:      p.TenantID,
		InternalAppID: p.InternalAppID,
		DepartmentID:  p.DepartmentID,
		Name:          p.Name,
		Code:          p.Code,
		Description:   p.Description,
		Level:         p.Level,
		Sort:          p.Sort,
		Status:        value_object.PositionStatus(p.Status),
		IsSystem:      p.IsSystem,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		DeletedAt:     p.DeletedAt,
	}

	return position
}

// FromDomainEntity 从领域实体创建数据模型
func (p *PositionModel) FromDomainEntity(position *entity.Position) {
	p.ID = position.ID
	p.TenantID = position.TenantID
	p.InternalAppID = position.InternalAppID
	p.DepartmentID = position.DepartmentID
	p.Name = position.Name
	p.Code = position.Code
	p.Description = position.Description
	p.Level = position.Level
	p.Sort = position.Sort
	p.Status = position.Status.String()
	p.IsSystem = position.IsSystem
	p.CreatedAt = position.CreatedAt
	p.UpdatedAt = position.UpdatedAt
	p.DeletedAt = position.DeletedAt
}

// NewPositionModelFromDomain 从领域实体创建数据模型
func NewPositionModelFromDomain(position *entity.Position) *PositionModel {
	model := &PositionModel{}
	model.FromDomainEntity(position)
	return model
}
