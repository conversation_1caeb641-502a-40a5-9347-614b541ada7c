package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// TenantModel 租户数据模型 - 仅用于数据库操作
type TenantModel struct {
	ID               int64  `gorm:"primaryKey"`
	TenantCode       string `gorm:"uniqueIndex;not null;size:64"`
	TenantName       string `gorm:"not null;size:128"`
	Status           string `gorm:"type:varchar(20);not null;default:'active'"`
	MaxUsers         int    `gorm:"default:0"`
	MaxStorage       int64  `gorm:"default:10737418240"`
	SubscriptionPlan string `gorm:"size:50;default:'basic'"`
	ExpiresAt        *time.Time
	CreatedAt        time.Time `gorm:"autoCreateTime"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (TenantModel) TableName() string {
	return "tenants"
}

// ToDomainEntity 转换为领域实体
func (t *TenantModel) ToDomainEntity() *entity.Tenant {
	tenant := &entity.Tenant{
		ID:               t.ID,
		TenantCode:       t.TenantCode,
		TenantName:       t.TenantName,
		Status:           value_object.TenantStatus(t.Status),
		MaxUsers:         t.MaxUsers,
		MaxStorage:       t.MaxStorage,
		SubscriptionPlan: t.SubscriptionPlan,
		ExpiresAt:        t.ExpiresAt,
		CreatedAt:        t.CreatedAt,
		UpdatedAt:        t.UpdatedAt,
	}

	return tenant
}

// FromDomainEntity 从领域实体创建数据模型
func (t *TenantModel) FromDomainEntity(tenant *entity.Tenant) {
	t.ID = tenant.ID
	t.TenantCode = tenant.TenantCode
	t.TenantName = tenant.TenantName
	t.Status = tenant.Status.String()
	t.MaxUsers = tenant.MaxUsers
	t.MaxStorage = tenant.MaxStorage
	t.SubscriptionPlan = tenant.SubscriptionPlan
	t.ExpiresAt = tenant.ExpiresAt
	t.CreatedAt = tenant.CreatedAt
	t.UpdatedAt = tenant.UpdatedAt
}

// NewTenantModelFromDomain 从领域实体创建数据模型
func NewTenantModelFromDomain(tenant *entity.Tenant) *TenantModel {
	model := &TenantModel{}
	model.FromDomainEntity(tenant)
	return model
}
