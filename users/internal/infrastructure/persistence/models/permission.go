package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// PermissionModel 权限数据模型 - 仅用于数据库操作
type PermissionModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	Name          string     `gorm:"not null;size:100"`
	Code          string     `gorm:"not null;size:100;index"`
	DisplayName   string     `gorm:"size:100"`
	Description   string     `gorm:"size:500"`
	ResourceType  string     `gorm:"size:50"` // 新增：资源类型
	ResourceID    int64      `gorm:"not null;index"`
	Action        string     `gorm:"size:50"` // 新增：操作类型
	Scope         string     `gorm:"type:varchar(20);not null;default:'self'"`
	Status        string     `gorm:"type:varchar(20);not null;default:'active'"`
	IsSystem      bool       `gorm:"default:false"`
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	DeletedAt     *time.Time `gorm:"index"`
}

// TableName 指定表名
func (PermissionModel) TableName() string {
	return "permissions"
}

// GetTenantID 获取租户ID
func (p *PermissionModel) GetTenantID() int64 {
	return p.TenantID
}

// SetTenantID 设置租户ID
func (p *PermissionModel) SetTenantID(tenantID int64) {
	p.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (p *PermissionModel) GetInternalAppID() int64 {
	return p.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (p *PermissionModel) SetInternalAppID(appID int64) {
	p.InternalAppID = appID
}

// BeforeCreate GORM创建前钩子
func (p *PermissionModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (p *PermissionModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (p *PermissionModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (p *PermissionModel) ToDomainEntity() *entity.Permission {
	permission := &entity.Permission{
		ID:            p.ID,
		TenantID:      p.TenantID,
		InternalAppID: p.InternalAppID,
		Name:          p.Name,
		Code:          p.Code,
		DisplayName:   p.DisplayName,
		Description:   p.Description,
		ResourceType:  p.ResourceType, // 新增字段
		ResourceID:    p.ResourceID,
		Action:        p.Action, // 新增字段
		Scope:         p.Scope,
		Status:        p.Status,
		IsSystem:      p.IsSystem,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		DeletedAt:     p.DeletedAt,
	}

	return permission
}

// FromDomainEntity 从领域实体创建数据模型
func (p *PermissionModel) FromDomainEntity(permission *entity.Permission) {
	p.ID = permission.ID
	p.TenantID = permission.TenantID
	p.InternalAppID = permission.InternalAppID
	p.Name = permission.Name
	p.Code = permission.Code
	p.DisplayName = permission.DisplayName
	p.Description = permission.Description
	p.ResourceType = permission.ResourceType // 新增字段
	p.ResourceID = permission.ResourceID
	p.Action = permission.Action // 新增字段
	p.Scope = permission.Scope
	p.Status = permission.Status
	p.IsSystem = permission.IsSystem
	p.CreatedAt = permission.CreatedAt
	p.UpdatedAt = permission.UpdatedAt
	p.DeletedAt = permission.DeletedAt
}

// NewPermissionModelFromDomain 从领域实体创建数据模型
func NewPermissionModelFromDomain(permission *entity.Permission) *PermissionModel {
	model := &PermissionModel{}
	model.FromDomainEntity(permission)
	return model
}
