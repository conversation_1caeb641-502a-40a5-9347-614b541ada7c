package persistence

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// BatchOperations 批量操作构建器
type BatchOperations[T any] struct {
	db     *gorm.DB
	ctx    context.Context
	logger logiface.Logger
}

// NewBatchOperations 创建批量操作构建器
func NewBatchOperations[T any](db *gorm.DB, ctx context.Context, logger logiface.Logger) *BatchOperations[T] {
	return &BatchOperations[T]{
		db:     db,
		ctx:    ctx,
		logger: logger,
	}
}

// BatchCreate 批量创建
func (bo *BatchOperations[T]) BatchCreate(entities []T, batchSize int) error {
	if batchSize <= 0 {
		batchSize = 1000
	}

	tx := bo.db.WithContext(bo.ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for i := 0; i < len(entities); i += batchSize {
		end := i + batchSize
		if end > len(entities) {
			end = len(entities)
		}

		batch := entities[i:end]
		if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("batch create failed at index %d: %w", i, err)
		}
	}

	return tx.Commit().Error
}

// BatchUpdate 批量更新
func (bo *BatchOperations[T]) BatchUpdate(entities []T, batchSize int) error {
	if batchSize <= 0 {
		batchSize = 1000
	}

	tx := bo.db.WithContext(bo.ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for i := 0; i < len(entities); i += batchSize {
		end := i + batchSize
		if end > len(entities) {
			end = len(entities)
		}

		batch := entities[i:end]
		for _, entity := range batch {
			if err := tx.Save(&entity).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("batch update failed at index %d: %w", i, err)
			}
		}
	}

	return tx.Commit().Error
}

// BatchDelete 批量删除
func (bo *BatchOperations[T]) BatchDelete(ids []int64) error {
	tx := bo.db.WithContext(bo.ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Delete(new(T), ids).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("batch delete failed: %w", err)
	}

	return tx.Commit().Error
}

// BatchSoftDelete 批量软删除
func (bo *BatchOperations[T]) BatchSoftDelete(ids []int64) error {
	tx := bo.db.WithContext(bo.ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Model(new(T)).Where("id IN ?", ids).Update("deleted_at", time.Now()).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("batch soft delete failed: %w", err)
	}

	return tx.Commit().Error
}

// BatchUpsert 批量插入或更新
func (bo *BatchOperations[T]) BatchUpsert(entities []T, batchSize int) error {
	if batchSize <= 0 {
		batchSize = 1000
	}

	tx := bo.db.WithContext(bo.ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for i := 0; i < len(entities); i += batchSize {
		end := i + batchSize
		if end > len(entities) {
			end = len(entities)
		}

		batch := entities[i:end]
		for _, entity := range batch {
			// 使用 ON DUPLICATE KEY UPDATE
			if err := tx.Clauses(clause.OnConflict{
				UpdateAll: true,
			}).Create(&entity).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("batch upsert failed at index %d: %w", i, err)
			}
		}
	}

	return tx.Commit().Error
}