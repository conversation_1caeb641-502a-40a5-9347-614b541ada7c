package context

import "errors"

var (
	// ErrNilTenantContext 租户上下文为空错误
	ErrNilTenantContext = errors.New("tenant context is nil")

	// ErrInvalidTenantID 无效的租户ID错误
	ErrInvalidTenantID = errors.New("invalid tenant ID")

	// ErrInvalidAppID 无效的应用ID错误
	ErrInvalidAppID = errors.New("invalid app ID")

	// ErrMissingTenantContext 缺少租户上下文错误
	ErrMissingTenantContext = errors.New("missing tenant context")

	// ErrInvalidTenantContext 无效的租户上下文错误
	ErrInvalidTenantContext = errors.New("invalid tenant context")
)
