package context

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
)

// TenantContext 租户上下文结构体
type TenantContext struct {
	TenantID      int64 `json:"tenant_id"`
	InternalAppID int64 `json:"internal_app_id"`
	UserID        int64 `json:"user_id"`
}

// GetTenantContext 从上下文中获取租户上下文
func GetTenantContext(ctx context.Context) *TenantContext {
	if ctx == nil {
		return nil
	}

	userID := int64(0)
	if userIDFromCtx, exists := usercontext.GetUserID(ctx); exists {
		userID = userIDFromCtx
	}

	return &TenantContext{
		TenantID:      model.GetTenantIDFromContext(ctx),
		InternalAppID: model.GetInternalAppIDFromContext(ctx),
		UserID:        userID,
	}
}

// SetTenantContext 设置租户上下文到上下文中
func SetTenantContext(ctx context.Context, tenantCtx *TenantContext) context.Context {
	if tenantCtx == nil {
		return ctx
	}

	// 使用 usercontext 包设置信息
	if tenantCtx.TenantID > 0 {
		appInfo := &usercontext.AppInfo{
			TenantID: tenantCtx.TenantID,
		}
		ctx = usercontext.SetAppInfo(ctx, appInfo)
	}

	if tenantCtx.InternalAppID > 0 {
		// 如果已经有用户信息，更新它
		if userInfo, exists := usercontext.GetUserInfo(ctx); exists && userInfo != nil {
			userInfo.InternalAppID = tenantCtx.InternalAppID
			ctx = usercontext.SetUserInfo(ctx, userInfo)
		} else {
			// 创建新的用户信息
			userInfo := &usercontext.UserInfo{
				InternalAppID: tenantCtx.InternalAppID,
			}
			ctx = usercontext.SetUserInfo(ctx, userInfo)
		}
	}

	return ctx
}

// WithTenantContext 创建包含租户上下文的上下文
func WithTenantContext(ctx context.Context, tenantID, appID int64) context.Context {
	// 使用 usercontext 包设置信息
	if tenantID > 0 {
		appInfo := &usercontext.AppInfo{
			TenantID: tenantID,
		}
		ctx = usercontext.SetAppInfo(ctx, appInfo)
	}

	if appID > 0 {
		// 如果已经有用户信息，更新它
		if userInfo, exists := usercontext.GetUserInfo(ctx); exists && userInfo != nil {
			userInfo.InternalAppID = appID
			ctx = usercontext.SetUserInfo(ctx, userInfo)
		} else {
			// 创建新的用户信息
			userInfo := &usercontext.UserInfo{
				InternalAppID: appID,
			}
			ctx = usercontext.SetUserInfo(ctx, userInfo)
		}
	}

	return ctx
}

// CreateTenantContext 创建租户上下文
func CreateTenantContext(tenantID, appID int64) *TenantContext {
	return &TenantContext{
		TenantID:      tenantID,
		InternalAppID: appID,
		UserID:        0,
	}
}

// CreateTenantContextFromUserContext 从 pkg/usercontext 的用户信息创建租户上下文
func CreateTenantContextFromUserContext(ctx context.Context) *TenantContext {
	if userInfo, exists := usercontext.GetUserInfo(ctx); exists && userInfo != nil {
		return &TenantContext{
			TenantID:      userInfo.TenantID,
			InternalAppID: userInfo.InternalAppID,
			UserID:        userInfo.UserID,
		}
	}
	return nil
}

// CreateTenantContextFromUserContextMust 从 pkg/usercontext 的用户信息创建租户上下文（使用 MustGet 函数）
func CreateTenantContextFromUserContextMust(ctx context.Context) *TenantContext {
	return &TenantContext{
		TenantID:      usercontext.MustGetTenantID(ctx),
		InternalAppID: usercontext.MustGetInternalAppID(ctx),
		UserID:        usercontext.MustGetUserID(ctx),
	}
}

// ValidateTenantContext 验证租户上下文
func ValidateTenantContext(tenantCtx *TenantContext) error {
	if tenantCtx == nil {
		return ErrNilTenantContext
	}

	if tenantCtx.TenantID <= 0 {
		return ErrInvalidTenantID
	}

	if tenantCtx.InternalAppID <= 0 {
		return ErrInvalidAppID
	}

	return nil
}

// IsValidTenantContext 检查租户上下文是否有效
func IsValidTenantContext(tenantCtx *TenantContext) bool {
	return ValidateTenantContext(tenantCtx) == nil
}
