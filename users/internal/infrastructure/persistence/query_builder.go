package persistence

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gorm.io/gorm"
)

/*
修复记录 - 2025-08-08

问题描述：
- GORM SQL 执行时出现 "LIMIT ?" 参数绑定错误
- 错误信息：Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '? ORDER BY created_at desc LIMIT ?' at line 1
- 问题仅在使用 QueryBuilder 时出现，直接使用 GORM 查询正常

根本原因：
- QueryBuilder 在 buildQuery() 方法中，每个条件都是独立的 Where 调用
- GORM 在处理多个独立 Where 调用时，参数绑定可能出现问题
- 特别是在处理分页参数 (offset, limit) 时，参数绑定失败导致 "?" 未被替换

修复方案：
1. 改进了 buildQuery() 方法中的参数绑定方式
2. 使用更明确的 whereClause 变量，避免字符串拼接中的参数绑定问题
3. 特殊处理 IS NULL 条件，避免参数绑定
4. 简化了 Find() 方法，移除了可能导致问题的 Session 重置

修复效果：
- 解决了 GORM 参数绑定失败的问题
- QueryBuilder 现在可以正常执行分页查询
- 保持了原有的功能和性能
*/

// TenantContext 租户上下文
type TenantContext struct {
	TenantID      int64
	InternalAppID int64
}

// Condition 查询条件
type Condition struct {
	Field    string
	Operator string
	Value    interface{}
	Logic    string // AND, OR
}

// OrderClause 排序子句
type OrderClause struct {
	Field     string
	Direction string // ASC, DESC
}

// QueryBuilder 查询构建器
type QueryBuilder[T any] struct {
	db         *gorm.DB
	ctx        context.Context
	query      *gorm.DB
	model      T
	tenant     *TenantContext
	preloads   []string
	orders     []OrderClause
	offset     int
	limit      int
	conditions []Condition
	groupBy    []string
	having     []Condition
	distinct   bool
	lockMode   string
	skipQuery  bool   // 手动标记，跳过查询直接返回空
	skipReason string // 跳过查询的原因
	logger     logiface.Logger
}

// NewQueryBuilder 创建查询构建器
func NewQueryBuilder[T any](db *gorm.DB, ctx context.Context, model T, logger logiface.Logger) *QueryBuilder[T] {
	return &QueryBuilder[T]{
		db:         db,
		ctx:        ctx,
		model:      model,
		tenant:     &TenantContext{TenantID: 0, InternalAppID: 0}, // 初始化为0，延迟获取
		preloads:   make([]string, 0),
		orders:     make([]OrderClause, 0),
		conditions: make([]Condition, 0),
		groupBy:    make([]string, 0),
		having:     make([]Condition, 0),
		logger:     logger,
	}
}

// WithTenantFilter 添加租户过滤条件
func (qb *QueryBuilder[T]) WithTenantFilter() *QueryBuilder[T] {
	// 延迟获取用户信息
	if qb.tenant.TenantID == 0 {
		tenantID, _ := usercontext.GetTenantID(qb.ctx)
		qb.tenant.TenantID = tenantID
	}

	if qb.tenant.InternalAppID == 0 {
		internalAppID, _ := usercontext.GetInternalAppID(qb.ctx)
		qb.tenant.InternalAppID = internalAppID
	}

	// 检查关键参数有效性
	if qb.tenant.TenantID <= 0 {
		qb.skipQuery = true
		qb.skipReason = "invalid tenant_id"
		return qb
	}

	if qb.tenant.InternalAppID <= 0 {
		qb.skipQuery = true
		qb.skipReason = "invalid internal_app_id"
		return qb
	}

	qb.conditions = append(qb.conditions, []Condition{
		{Field: "tenant_id", Operator: "=", Value: qb.tenant.TenantID, Logic: "AND"},
		{Field: "internal_app_id", Operator: "=", Value: qb.tenant.InternalAppID, Logic: "AND"},
	}...)
	return qb
}

// 手动标记跳过查询
func (qb *QueryBuilder[T]) SkipQuery(reason string) *QueryBuilder[T] {
	qb.skipQuery = true
	qb.skipReason = reason
	return qb
}

// 检查参数有效性并标记
func (qb *QueryBuilder[T]) ValidateAndSkipIfInvalid(condition bool, reason string) *QueryBuilder[T] {
	if condition {
		qb.skipQuery = true
		qb.skipReason = reason
	}
	return qb
}

// 检查用户ID有效性
func (qb *QueryBuilder[T]) ValidateUserID(userID int64) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(userID <= 0, "invalid user_id")
}

// 检查应用ID有效性
func (qb *QueryBuilder[T]) ValidateAppID(appID int64) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(appID <= 0, "invalid app_id")
}

// 检查租户ID有效性
func (qb *QueryBuilder[T]) ValidateTenantID(tenantID int64) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(tenantID <= 0, "invalid tenant_id")
}

// 检查字符串参数有效性
func (qb *QueryBuilder[T]) ValidateStringParam(value string, paramName string) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(value == "", "invalid "+paramName)
}

// 检查数组参数有效性
func (qb *QueryBuilder[T]) ValidateArrayParam(values []interface{}, paramName string) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(len(values) == 0, "invalid "+paramName)
}

// 检查int64数组参数有效性
func (qb *QueryBuilder[T]) ValidateInt64ArrayParam(values []int64, paramName string) *QueryBuilder[T] {
	return qb.ValidateAndSkipIfInvalid(len(values) == 0, "invalid "+paramName)
}

// WithCondition 添加条件
func (qb *QueryBuilder[T]) WithCondition(field, operator string, value interface{}) *QueryBuilder[T] {
	qb.conditions = append(qb.conditions, Condition{
		Field: field, Operator: operator, Value: value, Logic: "AND",
	})
	return qb
}

// WithOrCondition 添加OR条件
func (qb *QueryBuilder[T]) WithOrCondition(field, operator string, value interface{}) *QueryBuilder[T] {
	qb.conditions = append(qb.conditions, Condition{
		Field: field, Operator: operator, Value: value, Logic: "OR",
	})
	return qb
}

// WithInCondition 添加IN条件
func (qb *QueryBuilder[T]) WithInCondition(field string, values []interface{}) *QueryBuilder[T] {
	if len(values) == 0 {
		qb.skipQuery = true
		qb.skipReason = "empty values for IN condition"
		return qb
	}
	qb.conditions = append(qb.conditions, Condition{
		Field: field, Operator: "IN", Value: values, Logic: "AND",
	})
	return qb
}

// WithNotInCondition 添加NOT IN条件
func (qb *QueryBuilder[T]) WithNotInCondition(field string, values []interface{}) *QueryBuilder[T] {
	if len(values) == 0 {
		return qb
	}
	qb.conditions = append(qb.conditions, Condition{
		Field: field, Operator: "NOT IN", Value: values, Logic: "AND",
	})
	return qb
}

// WithInConditionInt64 添加int64数组的IN条件
func (qb *QueryBuilder[T]) WithInConditionInt64(field string, values []int64) *QueryBuilder[T] {
	interfaceValues := make([]interface{}, len(values))
	for i, v := range values {
		interfaceValues[i] = v
	}
	return qb.WithInCondition(field, interfaceValues)
}

// WithNotInConditionInt64 添加int64数组的NOT IN条件
func (qb *QueryBuilder[T]) WithNotInConditionInt64(field string, values []int64) *QueryBuilder[T] {
	interfaceValues := make([]interface{}, len(values))
	for i, v := range values {
		interfaceValues[i] = v
	}
	return qb.WithNotInCondition(field, interfaceValues)
}

// WithInConditionString 添加字符串数组的IN条件
func (qb *QueryBuilder[T]) WithInConditionString(field string, values []string) *QueryBuilder[T] {
	interfaceValues := make([]interface{}, len(values))
	for i, v := range values {
		interfaceValues[i] = v
	}
	return qb.WithInCondition(field, interfaceValues)
}

// WithNotInConditionString 添加字符串数组的NOT IN条件
func (qb *QueryBuilder[T]) WithNotInConditionString(field string, values []string) *QueryBuilder[T] {
	interfaceValues := make([]interface{}, len(values))
	for i, v := range values {
		interfaceValues[i] = v
	}
	return qb.WithNotInCondition(field, interfaceValues)
}

// WithLikeCondition 添加LIKE条件
func (qb *QueryBuilder[T]) WithLikeCondition(field, pattern string) *QueryBuilder[T] {
	qb.conditions = append(qb.conditions, Condition{
		Field: field, Operator: "LIKE", Value: "%" + pattern + "%", Logic: "AND",
	})
	return qb
}

// WithPagination 设置分页
func (qb *QueryBuilder[T]) WithPagination(pageNum, pageSize int) *QueryBuilder[T] {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	qb.offset = (pageNum - 1) * pageSize
	qb.limit = pageSize
	return qb
}

// WithOrder 添加排序
func (qb *QueryBuilder[T]) WithOrder(field, direction string) *QueryBuilder[T] {
	qb.orders = append(qb.orders, OrderClause{
		Field: field, Direction: direction,
	})
	return qb
}

// WithPreloads 添加预加载
func (qb *QueryBuilder[T]) WithPreloads(preloads ...string) *QueryBuilder[T] {
	qb.preloads = append(qb.preloads, preloads...)
	return qb
}

// WithGroupBy 添加分组
func (qb *QueryBuilder[T]) WithGroupBy(fields ...string) *QueryBuilder[T] {
	qb.groupBy = append(qb.groupBy, fields...)
	return qb
}

// WithHaving 添加HAVING条件
func (qb *QueryBuilder[T]) WithHaving(conditions ...Condition) *QueryBuilder[T] {
	qb.having = append(qb.having, conditions...)
	return qb
}

// WithDistinct 设置去重
func (qb *QueryBuilder[T]) WithDistinct() *QueryBuilder[T] {
	qb.distinct = true
	return qb
}

// WithLock 设置锁模式
func (qb *QueryBuilder[T]) WithLock(mode string) *QueryBuilder[T] {
	qb.lockMode = mode
	return qb
}

// buildQuery 构建查询
func (qb *QueryBuilder[T]) buildQuery() *gorm.DB {
	query := qb.db.WithContext(qb.ctx).Model(qb.model)

	// 应用条件 - 使用更安全的参数绑定方式
	if qb.conditions != nil {
		for i, condition := range qb.conditions {
			if condition.Operator == "IN" || condition.Operator == "NOT IN" {
				if i == 0 || condition.Logic == "AND" {
					query = query.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
				} else {
					query = query.Or(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
				}
			} else if condition.Operator == "IS" && condition.Value == nil {
				// 特殊处理 IS NULL
				if i == 0 || condition.Logic == "AND" {
					query = query.Where(fmt.Sprintf("%s IS NULL", condition.Field))
				} else {
					query = query.Or(fmt.Sprintf("%s IS NULL", condition.Field))
				}
			} else {
				// 使用更明确的参数绑定
				whereClause := fmt.Sprintf("%s %s ?", condition.Field, condition.Operator)
				if i == 0 || condition.Logic == "AND" {
					query = query.Where(whereClause, condition.Value)
				} else {
					query = query.Or(whereClause, condition.Value)
				}
			}
		}
	}

	// 应用预加载
	if qb.preloads != nil {
		for _, preload := range qb.preloads {
			query = query.Preload(preload)
		}
	}

	// 应用排序
	if qb.orders != nil {
		for _, order := range qb.orders {
			query = query.Order(fmt.Sprintf("%s %s", order.Field, order.Direction))
		}
	}

	// 应用分组
	if qb.groupBy != nil && len(qb.groupBy) > 0 {
		query = query.Group(strings.Join(qb.groupBy, ", "))
	}

	// 应用 HAVING
	if qb.having != nil {
		for _, having := range qb.having {
			query = query.Having(fmt.Sprintf("%s %s ?", having.Field, having.Operator), having.Value)
		}
	}

	// 应用 DISTINCT
	if qb.distinct {
		query = query.Distinct()
	}

	// 应用锁
	if qb.lockMode != "" {
		query = query.Set("gorm:query_option", qb.lockMode)
	}

	// 添加调试信息
	if qb.logger != nil {
		qb.logger.Debug(qb.ctx, "Built query",
			logiface.Int("conditions_count", len(qb.conditions)),
			logiface.Int("orders_count", len(qb.orders)),
			logiface.Int("preloads_count", len(qb.preloads)))
	}

	return query
}

// Find 查询多条记录
func (qb *QueryBuilder[T]) Find() ([]T, error) {
	// 检查是否需要跳过查询
	if qb.skipQuery {
		if qb.logger != nil {
			qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
		}
		return []T{}, nil
	}

	query := qb.buildQuery()

	// 修复分页条件判断：只要limit > 0就应用分页，offset可以为0（第一页）
	if qb.limit > 0 {
		query = query.Offset(qb.offset).Limit(qb.limit)
	}

	var results []T
	err := query.Find(&results).Error
	return results, err
}

// FindOne 查询单条记录
func (qb *QueryBuilder[T]) FindOne() (*T, error) {
	// 检查是否需要跳过查询
	if qb.skipQuery {
		if qb.logger != nil {
			qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
		}
		return nil, nil
	}

	query := qb.buildQuery()

	var result T
	err := query.First(&result).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &result, err
}

// Count 统计记录数
func (qb *QueryBuilder[T]) Count() (int64, error) {
	// 检查是否需要跳过查询
	if qb.skipQuery {
		if qb.logger != nil {
			qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
		}
		return 0, nil
	}

	query := qb.buildQuery()

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// Exists 检查是否存在
func (qb *QueryBuilder[T]) Exists() (bool, error) {
	// 检查是否需要跳过查询
	if qb.skipQuery {
		if qb.logger != nil {
			qb.logger.Debug(qb.ctx, "Skipping query", logiface.String("reason", qb.skipReason))
		}
		return false, nil
	}

	count, err := qb.Count()
	return count > 0, err
}

// QueryValidator 参数验证工具类
type QueryValidator struct {
	skipQuery  bool
	skipReason string
	logger     logiface.Logger
	ctx        context.Context
}

// NewQueryValidator 创建查询验证器
func NewQueryValidator(ctx context.Context, logger logiface.Logger) *QueryValidator {
	return &QueryValidator{
		ctx:    ctx,
		logger: logger,
	}
}

// ValidateUserID 验证用户ID
func (qv *QueryValidator) ValidateUserID(userID int64) *QueryValidator {
	if userID <= 0 {
		qv.skipQuery = true
		qv.skipReason = "invalid user_id"
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid user ID", logiface.Int64("user_id", userID))
		}
	}
	return qv
}

// ValidateAppID 验证应用ID
func (qv *QueryValidator) ValidateAppID(appID int64) *QueryValidator {
	if appID <= 0 {
		qv.skipQuery = true
		qv.skipReason = "invalid app_id"
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid app ID", logiface.Int64("app_id", appID))
		}
	}
	return qv
}

// ValidateTenantID 验证租户ID
func (qv *QueryValidator) ValidateTenantID(tenantID int64) *QueryValidator {
	if tenantID <= 0 {
		qv.skipQuery = true
		qv.skipReason = "invalid tenant_id"
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid tenant ID", logiface.Int64("tenant_id", tenantID))
		}
	}
	return qv
}

// ValidateStringParam 验证字符串参数
func (qv *QueryValidator) ValidateStringParam(value string, paramName string) *QueryValidator {
	if value == "" {
		qv.skipQuery = true
		qv.skipReason = "invalid " + paramName
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid string parameter", logiface.String("param_name", paramName))
		}
	}
	return qv
}

// ValidateArrayParam 验证数组参数
func (qv *QueryValidator) ValidateArrayParam(values []interface{}, paramName string) *QueryValidator {
	if len(values) == 0 {
		qv.skipQuery = true
		qv.skipReason = "invalid " + paramName
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid array parameter", logiface.String("param_name", paramName))
		}
	}
	return qv
}

// ValidateInt64ArrayParam 验证int64数组参数
func (qv *QueryValidator) ValidateInt64ArrayParam(values []int64, paramName string) *QueryValidator {
	if len(values) == 0 {
		qv.skipQuery = true
		qv.skipReason = "invalid " + paramName
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Invalid int64 array parameter", logiface.String("param_name", paramName))
		}
	}
	return qv
}

// ValidatePermission 验证权限
func (qv *QueryValidator) ValidatePermission(userInfo *usercontext.UserInfo, requiredTenantID int64) *QueryValidator {
	if userInfo == nil {
		qv.skipQuery = true
		qv.skipReason = "user not authenticated"
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "User not authenticated")
		}
		return qv
	}

	if requiredTenantID > 0 && requiredTenantID != userInfo.TenantID {
		qv.skipQuery = true
		qv.skipReason = "tenant_id mismatch"
		if qv.logger != nil {
			qv.logger.Debug(qv.ctx, "Tenant ID mismatch",
				logiface.Int64("required_tenant_id", requiredTenantID),
				logiface.Int64("user_tenant_id", userInfo.TenantID))
		}
	}
	return qv
}

// ShouldSkip 检查是否需要跳过查询
func (qv *QueryValidator) ShouldSkip() bool {
	return qv.skipQuery
}

// GetSkipReason 获取跳过原因
func (qv *QueryValidator) GetSkipReason() string {
	return qv.skipReason
}

// UpdateBuilder 更新构建器
type UpdateBuilder[T any] struct {
	db         *gorm.DB
	ctx        context.Context
	model      T
	tenant     *TenantContext
	conditions []Condition
	updates    map[string]interface{}
	logger     logiface.Logger
}

// NewUpdateBuilder 创建更新构建器
func NewUpdateBuilder[T any](db *gorm.DB, ctx context.Context, model T) *UpdateBuilder[T] {
	return &UpdateBuilder[T]{
		db:         db,
		ctx:        ctx,
		model:      model,
		tenant:     &TenantContext{TenantID: 0, InternalAppID: 0},
		conditions: make([]Condition, 0),
		updates:    make(map[string]interface{}),
	}
}

// WithTenantFilter 添加租户过滤条件
func (ub *UpdateBuilder[T]) WithTenantFilter() *UpdateBuilder[T] {
	// 延迟获取用户信息
	if ub.tenant.TenantID == 0 {
		tenantID, _ := usercontext.GetTenantID(ub.ctx)
		ub.tenant.TenantID = tenantID
	}

	if ub.tenant.InternalAppID == 0 {
		internalAppID, _ := usercontext.GetInternalAppID(ub.ctx)
		ub.tenant.InternalAppID = internalAppID
	}

	ub.conditions = append(ub.conditions, []Condition{
		{Field: "tenant_id", Operator: "=", Value: ub.tenant.TenantID, Logic: "AND"},
		{Field: "internal_app_id", Operator: "=", Value: ub.tenant.InternalAppID, Logic: "AND"},
	}...)
	return ub
}

// Set 设置更新字段
func (ub *UpdateBuilder[T]) Set(field string, value interface{}) *UpdateBuilder[T] {
	ub.updates[field] = value
	return ub
}

// SetMap 批量设置更新字段
func (ub *UpdateBuilder[T]) SetMap(updates map[string]interface{}) *UpdateBuilder[T] {
	for field, value := range updates {
		ub.updates[field] = value
	}
	return ub
}

// Increment 字段自增
func (ub *UpdateBuilder[T]) Increment(field string, value interface{}) *UpdateBuilder[T] {
	ub.updates[field] = gorm.Expr(fmt.Sprintf("%s + ?", field), value)
	return ub
}

// Decrement 字段自减
func (ub *UpdateBuilder[T]) Decrement(field string, value interface{}) *UpdateBuilder[T] {
	ub.updates[field] = gorm.Expr(fmt.Sprintf("%s - ?", field), value)
	return ub
}

// Where 添加条件
func (ub *UpdateBuilder[T]) Where(field, operator string, value interface{}) *UpdateBuilder[T] {
	ub.conditions = append(ub.conditions, Condition{
		Field: field, Operator: operator, Value: value, Logic: "AND",
	})
	return ub
}

// WhereIn 添加IN条件
func (ub *UpdateBuilder[T]) WhereIn(field string, values []interface{}) *UpdateBuilder[T] {
	ub.conditions = append(ub.conditions, Condition{
		Field: field, Operator: "IN", Value: values, Logic: "AND",
	})
	return ub
}

// buildUpdateQuery 构建更新查询
func (ub *UpdateBuilder[T]) buildUpdateQuery() *gorm.DB {
	query := ub.db.WithContext(ub.ctx).Model(ub.model)

	// 应用条件
	for i, condition := range ub.conditions {
		if i == 0 || condition.Logic == "AND" {
			query = query.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
		} else {
			query = query.Or(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
		}
	}

	return query
}

// Update 执行更新操作
func (ub *UpdateBuilder[T]) Update() error {
	if len(ub.updates) == 0 {
		return fmt.Errorf("no updates specified")
	}

	query := ub.buildUpdateQuery()
	return query.Updates(ub.updates).Error
}
