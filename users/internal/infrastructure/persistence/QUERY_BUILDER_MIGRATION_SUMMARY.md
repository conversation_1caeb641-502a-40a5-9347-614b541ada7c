# Query Builder 迁移总结

## 迁移概述

本次迁移将 `users/internal/infrastructure/persistence` 目录下的查询方法从直接使用 GORM 的方式迁移到统一的 Query Builder 模式，提高了代码的一致性和可维护性。

## 已迁移的方法

### RoleRepositoryImpl
- ✅ `GetUsersByRole` - 使用 JOIN 查询获取角色下的用户
- ✅ `GetRolesByUser` - 使用 JOIN 查询获取用户的角色  
- ✅ `GetPermissionsByRole` - 使用 JOIN 查询获取角色的权限
- ✅ `GetPermissionsByRoleIDs` - 使用复杂 JOIN 查询批量获取角色权限
- ✅ `GetRoleStats` - 使用多个统计查询获取角色统计信息

### DepartmentRepositoryImpl
- ✅ `GetUsersByDepartment` - 直接查询用户表获取部门用户
- ✅ `GetUserCountsByDepartmentIDs` - 使用 GROUP BY 查询批量获取部门用户数量

### AuthRepositoryImpl
- ✅ `FindLoginAttemptsByUsername` - 时间范围查询
- ✅ `FindLoginAttemptsByIP` - 时间范围查询
- ✅ `CountFailedAttemptsByUsername` - 统计查询
- ✅ `CountFailedAttemptsByIP` - 统计查询
- ✅ `CleanOldLoginAttempts` - 删除查询
- ✅ `RevokeSessionsByUserIDExcept` - 更新查询
- ✅ `RevokeSessionByJTI` - 更新查询
- ✅ `CleanExpiredSessions` - 更新查询
- ✅ `IsIPBlocked` - 时间比较查询
- ✅ `UnblockIP` - 删除查询
- ✅ `GetSessionStats` - 复杂统计查询
- ✅ `GetLoginAttemptStats` - 复杂统计查询

### ThirdPartyAccountRepositoryImpl
- ✅ `FindByUserID` - 分页查询
- ✅ `CountByUserID` - 统计查询
- ✅ `UpdateLastLogin` - 更新查询
- ✅ `DeactivateByUserIDAndProvider` - 更新查询
- ✅ `ActivateByUserIDAndProvider` - 更新查询

### BaseRepository
- ✅ `GetMaxSort` - 聚合查询

## 迁移模式

### 1. 查询方法迁移
使用 `NewQueryBuilder` 创建查询构建器，通过链式调用添加条件和验证：

```go
qb := NewQueryBuilder[ModelType](db, ctx, model, logger).
    WithTenantFilter().
    ValidateUserID(userID).
    WithCondition("field", "=", value).
    WithOrder("created_at", "DESC")

results, err := qb.Find()
```

### 2. 更新方法迁移
使用 `NewUpdateBuilder` 创建更新构建器：

```go
err := NewUpdateBuilder[ModelType](db, ctx, model).
    Set("field", value).
    WithTenantFilter().
    Where("id", "=", id).
    Update()
```

### 3. 复杂查询迁移
对于需要 JOIN 或复杂聚合的查询，使用 `buildQuery()` 方法获取基础查询，然后添加额外的 SQL 操作：

```go
qb := NewQueryBuilder[ResultType](db, ctx, result, logger)
query := qb.buildQuery().
    Table("table_name").
    Joins("JOIN other_table ON condition").
    Select("columns")
```

## 迁移优势

1. **统一性**: 所有查询都使用相同的构建器模式
2. **可维护性**: 参数验证和租户过滤逻辑统一处理
3. **可读性**: 链式调用使查询逻辑更清晰
4. **可扩展性**: 易于添加新的查询条件和验证规则
5. **错误处理**: 统一的错误处理和日志记录

## 注意事项

1. **参数验证**: 所有查询都包含适当的参数验证
2. **租户隔离**: 自动添加租户和应用ID过滤条件
3. **性能优化**: 保持原有的查询性能，避免 N+1 问题
4. **向后兼容**: 保持原有的接口不变，只修改内部实现

## 待迁移的方法

以下方法由于复杂性或特殊需求，暂时保持原有实现：

- `BlockIP` - 需要直接操作表结构
- 一些复杂的统计查询可能需要进一步优化

## 测试建议

1. 对所有迁移的方法进行单元测试
2. 验证参数验证逻辑的正确性
3. 确认租户隔离功能正常工作
4. 性能测试确保查询效率没有下降
