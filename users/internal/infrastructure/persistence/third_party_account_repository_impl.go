package persistence

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// ThirdPartyAccountRepositoryImpl 第三方账户仓储实现
type ThirdPartyAccountRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewThirdPartyAccountRepositoryImpl 创建第三方账户仓储实现
func NewThirdPartyAccountRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.ThirdPartyAccountRepository {
	return &ThirdPartyAccountRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建第三方账户
func (r *ThirdPartyAccountRepositoryImpl) Create(ctx context.Context, account *entity.ThirdPartyAccount) error {
	r.logger.Info(ctx, "creating third party account",
		logiface.Int64("user_id", account.UserID),
		logiface.String("provider", account.Provider),
		logiface.String("external_user_id", account.ExternalUserID),
	)

	model := model.NewThirdPartyAccountModelFromEntity(account)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		r.logger.Error(ctx, "failed to create third party account",
			logiface.Error(err),
			logiface.Int64("user_id", account.UserID),
			logiface.String("provider", account.Provider),
		)
		return fmt.Errorf("创建第三方账户失败: %w", err)
	}

	// 更新实体ID
	account.ID = model.ID

	r.logger.Info(ctx, "third party account created successfully",
		logiface.Int64("account_id", account.ID),
		logiface.Int64("user_id", account.UserID),
		logiface.String("provider", account.Provider),
	)

	return nil
}

// Update 更新第三方账户
func (r *ThirdPartyAccountRepositoryImpl) Update(ctx context.Context, account *entity.ThirdPartyAccount) error {
	r.logger.Info(ctx, "updating third party account",
		logiface.Int64("account_id", account.ID),
		logiface.Int64("user_id", account.UserID),
		logiface.String("provider", account.Provider),
	)

	model := model.NewThirdPartyAccountModelFromEntity(account)

	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		r.logger.Error(ctx, "failed to update third party account",
			logiface.Error(err),
			logiface.Int64("account_id", account.ID),
		)
		return fmt.Errorf("更新第三方账户失败: %w", err)
	}

	r.logger.Info(ctx, "third party account updated successfully",
		logiface.Int64("account_id", account.ID),
	)

	return nil
}

// Delete 删除第三方账户
func (r *ThirdPartyAccountRepositoryImpl) Delete(ctx context.Context, id int64) error {
	r.logger.Info(ctx, "deleting third party account",
		logiface.Int64("account_id", id),
	)

	if err := r.db.WithContext(ctx).Delete(&model.ThirdPartyAccountModel{}, id).Error; err != nil {
		r.logger.Error(ctx, "failed to delete third party account",
			logiface.Error(err),
			logiface.Int64("account_id", id),
		)
		return fmt.Errorf("删除第三方账户失败: %w", err)
	}

	r.logger.Info(ctx, "third party account deleted successfully",
		logiface.Int64("account_id", id),
	)

	return nil
}

// FindByID 根据ID查找第三方账户
func (r *ThirdPartyAccountRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.ThirdPartyAccount, error) {
	var model model.ThirdPartyAccountModel

	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "failed to find third party account by ID",
			logiface.Error(err),
			logiface.Int64("account_id", id),
		)
		return nil, fmt.Errorf("查找第三方账户失败: %w", err)
	}

	return model.ToEntity(), nil
}

// FindByUserIDAndProvider 根据用户ID和提供商查找第三方账户
func (r *ThirdPartyAccountRepositoryImpl) FindByUserIDAndProvider(ctx context.Context, userID int64, provider string) (*entity.ThirdPartyAccount, error) {
	var model model.ThirdPartyAccountModel

	if err := r.db.WithContext(ctx).Where("user_id = ? AND provider = ?", userID, provider).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "failed to find third party account by user ID and provider",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("provider", provider),
		)
		return nil, fmt.Errorf("查找第三方账户失败: %w", err)
	}

	return model.ToEntity(), nil
}

// FindByProviderAndExternalUserID 根据提供商和外部用户ID查找第三方账户
func (r *ThirdPartyAccountRepositoryImpl) FindByProviderAndExternalUserID(ctx context.Context, provider, externalUserID string) (*entity.ThirdPartyAccount, error) {
	var model model.ThirdPartyAccountModel

	if err := r.db.WithContext(ctx).Where("provider = ? AND external_user_id = ?", provider, externalUserID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "failed to find third party account by provider and external user ID",
			logiface.Error(err),
			logiface.String("provider", provider),
			logiface.String("external_user_id", externalUserID),
		)
		return nil, fmt.Errorf("查找第三方账户失败: %w", err)
	}

	return model.ToEntity(), nil
}

// FindByUserID 根据用户ID查找所有第三方账户
func (r *ThirdPartyAccountRepositoryImpl) FindByUserID(ctx context.Context, userID int64, page, size int) ([]*entity.ThirdPartyAccount, int64, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[model.ThirdPartyAccountModel](r.db, ctx, model.ThirdPartyAccountModel{}, r.logger).
		ValidateUserID(userID).
		WithTenantFilter().
		WithCondition("user_id", "=", userID).
		WithOrder("created_at", "DESC")

	// 计算总数
	total, err := qb.Count()
	if err != nil {
		r.logger.Error(ctx, "failed to count third party accounts",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		return nil, 0, fmt.Errorf("统计第三方账户失败: %w", err)
	}

	// 分页查询
	if page > 0 && size > 0 {
		qb.WithPagination(page, size)
	}

	models, err := qb.Find()
	if err != nil {
		r.logger.Error(ctx, "failed to find third party accounts by user ID",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int("page", page),
			logiface.Int("size", size),
		)
		return nil, 0, fmt.Errorf("查找第三方账户失败: %w", err)
	}

	// 转换为实体
	accounts := make([]*entity.ThirdPartyAccount, len(models))
	for i, model := range models {
		accounts[i] = model.ToEntity()
	}

	return accounts, total, nil
}

// CountByUserID 统计用户的第三方账户数量
func (r *ThirdPartyAccountRepositoryImpl) CountByUserID(ctx context.Context, userID int64) (int64, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[model.ThirdPartyAccountModel](r.db, ctx, model.ThirdPartyAccountModel{}, r.logger).
		ValidateUserID(userID).
		WithTenantFilter().
		WithCondition("user_id", "=", userID)

	count, err := qb.Count()
	if err != nil {
		r.logger.Error(ctx, "failed to count third party accounts by user ID",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		return 0, fmt.Errorf("统计第三方账户失败: %w", err)
	}

	return count, nil
}

// UpdateLastLogin 更新最后登录时间
func (r *ThirdPartyAccountRepositoryImpl) UpdateLastLogin(ctx context.Context, id int64) error {
	r.logger.Info(ctx, "updating third party account last login",
		logiface.Int64("account_id", id),
	)

	// 使用 UpdateBuilder 模式
	err := NewUpdateBuilder[model.ThirdPartyAccountModel](r.db, ctx, model.ThirdPartyAccountModel{}).
		Set("last_login_at", gorm.Expr("NOW()")).
		WithTenantFilter().
		Where("id", "=", id).
		Update()

	if err != nil {
		r.logger.Error(ctx, "failed to update third party account last login",
			logiface.Error(err),
			logiface.Int64("account_id", id),
		)
		return fmt.Errorf("更新最后登录时间失败: %w", err)
	}

	return nil
}

// DeactivateByUserIDAndProvider 停用用户的指定第三方账户
func (r *ThirdPartyAccountRepositoryImpl) DeactivateByUserIDAndProvider(ctx context.Context, userID int64, provider string) error {
	r.logger.Info(ctx, "deactivating third party account",
		logiface.Int64("user_id", userID),
		logiface.String("provider", provider),
	)

	// 使用 UpdateBuilder 模式
	err := NewUpdateBuilder[model.ThirdPartyAccountModel](r.db, ctx, model.ThirdPartyAccountModel{}).
		Set("status", "inactive").
		WithTenantFilter().
		Where("user_id", "=", userID).
		Where("provider", "=", provider).
		Update()

	if err != nil {
		r.logger.Error(ctx, "failed to deactivate third party account",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("provider", provider),
		)
		return fmt.Errorf("停用第三方账户失败: %w", err)
	}

	r.logger.Info(ctx, "third party account deactivated successfully",
		logiface.Int64("user_id", userID),
		logiface.String("provider", provider),
	)

	return nil
}

// ActivateByUserIDAndProvider 激活用户的指定第三方账户
func (r *ThirdPartyAccountRepositoryImpl) ActivateByUserIDAndProvider(ctx context.Context, userID int64, provider string) error {
	r.logger.Info(ctx, "activating third party account",
		logiface.Int64("user_id", userID),
		logiface.String("provider", provider),
	)

	// 使用 UpdateBuilder 模式
	err := NewUpdateBuilder[model.ThirdPartyAccountModel](r.db, ctx, model.ThirdPartyAccountModel{}).
		Set("status", "active").
		WithTenantFilter().
		Where("user_id", "=", userID).
		Where("provider", "=", provider).
		Update()

	if err != nil {
		r.logger.Error(ctx, "failed to activate third party account",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("provider", provider),
		)
		return fmt.Errorf("激活第三方账户失败: %w", err)
	}

	r.logger.Info(ctx, "third party account activated successfully",
		logiface.Int64("user_id", userID),
		logiface.String("provider", provider),
	)

	return nil
}
