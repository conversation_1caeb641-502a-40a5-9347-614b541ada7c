package persistence

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"

	"gorm.io/gorm"
)

// ResourceRelationRepositoryImpl 资源关系仓储实现
type ResourceRelationRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewResourceRelationRepository 创建资源关系仓储
func NewResourceRelationRepository(db *gorm.DB, logger logiface.Logger) repository.ResourceRelationRepository {
	return &ResourceRelationRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建资源关系
func (r *ResourceRelationRepositoryImpl) Create(ctx context.Context, relation *entity.ResourceRelation) error {
	return r.db.WithContext(ctx).Create(relation).Error
}

// Update 更新资源关系
func (r *ResourceRelationRepositoryImpl) Update(ctx context.Context, relation *entity.ResourceRelation) error {
	return r.db.WithContext(ctx).Save(relation).Error
}

// Delete 删除资源关系
func (r *ResourceRelationRepositoryImpl) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&entity.ResourceRelation{}, id).Error
}

// FindByID 根据ID查找资源关系
func (r *ResourceRelationRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.ResourceRelation, error) {
	var relation entity.ResourceRelation
	err := r.db.WithContext(ctx).First(&relation, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &relation, nil
}

// FindBySourceResource 根据源资源查找关系
func (r *ResourceRelationRepositoryImpl) FindBySourceResource(ctx context.Context, sourceResourceID int64) ([]entity.ResourceRelation, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)

	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[entity.ResourceRelation](r.db, ctx, entity.ResourceRelation{}, r.logger).
		WithCondition("source_resource_id", "=", sourceResourceID).
		WithCondition("tenant_id", "=", tenantID)

	// 执行查询
	relations, err := queryBuilder.Find()
	if err != nil {
		return nil, err
	}

	return relations, nil
}

// FindByTargetResource 根据目标资源查找关系
func (r *ResourceRelationRepositoryImpl) FindByTargetResource(ctx context.Context, targetResourceID int64) ([]entity.ResourceRelation, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)

	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[entity.ResourceRelation](r.db, ctx, entity.ResourceRelation{}, r.logger).
		WithCondition("target_resource_id", "=", targetResourceID).
		WithCondition("tenant_id", "=", tenantID)

	// 执行查询
	relations, err := queryBuilder.Find()
	if err != nil {
		return nil, err
	}

	return relations, nil
}

// FindByResources 根据源资源和目标资源查找关系
func (r *ResourceRelationRepositoryImpl) FindByResources(ctx context.Context, sourceResourceID, targetResourceID int64) (*entity.ResourceRelation, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)

	var relation entity.ResourceRelation
	err := r.db.WithContext(ctx).
		Where("source_resource_id = ? AND target_resource_id = ? AND tenant_id = ?", sourceResourceID, targetResourceID, tenantID).
		First(&relation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &relation, nil
}

// BatchCreate 批量创建资源关系
func (r *ResourceRelationRepositoryImpl) BatchCreate(ctx context.Context, relations []entity.ResourceRelation) error {
	if len(relations) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&relations).Error
}

// BatchDelete 批量删除资源关系
func (r *ResourceRelationRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Delete(&entity.ResourceRelation{}, ids).Error
}

// DeleteBySourceResource 根据源资源删除关系
func (r *ResourceRelationRepositoryImpl) DeleteBySourceResource(ctx context.Context, sourceResourceID int64) error {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	return r.db.WithContext(ctx).
		Where("source_resource_id = ? AND tenant_id = ?", sourceResourceID, tenantID).
		Delete(&entity.ResourceRelation{}).Error
}

// DeleteByTargetResource 根据目标资源删除关系
func (r *ResourceRelationRepositoryImpl) DeleteByTargetResource(ctx context.Context, targetResourceID int64) error {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	return r.db.WithContext(ctx).
		Where("target_resource_id = ? AND tenant_id = ?", targetResourceID, tenantID).
		Delete(&entity.ResourceRelation{}).Error
}

// GetRelatedResources 获取关联的资源
func (r *ResourceRelationRepositoryImpl) GetRelatedResources(ctx context.Context, resourceID int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var resources []entity.Resource
	err := r.db.WithContext(ctx).
		Joins("JOIN resource_relations rr ON (resource.id = rr.source_resource_id OR resource.id = rr.target_resource_id)").
		Where("rr.tenant_id = ? AND (rr.source_resource_id = ? OR rr.target_resource_id = ?) AND resource.id != ?",
			tenantID, resourceID, resourceID, resourceID).
		Find(&resources).Error
	return resources, err
}

// GetSourceResources 获取源资源
func (r *ResourceRelationRepositoryImpl) GetSourceResources(ctx context.Context, targetResourceID int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var resources []entity.Resource
	err := r.db.WithContext(ctx).
		Joins("JOIN resource_relations rr ON resource.id = rr.source_resource_id").
		Where("rr.target_resource_id = ? AND rr.tenant_id = ?", targetResourceID, tenantID).
		Find(&resources).Error
	return resources, err
}

// GetTargetResources 获取目标资源
func (r *ResourceRelationRepositoryImpl) GetTargetResources(ctx context.Context, sourceResourceID int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var resources []entity.Resource
	err := r.db.WithContext(ctx).
		Joins("JOIN resource_relations rr ON resource.id = rr.target_resource_id").
		Where("rr.source_resource_id = ? AND rr.tenant_id = ?", sourceResourceID, tenantID).
		Find(&resources).Error
	return resources, err
}

// Exists 检查关系是否存在
func (r *ResourceRelationRepositoryImpl) Exists(ctx context.Context, sourceResourceID, targetResourceID int64) (bool, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceRelation{}).
		Where("source_resource_id = ? AND target_resource_id = ? AND tenant_id = ?", sourceResourceID, targetResourceID, tenantID).
		Count(&count).Error
	return count > 0, err
}

// CountBySourceResource 统计源资源的关系数量
func (r *ResourceRelationRepositoryImpl) CountBySourceResource(ctx context.Context, sourceResourceID int64) (int64, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceRelation{}).
		Where("source_resource_id = ? AND tenant_id = ?", sourceResourceID, tenantID).
		Count(&count).Error
	return count, err
}

// CountByTargetResource 统计目标资源的关系数量
func (r *ResourceRelationRepositoryImpl) CountByTargetResource(ctx context.Context, targetResourceID int64) (int64, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceRelation{}).
		Where("target_resource_id = ? AND tenant_id = ?", targetResourceID, tenantID).
		Count(&count).Error
	return count, err
}
