package storage

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/fileupload/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/fileupload/service/storage"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
)

// OSSStorageService OSS存储服务实现
type OSSStorageService struct {
	endpoint        string
	region          string
	bucket          string
	accessKeyID     string
	accessKeySecret string
	pathPrefix      string
	stsClient       *sts.Client
	logger          logiface.Logger
}

// NewOSSStorageService 创建OSS存储服务
func NewOSSStorageService(endpoint, region, bucket, accessKeyID, accessKeySecret, pathPrefix string, logger logiface.Logger) (storage.StorageService, error) {
	// 创建STS客户端
	stsClient, err := sts.NewClientWithAccessKey(region, accessKeyID, accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建STS客户端失败: %w", err)
	}

	return &OSSStorageService{
		endpoint:        endpoint,
		region:          region,
		bucket:          bucket,
		accessKeyID:     accessKeyID,
		accessKeySecret: accessKeySecret,
		pathPrefix:      pathPrefix,
		stsClient:       stsClient,
		logger:          logger,
	}, nil
}

// UploadFile 上传文件（OSS直传不支持服务端上传，返回错误）
func (s *OSSStorageService) UploadFile(ctx context.Context, file io.Reader, fileName, fileType string, fileSize int64, config *entity.FileUploadConfig) (*storage.UploadResult, error) {
	// OSS直传模式下，文件直接由客户端上传到OSS，服务端不处理文件上传
	return nil, fmt.Errorf("OSS直传模式下不支持服务端上传")
}

// DeleteFile 删除文件
func (s *OSSStorageService) DeleteFile(ctx context.Context, storagePath, storageType string) error {
	// TODO: 实现OSS文件删除
	// 这里需要实现OSS SDK的文件删除功能
	return fmt.Errorf("OSS文件删除功能暂未实现")
}

// GetFileURL 获取文件访问URL
func (s *OSSStorageService) GetFileURL(ctx context.Context, storagePath, storageType string, expireSeconds int) (string, error) {
	// TODO: 实现OSS文件访问URL生成
	// 这里需要实现OSS SDK的签名URL生成功能
	return "", fmt.Errorf("OSS文件访问URL生成功能暂未实现")
}

// GenerateUploadToken 生成上传令牌（STS临时凭证）
func (s *OSSStorageService) GenerateUploadToken(ctx context.Context, config *entity.FileUploadConfig) (*entity.UploadToken, error) {
	// 1. 调用STS服务获取临时凭证
	stsRequest := sts.CreateAssumeRoleRequest()
	stsRequest.Scheme = "https"
	// 使用配置中的角色ARN，如果没有配置则使用默认角色
	roleArn := ""
	if extraConfig := config.GetExtraConfig(); extraConfig != nil {
		if ossRoleArn, ok := extraConfig["oss_role_arn"].(string); ok {
			roleArn = ossRoleArn
		}
	}
	if roleArn == "" {
		roleArn = fmt.Sprintf("acs:ram::%s:role/oss-upload-role", s.accessKeyID)
	}
	stsRequest.RoleArn = roleArn
	stsRequest.RoleSessionName = "oss-upload-session"
	stsRequest.DurationSeconds = requests.NewInteger(3600) // 1小时有效期

	stsResponse, err := s.stsClient.AssumeRole(stsRequest)
	if err != nil {
		s.logger.Error(ctx, "failed to get STS token", logiface.Error(err))
		return nil, fmt.Errorf("获取STS临时凭证失败: %w", err)
	}

	// 2. 创建上传令牌
	token := &entity.UploadToken{
		TenantID:  config.TenantID,
		UserID:    0, // 将在应用层设置
		SceneCode: config.SceneCode,
		Token:     generateToken(),
		ExpireAt:  time.Now().Add(45 * time.Minute), // 45分钟过期
	}

	// 3. 设置OSS配置
	ossConfig := &entity.OSSConfig{
		Endpoint:        s.endpoint,
		Bucket:          s.bucket,
		AccessKeyID:     stsResponse.Credentials.AccessKeyId,
		AccessKeySecret: stsResponse.Credentials.AccessKeySecret,
		SecurityToken:   stsResponse.Credentials.SecurityToken,
		Expiration:      stsResponse.Credentials.Expiration,
		Region:          s.region,
		PathPrefix:      s.pathPrefix,
	}

	token.SetOSSConfig(ossConfig)

	return token, nil
}

// ValidateFile 验证文件
func (s *OSSStorageService) ValidateFile(ctx context.Context, file io.Reader, fileName, fileType string, fileSize int64, config *entity.FileUploadConfig) error {
	// 1. 验证文件大小
	if fileSize > config.MaxFileSize {
		s.logger.Error(ctx, "file size exceeded", logiface.Int64("file_size", fileSize), logiface.Int64("max_size", config.MaxFileSize))
		return fmt.Errorf("文件大小超出限制")
	}

	// 2. 验证文件类型
	if !config.IsFileTypeAllowed(fileType) {
		s.logger.Error(ctx, "file type not allowed", logiface.String("file_type", fileType))
		return fmt.Errorf("文件类型不允许")
	}

	// 3. 验证文件名
	if fileName == "" || len(fileName) > 255 {
		return fmt.Errorf("文件名无效")
	}

	return nil
}

// generateToken 生成随机令牌
func generateToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
