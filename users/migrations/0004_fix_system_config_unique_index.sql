-- 修复 system_config 唯一索引，纳入 internal_app_id
-- 注意：在不同环境中原索引名可能不同，请视情况调整
ALTER TABLE `system_config`
  DROP INDEX `uq_config_tenant_key`,
  ADD UNIQUE INDEX `uq_config_tenant_app_key` (`tenant_id`, `internal_app_id`, `config_key`);

-- 辅助索引（如果尚未存在）
-- MySQL 不支持 IF NOT EXISTS 于 CREATE INDEX，执行前请判断或忽略错误
CREATE INDEX `idx_system_config_internal_app_id` ON `system_config` (`internal_app_id`);
CREATE INDEX `idx_system_config_tenant_internal_app` ON `system_config` (`tenant_id`, `internal_app_id`);


