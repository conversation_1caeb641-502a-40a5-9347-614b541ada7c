-- ==============================================
-- 迁移: 为users模块相关表添加appId字段
-- 版本: 0002
-- 创建时间: 2025-01-27
-- 说明: 为包含tenantId的表添加appId字段，对外使用appId，对内使用internal_app_id
-- 优化：系统内部表全部使用bigint类型的appId字段，提升存储性能
-- ==============================================

-- 1. 为users表添加internal_app_id字段
ALTER TABLE `users` 
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '应用ID，bigint类型提升性能' AFTER `tenant_id`;

-- 为internal_app_id字段创建索引，优化查询性能
CREATE INDEX `idx_users_internal_app_id` ON `users`(`internal_app_id`);
CREATE INDEX `idx_users_tenant_internal_app` ON `users`(`tenant_id`, `internal_app_id`);

-- 2. 为user_ext表添加internal_app_id字段
ALTER TABLE `user_ext` 
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '应用ID，bigint类型提升性能' AFTER `tenant_id`;

-- 为internal_app_id字段创建索引，优化查询性能
CREATE INDEX `idx_user_ext_internal_app_id` ON `user_ext`(`internal_app_id`);
CREATE INDEX `idx_user_ext_tenant_internal_app` ON `user_ext`(`tenant_id`, `internal_app_id`);

-- 3. 为departments表添加internal_app_id字段
ALTER TABLE `departments` 
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '应用ID，bigint类型提升性能' AFTER `tenant_id`;

-- 为internal_app_id字段创建索引，优化查询性能
CREATE INDEX `idx_departments_internal_app_id` ON `departments`(`internal_app_id`);
CREATE INDEX `idx_departments_tenant_internal_app` ON `departments`(`tenant_id`, `internal_app_id`);

-- 4. 为positions表添加internal_app_id字段
ALTER TABLE `positions` 
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '应用ID，bigint类型提升性能' AFTER `tenant_id`;

-- 为internal_app_id字段创建索引，优化查询性能
CREATE INDEX `idx_positions_internal_app_id` ON `positions`(`internal_app_id`);
CREATE INDEX `idx_positions_tenant_internal_app` ON `positions`(`tenant_id`, `internal_app_id`);

-- 5. 为roles表添加appId字段
ALTER TABLE `roles` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_roles_app_id` ON `roles`(`app_id`);
CREATE INDEX `idx_roles_internal_app_id` ON `roles`(`internal_app_id`);
CREATE INDEX `idx_roles_tenant_internal_app` ON `roles`(`tenant_id`, `internal_app_id`);

-- 6. 为permissions表添加appId字段
ALTER TABLE `permissions` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_permissions_app_id` ON `permissions`(`app_id`);
CREATE INDEX `idx_permissions_internal_app_id` ON `permissions`(`internal_app_id`);
CREATE INDEX `idx_permissions_tenant_internal_app` ON `permissions`(`tenant_id`, `internal_app_id`);

-- 7. 为user_roles表添加appId字段
ALTER TABLE `user_roles` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_user_roles_app_id` ON `user_roles`(`app_id`);
CREATE INDEX `idx_user_roles_internal_app_id` ON `user_roles`(`internal_app_id`);
CREATE INDEX `idx_user_roles_tenant_internal_app` ON `user_roles`(`tenant_id`, `internal_app_id`);

-- 8. 为role_permissions表添加appId字段
ALTER TABLE `role_permissions` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_role_permissions_app_id` ON `role_permissions`(`app_id`);
CREATE INDEX `idx_role_permissions_internal_app_id` ON `role_permissions`(`internal_app_id`);
CREATE INDEX `idx_role_permissions_tenant_internal_app` ON `role_permissions`(`tenant_id`, `internal_app_id`);

-- 9. 更新现有数据，设置默认应用ID
-- 为现有数据设置默认的系统应用ID
UPDATE `users` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `user_ext` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `departments` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `positions` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `roles` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `permissions` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `user_roles` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `role_permissions` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;

-- 10. 验证修改结果
SHOW COLUMNS FROM `users` LIKE 'app_id';
SHOW COLUMNS FROM `users` LIKE 'internal_app_id'; 