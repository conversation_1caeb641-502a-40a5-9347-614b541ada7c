-- 应用管理表创建脚本
-- 创建时间: 2025-08-04

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for applications
-- ----------------------------
DROP TABLE IF EXISTS `applications`;
CREATE TABLE `applications` (
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，分布式ID，系统内部使用',
  `app_id` varchar(64) NOT NULL COMMENT '对外公开的应用ID，UUID格式',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `app_name` varchar(100) NOT NULL COMMENT '应用名称',
  `app_code` varchar(50) NOT NULL COMMENT '应用代码，英文标识',
  `description` text COMMENT '应用描述',
  `app_type` varchar(20) NOT NULL DEFAULT 'web' COMMENT '应用类型：web, mobile, api, desktop',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '应用状态：active, inactive, suspended',
  `app_secret` varchar(128) NOT NULL COMMENT '应用密钥，用于API调用认证',
  `callback_urls` json COMMENT '回调URL列表，JSON数组格式',
  `allowed_origins` json COMMENT '允许的源域名列表，JSON数组格式',
  `scopes` json COMMENT '应用权限范围，JSON数组格式',
  `rate_limit` int DEFAULT 1000 COMMENT '频率限制，每分钟请求数',
  `logo_url` varchar(255) COMMENT '应用图标URL',
  `homepage_url` varchar(255) COMMENT '应用主页URL',
  `privacy_policy_url` varchar(255) COMMENT '隐私政策URL',
  `terms_of_service_url` varchar(255) COMMENT '服务条款URL',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统应用',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `config` json COMMENT '应用自定义配置，JSON格式',
  `created_by` bigint NOT NULL COMMENT '创建者用户ID',
  `updated_by` bigint DEFAULT NULL COMMENT '最后更新者用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间，软删除',
  PRIMARY KEY (`internal_app_id`),
  UNIQUE KEY `uk_app_id` (`AppID`),
  UNIQUE KEY `uk_tenant_app_code` (`tenant_id`, `app_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_app_code` (`app_code`),
  KEY `idx_status` (`status`),
  KEY `idx_app_type` (`app_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用管理表';

-- ----------------------------
-- Table structure for application_tokens
-- ----------------------------
DROP TABLE IF EXISTS `application_tokens`;
CREATE TABLE `application_tokens` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID',
  `token_type` varchar(20) NOT NULL COMMENT 'token类型：access_token, api_key, webhook_secret',
  `token_value` varchar(255) NOT NULL COMMENT 'token值',
  `token_name` varchar(100) COMMENT 'token名称，便于管理',
  `permissions` json COMMENT 'token权限范围，JSON数组格式',
  `expires_at` datetime DEFAULT NULL COMMENT 'token过期时间，NULL表示永不过期',
  `last_used_at` datetime DEFAULT NULL COMMENT '最后使用时间',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_by` bigint NOT NULL COMMENT '创建者用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token_value` (`token_value`),
  KEY `idx_internal_app_id` (`internal_app_id`),
  KEY `idx_token_type` (`token_type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_app_tokens_app` FOREIGN KEY (`internal_app_id`) REFERENCES `applications` (`internal_app_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用Token管理表';

-- ----------------------------
-- Table structure for application_usage_logs
-- ----------------------------
DROP TABLE IF EXISTS `application_usage_logs`;
CREATE TABLE `application_usage_logs` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID，API调用时可能为空',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `http_method` varchar(10) NOT NULL COMMENT 'HTTP方法：GET, POST, PUT, DELETE等',
  `request_ip` varchar(45) COMMENT '请求IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `request_size` int DEFAULT 0 COMMENT '请求大小（字节）',
  `response_size` int DEFAULT 0 COMMENT '响应大小（字节）',
  `response_status` int NOT NULL COMMENT 'HTTP响应状态码',
  `response_time` int DEFAULT 0 COMMENT '响应时间（毫秒）',
  `error_message` text COMMENT '错误信息，成功时为空',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  PRIMARY KEY (`id`),
  KEY `idx_internal_app_id` (`internal_app_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_endpoint` (`api_endpoint`),
  KEY `idx_response_status` (`response_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_app_usage_logs_app` FOREIGN KEY (`internal_app_id`) REFERENCES `applications` (`internal_app_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用使用日志表';

-- ----------------------------
-- Insert default system application
-- ----------------------------
INSERT INTO `applications` (
  `internal_app_id`, 
  `app_id`, 
  `tenant_id`, 
  `app_name`, 
  `app_code`, 
  `description`, 
  `app_type`, 
  `status`, 
  `app_secret`, 
  `scopes`, 
  `is_system`, 
  `is_active`, 
  `created_by`
) VALUES (
  1, 
  'sys-default-app-001', 
  1, 
  '系统默认应用', 
  'system_default', 
  '系统内置默认应用，用于基础功能', 
  'system', 
  'active', 
  'sys_secret_' || UNIX_TIMESTAMP() || '_' || FLOOR(RAND() * 10000), 
  JSON_ARRAY('read', 'write', 'admin'), 
  1, 
  1, 
  1
);

SET FOREIGN_KEY_CHECKS = 1;