package id_generator

import (
	"context"
	"testing"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockSimpleIDGenerator 模拟ID生成器服务
type MockSimpleIDGenerator struct {
	mock.Mock
}

func (m *MockSimpleIDGenerator) NextID(ctx context.Context, businessType string, tenantID int64) (int64, error) {
	args := m.Called(ctx, businessType, tenantID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockSimpleIDGenerator) NextIDs(ctx context.Context, businessType string, tenantID int64, count int) ([]int64, error) {
	args := m.Called(ctx, businessType, tenantID, count)
	return args.Get(0).([]int64), args.Error(1)
}

// MockLogger 测试专用的模拟日志器
type MockLogger struct{}

func (l *<PERSON>ckLogger) Debug(ctx context.Context, msg string, fields ...logiface.Field) {}
func (l *MockLogger) Info(ctx context.Context, msg string, fields ...logiface.Field)  {}
func (l *MockLogger) Warn(ctx context.Context, msg string, fields ...logiface.Field)  {}
func (l *MockLogger) Error(ctx context.Context, msg string, fields ...logiface.Field) {}
func (l *MockLogger) Fatal(ctx context.Context, msg string, fields ...logiface.Field) {}
func (l *MockLogger) WithCallerSkip(skip int) logiface.Logger                         { return l }
func (l *MockLogger) WithFields(fields ...logiface.Field) logiface.Logger             { return l }
func (l *MockLogger) With(fields ...logiface.Field) logiface.Logger                   { return l }
func (l *MockLogger) WithContext(ctx context.Context) logiface.Logger                 { return l }
func (l *MockLogger) Sync() error                                                     { return nil }

// 测试初始化
func init() {
	// 为测试环境创建一个模拟日志器
	// 这样可以避免依赖全局日志系统配置
}

// createTestGenerator 创建测试用的ID生成器
func createTestGenerator(mockService *MockSimpleIDGenerator) *IDGenerator {
	generator := &IDGenerator{
		service: mockService,
		config:  DefaultConfig(),
		logger:  &MockLogger{}, // 使用测试专用的模拟日志器
	}

	// 初始化监控指标
	generator.metrics.typeCounts = make(map[IDType]int64)
	generator.metrics.errorCounts = make(map[string]int64)
	generator.metrics.performanceStats = make(map[IDType][]int64)
	generator.metrics.lastResetTime = time.Now()

	return generator
}

// createTestGeneratorWithConfig 使用配置创建测试用的ID生成器
func createTestGeneratorWithConfig(mockService *MockSimpleIDGenerator, config *IDGeneratorConfig) *IDGenerator {
	if config == nil {
		config = DefaultConfig()
	}

	generator := &IDGenerator{
		service: mockService,
		config:  config,
		logger:  &MockLogger{}, // 使用测试专用的模拟日志器
	}

	// 初始化监控指标
	generator.metrics.typeCounts = make(map[IDType]int64)
	generator.metrics.errorCounts = make(map[string]int64)
	generator.metrics.performanceStats = make(map[IDType][]int64)
	generator.metrics.lastResetTime = time.Now()

	return generator
}

func TestNewIDGenerator(t *testing.T) {
	t.Run("should create ID generator with default config", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		assert.NotNil(t, generator)
		assert.Equal(t, mockService, generator.service)
		assert.NotNil(t, generator.config)
		assert.Equal(t, int64(1), generator.config.DefaultTenantID)
		assert.True(t, generator.config.EnableMetrics)
		assert.False(t, generator.config.EnableDetailedLogging)
	})

	t.Run("should panic when service is nil", func(t *testing.T) {
		assert.Panics(t, func() {
			NewIDGenerator(nil)
		})
	})
}

func TestNewIDGeneratorWithConfig(t *testing.T) {
	t.Run("should create ID generator with custom config", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		customConfig := &IDGeneratorConfig{
			DefaultTenantID:       123,
			EnableMetrics:         false,
			EnableDetailedLogging: true,
		}

		generator := createTestGeneratorWithConfig(mockService, customConfig)

		assert.NotNil(t, generator)
		assert.Equal(t, mockService, generator.service)
		assert.Equal(t, customConfig, generator.config)
	})

	t.Run("should use default config when custom config is nil", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGeneratorWithConfig(mockService, nil)

		assert.NotNil(t, generator)
		assert.Equal(t, DefaultConfig(), generator.config)
	})
}

func TestGenerateID(t *testing.T) {
	t.Run("should generate distributed ID with default tenant", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		mockService.On("NextID", mock.Anything, "test_business", int64(1)).Return(expectedID, nil)

		id, err := generator.GenerateID(context.Background(), "test_business")

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})
}

func TestGenerateIDWithType(t *testing.T) {
	t.Run("should generate distributed ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		mockService.On("NextID", mock.Anything, "test_business", int64(0)).Return(expectedID, nil)

		id, err := generator.GenerateIDWithType(context.Background(), DistributedID, "test_business", 0)

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})

	t.Run("should generate snowflake ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		mockService.On("NextID", mock.Anything, "snowflake_test_business", int64(0)).Return(expectedID, nil)

		id, err := generator.GenerateIDWithType(context.Background(), SnowflakeID, "test_business", 0)

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})

	t.Run("should return error for invalid ID type", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		_, err := generator.GenerateIDWithType(context.Background(), "invalid_type", "test_business", 0)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid ID type")
	})

	t.Run("should return error for empty business type", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		_, err := generator.GenerateIDWithType(context.Background(), DistributedID, "", 0)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "business type cannot be empty")
	})

	t.Run("should return error for negative tenant ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		_, err := generator.GenerateIDWithType(context.Background(), DistributedID, "test_business", -1)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "tenant ID cannot be negative")
	})
}

func TestGenerateBatchIDs(t *testing.T) {
	t.Run("should generate batch distributed IDs", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedIDs := []int64{1, 2, 3, 4, 5}
		mockService.On("NextIDs", mock.Anything, "test_business", int64(0), 5).Return(expectedIDs, nil)

		ids, err := generator.GenerateBatchIDs(context.Background(), DistributedID, "test_business", 0, 5)

		assert.NoError(t, err)
		assert.Equal(t, expectedIDs, ids)
		mockService.AssertExpectations(t)
	})

	t.Run("should return error for invalid count", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		_, err := generator.GenerateBatchIDs(context.Background(), DistributedID, "test_business", 0, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid count")

		_, err = generator.GenerateBatchIDs(context.Background(), DistributedID, "test_business", 0, 10001)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid count")
	})

	t.Run("should return error for unsupported ID type", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		_, err := generator.GenerateBatchIDs(context.Background(), UUID, "test_business", 0, 5)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported ID type for batch generation")
	})
}

func TestSpecificIDGeneration(t *testing.T) {
	t.Run("should generate user ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "user", int64(1)).Return(expectedID, nil)

		id, err := generator.GenerateUserID(context.Background())

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})

	t.Run("should generate tenant ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "tenant", int64(1)).Return(expectedID, nil)

		id, err := generator.GenerateTenantID(context.Background())

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})

	t.Run("should generate role ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "role", int64(1)).Return(expectedID, nil)

		id, err := generator.GenerateRoleID(context.Background())

		assert.NoError(t, err)
		assert.Equal(t, expectedID, id)
		mockService.AssertExpectations(t)
	})
}

func TestSessionAndRequestID(t *testing.T) {
	t.Run("should generate session ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		sessionID, err := generator.GenerateSessionID(context.Background())

		assert.NoError(t, err)
		assert.Contains(t, sessionID, "sess_")
	})

	t.Run("should generate request ID", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		requestID, err := generator.GenerateRequestID(context.Background())

		assert.NoError(t, err)
		assert.Contains(t, requestID, "req_")
	})
}

func TestMetrics(t *testing.T) {
	t.Run("should record metrics when enabled", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		// 启用监控
		generator.config.EnableMetrics = true

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "test_business", int64(1)).Return(expectedID, nil)

		_, err := generator.GenerateID(context.Background(), "test_business")
		assert.NoError(t, err)

		metrics := generator.GetMetrics()
		assert.Equal(t, int64(1), metrics["total_generated"])

		typeCounts := metrics["type_counts"].(map[IDType]int64)
		assert.Equal(t, int64(1), typeCounts[DistributedID])
	})

	t.Run("should not record metrics when disabled", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		// 禁用监控
		generator.config.EnableMetrics = false

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "test_business", int64(1)).Return(expectedID, nil)

		_, err := generator.GenerateID(context.Background(), "test_business")
		assert.NoError(t, err)

		metrics := generator.GetMetrics()
		assert.Equal(t, int64(0), metrics["total_generated"])
	})

	t.Run("should reset metrics", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		// 启用监控
		generator.config.EnableMetrics = true

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "test_business", int64(1)).Return(expectedID, nil)

		_, err := generator.GenerateID(context.Background(), "test_business")
		assert.NoError(t, err)

		// 重置指标
		generator.ResetMetrics()

		metrics := generator.GetMetrics()
		assert.Equal(t, int64(0), metrics["total_generated"])
	})
}

func TestConfigManagement(t *testing.T) {
	t.Run("should get config", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		config := generator.GetConfig()
		assert.NotNil(t, config)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		assert.Equal(t, int64(1), config.DefaultTenantID)
	})

	t.Run("should update config", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		newConfig := &IDGeneratorConfig{
			DefaultTenantID: 999,
			EnableMetrics:   false,
		}

		generator.UpdateConfig(newConfig)

		config := generator.GetConfig()
		assert.Equal(t, int64(999), config.DefaultTenantID)
		assert.False(t, config.EnableMetrics)
	})
}

func TestPerformanceMonitoring(t *testing.T) {
	t.Run("should record performance metrics", func(t *testing.T) {
		mockService := &MockSimpleIDGenerator{}
		generator := createTestGenerator(mockService)

		// 启用监控
		generator.config.EnableMetrics = true

		expectedID := int64(12345)
		// 使用DefaultConfig()中的DefaultTenantID，应该是1
		mockService.On("NextID", mock.Anything, "test_business", int64(1)).Return(expectedID, nil)

		start := time.Now()
		_, err := generator.GenerateID(context.Background(), "test_business")
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.Greater(t, duration, time.Duration(0))
	})
}
