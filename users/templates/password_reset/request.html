<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重新申请密码重置</title>
    <link rel="stylesheet" href="/static/css/password-reset.css">
    <style>
      .form-group { margin-bottom: 16px; }
      .hint { color: #666; font-size: 12px; margin-top: 8px; }
      .error { color: #ff4d4f; margin-top: 8px; }
      .success { color: #52c41a; margin-top: 8px; }
    </style>
    <script>
      async function submitRequest(event) {
        event.preventDefault();
        const email = document.getElementById('email').value.trim();
        const msg = document.getElementById('msg');
        msg.textContent = '';
        msg.className = '';
        if (!email) {
          msg.textContent = '请输入邮箱地址';
          msg.className = 'error';
          return;
        }
        try {
          const res = await fetch('/api/user/auth/forgot-password', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email })
          });
          const data = await res.json();
          if (data && typeof data.code !== 'undefined') {
            if (data.code === 0) {
              msg.textContent = '重置邮件已发送，请检查邮箱';
              msg.className = 'success';
            } else if (data.code === 40018) {
              msg.textContent = '发送频率过高，请稍后再试';
              msg.className = 'error';
            } else {
              msg.textContent = data.message || '发送失败，请稍后重试';
              msg.className = 'error';
            }
          } else {
            msg.textContent = '发送失败，请稍后重试';
            msg.className = 'error';
          }
        } catch (e) {
          msg.textContent = '网络错误，请稍后重试';
          msg.className = 'error';
        }
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>重新申请密码重置</h1>
        <p>输入注册邮箱，我们会发送重置链接</p>
      </div>
      <form onsubmit="submitRequest(event)">
        <div class="form-group">
          <label for="email">邮箱地址</label>
          <input type="email" id="email" name="email" required placeholder="<EMAIL>" />
          <div class="hint">请确保邮箱正确且可接收邮件</div>
        </div>
        <div class="form-actions">
          <button type="submit" class="btn">发送重置邮件</button>
          <a href="/" class="btn btn-secondary" style="margin-left: 8px;">返回首页</a>
        </div>
        <div id="msg" class="hint"></div>
      </form>
    </div>
    <script src="/static/js/password-reset.js"></script>
  </body>
  </html>

