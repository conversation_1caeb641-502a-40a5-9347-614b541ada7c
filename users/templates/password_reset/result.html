<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - 密码重置</title>
    <link rel="stylesheet" href="/static/css/password-reset.css">
</head>
<body>
    <div class="container {{.Type}}">
        {{if eq .Type "success"}}
            <div class="result-icon success">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="#52c41a"/>
                </svg>
            </div>
            <h1 class="title">{{.Title}}</h1>
            <p class="message">{{.Message}}</p>
            
            <div class="actions">
                {{if .ShowLoginButton}}
                    <a href="{{.LoginURL}}" class="btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11 7L9.6 8.4L12.2 11H2V13H12.2L9.6 15.6L11 17L16 12L11 7ZM20 19H12V21H20V19Z" fill="currentColor"/>
                        </svg>
                        立即登录
                    </a>
                {{else}}
                    <a href="/login" class="btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11 7L9.6 8.4L12.2 11H2V13H12.2L9.6 15.6L11 17L16 12L11 7ZM20 19H12V21H20V19Z" fill="currentColor"/>
                        </svg>
                        立即登录
                    </a>
                {{end}}
                <a href="/" class="btn btn-secondary">返回首页</a>
            </div>
            
            <div class="info-card">
                <h3>安全提醒</h3>
                <ul>
                    <li>请妥善保管您的新密码</li>
                    <li>建议定期更换密码以提高安全性</li>
                    <li>不要在多个网站使用相同密码</li>
                    <li>如发现异常登录，请及时联系客服</li>
                </ul>
            </div>
        {{else}}
            <div class="result-icon error">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="#ff4d4f"/>
                </svg>
            </div>
            <h1 class="title">{{.Title}}</h1>
            <p class="message">{{.Message}}</p>
            
            <div class="actions">
                {{if .ShowResetRequestButton}}
                    <a href="{{.PasswordResetRequestURL}}" class="btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 21H5V3H13V9H19V21Z" fill="currentColor"/>
                        </svg>
                        重新申请重置
                    </a>
                {{else}}
                    <a href="/password-reset/request" class="btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 21H5V3H13V9H19V21Z" fill="currentColor"/>
                        </svg>
                        重新申请重置
                    </a>
                {{end}}
                {{if .ShowLoginButton}}
                    <a href="{{.LoginURL}}" class="btn btn-secondary">返回登录</a>
                {{else}}
                    <a href="/login" class="btn btn-secondary">返回登录</a>
                {{end}}
            </div>
            
            <div class="info-card">
                <h3>常见问题</h3>
                <ul>
                    <li>重置链接可能已过期，请重新申请</li>
                    <li>请检查邮箱地址是否正确</li>
                    <li>如问题持续存在，请联系客服</li>
                    <li>建议检查垃圾邮件文件夹</li>
                </ul>
            </div>
        {{end}}
    </div>

    <script src="/static/js/password-reset.js"></script>
</body>
</html> 