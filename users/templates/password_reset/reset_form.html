<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置 - {{.TenantName}}</title>
    <link rel="stylesheet" href="/static/css/password-reset.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 21H5V3H13V9H19V21Z" fill="#1890ff"/>
                </svg>
            </div>
            <h1>重置密码</h1>
            <p>请设置您的新密码</p>
        </div>
        <div class="warning">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L1 21H23L12 2ZM12 6L19.5 19H4.5L12 6ZM11 10V14H13V10H11ZM11 16V18H13V16H11Z" fill="#faad14"/>
            </svg>
            <span>此重置链接将在 {{.ExpiresAt}} 后失效，请及时完成密码重置</span>
        </div>

        <form method="POST" action="{{.PasswordResetURL}}" id="resetForm">
            <input type="hidden" name="token" value="{{.Token}}">
            
            <div class="form-group">
                <label for="new_password">新密码</label>
                <div class="input-wrapper">
                    <input type="password" id="new_password" name="new_password" required 
                           placeholder="请输入新密码" minlength="8">
                    <div class="password-strength">
                        <span id="strength-text">密码强度：弱</span>
                        <div class="strength-bar">
                            <div class="strength-fill" id="strength-fill"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="confirm_password">确认密码</label>
                <div class="input-wrapper">
                    <input type="password" id="confirm_password" name="confirm_password" required 
                           placeholder="请再次输入新密码" minlength="8">
                    <div class="password-match" id="password-match">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="#52c41a"/>
                        </svg>
                        <span>密码匹配</span>
                    </div>
                    <div class="password-mismatch" id="password-mismatch">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="#ff4d4f"/>
                        </svg>
                        <span>密码不匹配</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn" id="submitBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 21H5V3H13V9H19V21Z" fill="currentColor"/>
                    </svg>
                    重置密码
                </button>
            </div>
        </form>
    </div>

    <script src="/static/js/password-reset.js"></script>
</body>
</html> 