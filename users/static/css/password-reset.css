* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: #fafafa;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #333;
    line-height: 1.6;
}

.container {
    background: white;
    border-radius: 8px;
    padding: 40px;
    width: 100%;
    max-width: 480px;
}

.header {
    text-align: center;
    margin-bottom: 32px;
}

.logo {
    margin-bottom: 16px;
}

.header h1 {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.header p {
    color: #8c8c8c;
    font-size: 14px;
}

.info-card {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 24px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    color: #8c8c8c;
    font-size: 14px;
}

.info-item .value {
    color: #262626;
    font-size: 14px;
    font-weight: 500;
}

.warning {
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #d46b08;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #262626;
    font-weight: 500;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.form-group input:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-group input::placeholder {
    color: #bfbfbf;
}

.password-strength {
    margin-top: 8px;
    font-size: 12px;
    color: #8c8c8c;
}

.strength-bar {
    height: 3px;
    background: #f0f0f0;
    border-radius: 2px;
    margin-top: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.password-match,
.password-mismatch {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.password-match {
    color: #52c41a;
}

.password-mismatch {
    color: #ff4d4f;
}

.password-match.show,
.password-mismatch.show {
    opacity: 1;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    justify-content: center;
}

.btn:hover {
    background: #40a9ff;
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #f5f5f5;
    color: #595959;
    border: 1px solid #d9d9d9;
}

.btn-secondary:hover {
    background: #e6f7ff;
    color: #1890ff;
    border-color: #1890ff;
}

.actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.actions .btn {
    flex: 1;
}

/* 表单提交按钮区域，居中显示 */
.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}

/* 结果页面样式 */
.result-icon {
    text-align: center;
    margin-bottom: 24px;
}

.result-icon.success svg {
    color: #52c41a;
}

.result-icon.error svg {
    color: #ff4d4f;
}

.title {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 16px;
}

.message {
    color: #8c8c8c;
    font-size: 16px;
    text-align: center;
    margin-bottom: 32px;
}

.info-card h3 {
    color: #262626;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.info-card ul {
    color: #595959;
    font-size: 14px;
    line-height: 1.6;
    padding-left: 20px;
}

.info-card li {
    margin-bottom: 8px;
}

.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 24px;
        margin: 16px;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .actions .btn {
        width: 100%;
    }
    
    .header h1 {
        font-size: 20px;
    }
    
    .title {
        font-size: 20px;
    }
} 