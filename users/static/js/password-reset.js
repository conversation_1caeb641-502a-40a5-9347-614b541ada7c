// 密码强度检查
function checkPasswordStrength(password) {
    let score = 0;
    let feedback = [];

    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    const strength = ['弱', '一般', '中等', '强', '很强'];
    const colors = ['#ff4d4f', '#faad14', '#1890ff', '#52c41a', '#13c2c2'];
    const widths = ['20%', '40%', '60%', '80%', '100%'];

    return {
        score: score - 1,
        text: strength[score - 1] || '弱',
        color: colors[score - 1] || '#ff4d4f',
        width: widths[score - 1] || '20%'
    };
}

// 密码匹配检查
function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const passwordMatch = document.getElementById('password-match');
    const passwordMismatch = document.getElementById('password-mismatch');

    if (!newPassword || !confirmPassword || !passwordMatch || !passwordMismatch) {
        return;
    }

    const newPwd = newPassword.value;
    const confirmPwd = confirmPassword.value;

    if (confirmPwd === '') {
        passwordMatch.classList.remove('show');
        passwordMismatch.classList.remove('show');
        return;
    }

    if (newPwd === confirmPwd) {
        passwordMatch.classList.add('show');
        passwordMismatch.classList.remove('show');
    } else {
        passwordMatch.classList.remove('show');
        passwordMismatch.classList.add('show');
    }
}

// 表单验证
function validateForm() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submitBtn');

    if (!newPassword || !confirmPassword || !submitBtn) {
        return false;
    }

    const newPwd = newPassword.value;
    const confirmPwd = confirmPassword.value;
    const strength = checkPasswordStrength(newPwd);

    const isValid = newPwd.length >= 8 && 
                  newPwd === confirmPwd && 
                  strength.score >= 2;

    submitBtn.disabled = !isValid;
    return isValid;
}

// 初始化密码重置表单
function initPasswordResetForm() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const strengthText = document.getElementById('strength-text');
    const strengthFill = document.getElementById('strength-fill');
    const resetForm = document.getElementById('resetForm');

    if (!newPassword || !confirmPassword) {
        return;
    }

    // 密码强度检查事件
    newPassword.addEventListener('input', function() {
        const strength = checkPasswordStrength(this.value);
        if (strengthText) {
            strengthText.textContent = `密码强度：${strength.text}`;
        }
        if (strengthFill) {
            strengthFill.style.width = strength.width;
            strengthFill.style.backgroundColor = strength.color;
        }
        checkPasswordMatch();
        validateForm();
    });

    // 确认密码检查事件
    confirmPassword.addEventListener('input', function() {
        checkPasswordMatch();
        validateForm();
    });

    // 表单提交验证
    if (resetForm) {
        resetForm.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                alert('请检查密码输入是否正确');
            }
        });
    }
}

// 初始化结果页面
function initResultPage() {
    // 成功页面5秒后自动跳转到登录页
    const container = document.querySelector('.container.success');
    if (container) {
        setTimeout(function() {
            window.location.href = '/login';
        }, 5000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPasswordResetForm();
    initResultPage();
}); 