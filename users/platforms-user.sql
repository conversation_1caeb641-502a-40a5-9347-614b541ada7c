/*
 Navicat Premium Data Transfer

 Source Server         : nas-mysql-remote
 Source Server Type    : MySQL
 Source Server Version : 90100 (9.1.0)
 Source Host           : **************:3308
 Source Schema         : platforms-user

 Target Server Type    : MySQL
 Target Server Version : 90100 (9.1.0)
 File Encoding         : 65001

 Date: 10/08/2025 23:59:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for applications
-- ----------------------------
DROP TABLE IF EXISTS `applications`;
CREATE TABLE `applications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用编码，全局唯一',
  `internal_app_id` bigint NOT NULL COMMENT '对内使用的appID',
  `app_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `app_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用类型：admin-管理后台，mobile-移动端，web-Web端，api-API服务，miniprogram-小程序',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用描述',
  `public_config` json DEFAULT NULL COMMENT '公开配置信息，JSON格式',
  `encrypted_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '加密后的应用密钥',
  `encrypted_config` json DEFAULT NULL COMMENT '加密后的私密配置，JSON格式',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '应用状态：active-活跃，disabled-禁用，expired-过期',
  `max_users` int DEFAULT '0' COMMENT '最大用户数，0表示不限制',
  `max_requests_per_minute` int DEFAULT '1000' COMMENT '每分钟最大请求数',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统应用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间',
  `created_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `updated_by` bigint DEFAULT NULL COMMENT '更新者ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_app_id` (`app_id`) USING BTREE,
  UNIQUE KEY `uk_internal_app_id` (`internal_app_id`) USING BTREE,
  UNIQUE KEY `idx_tenant_id` (`tenant_id`,`app_name`) USING BTREE,
  KEY `idx_app_type` (`app_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=10002 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用表';

-- ----------------------------
-- Records of applications
-- ----------------------------
BEGIN;
INSERT INTO `applications` (`id`, `tenant_id`, `app_id`, `internal_app_id`, `app_name`, `app_type`, `description`, `public_config`, `encrypted_secret`, `encrypted_config`, `status`, `max_users`, `max_requests_per_minute`, `is_system`, `sort_order`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`) VALUES (1, 1, 'k3J8v1Q9s2L5n8W0p4X7aA', 1, 'system', 'admin', NULL, NULL, NULL, NULL, 'active', 0, 1000, 0, 0, '2025-08-07 01:37:00', '2025-08-07 01:37:00', NULL, NULL, NULL);
INSERT INTO `applications` (`id`, `tenant_id`, `app_id`, `internal_app_id`, `app_name`, `app_type`, `description`, `public_config`, `encrypted_secret`, `encrypted_config`, `status`, `max_users`, `max_requests_per_minute`, `is_system`, `sort_order`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`) VALUES (10001, 1, 'q6N-TLt5Q1UtGnq5B5dXkA', 10001, 'ilike', 'mobile', 'ilike app', '{}', '', NULL, 'active', 0, 1000, 0, 0, '2025-08-07 01:39:19', '2025-08-07 09:52:36', NULL, NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for audit_log
-- ----------------------------
DROP TABLE IF EXISTS `audit_log`;
CREATE TABLE `audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：login, logout, create, update, delete等',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型：user, role, permission等',
  `resource_id` bigint DEFAULT NULL COMMENT '资源ID',
  `details` json DEFAULT NULL COMMENT '操作详情（JSON格式）',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_audit_tenant_id` (`tenant_id`),
  KEY `idx_audit_user_id` (`user_id`),
  KEY `idx_audit_created_at` (`created_at`),
  KEY `idx_audit_resource` (`resource_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- ----------------------------
-- Records of audit_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_sessions
-- ----------------------------
DROP TABLE IF EXISTS `auth_sessions`;
CREATE TABLE `auth_sessions` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，UUID格式',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `jti` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'JWT ID，用于令牌撤销和验证',
  `access_token_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问令牌哈希，用于快速验证',
  `refresh_token_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '刷新令牌哈希，用于快速验证',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '会话状态：active-活跃，expired-过期，revoked-撤销，logged_out-登出',
  `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型：desktop-桌面，mobile-移动，tablet-平板',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `os_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统版本',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `browser_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器版本',
  `device_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备型号',
  `screen_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '屏幕尺寸',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时区',
  `fingerprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备指纹',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `last_used_at` datetime NOT NULL COMMENT '最后使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_auth_sessions_jti` (`jti`),
  KEY `idx_auth_sessions_user_id` (`user_id`),
  KEY `idx_auth_sessions_tenant_id` (`tenant_id`),
  KEY `idx_auth_sessions_status` (`status`),
  KEY `idx_auth_sessions_expires_at` (`expires_at`),
  KEY `idx_auth_sessions_jti` (`jti`),
  KEY `idx_auth_sessions_access_token_hash` (`access_token_hash`),
  KEY `idx_auth_sessions_refresh_token_hash` (`refresh_token_hash`),
  KEY `idx_auth_sessions_internal_app_id` (`internal_app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='认证会话表（JTI + 令牌哈希方案）';

-- ----------------------------
-- Records of auth_sessions
-- ----------------------------
BEGIN;
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('03c381e6-1f10-4edd-911a-05533cd13794', 1001, 1, 1, '7c5a0ad9-3111-4d47-924e-c462570bfd7e', 'f3d9dd94bbcdc50f965866c706a13ad9769a73496a787546e302e4b035a3fd1a', '9b183695d5bc567b6422e16afefb9d8d0ecbc3d2ea023ee3cc98cf231a2ae2b2', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:55:07', '2025-08-07 17:55:07', '2025-08-07 17:55:07', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('04c02ba1-7861-4a08-b128-ccc2dd0bc6ee', 2001, 2, 10001, '94f47c0e-5008-4c8a-aca6-1b4a42fab4a2', 'a16ebbe31bd245e55bf8af44590ea3d5cf808a59aeaa1a5e88e72639fcc10598', '14af3907356729161fc802fcd8ce0d00bbe74ba369eeaf20e521f806e954396f', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-10 13:23:19', '2025-08-09 13:23:19', '2025-08-09 13:23:19', '2025-08-09 13:23:19');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('077cda42-1fcf-4fe1-a5b4-213f7dfd583a', 2001, 2, 0, '4106c974-a932-43a2-b2b2-f652d4a3400b', 'f2a5510ddcc78ac9f3506aa216168822dae5c6264fe00a4e902e32e8b2d2c481', 'c20ac9876d3adbccbeaa9ff4aaef083f26a34cff860c59742d4841432affe4da', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-03 07:05:36', '2025-08-02 07:05:36', '2025-08-02 07:05:36', '2025-08-02 07:05:36');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('0e808e28-7c51-45bd-b319-fe34201eb98e', 1001, 1, 1, '09d3ee21-15c8-4c57-8b54-15598731361d', '129234b2730e8e59e7b7e33587015325ab7289f730022f2e7b1148244855289d', '54fe52811cccfa5714c2be9e2447f1309a6635fbf873b56c6cde6d655627e3e0', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:37:59', '2025-08-07 17:37:59', '2025-08-07 17:37:59', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('118b81b8-3631-4ada-b2d5-2df7ce81cf6f', 1001, 1, 1, '3b325d44-79ac-41d4-8a69-8e95a11bd665', '40e3f03219af9a82acdb954718e22dbc5b2c401db7ab2ffa4de81316b8c36337', 'e349a5e78e5ea5a1762b4d723665e96bf5c99adafdc14c33a7b70a32072c8970', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:34:30', '2025-08-07 17:34:30', '2025-08-07 17:34:30', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('19b2a893-176f-45d3-8bd7-de0a545801be', 1001, 1, 1, 'ad26e453-3ac0-449f-a20d-4ed7c3423d98', 'd43029d0ef0e7c8e3ae69c451d0cd2c73b0e815e346371e3725f5a388c213c64', 'ba0a8cb112e9b07b8fc4dfdd216d7f5d87affad3c372ecf1ed62d7b3e86b0d64', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 13:45:54', '2025-08-07 13:45:54', '2025-08-07 13:45:54', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('26dd192f-e227-4cde-9289-0cae68c92390', 1001, 1, 0, 'f8788413-95c1-4140-8e65-b2ae8255d26e', 'edf5429da1d7a05d7b45cec2b7b814d50029f0337801d09098625fa96a349e38', '09e90f9757d14eaf7fae15059f6e5b629b306b9865bf3938d7cf0e32228c630b', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '137.0', '', '', '', '', '20', '2025-08-03 05:10:34', '2025-08-02 05:10:34', '2025-08-02 05:10:34', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('2b6e764a-8828-4387-ad45-491d6b77de9f', 2001, 2, 0, '174d437b-faf7-410c-b726-e51a89b6b90c', '8c9e21a08b05c04a98e3f0c7cbe213b67cbe65b05a6bd7dd038aee4eb7e48e84', '583b4710e4ed4a91353af4f9367e393f6d744195e793dcad84b26eae2fea2c3b', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-03 12:06:46', '2025-08-02 12:06:46', '2025-08-02 12:06:46', '2025-08-02 12:06:46');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('3df7949e-db24-405a-9371-682e5ed17b06', 2001, 2, 0, 'cab7c024-6c5b-4609-80c3-cdfd6bb6005b', '8c63d8e36646280938e3ee53c3228423b9ffb76345dfe34a11414ae689f38b41', '14774150602c0a4dffa359a14c3252af3c6675230b103482b6b9d2492ce9e8c5', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-02 15:57:34', '2025-08-01 15:57:34', '2025-08-01 15:57:34', '2025-08-01 15:57:34');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('465149d6-a0ab-4a13-a3d4-6a3dcc0c7955', 1001, 1, 0, '8d986b20-ef3e-46e7-8729-f3e744098fea', 'f91d78ae80857b4ad8acf94c3c3c03aed5ef1ff920d2d21b8ec427b75418d0f4', '0d4505a944f62025591242bb4ad2f240f67cd385e28b8b25a747b2ee040d687a', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '137.0', '', '', '', '', '20', '2025-08-02 15:06:44', '2025-08-01 15:06:44', '2025-08-01 15:06:44', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('49cadc6d-c618-4326-897a-9176ed97127d', 1001, 1, 1, 'ac5955fd-0815-4cc8-a0b3-6b7850c7aa81', '911401e15dc04387003b7b776e074ff3fc18290052ec3f2a7ef6dae0d07a605a', 'b368badb2bceabb66175152d411dab875ac31606c75dae5a3a63582e70385796', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-07 02:39:27', '2025-08-06 02:39:27', '2025-08-06 02:39:27', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('4ffb57f6-2f73-4694-8249-3bac5b25b86a', 1001, 1, 0, '72f44d94-07f0-4d18-9696-078286c3f499', '57c97ac5b9a9e7fd63451bff220579e48b9352bdfd654a57c0a4e7520f006268', 'c0daadcb6869e0d07f39b8e2147ca3d25c08cb2b39ad4a88e565a1fd88674b3a', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-01 16:26:42', '2025-07-31 16:26:42', '2025-07-31 16:26:42', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('51524407-9364-4592-a198-377acc19de87', 2001, 2, 0, '414a4848-2f23-4e31-b77e-0448a90effa0', '3167770a8a7181e7d05f57cdb9a7353c8eebba4409ac0704b62e1ee943c19f6b', '08afdec9c0280ee24b8a4cea5e4ecb2c48374e580c5e464efea9557add20bc66', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-03 11:55:04', '2025-08-02 11:55:04', '2025-08-02 11:55:04', '2025-08-02 11:55:04');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('55f602e2-bedd-4c34-abb9-4337a8ea6a24', 2001, 2, 0, 'ccfdb222-68df-418e-990e-3617dcf90760', 'bc1d3c3b1b4756bf542e7002e1216cff02d6c4847c9d721b1e79d972d10eeb56', '32b7f439a4014e581c5e31ff9ee2ae87710154e6f6bd91cc63c0aaa4cbcf4dde', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-05 07:45:48', '2025-08-04 07:45:48', '2025-08-04 07:45:48', '2025-08-04 07:45:48');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('6601786e-f3f7-4954-839e-2c66add88acf', 1001, 1, 1, '422d9a8e-ca84-419e-897a-8c765f45ca16', 'bde76068907b86c0e4bc28b567675ac01459d9ece4a6764acc8a30bcf528563f', '814963fbd750f4e336fd708e66315f25a204be4e7d574634869193caed925723', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-07 03:05:19', '2025-08-06 03:05:19', '2025-08-06 03:05:19', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('6650ac1f-85c9-48c0-9569-36752b8aaf52', 1001, 1, 1, '520a16b9-7248-4bf4-bcfe-8e01170c61f4', 'c08f4896c80c7099fab716235976c305bc7c3a9eb481478b01cbe1201b5e7fff', '515bb1d1b1f706ef57a90852137e1a825bcd55886df8264e11b580409b88182a', 'active', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-11 14:23:57', '2025-08-10 14:23:57', '2025-08-10 14:23:59', '2025-08-10 14:23:59');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('686a3127-2b27-42f4-8d33-3ef646d609d8', 1001, 1, 0, '38023da7-e829-4471-9f77-72261783315c', 'c61dd54251dc83579f8635847c05ad5e006b94decb6e47e64b761ff3da01939e', '7e627116757b7eaebd331105f2625afa15c2cc7c86f08f7d0e6b1ad2faa3777f', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-07-29 14:43:09', '2025-07-28 14:43:09', '2025-07-28 14:43:09', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('6d11cf30-4f23-44d0-b398-934c0fb00c58', 1001, 1, 0, 'a060eb12-31df-4772-9067-fcb3172836d9', 'a6f3ab80969a8c4dac2960ff340c19886cebbbca28f01a414877cc7935e224e1', '48d13fa07ec80666b8ace137696dd4e8dcfb45bd1edded748afa0f1fc30389d2', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-07-30 01:02:33', '2025-07-29 01:02:33', '2025-07-29 01:02:33', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('8cdfda1b-bb98-49c2-aa44-32f0bd1fdad5', 2001, 2, 0, 'aca3e1e7-addb-43fb-b9b5-5da6bb3a5192', '6e001f7570f6696c003e09f61b4eafc1ad6639f467c10f990e5ce5ebbaf06951', 'f456fdd680739c82bd147922eb8bf6c4092cf064d51f2957d203867f74bc6ef5', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-03 07:03:39', '2025-08-02 07:03:39', '2025-08-02 07:03:39', '2025-08-02 07:03:39');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('a130076a-2412-420e-ac65-0b67ab6d7232', 2001, 2, 0, 'e16c08ab-734b-4846-8037-a2c3f8ea959f', 'fc3a6cbadc93f97f846b2636198c4bdece2582626d5194521dd347b6e19fed94', 'd982863a3443ba3fd0948a085638889e367a90847eb480946e07b03b763ff19b', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-05 11:46:59', '2025-08-04 11:46:59', '2025-08-04 11:47:00', '2025-08-04 11:47:00');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('c3353940-584e-479e-9d1d-9042876949e5', 2001, 2, 0, '9a070141-79c0-4c5f-8b1a-a422effaec2f', '829ea7cad3c5623f17a0b0d8a7d1b36193589368e57a63d6fb64e0be817d6881', 'b861f400227a401514b4a3319eb3cf70eaa4df202a9aa96f4148a8bba0a1b2ed', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-05 12:26:02', '2025-08-04 12:26:02', '2025-08-04 12:26:02', '2025-08-04 12:26:02');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('d7bb1839-f03e-432a-a669-27862c6b3a9e', 1001, 1, 1, '734e36c9-d02b-4314-8373-edce88aba004', 'b4cf8f99db4dbf100edeb7fe43b0bad37a42152bc2e2eacd0646de72ca7ef86a', '54327f89071648205b45f3e2c22163c87721e8e6f13c0335bb37cddfc3318826', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:34:13', '2025-08-07 17:34:13', '2025-08-07 17:34:13', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('d9aaa9a7-81f2-423b-80aa-165e2c21ab9e', 2001, 2, 0, 'ae108e8f-62b4-4377-84e0-077cb5750b79', '8aea03f625a7a8b5e16663ecbad21d6d65a041340a0073a2ae64b23b4121c086', '3b976b1592e147dc85200098c88a280cbfd4b967a835163c4a5d4465da732475', 'active', 'desktop', '', '', '', '', '', '', '', '', '7', '2025-08-03 12:34:15', '2025-08-02 12:34:15', '2025-08-02 12:34:15', '2025-08-02 12:34:15');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('de004984-b6f0-46c3-91c2-7aa5e83ed3cb', 1001, 1, 0, '440dd3e7-c362-409e-b248-256054a90934', '3c04840d3e938812a8366c00fea6ce35ad802c8b25e7b9d2253ed0eb9ce94284', '1969ad588d68f14d95def731a116d1340f06a76d38e9d87bd178e3e9b7dda9c7', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-07-30 12:21:23', '2025-07-29 12:21:23', '2025-07-29 12:21:23', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('e011bc59-8179-4324-a6ea-8b7e76bfc2b8', 1001, 1, 0, '7590ad1c-f578-4557-bd9a-1674502efce2', '3ec69be964f8b96086c9bb02b530761edbdc29f883b74fdee1de47461939484a', '2eff04e9988269b7b1ec54018b8a292b0d83b4197789dbe9a553795e96c2c368', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '137.0', '', '', '', '', '20', '2025-08-03 05:05:40', '2025-08-02 05:05:40', '2025-08-02 05:05:40', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('e33c1c6f-0057-4699-b7af-bd5b643f2a3c', 1001, 1, 1, '5f7e3397-35b5-4b0b-92e5-01de5f22a87f', '3456fa6ab16f1dbac06e0af4e94f182b47edce2ec6b46e6139d1de674c38e382', '8b4668a7f6cad6b7c6ec002228f4bec240888515b82e9e0dea7a998fa0e6a776', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:34:15', '2025-08-07 17:34:15', '2025-08-07 17:34:15', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('e4a870a3-3be5-4e0a-b618-a53d166964c9', 1001, 1, 1, '1973e01a-f3b9-4308-958c-1713a922c508', '873537fe9ee365b5c582b6556bc44268cad1857171028380c1d6450dba8b7b18', '84da837c25b40645403fbce5b8041aed27f421ca42a96f65f6bc9008afc33818', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 09:45:15', '2025-08-07 09:45:15', '2025-08-07 09:45:15', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('e8516dab-940c-4202-83bb-ddc588d074c5', 1001, 1, 1, '2d6eb027-c592-4afa-a528-9aab8cfd3cdb', '80553f2b0f02d42ed169a72ee5ba5dc2a7f272b5b4f803b3fad106385a9cd202', 'c824f3e3de56d4627bf522a7c5a95a34d18e5f8dc65953013c50ac561411ad93', 'active', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-09 14:19:33', '2025-08-08 14:19:33', '2025-08-08 14:19:33', '2025-08-08 14:19:33');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('ebd411eb-286d-47fd-94ff-4f540bf078c3', 1001, 1, 0, '9a8110ca-531b-4828-9c03-2b3a9c11466e', '6a5aa4ff2033059fd6c2ec3638e9d821411a1bcb57a3f9b5b521bea8dda9adb8', '75219071518132813d7c3296e9172f26e7b956cb1b5fe41f65b4b6c4cac7515f', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '137.0', '', '', '', '', '20', '2025-08-03 05:06:02', '2025-08-02 05:06:02', '2025-08-02 05:06:02', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('f0649854-53d5-4fee-8e7f-b1dcf78e2282', 1001, 1, 1, 'b29d8816-13b2-4030-8aa0-b85a516848c2', '450dff99fd52eb2491535da64a3992e687f4ad095ce545af1e26b352af83cd2a', '0fe07ef51691af9c3b8f0cff1526d78cd9c16b702f523139127a82e5940e509e', 'active', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-10 15:04:43', '2025-08-09 15:04:43', '2025-08-09 15:04:43', '2025-08-09 15:04:43');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('f1e92e3f-fa49-435b-bf08-02c3a7235c48', 1001, 1, 1, '36f02e2a-a9cb-4f0f-97bf-7d53447c4220', '872aa1a538132f1bf162024f7a4a1255c17d9a8d912bbf32bd5bd7eec5faf755', 'e87ff34d4e152bcedd703bb74c567867eb813e7bdee5bb612a20c8996a4f6453', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 13:25:25', '2025-08-07 13:25:25', '2025-08-07 13:25:25', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('f46025cb-bfae-49ce-9b3b-52f4ac5275e0', 1001, 1, 1, 'dc582868-bdff-4005-8ef2-4b1faa513088', '67e021177173fea3e505dc9540537b2403853de945ef0e93449e76d36604ce38', 'bd15d870f41c470946b13fa68319426eac27687810fbedd25bc16a3b6569b7ea', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:48:10', '2025-08-07 17:48:10', '2025-08-07 17:48:10', '2025-08-08 14:19:25');
INSERT INTO `auth_sessions` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `jti`, `access_token_hash`, `refresh_token_hash`, `status`, `device_type`, `os`, `os_version`, `browser`, `browser_version`, `device_model`, `screen_size`, `language`, `timezone`, `fingerprint`, `expires_at`, `last_used_at`, `created_at`, `updated_at`) VALUES ('fb35e6d7-ab34-4e72-ba0d-be9ac06647e2', 1001, 1, 1, '35185f92-b67c-48ba-9380-10fc72ad3957', 'e933985b5038bcee8a77d5108fef15d087943bee3358db04efd1429d80784323', '554165e88b50c96b8d3848dbd7ae366500ca0dd0283d7211c2d24d820242501a', 'revoked', 'desktop', 'macOS', '10.15', 'Chrome', '138.0', '', '', '', '', '20', '2025-08-08 17:45:22', '2025-08-07 17:45:22', '2025-08-07 17:45:22', '2025-08-08 14:19:25');
COMMIT;

-- ----------------------------
-- Table structure for blocked_ips
-- ----------------------------
DROP TABLE IF EXISTS `blocked_ips`;
CREATE TABLE `blocked_ips` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '阻止原因',
  `blocked_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阻止时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_blocked_ips_tenant_id` (`tenant_id`),
  KEY `idx_blocked_ips_ip_address` (`ip_address`),
  KEY `idx_blocked_ips_expires_at` (`expires_at`),
  KEY `idx_blocked_ips_tenant_ip` (`tenant_id`,`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP阻止表';

-- ----------------------------
-- Records of blocked_ips
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` bigint NOT NULL COMMENT '分布式id',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `parent_id` bigint DEFAULT NULL COMMENT '父部门ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门编码',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门描述',
  `level` int NOT NULL DEFAULT '1' COMMENT '部门层级',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '部门状态：active-活跃，disabled-禁用',
  `manager_id` bigint DEFAULT NULL COMMENT '部门负责人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_department_tenant_code` (`tenant_id`,`code`),
  KEY `idx_department_tenant_id` (`tenant_id`),
  KEY `idx_department_parent_id` (`parent_id`),
  KEY `idx_department_manager_id` (`manager_id`),
  KEY `idx_department_status` (`status`),
  KEY `idx_department_deleted` (`deleted_at`),
  KEY `idx_departments_internal_app_id` (`internal_app_id`),
  KEY `idx_departments_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=727867074570883073 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ----------------------------
-- Records of departments
-- ----------------------------
BEGIN;
INSERT INTO `departments` (`id`, `tenant_id`, `internal_app_id`, `parent_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `manager_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (727867074570883072, 1, 1, NULL, '测试', 'LNS:001', '测试描述', 1, 1, 'active', NULL, '2025-07-01 12:43:54', '2025-08-07 18:02:54', NULL);
COMMIT;

-- ----------------------------
-- Table structure for file_records
-- ----------------------------
DROP TABLE IF EXISTS `file_records`;
CREATE TABLE `file_records` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `user_id` bigint NOT NULL COMMENT '上传用户ID',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `file_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件MD5哈希',
  `storage_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储路径',
  `storage_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储类型：oss, local',
  `access_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问URL',
  `is_temporary` tinyint(1) DEFAULT '1' COMMENT '是否为临时文件',
  `is_permanent` tinyint(1) DEFAULT '0' COMMENT '是否已标记为永久',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，deleted-已删除',
  `meta` text COLLATE utf8mb4_unicode_ci COMMENT '元数据信息，JSON格式',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_file_tenant_id` (`tenant_id`),
  KEY `idx_file_user_id` (`user_id`),
  KEY `idx_file_scene_code` (`scene_code`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_file_temporary` (`is_temporary`),
  KEY `idx_file_permanent` (`is_permanent`),
  KEY `idx_file_expire_at` (`expire_at`),
  KEY `idx_file_status` (`status`),
  KEY `idx_file_created_at` (`created_at`),
  KEY `idx_file_deleted_at` (`deleted_at`),
  KEY `idx_file_storage_type` (`storage_type`),
  KEY `idx_file_file_type` (`file_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件记录表';

-- ----------------------------
-- Records of file_records
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for file_upload_configs
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_configs`;
CREATE TABLE `file_upload_configs` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `scene_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景名称',
  `allowed_types` text COLLATE utf8mb4_unicode_ci COMMENT '允许的文件类型，JSON格式',
  `max_file_size` bigint NOT NULL COMMENT '最大文件大小（字节）',
  `is_temporary` tinyint(1) DEFAULT '0' COMMENT '是否为临时文件',
  `enable_dedup` tinyint(1) DEFAULT '0' COMMENT '是否开启文件去重',
  `temp_expire_time` int DEFAULT '30' COMMENT '临时文件过期时间（分钟）',
  `is_system_scene` tinyint(1) DEFAULT '0' COMMENT '是否为系统场景',
  `extra_config` text COLLATE utf8mb4_unicode_ci COMMENT '额外配置信息，JSON格式',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，disabled-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_tenant_scene` (`tenant_id`,`scene_code`),
  KEY `idx_config_tenant_id` (`tenant_id`),
  KEY `idx_config_scene_code` (`scene_code`),
  KEY `idx_config_status` (`status`),
  KEY `idx_config_system_scene` (`is_system_scene`),
  KEY `idx_config_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置表';

-- ----------------------------
-- Records of file_upload_configs
-- ----------------------------
BEGIN;
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1001, 1, 1, 'general', '通用文件上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\"]', 104857600, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1002, 1, 1, 'image', '图片上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"bmp\",\"webp\"]', 20971520, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1003, 1, 1, 'document', '文档上传', '[\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\"]', 52428800, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1004, 1, 1, 'temporary', '临时文件上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"txt\"]', 10485760, 1, 0, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1005, 1, 1, 'avatar', '头像上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\"]', 5242880, 1, 1, 0, 0, '', 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `internal_app_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1006, 1, 1, 'attachment', '附件上传', '[\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\",\"7z\"]', 104857600, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-08-07 18:03:08');
COMMIT;

-- ----------------------------
-- Table structure for id_sequence
-- ----------------------------
DROP TABLE IF EXISTS `id_sequence`;
CREATE TABLE `id_sequence` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `business_type` varchar(50) NOT NULL,
  `sequence_name` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '序列描述',
  `tenant_id` bigint NOT NULL,
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `current_value` bigint NOT NULL DEFAULT '0',
  `increment_step` int NOT NULL DEFAULT '1' COMMENT '步长',
  `cache_size` int NOT NULL DEFAULT '100' COMMENT '缓存大小',
  `max_value` bigint NOT NULL DEFAULT '100' COMMENT '最大值',
  `min_value` bigint NOT NULL DEFAULT '1',
  `threshold` int NOT NULL DEFAULT '20' COMMENT '预分配阈值百分比',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `remarks` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sequence_business_tenant` (`business_type`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of id_sequence
-- ----------------------------
BEGIN;
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (4, 'user', 'user_id', '', 1, 1, 48034, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-09 15:01:55');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (5, 'tenant', 'tenant_id', '', 1, 1, 48035, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-07 01:38:35');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (6, 'role', 'role_id', '', 1, 1, 48072, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-07 01:38:36');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (7, 'permission', 'permission_id', '', 1, 1, 48068, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-07 01:38:38');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (8, 'department', 'department_id', '', 1, 1, 48068, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-07 01:38:40');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (9, 'position', 'position_id', '', 1, 1, 48068, 1, 2, 0, 1, 20, 1, '', '2025-07-06 05:50:19', '2025-08-07 01:38:41');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (14, 'resource', '系统资源', '', 1, 1, 3107, 1, 2, 0, 1, 20, 1, '', '2025-07-07 02:57:54', '2025-08-07 01:38:43');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (28, 'email_account', '邮件账户', '', 1, 1, 43, 1, 2, 0, 1, 20, 1, '', '2025-07-28 10:10:48', '2025-08-07 01:38:44');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (29, 'email_message', '邮件消息记录', '', 1, 1, 6098, 1, 1000, 0, 1, 20, 1, '', '2025-07-29 12:34:14', '2025-08-09 09:14:55');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (30, 'email_template', '邮件模版', '', 1, 1, 98, 1, 1000, 0, 1, 20, 1, '', '2025-07-29 12:35:07', '2025-08-07 01:39:05');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `internal_app_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (31, 'application', 'internalAppId', '', 1, 1, 10005, 1, 5, 0, 1, 20, 1, '内部appId生成', '2025-08-07 01:38:31', '2025-08-07 01:42:24');
COMMIT;

-- ----------------------------
-- Table structure for login_attempts
-- ----------------------------
DROP TABLE IF EXISTS `login_attempts`;
CREATE TABLE `login_attempts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID（可能为空，因为用户名可能不存在）',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'failed' COMMENT '尝试状态：success-成功，failed-失败，locked-锁定',
  `attempted_at` datetime NOT NULL COMMENT '尝试时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_login_attempts_user_id` (`user_id`),
  KEY `idx_login_attempts_tenant_id` (`tenant_id`),
  KEY `idx_login_attempts_username` (`username`),
  KEY `idx_login_attempts_ip_address` (`ip_address`),
  KEY `idx_login_attempts_status` (`status`),
  KEY `idx_login_attempts_attempted_at` (`attempted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录尝试表';

-- ----------------------------
-- Records of login_attempts
-- ----------------------------
BEGIN;
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (112, NULL, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-08-07 09:40:10', '2025-08-07 09:40:10');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (113, NULL, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-08-07 09:42:34', '2025-08-07 09:42:34');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (114, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 09:45:15', '2025-08-07 09:45:15');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (115, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 13:25:25', '2025-08-07 13:25:25');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (116, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 13:45:54', '2025-08-07 13:45:54');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (117, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:34:13', '2025-08-07 17:34:13');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (118, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:34:15', '2025-08-07 17:34:15');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (119, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:34:30', '2025-08-07 17:34:30');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (120, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:37:59', '2025-08-07 17:37:59');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (121, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:45:22', '2025-08-07 17:45:22');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (122, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:48:10', '2025-08-07 17:48:10');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (123, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-07 17:55:07', '2025-08-07 17:55:07');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (124, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-08 14:19:33', '2025-08-08 14:19:33');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (125, NULL, 1, 10001, '123456', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-08-09 07:02:54', '2025-08-09 07:02:54');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (126, 2001, 1, 10001, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'success', '2025-08-09 13:23:19', '2025-08-09 13:23:19');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (127, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-09 15:04:43', '2025-08-09 15:04:43');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `internal_app_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (128, 1001, 1, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-08-10 14:23:59', '2025-08-10 14:23:59');
COMMIT;

-- ----------------------------
-- Table structure for mfa_configs
-- ----------------------------
DROP TABLE IF EXISTS `mfa_configs`;
CREATE TABLE `mfa_configs` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MFA类型：totp-基于时间的一次性密码，sms-短信验证码，email-邮件验证码',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'TOTP密钥',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用',
  `backup_codes` json DEFAULT NULL COMMENT '备用码（JSON数组）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `idx_mfa_configs_user_id` (`user_id`),
  KEY `idx_mfa_configs_type` (`type`),
  KEY `idx_mfa_configs_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MFA配置表';

-- ----------------------------
-- Records of mfa_configs
-- ----------------------------
BEGIN;
INSERT INTO `mfa_configs` (`user_id`, `type`, `secret`, `phone`, `email`, `enabled`, `backup_codes`, `created_at`, `updated_at`) VALUES (1001, 'totp', 'mock_secret_key', NULL, NULL, 1, NULL, '2025-06-27 18:24:27', '2025-06-27 18:24:27');
COMMIT;

-- ----------------------------
-- Table structure for oauth_channel_config
-- ----------------------------
DROP TABLE IF EXISTS `oauth_channel_config`;
CREATE TABLE `oauth_channel_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `channel_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道名称',
  `icon_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `client_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `client_secret` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `auth_url` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token_url` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_info_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `redirect_uri` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `grant_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'authorization_code',
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `jwks_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `response_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'code',
  `issuer` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `config` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_code` (`channel_code`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_order_num` (`order_num`),
  KEY `idx_channel_code` (`channel_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth渠道配置表';

-- ----------------------------
-- Records of oauth_channel_config
-- ----------------------------
BEGIN;
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (1, 'github', 'GitHub', 'https://github.com/favicon.ico', 'your_github_client_id', 'your_github_client_secret', 'https://github.com/login/oauth/authorize', 'https://github.com/login/oauth/access_token', 'https://api.github.com/user', 'http://localhost:8084/api/user/oauth-login/callback', 'user:email', 'authorization_code', 1, 1, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (2, 'google', 'Google', 'https://www.google.com/favicon.ico', 'your_google_client_id', 'your_google_client_secret', 'https://accounts.google.com/o/oauth2/auth', 'https://oauth2.googleapis.com/token', 'https://www.googleapis.com/oauth2/v2/userinfo', 'http://localhost:8084/api/user/oauth-login/callback', 'openid email profile', 'authorization_code', 1, 2, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (3, 'wechat', '微信', 'https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png', 'your_wechat_appid', 'your_wechat_secret', 'https://open.weixin.qq.com/connect/oauth2/authorize', 'https://api.weixin.qq.com/sns/oauth2/access_token', 'https://api.weixin.qq.com/sns/userinfo', 'http://localhost:8084/api/user/oauth-login/callback', 'snsapi_userinfo', 'authorization_code', 1, 3, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限描述',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型：user, role, permission, tenant等',
  `resource_id` bigint DEFAULT NULL COMMENT '资源ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作类型：create, read, update, delete等',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '权限状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统权限',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限编码',
  `scope` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'self' COMMENT '权限范围：all-全部权限，self-普通权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_permission_tenant_name` (`tenant_id`,`name`),
  UNIQUE KEY `uq_permission_tenant_code` (`tenant_id`,`code`),
  KEY `idx_permission_tenant_id` (`tenant_id`),
  KEY `idx_permission_resource` (`resource_type`),
  KEY `idx_permission_action` (`action`),
  KEY `idx_permission_system` (`is_system`),
  KEY `idx_permission_status` (`status`),
  KEY `idx_permission_deleted` (`deleted_at`),
  KEY `idx_permission_code` (`code`),
  KEY `idx_permission_scope` (`scope`),
  KEY `idx_permissions_internal_app_id` (`internal_app_id`),
  KEY `idx_permissions_tenant_internal_app` (`tenant_id`,`internal_app_id`),
  CONSTRAINT `chk_permission_scope` CHECK ((`scope` in (_utf8mb4'all',_utf8mb4'self')))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- ----------------------------
-- Records of permissions
-- ----------------------------
BEGIN;
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1001, 1, 1, 'user:create', '创建用户', '创建新用户', 'user', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'USER_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1002, 1, 1, 'user:read', '查看用户', '查看用户信息', 'user', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'USER_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1003, 1, 1, 'user:update', '更新用户', '更新用户信息', 'user', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'USER_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1004, 1, 1, 'user:delete', '删除用户', '删除用户', 'user', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'USER_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1005, 1, 1, 'role:create', '创建角色', '创建新角色', 'role', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'ROLE_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1006, 1, 1, 'role:read', '查看角色', '查看角色信息', 'role', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'ROLE_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1007, 1, 1, 'role:update', '更新角色', '更新角色信息', 'role', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'ROLE_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1008, 1, 1, 'role:delete', '删除角色', '删除角色', 'role', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'ROLE_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1009, 1, 1, 'permission:read', '查看权限', '查看权限信息', 'permission', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'PERMISSION_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1010, 1, 1, 'tenant:read', '查看租户', '查看租户信息', 'tenant', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'TENANT_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1011, 1, 1, 'tenant:update', '更新租户', '更新租户信息', 'tenant', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'TENANT_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1012, 1, 1, 'department:create', '创建部门', '创建新部门', 'department', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'DEPARTMENT_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1013, 1, 1, 'department:read', '查看部门', '查看部门信息', 'department', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'DEPARTMENT_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1014, 1, 1, 'department:update', '更新部门', '更新部门信息', 'department', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'DEPARTMENT_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1015, 1, 1, 'department:delete', '删除部门', '删除部门', 'department', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'DEPARTMENT_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1016, 1, 1, 'position:create', '创建职位', '创建新职位', 'position', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'POSITION_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1017, 1, 1, 'position:read', '查看职位', '查看职位信息', 'position', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'POSITION_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1018, 1, 1, 'position:update', '更新职位', '更新职位信息', 'position', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'POSITION_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1019, 1, 1, 'position:delete', '删除职位', '删除职位', 'position', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-08-09 02:41:02', NULL, 'POSITION_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3009, 1, 1, 'permission:create', '创建权限', '创建新权限', NULL, 10003, 'create', 'active', 1, '2025-07-07 09:47:02', '2025-08-09 02:41:02', NULL, 'PERMISSION_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3011, 1, 1, 'permission:update', '更新权限', '更新权限信息', NULL, 10003, 'update', 'active', 1, '2025-07-07 09:47:02', '2025-08-09 02:41:02', NULL, 'PERMISSION_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3012, 1, 1, 'permission:delete', '删除权限', '删除权限', NULL, 10003, 'delete', 'active', 1, '2025-07-07 09:47:02', '2025-08-09 02:41:02', NULL, 'PERMISSION_DELETE', 'all');
COMMIT;

-- ----------------------------
-- Table structure for plugin
-- ----------------------------
DROP TABLE IF EXISTS `plugin`;
CREATE TABLE `plugin` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '插件名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件描述',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '插件版本',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件作者',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'disabled' COMMENT '插件状态：enabled-启用，disabled-禁用，error-错误',
  `config` json DEFAULT NULL COMMENT '插件配置（JSON格式）',
  `installed_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '安装时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_plugin_tenant_name` (`tenant_id`,`name`),
  KEY `idx_plugin_tenant_id` (`tenant_id`),
  KEY `idx_plugin_status` (`status`),
  KEY `idx_plugin_internal_app_id` (`internal_app_id`),
  KEY `idx_plugin_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='插件表';

-- ----------------------------
-- Records of plugin
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for positions
-- ----------------------------
DROP TABLE IF EXISTS `positions`;
CREATE TABLE `positions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '职位名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '职位编码',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职位描述',
  `level` int NOT NULL DEFAULT '1' COMMENT '职位级别',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '职位状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统职位',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_position_tenant_code` (`tenant_id`,`code`),
  KEY `idx_position_tenant_id` (`tenant_id`),
  KEY `idx_position_department_id` (`department_id`),
  KEY `idx_position_status` (`status`),
  KEY `idx_position_system` (`is_system`),
  KEY `idx_position_deleted` (`deleted_at`),
  KEY `idx_positions_internal_app_id` (`internal_app_id`),
  KEY `idx_positions_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职位表';

-- ----------------------------
-- Records of positions
-- ----------------------------
BEGIN;
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728487499189456896, 1, 1, NULL, '业务员', 'LNS', '', 1, 1, 'active', 0, '2025-07-03 05:49:14', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527070199549952, 1, 1, NULL, '开发1751531188', 'dev1751531188', '', 1, 0, 'active', 0, '2025-07-03 08:26:29', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527072024072192, 1, 1, NULL, '开发', 'dev004', '', 1, 0, 'active', 0, '2025-07-03 08:26:29', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527072938430464, 1, 1, NULL, '开发\'; DROP TABLE user; --', 'dev007', '', 1, 0, 'active', 0, '2025-07-03 08:26:30', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527074070892544, 1, 1, NULL, '并发职位', 'dev009', '', 1, 0, 'active', 0, '2025-07-03 08:26:30', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527572031246336, 1, 1, NULL, '开发1751531308', 'dev1751531308', '', 1, 0, 'active', 0, '2025-07-03 08:28:29', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728532089850302464, 1, 1, NULL, '开发1751532385', 'dev1751532385', '', 1, 0, 'active', 0, '2025-07-03 08:46:26', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728532319723327488, 1, 1, NULL, '开发1751532440', 'dev1751532440', '', 1, 0, 'active', 0, '2025-07-03 08:47:21', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728537714411966464, 1, 1, NULL, '开发1751533726', 'dev1751533726', '', 1, 0, 'active', 0, '2025-07-03 09:08:47', '2025-08-09 02:41:25', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `internal_app_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728537910449541120, 1, 1, NULL, '开发1751533773', 'dev1751533773', '', 1, 0, 'active', 0, '2025-07-03 09:09:33', '2025-08-09 02:41:25', NULL);
COMMIT;

-- ----------------------------
-- Table structure for rate_limit_config
-- ----------------------------
DROP TABLE IF EXISTS `rate_limit_config`;
CREATE TABLE `rate_limit_config` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID（NULL表示全局配置）',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '限流配置名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '限流配置描述',
  `target_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标类型：global, tenant, user, api',
  `target_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标值（如API路径、用户ID等）',
  `limit_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '限流类型：requests_per_second, requests_per_minute, requests_per_hour',
  `limit_value` int NOT NULL COMMENT '限流值',
  `burst_size` int DEFAULT '0' COMMENT '突发大小',
  `window_size` int DEFAULT '60' COMMENT '时间窗口大小（秒）',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_rate_limit_tenant_name` (`tenant_id`,`name`),
  KEY `idx_rate_limit_tenant_id` (`tenant_id`),
  KEY `idx_rate_limit_target` (`target_type`,`target_value`),
  KEY `idx_rate_limit_enabled` (`enabled`),
  KEY `idx_rate_limit_internal_app_id` (`internal_app_id`),
  KEY `idx_rate_limit_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限流配置表';

-- ----------------------------
-- Records of rate_limit_config
-- ----------------------------
BEGIN;
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `internal_app_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4001, NULL, 1, 'global_rate_limit', '全局限流', 'global', NULL, 'requests_per_second', 10000, 2000, 1, 1, '2025-06-27 17:06:36', '2025-08-09 02:41:41');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `internal_app_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4002, NULL, 1, 'tenant_rate_limit', '租户限流', 'tenant', NULL, 'requests_per_second', 1000, 200, 1, 1, '2025-06-27 17:06:36', '2025-08-09 02:41:41');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `internal_app_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4003, NULL, 1, 'user_rate_limit', '用户限流', 'user', NULL, 'requests_per_second', 100, 20, 1, 1, '2025-06-27 17:06:36', '2025-08-09 02:41:41');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `internal_app_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4004, NULL, 1, 'login_api_rate_limit', '登录接口限流', 'api', '/api/v1/auth/login', 'requests_per_minute', 5, 1, 60, 1, '2025-06-27 17:06:36', '2025-08-09 02:41:41');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `internal_app_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4005, NULL, 1, 'user_api_rate_limit', '用户接口限流', 'api', '/api/v1/users', 'requests_per_minute', 60, 10, 60, 1, '2025-06-27 17:06:36', '2025-08-09 02:41:41');
COMMIT;

-- ----------------------------
-- Table structure for resource
-- ----------------------------
DROP TABLE IF EXISTS `resource`;
CREATE TABLE `resource` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源描述',
  `resource_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型：menu-菜单，button-按钮，api-接口，page-页面\n',
  `service_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用服务名称',
  `request_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求数据类型: json, form, file, text, stream, xml, binary',
  `response_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '响应数据类型: json, html, xml, stream, file, text, binary',
  `api_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'HTTP方法: GET, POST, PUT, DELETE, PATCH',
  `content_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Content-Type: application/json, multipart/form-data等',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父资源ID',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源路径',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统资源',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除实践',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开访问',
  `public_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'none' COMMENT '公开级别：none-非公开，anonymous-匿名，authenticated-已认证，conditional-条件式',
  `assignable` tinyint(1) DEFAULT '1' COMMENT '是否可分配给用户，TRUE表示可以分配，FALSE表示不可分配',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_resource_tenant_name` (`tenant_id`,`name`),
  KEY `idx_resource_tenant_id` (`tenant_id`),
  KEY `idx_resource_parent_id` (`parent_id`),
  KEY `idx_resource_system` (`is_system`),
  KEY `idx_resource_public` (`tenant_id`,`is_public`,`public_level`),
  KEY `idx_resource_public_path` (`tenant_id`,`path`,`is_public`),
  KEY `idx_resource_assignable` (`tenant_id`,`assignable`),
  KEY `idx_resource_internal_app_id` (`internal_app_id`),
  KEY `idx_resource_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源表';

-- ----------------------------
-- Records of resource
-- ----------------------------
BEGIN;
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (1, 1, 1, 'user_list', '用户列表查询', '管理后台查询用户列表', 'api', 'user-service', 'json', 'json', 'POST', '', 0, '/api/user/list', '', 1, 0, NULL, '2025-07-24 10:33:11', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (2, 1, 1, 'admin_user_create', '管理员创建用户', '管理后台手动创建用户', 'api', 'user-service', 'json', 'json', 'POST', '', 0, '/api/user/create', '', 2, 0, NULL, '2025-07-24 10:40:36', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (100, 1, 1, 'dashboard', '仪表盘', '系统仪表盘页面', 'page', NULL, NULL, NULL, NULL, NULL, 0, '/dashboard', 'DashboardOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (101, 1, 1, 'user-management', '用户管理', '用户管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'TeamOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (102, 1, 1, 'system-management', '系统管理', '系统管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'SettingOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (103, 1, 1, 'file-system', '文件系统', '文件系统相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'FolderOutlined', 4, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (104, 1, 1, 'communication', '通讯管理', '通讯管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'MailOutlined', 5, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (105, 1, 1, 'user-system', '用户系统', '用户系统相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'UserSwitchOutlined', 6, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (106, 1, 1, 'tools', '工具箱', '工具箱相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'ThunderboltOutlined', 7, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (107, 1, 1, 'tenant', '租户管理', '租户管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 0, '/tenant', 'BankOutlined', 8, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (108, 1, 1, 'user', '用户列表', '用户列表管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/user', 'UserOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (109, 1, 1, 'department', '组织架构', '组织架构管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/department', 'ApartmentOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (110, 1, 1, 'position', '职位管理', '职位管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/position', 'SolutionOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (111, 1, 1, 'role', '角色管理', '角色管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/role', 'CrownOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (112, 1, 1, 'permission', '权限管理', '权限管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/permission', 'SafetyCertificateOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (113, 1, 1, 'permission-group', '权限组管理', '权限组管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/permission-group', 'TeamOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (114, 1, 1, 'resource', '资源管理', '资源管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/resource', 'DatabaseOutlined', 4, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (115, 1, 1, 'api-management', 'API管理', 'API管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/api-management', 'ApiOutlined', 5, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (116, 1, 1, 'file-system/files', '文件管理', '文件管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/files', 'FileTextOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (117, 1, 1, 'file-system/scenes', '场景配置', '场景配置页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/scenes', 'SettingOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (118, 1, 1, 'file-system/upload', '文件上传', '文件上传页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/upload', 'UploadOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (119, 1, 1, 'email/accounts', '邮件账户', '邮件账户管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 104, '/email/accounts', 'UserOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (120, 1, 1, 'email/templates', '邮件模板', '邮件模板管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 104, '/email/templates', 'FileTextOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (121, 1, 1, 'users/policies', '验证策略', '验证策略管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 105, '/users/policies', 'SecurityScanOutlined', 1, 1, NULL, '2025-07-25 01:35:36', '2025-08-09 02:41:47', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `internal_app_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (122, 1, 1, 'idgenerator', 'ID生成器', 'ID生成器页面', 'page', NULL, NULL, NULL, NULL, NULL, 106, '/idgenerator/sequences', 'KeyOutlined', 1, 1, NULL, '2025-07-25 01:35:36', '2025-08-09 02:41:47', 0, 'none', 1);
COMMIT;

-- ----------------------------
-- Table structure for resource_relations
-- ----------------------------
DROP TABLE IF EXISTS `resource_relations`;
CREATE TABLE `resource_relations` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `source_resource_id` bigint NOT NULL COMMENT '源资源ID',
  `target_resource_id` bigint NOT NULL COMMENT '目标资源ID',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_resource_relation` (`tenant_id`,`source_resource_id`,`target_resource_id`),
  KEY `idx_resource_relations_tenant_id` (`tenant_id`),
  KEY `idx_resource_relations_source_id` (`source_resource_id`),
  KEY `idx_resource_relations_target_id` (`target_resource_id`),
  KEY `idx_resource_relations_internal_app_id` (`internal_app_id`),
  KEY `idx_resource_relations_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源关联关系表';

-- ----------------------------
-- Records of resource_relations
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `granted_by` bigint NOT NULL COMMENT '授权人ID',
  `granted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_role_permission` (`role_id`,`permission_id`),
  KEY `idx_role_permissions_role_id` (`role_id`),
  KEY `idx_role_permissions_permission_id` (`permission_id`),
  KEY `idx_role_permissions_granted_by` (`granted_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ----------------------------
-- Records of role_permissions
-- ----------------------------
BEGIN;
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5001, 2001, 3001, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5002, 2001, 3002, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5003, 2001, 3003, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5004, 2001, 3004, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5005, 2001, 3005, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5006, 2001, 3006, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5007, 2001, 3007, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5008, 2001, 3008, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5009, 2001, 3009, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5010, 2001, 3010, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5011, 2001, 3011, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5012, 2001, 3012, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5013, 2001, 3013, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5014, 2001, 3014, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5015, 2001, 3015, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5016, 2001, 3016, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5017, 2001, 3017, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5018, 2001, 3018, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5019, 2001, 3019, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5020, 2001, 3020, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
COMMIT;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '角色编码',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '角色状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统角色',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序字段',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_role_tenant_name` (`tenant_id`,`name`),
  UNIQUE KEY `uq_role_code` (`tenant_id`,`code`),
  KEY `idx_role_tenant_id` (`tenant_id`),
  KEY `idx_role_system` (`is_system`),
  KEY `idx_role_status` (`status`),
  KEY `idx_role_deleted` (`deleted_at`),
  KEY `idx_roles_internal_app_id` (`internal_app_id`),
  KEY `idx_roles_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- ----------------------------
-- Records of roles
-- ----------------------------
BEGIN;
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2001, 1, 1, 'super_admin', 'super_admin', '系统管理员', '系统管理员，拥有所有权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2002, 1, 1, 'admin', 'admin', '普通用户', '普通用户，基础权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2003, 1, 1, 'user', 'user', '普通用户', '普通用户，基础权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2004, 1, 1, 'manager', 'manager', '部门经理', '部门经理，部门管理权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601011895996416, 1, 1, '业务员', 'lns:worker', '业务员', '', 'active', 0, 0, '2025-06-28 00:53:01', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601602584023040, 1, 1, '测试角色', 'test_role', '测试角色', '这是一个测试角色', 'active', 0, 0, '2025-06-28 00:55:22', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601784021225472, 1, 1, '测试角色3', 'test_role3', '测试角色3', '这是一个测试角色', 'active', 0, 0, '2025-06-28 00:56:05', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726666439049613312, 1, 1, 'test', 'test', 'test', '', 'active', 0, 0, '2025-06-28 05:13:00', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726687864112287744, 1, 1, 'ddd', 'lns:user', 'ddd', '', 'active', 0, 0, '2025-06-28 06:38:08', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775673351311360, 1, 1, '调试角色', 'DEBUG_ROLE', '调试角色', '用于调试的角色', 'active', 0, 0, '2025-06-28 12:27:03', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775694297665536, 1, 1, '测试角色1', 'TEST_ROLE_1', '测试角色1', '测试角色1描述', 'active', 0, 0, '2025-06-28 12:27:08', '2025-08-07 18:02:40', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `internal_app_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775695082000384, 1, 1, '测试角色2', 'TEST_ROLE_2', '测试角色2', '测试角色2描述', 'active', 0, 0, '2025-06-28 12:27:09', '2025-08-07 18:02:40', NULL);
COMMIT;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID（NULL表示全局配置）',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_config_tenant_key` (`config_key`,`internal_app_id`,`tenant_id`) USING BTREE,
  KEY `idx_config_tenant_id` (`tenant_id`),
  KEY `idx_config_key` (`config_key`),
  KEY `idx_config_system` (`is_system`),
  KEY `idx_system_config_internal_app_id` (`internal_app_id`),
  KEY `idx_system_config_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ----------------------------
-- Records of system_config
-- ----------------------------
BEGIN;
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (1, 2, 1, 'password_policy', '{\"min_length\":8,\"max_length\":32,\"require_uppercase\":false,\"require_lowercase\":false,\"require_digits\":false,\"require_special_chars\":false,\"forbidden_patterns\":[\"123456\",\"password\",\"admin\"],\"password_history_count\":5,\"expire_days\":90}', 'json', NULL, 0, '2025-07-20 02:13:34', '2025-08-09 02:43:07');
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (3, 4, 1, 'registration_methods', '{\"email\":{\"enabled\":true,\"require_verification\":true,\"manual_activation\":false,\"verification_template_code\":\"account_activation\"},\"phone\":{\"enabled\":true,\"require_verification\":false,\"manual_activation\":false},\"oauth\":{\"enabled\":true,\"manual_activation\":false},\"admin_creation\":{\"enabled\":true,\"require_approval\":false}}', 'json', NULL, 0, '2025-07-27 03:18:48', '2025-08-09 02:43:07');
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (4, 2, 1, 'registration_methods', '{\"email\":{\"enabled\":true,\"require_verification\":true,\"manual_activation\":false,\"verification_template_code\":\"account_activation\"},\"phone\":{\"enabled\":true,\"require_verification\":false,\"manual_activation\":false},\"oauth\":{\"enabled\":true,\"manual_activation\":false},\"admin_creation\":{\"enabled\":true,\"require_approval\":false}}', 'json', NULL, 0, '2025-07-27 03:18:59', '2025-08-09 02:43:07');
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (5, 1, 1, 'password_policy', '{\"min_length\":8,\"max_length\":32,\"require_uppercase\":true,\"require_lowercase\":true,\"require_digits\":true,\"require_special_chars\":false,\"forbidden_patterns\":[\"123456\",\"password\",\"admin\"],\"password_history_count\":5,\"expire_days\":90}', 'json', NULL, 0, '2025-07-31 17:07:27', '2025-08-09 02:43:07');
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (6, 4, 0, 'password_policy', '{\"min_length\":8,\"max_length\":32,\"require_uppercase\":true,\"require_lowercase\":true,\"require_digits\":true,\"require_special_chars\":false,\"forbidden_patterns\":[\"123456\",\"password\",\"admin\"],\"password_history_count\":5,\"expire_days\":90}', 'json', NULL, 0, '2025-08-09 15:10:38', '2025-08-09 15:10:38');
INSERT INTO `system_config` (`id`, `tenant_id`, `internal_app_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (7, 1, 1, 'registration_methods', '{\"email\":{\"enabled\":true,\"require_verification\":true,\"manual_activation\":true,\"verification_template_code\":\"template-0\"},\"phone\":{\"enabled\":false,\"require_verification\":false,\"manual_activation\":false,\"verification_template_code\":\"\"},\"oauth\":{\"enabled\":false,\"manual_activation\":false},\"admin_creation\":{\"enabled\":false,\"require_approval\":false}}', 'json', NULL, 0, '2025-08-09 22:59:37', '2025-08-10 13:32:25');
COMMIT;

-- ----------------------------
-- Table structure for tenant_oauth_channel
-- ----------------------------
DROP TABLE IF EXISTS `tenant_oauth_channel`;
CREATE TABLE `tenant_oauth_channel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `channel_config_id` bigint NOT NULL COMMENT '渠道配置ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义渠道名称',
  `icon_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义图标URL',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `config` json DEFAULT NULL COMMENT '自定义配置',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_channel` (`tenant_id`,`channel_config_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_channel_config_id` (`channel_config_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_order_num` (`order_num`),
  KEY `idx_tenant_oauth_channel_internal_app_id` (`internal_app_id`),
  KEY `idx_tenant_oauth_channel_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户OAuth渠道关联表';

-- ----------------------------
-- Records of tenant_oauth_channel
-- ----------------------------
BEGIN;
INSERT INTO `tenant_oauth_channel` (`id`, `tenant_id`, `internal_app_id`, `channel_config_id`, `name`, `icon_url`, `enabled`, `order_num`, `config`, `created_at`, `updated_at`) VALUES (1, 1, 1, 1, 'GitHub登录', NULL, 1, 1, NULL, '2025-07-12 15:13:44', '2025-08-09 02:43:12');
INSERT INTO `tenant_oauth_channel` (`id`, `tenant_id`, `internal_app_id`, `channel_config_id`, `name`, `icon_url`, `enabled`, `order_num`, `config`, `created_at`, `updated_at`) VALUES (2, 1, 1, 2, 'Google登录', NULL, 1, 2, NULL, '2025-07-12 15:13:44', '2025-08-09 02:43:12');
COMMIT;

-- ----------------------------
-- Table structure for tenants
-- ----------------------------
DROP TABLE IF EXISTS `tenants`;
CREATE TABLE `tenants` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户编码，业务唯一标识',
  `tenant_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名称',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '租户状态：active-活跃，disabled-禁用，expired-过期',
  `max_users` int DEFAULT '1000' COMMENT '最大用户数',
  `max_storage` bigint DEFAULT '***********' COMMENT '最大存储空间（字节）',
  `subscription_plan` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'basic' COMMENT '订阅计划：basic-基础版，pro-专业版，enterprise-企业版',
  `expires_at` datetime DEFAULT NULL COMMENT '租户过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_code` (`tenant_code`),
  KEY `idx_tenant_code` (`tenant_code`),
  KEY `idx_tenant_status` (`status`),
  KEY `idx_tenant_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- ----------------------------
-- Records of tenants
-- ----------------------------
BEGIN;
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (1, 'system', '系统租户', 'active', 1000, ***********, 'enterprise', NULL, '2025-07-21 14:14:12', '2025-07-21 14:14:22');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (2, 'ilike', 'ilike', 'active', 0, 10, 'enterprise', NULL, '2025-07-09 10:37:02', '2025-08-02 06:54:01');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (3, 'demo', '演示租户', 'active', 1000, ***********, 'basic', NULL, '2025-06-27 17:07:12', '2025-07-21 14:13:38');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (4, 'prompts', '提示词项目', 'active', 1000, ***********, 'basic', NULL, '2025-07-21 15:25:39', '2025-07-21 15:25:39');
COMMIT;

-- ----------------------------
-- Table structure for third_party_account
-- ----------------------------
DROP TABLE IF EXISTS `third_party_account`;
CREATE TABLE `third_party_account` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提供商：wechat, dingtalk, feishu, google, microsoft等',
  `external_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部用户ID',
  `external_username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部用户名',
  `external_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部邮箱',
  `external_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部头像',
  `access_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '访问令牌',
  `refresh_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '刷新令牌',
  `token_expires_at` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，disabled-禁用',
  `bind_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_third_party_user_provider` (`user_id`,`provider`),
  UNIQUE KEY `uq_third_party_provider_external` (`provider`,`external_user_id`),
  KEY `idx_third_party_user_id` (`user_id`),
  KEY `idx_third_party_provider` (`provider`),
  KEY `idx_third_party_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='三方账号表';

-- ----------------------------
-- Records of third_party_account
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for upload_tokens
-- ----------------------------
DROP TABLE IF EXISTS `upload_tokens`;
CREATE TABLE `upload_tokens` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上传令牌',
  `oss_config` text COLLATE utf8mb4_unicode_ci COMMENT 'OSS配置信息，JSON格式',
  `expire_at` datetime NOT NULL COMMENT '过期时间（通常30-60分钟）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_token_tenant_id` (`tenant_id`),
  KEY `idx_token_user_id` (`user_id`),
  KEY `idx_token_scene_code` (`scene_code`),
  KEY `idx_token_expire_at` (`expire_at`),
  KEY `idx_token_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传令牌表';

-- ----------------------------
-- Records of upload_tokens
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_departments
-- ----------------------------
DROP TABLE IF EXISTS `user_departments`;
CREATE TABLE `user_departments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_department` (`user_id`,`department_id`),
  KEY `idx_user_departments_user_id` (`user_id`),
  KEY `idx_user_departments_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关联表';

-- ----------------------------
-- Records of user_departments
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_ext
-- ----------------------------
DROP TABLE IF EXISTS `user_ext`;
CREATE TABLE `user_ext` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '扩展属性键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '扩展属性值',
  `ext_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '扩展属性类型：string, number, boolean, json',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_ext_key` (`user_id`,`ext_key`),
  KEY `idx_user_ext_user_id` (`user_id`),
  KEY `idx_user_ext_key` (`ext_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户扩展属性表';

-- ----------------------------
-- Records of user_ext
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_password_history
-- ----------------------------
DROP TABLE IF EXISTS `user_password_history`;
CREATE TABLE `user_password_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希（BCrypt加密）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_password_history_user_id` (`user_id`),
  KEY `idx_user_password_history_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户密码历史表';

-- ----------------------------
-- Records of user_password_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_positions
-- ----------------------------
DROP TABLE IF EXISTS `user_positions`;
CREATE TABLE `user_positions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `position_id` bigint NOT NULL COMMENT '职位ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_position` (`user_id`,`position_id`),
  KEY `idx_user_positions_user_id` (`user_id`),
  KEY `idx_user_positions_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户职位关联表';

-- ----------------------------
-- Records of user_positions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_roles
-- ----------------------------
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id，无业务用途',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `granted_by` bigint NOT NULL COMMENT '授权人ID',
  `granted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `expires_at` datetime DEFAULT NULL COMMENT '授权过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_role` (`user_id`,`role_id`),
  KEY `idx_user_roles_user_id` (`user_id`),
  KEY `idx_user_roles_role_id` (`role_id`),
  KEY `idx_user_roles_granted_by` (`granted_by`),
  KEY `idx_user_roles_expires` (`expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4002 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- ----------------------------
-- Records of user_roles
-- ----------------------------
BEGIN;
INSERT INTO `user_roles` (`id`, `user_id`, `role_id`, `granted_by`, `granted_at`, `expires_at`, `created_at`) VALUES (4001, 1001, 2001, 1001, '2025-07-07 09:47:02', NULL, '2025-06-27 17:07:12');
COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '用户状态：active-活跃，disabled-禁用，locked-锁定',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希（BCrypt加密）',
  `password_changed_at` datetime DEFAULT NULL COMMENT '密码最后修改时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `failed_count` int DEFAULT '0' COMMENT '失败次数',
  `locked_at` datetime DEFAULT NULL COMMENT '锁定时间',
  `locked_until` datetime DEFAULT NULL COMMENT '锁定到期时间',
  `lock_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '锁定原因',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `position_id` bigint DEFAULT NULL COMMENT '职位ID',
  `employee_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '员工编号',
  `hire_date` datetime DEFAULT NULL COMMENT '入职日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_tenant_email` (`email`,`internal_app_id`,`tenant_id`) USING BTREE,
  UNIQUE KEY `uq_user_tenant_username` (`username`,`internal_app_id`,`tenant_id`) USING BTREE,
  KEY `idx_user_tenant_id` (`tenant_id`),
  KEY `idx_user_status` (`status`),
  KEY `idx_user_username` (`username`),
  KEY `idx_user_email` (`email`),
  KEY `idx_user_deleted` (`deleted_at`),
  KEY `idx_user_department_id` (`department_id`),
  KEY `idx_user_position_id` (`position_id`),
  KEY `idx_user_employee_id` (`employee_id`),
  KEY `idx_users_internal_app_id` (`internal_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1, 1, 1, 'testuser5', '<EMAIL>', '', '测试用户5', '', 'active', 0, '$2a$10$UBtBpjUUrrzvMfEvnJMzROgPu5jWfkqGiu4AySEKRSAs6HEC2ho8.', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:15:34', '2025-08-07 01:37:30', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2, 1, 1, 'testuser6', '<EMAIL>', '', '测试用户6', '', 'active', 0, '$2a$10$VBMz8HgwZtKgBy0kqqERJ.emegBex62TGWHrQZTpPZbYi9BQS8z92', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:15:53', '2025-08-07 01:37:32', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (3, 1, 1, 'testuser7', '<EMAIL>', '', '测试用户7', '', 'active', 0, '$2a$10$zJBipyzgrYHvbS/LTLuoluZLCG8BdIgt3rlKvBN4m7RrB01HfljYW', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:16:01', '2025-08-07 01:37:34', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1001, 1, 1, 'admin', '<EMAIL>', '', '系统管理员', '', 'active', 0, '$2a$10$eKcxwPBZUAJQds4mUmQqVeTTNDE73j6I/HoZIh7VLZM8CPLrWhCaq', NULL, '2025-06-27 12:13:11', '::1', 1, 4, NULL, NULL, '', NULL, NULL, '', NULL, '2025-06-27 17:07:12', '2025-08-07 01:37:36', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1002, 1, 0, 'testuser9', '<EMAIL>', '', '测试用户9', '', 'active', 0, '$2a$10$vx.MJE7rZz6A/RpfV0jugO5x8gE8YgXzsM6kzoGkG3/uFsw1U6PPG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:43:18', '2025-07-06 08:43:18', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2001, 2, 1, 'kevin', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$V5EdpbhP4.r55m3R3fLtUuTDjFImv5KD5cQgVPTmp8YiLNYusShq6', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-20 17:43:02', '2025-08-09 14:37:27', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2002, 2, 0, 'testuser', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$DbRpE24B7I7m6t45mpqDiu/22D/JnARZYeS2dLs6c9w/btXDndEi2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-20 17:43:08', '2025-07-20 17:43:08', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (17003, 4, 0, 'kevin_prompts', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$4Pi1.4adKgMcCVjUFnaCzOa4Uof0/8FiptcaeX83VeRpv1txAEvx2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-27 03:40:56', '2025-07-27 03:40:56', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (48033, 1, 10001, 'kevin', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$vORxEz7lJIvt44cuT9n6n.VhOOUFxHo6tpiGjPoLNXGnKeN0ftRTS', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-08-09 15:01:55', '2025-08-09 15:01:55', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (727751058184474624, 1, 0, 'testuser', '<EMAIL>', '', 'Test User', '', 'active', 0, '$2a$10$RPOboNjYsPxHI.RNtLcRo.Eu0HbrS44ZW7Pcyp.TXMdT1zqHHe7u6', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-01 05:02:53', '2025-07-01 05:02:53', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (727876535444312064, 1, 0, 'testuser1', '<EMAIL>', '13900000000', '新名字', '', 'inactive', 0, '$2a$10$2yH3QSf.9hKfjbrNRQEaV.sjpHVnj3/URaQv7oMR2kSzd7HX0Rdv2', NULL, NULL, '', 0, 0, NULL, NULL, '', 1, 1, '', NULL, '2025-07-01 13:21:29', '2025-07-01 14:56:35', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575207857033216, 1, 0, 'testuser001', '<EMAIL>', '13800138001', '测试用户001', '', 'active', 0, '$2a$10$6YqT2dPaaPtyjDmJsUQn3.ZcNAzh/eLUvIaI/fmJ82SNwDpVTHbVK', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:24', '2025-07-06 05:51:24', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575244007739392, 1, 0, 'testuser002', '<EMAIL>', '13800138002', '测试用户002', '', 'active', 0, '$2a$10$6nLSu/cORsiW/x3/uG65/./6af800taQVOMrXY7mXrW83JnrvIj6K', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:33', '2025-07-06 05:51:33', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575276794613760, 1, 0, 'testuser003', '<EMAIL>', '13800138003', '测试用户003', '', 'active', 0, '$2a$10$Wxx/SxZRH5PLPCsvfBX7KOL1mFhC7u4XRFwOX90dBXr9IaOlecCO2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:41', '2025-07-06 05:51:41', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729576201215021056, 1, 0, 'distributed001', '<EMAIL>', '13800138101', '分布式ID测试用户001', '', 'active', 0, '$2a$10$dzQvwuNYpf/iCIYR3WQztu3tFMfA9s3sI5JW39yrqAYeQATQiTeeG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:55:21', '2025-07-06 05:55:21', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729576239341244416, 1, 0, 'distributed002', '<EMAIL>', '13800138102', '分布式ID测试用户002', '', 'active', 0, '$2a$10$ryFqih3UdW.eNYYpmx/iGey1SxJQOxz0Karo5ZgPbZHS63PWVS0HG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:55:30', '2025-07-06 05:55:30', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577054139322368, 1, 0, 'distributed_new_01', '<EMAIL>', '13800138201', '分布式ID新测试用户01', '', 'active', 0, '$2a$10$ZUl.mN6AhGfpqOuK0zbI7eBJfb0Ptx2yEue0dIU6Au27yIzTT9W1O', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:58:45', '2025-07-06 05:58:45', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577091502182400, 1, 0, 'distributed_new_02', '<EMAIL>', '***********', '分布式ID新测试用户02', '', 'active', 0, '$2a$10$zv1El8l/w7T0kNmjoYb5leoSYElYABN8qMkOujtPThA3520spbun.', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:58:54', '2025-07-06 05:58:54', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577427663065088, 1, 0, 'distributed_test01', '<EMAIL>', '***********', '分布式ID测试用户01', '', 'active', 0, '$2a$10$GrJrf.fGVbsNUtI08vm77OxmGu6Sw6VfHI5lXvFceWcfRp.3RY12C', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 06:00:14', '2025-07-06 06:00:14', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577464078012416, 1, 0, 'distributed_test02', '<EMAIL>', '13800138302', '分布式ID测试用户02', '', 'active', 0, '$2a$10$kBs52C7TzJUu23S5YSZpyOUfLQ4ODvxYkxrqug08vqlG2sLERrPLC', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 06:00:22', '2025-07-06 06:00:22', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `internal_app_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729611211011788800, 1, 0, 'testuser4', '<EMAIL>', '', '测试用户4', '', 'active', 0, '$2a$10$.tyKf3.LSJF59tyUKdcfvOyVUWlg9DmYMmQa2RTs5aysgf9pN.H2a', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:14:28', '2025-07-06 08:14:28', NULL);
COMMIT;

-- ----------------------------
-- Table structure for verification_configs
-- ----------------------------
DROP TABLE IF EXISTS `verification_configs`;
CREATE TABLE `verification_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID，bigint类型提升性能',
  `config_mode` enum('static','dynamic') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'static' COMMENT '配置模式：static=静态配置，dynamic=动态策略',
  `purpose` tinyint unsigned DEFAULT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
  `target_type` tinyint unsigned NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
  `business_scene` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务场景：user_register, password_reset, email_change等',
  `judgment_dimension` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '判定维度：ip_location, user_behavior, device_info等',
  `condition_expr` text COLLATE utf8mb4_unicode_ci COMMENT '条件表达式，支持复杂逻辑判断',
  `token_type` tinyint unsigned NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
  `token_length` int NOT NULL DEFAULT '6' COMMENT '验证码长度',
  `expire_minutes` int NOT NULL DEFAULT '30' COMMENT '过期时间（分钟）',
  `max_attempts` int NOT NULL DEFAULT '5' COMMENT '最大尝试次数',
  `rate_limit_per_minute` int NOT NULL DEFAULT '3' COMMENT '每分钟限制',
  `rate_limit_per_hour` int NOT NULL DEFAULT '10' COMMENT '每小时限制',
  `rate_limit_per_day` int NOT NULL DEFAULT '50' COMMENT '每日限制',
  `template_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板代码',
  `require_verification` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要验证',
  `verification_level` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '验证级别：1=低,2=中,3=高',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_static_config` (`tenant_id`,`purpose`,`target_type`,`config_mode`),
  UNIQUE KEY `uk_dynamic_config` (`tenant_id`,`business_scene`,`judgment_dimension`,`config_mode`),
  KEY `idx_tenant_mode` (`tenant_id`,`config_mode`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_priority` (`priority`),
  KEY `idx_business_scene` (`business_scene`),
  KEY `idx_purpose` (`purpose`),
  KEY `idx_target_type` (`target_type`),
  KEY `idx_token_type` (`token_type`),
  KEY `idx_template_code` (`template_code`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_verification_configs_internal_app_id` (`internal_app_id`),
  KEY `idx_verification_configs_tenant_internal_app` (`tenant_id`,`internal_app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一验证配置表';

-- ----------------------------
-- Records of verification_configs
-- ----------------------------
BEGIN;
INSERT INTO `verification_configs` (`id`, `tenant_id`, `internal_app_id`, `config_mode`, `purpose`, `target_type`, `business_scene`, `judgment_dimension`, `condition_expr`, `token_type`, `token_length`, `expire_minutes`, `max_attempts`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `template_code`, `require_verification`, `verification_level`, `priority`, `is_active`, `description`, `created_at`, `updated_at`, `deleted_at`) VALUES (1, 1, 1, 'static', 1, 1, NULL, NULL, NULL, 1, 6, 30, 3, 1, 10, 50, 'account_activation', 1, 1, 10, 1, NULL, '2025-07-27 11:34:56', '2025-08-08 16:59:39', NULL);
INSERT INTO `verification_configs` (`id`, `tenant_id`, `internal_app_id`, `config_mode`, `purpose`, `target_type`, `business_scene`, `judgment_dimension`, `condition_expr`, `token_type`, `token_length`, `expire_minutes`, `max_attempts`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `template_code`, `require_verification`, `verification_level`, `priority`, `is_active`, `description`, `created_at`, `updated_at`, `deleted_at`) VALUES (2, 1, 1, 'static', 2, 1, NULL, NULL, NULL, 1, 6, 1440, 2, 3, 5, 5, 'password_reset', 1, 1, 10, 1, NULL, '2025-07-27 13:40:03', '2025-08-08 16:59:41', NULL);
COMMIT;

-- ----------------------------
-- Table structure for verification_tokens
-- ----------------------------
DROP TABLE IF EXISTS `verification_tokens`;
CREATE TABLE `verification_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用ID，bigint类型提升性能',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '用户ID，可为空（如注册时）',
  `token` varchar(255) NOT NULL COMMENT '验证令牌/验证码',
  `token_type` tinyint unsigned NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
  `target` varchar(255) NOT NULL COMMENT '目标地址（邮箱/手机号）',
  `target_type` tinyint unsigned NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
  `purpose` tinyint unsigned NOT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
  `template_code` varchar(100) NOT NULL COMMENT '关联的模板代码',
  `status` tinyint unsigned DEFAULT '1' COMMENT '状态：1=未使用,2=已使用,3=已过期,4=已撤销',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `revoked_at` timestamp NULL DEFAULT NULL COMMENT '撤销时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP地址',
  `user_agent` text COMMENT '用户代理',
  `attempt_count` int DEFAULT '0' COMMENT '尝试次数',
  `max_attempts` int DEFAULT '5' COMMENT '最大尝试次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_target` (`target`),
  KEY `idx_purpose` (`purpose`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=738011945692565542 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证令牌表';

-- ----------------------------
-- Records of verification_tokens
-- ----------------------------
BEGIN;
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `internal_app_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (738011945692565540, 2, 10001, 2001, '4ef343cd9de4ee40f228e557bbb5616447fa94173799f1a91552207ee71eb01d', 1, '<EMAIL>', 1, 2, 'password_reset', 1, '2025-08-10 09:01:49', NULL, NULL, '', '', 0, 2, '2025-08-09 09:01:49', '2025-08-09 09:01:49');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `internal_app_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (738011945692565541, 2, 10001, 2001, '70e3c72e1add503847ecb19c24f6aa4948451b2f03370a6108536b5f4ff27068', 1, '<EMAIL>', 1, 2, 'password_reset', 2, '2025-08-10 09:14:55', '2025-08-09 12:40:45', NULL, '', '', 0, 2, '2025-08-09 09:14:55', '2025-08-09 12:40:45');
COMMIT;

-- ----------------------------
-- Table structure for verification_trigger_logs
-- ----------------------------
DROP TABLE IF EXISTS `verification_trigger_logs`;
CREATE TABLE `verification_trigger_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `device_id` varchar(128) DEFAULT NULL COMMENT '设备ID',
  `policy_id` bigint DEFAULT NULL COMMENT '命中的策略ID',
  `verification_needed` tinyint(1) NOT NULL COMMENT '是否需要验证',
  `verification_level` enum('none','low','medium','high') NOT NULL COMMENT '验证级别',
  `trigger_reason` varchar(255) DEFAULT NULL COMMENT '触发原因/命中条件',
  `scene` varchar(32) NOT NULL COMMENT '业务场景，如login、register',
  `request_id` varchar(64) DEFAULT NULL COMMENT '请求唯一ID，便于链路追踪',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `extra` json DEFAULT NULL COMMENT '其他扩展信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证触发判定记录表';

-- ----------------------------
-- Records of verification_trigger_logs
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
