-- 创建categories表
CREATE TABLE IF NOT EXISTS `categories` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `name` varchar(50) NOT NULL COMMENT '分类名称',
    `code` varchar(50) NOT NULL COMMENT '分类编码',
    `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
    `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
    `level` int DEFAULT '0' COMMENT '分类层级',
    `path` varchar(255) DEFAULT NULL COMMENT '分类路径',
    `sort` int DEFAULT '0' COMMENT '排序权重',
    `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort` (`sort`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 插入初始分类数据
INSERT INTO `categories` (`id`, `name`, `code`, `description`, `parent_id`, `level`, `path`, `sort`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '网购商品', 'online-shopping', '记录网购商品信息', 0, 0, '/1', 1, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'),
(2, '美食记录', 'food', '记录美食体验', 0, 0, '/2', 2, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'),
(3, '旅游记录', 'travel', '记录旅游体验', 0, 0, '/3', 3, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'),
(4, '餐厅信息', 'restaurant', '记录餐厅信息', 2, 1, '/2/4', 1, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'),
(5, '电影记录', 'movie', '记录电影观看体验', 0, 0, '/5', 4, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'),
(6, '书籍记录', 'book', '记录书籍阅读体验', 0, 0, '/6', 5, 1, '2025-06-19 17:08:22', '2025-06-19 17:08:22'); 