package dto

import "time"

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	Filename         string `json:"filename"`
	OriginalFilename string `json:"original_filename"`
	FileSize         uint64 `json:"file_size"`
	MimeType         string `json:"mime_type"`
	FileType         string `json:"file_type"`
	IsPublic         bool   `json:"is_public"`
}

// FileResponse 文件响应
type FileResponse struct {
	ID               uint64    `json:"id"`
	UserID           uint64    `json:"user_id"`
	Filename         string    `json:"filename"`
	OriginalFilename string    `json:"original_filename"`
	FilePath         string    `json:"file_path"`
	FileSize         uint64    `json:"file_size"`
	MimeType         string    `json:"mime_type"`
	FileType         string    `json:"file_type"`
	HashValue        string    `json:"hash_value,omitempty"`
	IsPublic         bool      `json:"is_public"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// FileListRequest 文件列表请求
type FileListRequest struct {
	UserID uint64 `json:"user_id"`
	Page   int    `json:"page"`
	Size   int    `json:"size"`
}

// FileListResponse 文件列表响应
type FileListResponse struct {
	Files      []*FileResponse `json:"files"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Size       int             `json:"size"`
	TotalPages int             `json:"total_pages"`
}

// FileUpdateRequest 文件更新请求
type FileUpdateRequest struct {
	ID               uint64 `json:"id"`
	Filename         string `json:"filename,omitempty"`
	OriginalFilename string `json:"original_filename,omitempty"`
	IsPublic         bool   `json:"is_public"`
}

// FileAccessRequest 文件访问请求
type FileAccessRequest struct {
	ID     uint64 `json:"id"`
	UserID uint64 `json:"user_id"`
}