package service

import (
	"context"
	"fmt"
	"math"
	"time"

	"gitee.com/heiyee/platforms/ilike-backend/internal/application/comment/dto"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/comment/entity"
	commentRepo "gitee.com/heiyee/platforms/ilike-backend/internal/domain/comment/repository"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// CommentApplicationService 评论应用服务
type CommentApplicationService struct {
	commentRepo commentRepo.CommentRepository
}

// NewCommentApplicationService 创建评论应用服务
func NewCommentApplicationService(
	commentRepo commentRepo.CommentRepository,
) *CommentApplicationService {
	return &CommentApplicationService{
		commentRepo: commentRepo,
	}
}

// CreateComment 创建评论
func (s *CommentApplicationService) CreateComment(ctx context.Context, req *dto.CommentCreateRequest) (*dto.CommentResponse, error) {
	userCtx, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("未授权访问")
	}

	comment := &entity.Comment{
		UserID:   uint64(userCtx.UserID),
		RecordID: req.RecordID,
		ParentID: req.ParentID,
		Content:  req.Content,
	}

	// 如果是回复，验证父评论存在
	if req.ParentID > 0 {
		parentComment, err := s.commentRepo.GetByID(ctx, req.ParentID)
		if err != nil {
			return nil, err
		}
		if parentComment == nil {
			return nil, fmt.Errorf("父评论不存在")
		}
		// 确保回复的是同一个记录下的评论
		if parentComment.RecordID != req.RecordID {
			return nil, fmt.Errorf("回复的评论不属于指定记录")
		}
	}

	if err := s.commentRepo.Create(ctx, comment); err != nil {
		return nil, err
	}

	return s.convertToCommentResponse(comment), nil
}

// GetComment 获取评论详情
func (s *CommentApplicationService) GetComment(ctx context.Context, commentID uint64) (*dto.CommentResponse, error) {
	comment, err := s.commentRepo.GetByID(ctx, commentID)
	if err != nil {
		return nil, err
	}

	return s.convertToCommentResponse(comment), nil
}

// GetCommentsByRecord 获取记录的评论列表
func (s *CommentApplicationService) GetCommentsByRecord(ctx context.Context, req *dto.CommentListRequest) (*dto.CommentListResponse, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 20
	}

	offset := (req.Page - 1) * req.Size

	var comments []*entity.Comment
	var total int64
	var err error

	if req.ParentID > 0 {
		// 获取回复列表
		comments, err = s.commentRepo.GetRepliesByParentID(ctx, req.ParentID, req.Size, offset)
		if err != nil {
			return nil, err
		}
		total, err = s.commentRepo.CountRepliesByParentID(ctx, req.ParentID)
		if err != nil {
			return nil, err
		}
	} else {
		// 获取顶级评论
		comments, err = s.commentRepo.GetTopLevelComments(ctx, req.RecordID, req.Size, offset)
		if err != nil {
			return nil, err
		}
		total, err = s.commentRepo.CountByRecordID(ctx, req.RecordID)
		if err != nil {
			return nil, err
		}
	}

	var commentResponses []*dto.CommentResponse
	for _, comment := range comments {
		commentResponses = append(commentResponses, s.convertToCommentResponse(comment))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.Size)))

	return &dto.CommentListResponse{
		Comments:   commentResponses,
		Total:      total,
		Page:       req.Page,
		Size:       req.Size,
		TotalPages: totalPages,
	}, nil
}

// UpdateComment 更新评论
func (s *CommentApplicationService) UpdateComment(ctx context.Context, req *dto.CommentUpdateRequest) (*dto.CommentResponse, error) {
	userCtx, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("未授权访问")
	}

	comment, err := s.commentRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !comment.IsOwner(uint64(userCtx.UserID)) {
		return nil, fmt.Errorf("无权限修改此评论")
	}

	comment.Content = req.Content
	comment.UpdatedAt = time.Now()

	if err := s.commentRepo.Update(ctx, comment); err != nil {
		return nil, err
	}

	return s.convertToCommentResponse(comment), nil
}

// DeleteComment 删除评论
func (s *CommentApplicationService) DeleteComment(ctx context.Context, req *dto.CommentDeleteRequest) error {
	userCtx, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("未授权访问")
	}

	comment, err := s.commentRepo.GetByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 检查权限
	if !comment.IsOwner(uint64(userCtx.UserID)) {
		return fmt.Errorf("无权限删除此评论")
	}

	return s.commentRepo.Delete(ctx, req.ID)
}

// LikeComment 点赞评论
func (s *CommentApplicationService) LikeComment(ctx context.Context, req *dto.CommentLikeRequest) error {
	userCtx, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("未授权访问")
	}

	// 检查评论是否存在
	comment, err := s.commentRepo.GetByID(ctx, req.CommentID)
	if err != nil {
		return err
	}
	if comment == nil {
		return fmt.Errorf("评论不存在")
	}

	// TODO: 实现点赞逻辑，需要注入likeRepo
	_ = userCtx // 暂时避免未使用变量警告
	
	// 更新评论点赞数
	return s.commentRepo.UpdateLikeCount(ctx, req.CommentID, true)
}

// UnlikeComment 取消点赞评论
func (s *CommentApplicationService) UnlikeComment(ctx context.Context, req *dto.CommentLikeRequest) error {
	userCtx, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("未授权访问")
	}

	// TODO: 实现取消点赞逻辑，需要注入likeRepo
	_ = userCtx // 暂时避免未使用变量警告

	// 更新评论点赞数
	return s.commentRepo.UpdateLikeCount(ctx, req.CommentID, false)
}

// GetUserComments 获取用户的评论列表
func (s *CommentApplicationService) GetUserComments(ctx context.Context, userID uint64, page, size int) (*dto.CommentListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	offset := (page - 1) * size

	comments, err := s.commentRepo.GetByUserID(ctx, userID, size, offset)
	if err != nil {
		return nil, err
	}

	var commentResponses []*dto.CommentResponse
	for _, comment := range comments {
		commentResponses = append(commentResponses, s.convertToCommentResponse(comment))
	}

	// 这里简化处理，实际可能需要单独的计数方法
	total := int64(len(comments))
	totalPages := int(math.Ceil(float64(total) / float64(size)))

	return &dto.CommentListResponse{
		Comments:   commentResponses,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}, nil
}

// convertToCommentResponse 转换为评论响应
func (s *CommentApplicationService) convertToCommentResponse(comment *entity.Comment) *dto.CommentResponse {
	return &dto.CommentResponse{
		ID:         comment.ID,
		UserID:     comment.UserID,
		RecordID:   comment.RecordID,
		ParentID:   comment.ParentID,
		Content:    comment.Content,
		LikeCount:  comment.LikeCount,
		ReplyCount: comment.ReplyCount,
		CreatedAt:  comment.CreatedAt,
		UpdatedAt:  comment.UpdatedAt,
	}
}
