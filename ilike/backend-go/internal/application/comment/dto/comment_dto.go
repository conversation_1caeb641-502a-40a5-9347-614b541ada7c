package dto

import "time"

// CommentCreateRequest 创建评论请求
type CommentCreateRequest struct {
	RecordID uint64 `json:"record_id" binding:"required"`
	ParentID uint64 `json:"parent_id,omitempty"`
	Content  string `json:"content" binding:"required"`
}

// CommentUpdateRequest 更新评论请求
type CommentUpdateRequest struct {
	ID      uint64 `json:"id" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// CommentResponse 评论响应
type CommentResponse struct {
	ID         uint64    `json:"id"`
	UserID     uint64    `json:"user_id"`
	RecordID   uint64    `json:"record_id"`
	ParentID   uint64    `json:"parent_id"`
	Content    string    `json:"content"`
	LikeCount  uint32    `json:"like_count"`
	ReplyCount uint32    `json:"reply_count"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// CommentListRequest 评论列表请求
type CommentListRequest struct {
	RecordID uint64 `json:"record_id"`
	ParentID uint64 `json:"parent_id,omitempty"` // 如果指定，获取回复列表
	Page     int    `json:"page"`
	Size     int    `json:"size"`
}

// CommentListResponse 评论列表响应
type CommentListResponse struct {
	Comments   []*CommentResponse `json:"comments"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Size       int                `json:"size"`
	TotalPages int                `json:"total_pages"`
}

// CommentLikeRequest 评论点赞请求
type CommentLikeRequest struct {
	CommentID uint64 `json:"comment_id" binding:"required"`
}

// CommentDeleteRequest 删除评论请求
type CommentDeleteRequest struct {
	ID uint64 `json:"id" binding:"required"`
}