package errors

import (
	"fmt"
)

// iLike模块错误码范围: 120000-129999
const (
	// 通用错误 (120000-120099)
	CodeResourceNotFound      = 120000 // 资源不存在 - 记录、标签、分类等资源不存在
	CodeResourceAlreadyExists = 120001 // 资源已存在 - 记录、标签等资源已存在
	CodePermissionDenied      = 120002 // 权限不足 - 用户无权访问或操作该资源
	CodeValidationFailed      = 120003 // 验证失败 - 输入参数验证失败
	CodeOperationNotAllowed   = 120004 // 操作不被允许 - 业务规则不允许该操作

	// 记录相关错误 (120100-120199)
	CodeRecordInvalidTitle   = 120100 // 记录标题无效 - 标题为空、过长或包含非法字符
	CodeRecordInvalidContent = 120101 // 记录内容无效 - 内容格式错误或过长
	CodeRecordInvalidType    = 120102 // 记录类型无效 - 记录类型不存在或已禁用
	CodeRecordInvalidSchema  = 120103 // 记录Schema无效 - Schema格式错误或版本不匹配
	CodeRecordAlreadyLiked   = 120104 // 记录已点赞 - 用户已对该记录点赞
	CodeRecordNotLiked       = 120105 // 记录未点赞 - 用户未对该记录点赞

	// 标签相关错误 (120200-120299)
	CodeTagInvalidName     = 120200 // 标签名称无效 - 标签名为空、过长或包含非法字符
	CodeTagAlreadyAssigned = 120201 // 标签已分配 - 该标签已分配给记录
	CodeTagNotAssigned     = 120202 // 标签未分配 - 该标签未分配给记录
	CodeTagTooMany         = 120203 // 标签数量过多 - 记录关联的标签数量超过限制

	// 评论相关错误 (120300-120399)
	CodeCommentInvalidContent = 120300 // 评论内容无效 - 评论内容为空、过长或包含非法字符
	CodeCommentAlreadyLiked   = 120301 // 评论已点赞 - 用户已对该评论点赞
	CodeCommentNotLiked       = 120302 // 评论未点赞 - 用户未对该评论点赞

	// 文件相关错误 (120400-120499)
	CodeFileUploadFailed  = 120400 // 文件上传失败 - 文件上传过程中发生错误
	CodeFileInvalidFormat = 120401 // 文件格式无效 - 文件格式不被支持
	CodeFileTooLarge      = 120402 // 文件过大 - 文件大小超过限制
	CodeFileNotFound      = 120403 // 文件不存在 - 指定的文件不存在

	// 系统错误 (120900-120999)
	CodeStorageError       = 120900 // 存储错误 - 数据库操作失败
	CodeCacheError         = 120901 // 缓存错误 - 缓存操作失败
	CodeIntegrationFailed  = 120902 // 系统集成失败 - 调用外部服务失败
	CodeRateLimitExceeded  = 120903 // 频率限制 - 请求频率超过限制
	CodeServiceUnavailable = 120904 // 服务不可用 - 系统暂时不可用
)

// 错误消息映射
var errorMessages = map[int]string{
	// 通用错误消息
	CodeResourceNotFound:      "资源不存在",
	CodeResourceAlreadyExists: "资源已存在",
	CodePermissionDenied:      "权限不足",
	CodeValidationFailed:      "输入验证失败",
	CodeOperationNotAllowed:   "操作不被允许",

	// 记录相关错误消息
	CodeRecordInvalidTitle:   "记录标题无效",
	CodeRecordInvalidContent: "记录内容无效",
	CodeRecordInvalidType:    "记录类型无效",
	CodeRecordInvalidSchema:  "记录Schema无效",
	CodeRecordAlreadyLiked:   "已点赞过此记录",
	CodeRecordNotLiked:       "未点赞此记录",

	// 标签相关错误消息
	CodeTagInvalidName:     "标签名称无效",
	CodeTagAlreadyAssigned: "标签已分配",
	CodeTagNotAssigned:     "标签未分配",
	CodeTagTooMany:         "标签数量过多",

	// 评论相关错误消息
	CodeCommentInvalidContent: "评论内容无效",
	CodeCommentAlreadyLiked:   "已点赞过此评论",
	CodeCommentNotLiked:       "未点赞此评论",

	// 文件相关错误消息
	CodeFileUploadFailed:  "文件上传失败",
	CodeFileInvalidFormat: "文件格式无效",
	CodeFileTooLarge:      "文件过大",
	CodeFileNotFound:      "文件不存在",

	// 系统错误消息
	CodeStorageError:       "存储错误",
	CodeCacheError:         "缓存错误",
	CodeIntegrationFailed:  "系统集成失败",
	CodeRateLimitExceeded:  "请求频率超限，请稍后重试",
	CodeServiceUnavailable: "服务暂时不可用，请稍后重试",
}

// ILikeError iLike模块自定义错误
type ILikeError struct {
	Code    int    `json:"code"`    // 业务错误码
	Message string `json:"message"` // 用户友好的错误消息
	Details string `json:"details"` // 内部详细错误信息（可选）
}

// Error 实现 error 接口
func (e *ILikeError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s", e.Message, e.Details)
	}
	return e.Message
}

// GetCode 获取错误码
func (e *ILikeError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
func (e *ILikeError) GetMessage() string {
	return e.Message
}

// GetDetails 获取详细错误信息
func (e *ILikeError) GetDetails() string {
	return e.Details
}

// New 创建新的iLike模块错误
func New(code int, details ...string) *ILikeError {
	message := errorMessages[code]
	if message == "" {
		message = "操作失败，请稍后重试"
	}

	var detail string
	if len(details) > 0 {
		detail = details[0]
	}

	return &ILikeError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// NewWithMessage 创建带自定义消息的iLike模块错误
func NewWithMessage(code int, message string, details ...string) *ILikeError {
	var detail string
	if len(details) > 0 {
		detail = details[0]
	}

	return &ILikeError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// 便捷错误创建函数

// NewResourceNotFoundError 创建资源不存在错误
func NewResourceNotFoundError(resource string) *ILikeError {
	return New(CodeResourceNotFound, fmt.Sprintf("资源类型: %s", resource))
}

// NewResourceAlreadyExistsError 创建资源已存在错误
func NewResourceAlreadyExistsError(resource string) *ILikeError {
	return New(CodeResourceAlreadyExists, fmt.Sprintf("资源类型: %s", resource))
}

// NewPermissionDeniedError 创建权限不足错误
func NewPermissionDeniedError(operation string) *ILikeError {
	return New(CodePermissionDenied, fmt.Sprintf("操作: %s", operation))
}

// NewValidationFailedError 创建验证失败错误
func NewValidationFailedError(field, reason string) *ILikeError {
	return New(CodeValidationFailed, fmt.Sprintf("字段: %s, 原因: %s", field, reason))
}

// NewRecordInvalidTitleError 创建记录标题无效错误
func NewRecordInvalidTitleError(reason string) *ILikeError {
	return New(CodeRecordInvalidTitle, reason)
}

// NewRecordInvalidContentError 创建记录内容无效错误
func NewRecordInvalidContentError(reason string) *ILikeError {
	return New(CodeRecordInvalidContent, reason)
}

// NewRecordAlreadyLikedError 创建记录已点赞错误
func NewRecordAlreadyLikedError(recordID interface{}) *ILikeError {
	return New(CodeRecordAlreadyLiked, fmt.Sprintf("记录ID: %v", recordID))
}

// NewRecordNotLikedError 创建记录未点赞错误
func NewRecordNotLikedError(recordID interface{}) *ILikeError {
	return New(CodeRecordNotLiked, fmt.Sprintf("记录ID: %v", recordID))
}

// NewTagInvalidNameError 创建标签名称无效错误
func NewTagInvalidNameError(reason string) *ILikeError {
	return New(CodeTagInvalidName, reason)
}

// NewTagTooManyError 创建标签过多错误
func NewTagTooManyError(maxTags int) *ILikeError {
	return New(CodeTagTooMany, fmt.Sprintf("最大标签数: %d", maxTags))
}

// NewCommentInvalidContentError 创建评论内容无效错误
func NewCommentInvalidContentError(reason string) *ILikeError {
	return New(CodeCommentInvalidContent, reason)
}

// NewCommentNotFoundError 创建评论不存在错误
func NewCommentNotFoundError() *ILikeError {
	return New(CodeResourceNotFound, "评论不存在")
}

// NewFileUploadFailedError 创建文件上传失败错误
func NewFileUploadFailedError(reason string) *ILikeError {
	return New(CodeFileUploadFailed, reason)
}

// NewFileTooLargeError 创建文件过大错误
func NewFileTooLargeError(size, maxSize int64) *ILikeError {
	return New(CodeFileTooLarge, fmt.Sprintf("文件大小: %d bytes, 最大限制: %d bytes", size, maxSize))
}

// NewStorageError 创建存储错误
func NewStorageError(details string) *ILikeError {
	return New(CodeStorageError, details)
}

// NewIntegrationFailedError 创建系统集成失败错误
func NewIntegrationFailedError(details string) *ILikeError {
	return New(CodeIntegrationFailed, details)
}

// NewRateLimitExceededError 创建频率限制错误
func NewRateLimitExceededError(limit int, window string) *ILikeError {
	return New(CodeRateLimitExceeded, fmt.Sprintf("限制: %d 次/%s", limit, window))
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(reason string) *ILikeError {
	return New(CodeServiceUnavailable, reason)
}

// IsILikeError 判断是否为iLike模块错误码
func IsILikeError(code int) bool {
	return code >= 120000 && code <= 129999
}

// GetErrorMessage 获取错误码对应的错误消息
func GetErrorMessage(code int) string {
	if message, exists := errorMessages[code]; exists {
		return message
	}
	return "操作失败，请稍后重试"
}
