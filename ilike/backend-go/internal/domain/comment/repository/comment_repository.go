package repository

import (
	"context"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/comment/entity"
)

// CommentRepository 评论仓储接口
type CommentRepository interface {
	// Create 创建评论
	Create(ctx context.Context, comment *entity.Comment) error
	
	// GetByID 通过ID获取评论
	GetByID(ctx context.Context, id uint64) (*entity.Comment, error)
	
	// GetByRecordID 获取记录的评论列表
	GetByRecordID(ctx context.Context, recordID uint64, limit, offset int) ([]*entity.Comment, error)
	
	// GetRepliesByParentID 获取父评论的回复列表
	GetRepliesByParentID(ctx context.Context, parentID uint64, limit, offset int) ([]*entity.Comment, error)
	
	// GetByUserID 获取用户的评论列表
	GetByUserID(ctx context.Context, userID uint64, limit, offset int) ([]*entity.Comment, error)
	
	// Update 更新评论
	Update(ctx context.Context, comment *entity.Comment) error
	
	// Delete 软删除评论
	Delete(ctx context.Context, id uint64) error
	
	// UpdateLikeCount 更新评论点赞数
	UpdateLikeCount(ctx context.Context, commentID uint64, increment bool) error
	
	// UpdateReplyCount 更新回复数
	UpdateReplyCount(ctx context.Context, commentID uint64, increment bool) error
	
	// CountByRecordID 获取记录的评论总数
	CountByRecordID(ctx context.Context, recordID uint64) (int64, error)
	
	// CountRepliesByParentID 获取父评论的回复总数
	CountRepliesByParentID(ctx context.Context, parentID uint64) (int64, error)
	
	// GetTopLevelComments 获取顶级评论（非回复）
	GetTopLevelComments(ctx context.Context, recordID uint64, limit, offset int) ([]*entity.Comment, error)
}