package entity

import (
	"time"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
)

// Comment 评论实体
type Comment struct {
	ID         uint64     `json:"id" db:"id"`
	UserID     uint64     `json:"user_id" db:"user_id"`
	RecordID   uint64     `json:"record_id" db:"record_id"`
	ParentID   uint64     `json:"parent_id" db:"parent_id"`
	Content    string     `json:"content" db:"content"`
	LikeCount  uint32     `json:"like_count" db:"like_count"`
	ReplyCount uint32     `json:"reply_count" db:"reply_count"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at" db:"deleted_at"`
}

// ValidateCreate 验证评论创建
func (c *Comment) ValidateCreate() error {
	if c.UserID == 0 {
		return errors.NewValidationFailedError("用户ID", "不能为空")
	}

	if c.RecordID == 0 {
		return errors.NewValidationFailedError("记录ID", "不能为空")
	}

	if c.Content == "" {
		return errors.NewCommentInvalidContentError("评论内容不能为空")
	}

	if len(c.Content) > 1000 {
		return errors.NewCommentInvalidContentError("评论内容长度不能超过1000个字符")
	}

	return nil
}

// IsReply 是否为回复评论
func (c *Comment) IsReply() bool {
	return c.ParentID > 0
}

// IsOwner 检查是否为评论所有者
func (c *Comment) IsOwner(userID uint64) bool {
	return c.UserID == userID
}

// IncrementLikeCount 增加点赞数
func (c *Comment) IncrementLikeCount() {
	c.LikeCount++
}

// DecrementLikeCount 减少点赞数
func (c *Comment) DecrementLikeCount() {
	if c.LikeCount > 0 {
		c.LikeCount--
	}
}

// IncrementReplyCount 增加回复数
func (c *Comment) IncrementReplyCount() {
	c.ReplyCount++
}

// DecrementReplyCount 减少回复数
func (c *Comment) DecrementReplyCount() {
	if c.ReplyCount > 0 {
		c.ReplyCount--
	}
}
