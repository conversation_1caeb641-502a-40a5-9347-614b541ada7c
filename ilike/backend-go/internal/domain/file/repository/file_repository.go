package repository

import (
	"context"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/file/entity"
)

// FileRepository 文件仓储接口
type FileRepository interface {
	// Create 创建文件记录
	Create(ctx context.Context, file *entity.File) error
	
	// GetByID 通过ID获取文件
	GetByID(ctx context.Context, id uint64) (*entity.File, error)
	
	// GetByHashValue 通过哈希值获取文件
	GetByHashValue(ctx context.Context, hashValue string) (*entity.File, error)
	
	// GetByUserID 获取用户的文件列表
	GetByUserID(ctx context.Context, userID uint64, limit, offset int) ([]*entity.File, error)
	
	// Update 更新文件信息
	Update(ctx context.Context, file *entity.File) error
	
	// Delete 软删除文件
	Delete(ctx context.Context, id uint64) error
	
	// HardDelete 硬删除文件记录
	HardDelete(ctx context.Context, id uint64) error
	
	// CountByUserID 获取用户文件总数
	CountByUserID(ctx context.Context, userID uint64) (int64, error)
	
	// GetPublicFiles 获取公开文件列表
	GetPublicFiles(ctx context.Context, limit, offset int) ([]*entity.File, error)
}