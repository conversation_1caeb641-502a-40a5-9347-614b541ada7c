package repository

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/persistence/models"

	"gitee.com/heiyee/platforms/pkg/logiface"

	"gorm.io/gorm"
)

// SearchHistoryRepositoryImpl 搜索历史仓储实现
type SearchHistoryRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSearchHistoryRepository 创建搜索历史仓储实例
func NewSearchHistoryRepository(db *gorm.DB, logger logiface.Logger) *SearchHistoryRepositoryImpl {
	return &SearchHistoryRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// SaveSearchHistory 保存搜索历史
func (r *SearchHistoryRepositoryImpl) SaveSearchHistory(ctx context.Context, userID int64, keyword, searchType string, resultCount int64) error {
	// 检查是否已存在相同的搜索记录（24小时内）
	var existingHistory models.SearchHistory
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND keyword = ? AND type = ? AND created_at > ?",
			userID, keyword, searchType, time.Now().Add(-24*time.Hour)).
		First(&existingHistory).Error

	if err == nil {
		// 更新现有记录
		existingHistory.ResultCount = resultCount
		existingHistory.UpdatedAt = time.Now()
		if err := r.db.WithContext(ctx).Save(&existingHistory).Error; err != nil {
			r.logger.Error(ctx, "更新搜索历史失败", logiface.Error(err))
			return errors.NewStorageError(fmt.Sprintf("search history update failed: %v", err))
		}
		return nil
	}

	// 创建新的搜索历史记录
	history := &models.SearchHistory{
		UserID:      userID,
		Keyword:     keyword,
		Type:        searchType,
		ResultCount: resultCount,
	}

	if err := r.db.WithContext(ctx).Create(history).Error; err != nil {
		r.logger.Error(ctx, "保存搜索历史失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("search history save failed: %v", err))
	}

	// 清理旧的搜索历史（保留最近100条）
	go r.cleanupOldHistory(userID)

	return nil
}

// GetSearchHistory 获取用户搜索历史
func (r *SearchHistoryRepositoryImpl) GetSearchHistory(ctx context.Context, userID int64, limit int) ([]string, error) {
	if limit <= 0 {
		limit = 20
	}

	var histories []models.SearchHistory
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Limit(limit).
		Find(&histories).Error; err != nil {
		r.logger.Error(ctx, "获取搜索历史失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("search history retrieval failed: %v", err))
	}

	keywords := make([]string, len(histories))
	for i, history := range histories {
		keywords[i] = history.Keyword
	}

	return keywords, nil
}

// GetPopularSearchKeywords 获取热门搜索关键词
func (r *SearchHistoryRepositoryImpl) GetPopularSearchKeywords(ctx context.Context, limit int) ([]string, error) {
	if limit <= 0 {
		limit = 10
	}

	var results []struct {
		Keyword string
		Count   int64
	}

	// 统计最近7天的搜索关键词
	if err := r.db.WithContext(ctx).
		Model(&models.SearchHistory{}).
		Select("keyword, COUNT(*) as count").
		Where("created_at > ?", time.Now().Add(-7*24*time.Hour)).
		Group("keyword").
		Order("count DESC").
		Limit(limit).
		Find(&results).Error; err != nil {
		r.logger.Error(ctx, "获取热门搜索关键词失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("popular search keywords retrieval failed: %v", err))
	}

	keywords := make([]string, len(results))
	for i, result := range results {
		keywords[i] = result.Keyword
	}

	return keywords, nil
}

// DeleteSearchHistory 删除用户搜索历史
func (r *SearchHistoryRepositoryImpl) DeleteSearchHistory(ctx context.Context, userID int64, keyword string) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND keyword = ?", userID, keyword).
		Delete(&models.SearchHistory{}).Error; err != nil {
		r.logger.Error(ctx, "删除搜索历史失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("search history deletion failed: %v", err))
	}
	return nil
}

// ClearSearchHistory 清空用户搜索历史
func (r *SearchHistoryRepositoryImpl) ClearSearchHistory(ctx context.Context, userID int64) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&models.SearchHistory{}).Error; err != nil {
		r.logger.Error(ctx, "清空搜索历史失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("search history clear failed: %v", err))
	}
	return nil
}

// cleanupOldHistory 清理旧的搜索历史（异步执行）
func (r *SearchHistoryRepositoryImpl) cleanupOldHistory(userID int64) {
	ctx := context.Background()

	// 获取用户搜索历史总数
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.SearchHistory{}).
		Where("user_id = ?", userID).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "获取搜索历史总数失败", logiface.Error(err))
		return
	}

	// 如果超过100条，删除最旧的记录
	if count > 100 {
		var oldHistories []models.SearchHistory
		if err := r.db.WithContext(ctx).
			Where("user_id = ?", userID).
			Order("created_at ASC").
			Limit(int(count - 100)).
			Find(&oldHistories).Error; err != nil {
			r.logger.Error(ctx, "获取旧搜索历史失败", logiface.Error(err))
			return
		}

		if len(oldHistories) > 0 {
			ids := make([]int64, len(oldHistories))
			for i, history := range oldHistories {
				ids[i] = history.ID
			}

			if err := r.db.WithContext(ctx).
				Where("id IN ?", ids).
				Delete(&models.SearchHistory{}).Error; err != nil {
				r.logger.Error(ctx, "删除旧搜索历史失败", logiface.Error(err))
			}
		}
	}
}

// GetSearchStatistics 获取搜索统计信息
func (r *SearchHistoryRepositoryImpl) GetSearchStatistics(ctx context.Context, userID int64, days int) (map[string]interface{}, error) {
	if days <= 0 {
		days = 7
	}

	startTime := time.Now().Add(-time.Duration(days) * 24 * time.Hour)

	// 获取搜索总数
	var totalSearches int64
	if err := r.db.WithContext(ctx).
		Model(&models.SearchHistory{}).
		Where("user_id = ? AND created_at > ?", userID, startTime).
		Count(&totalSearches).Error; err != nil {
		r.logger.Error(ctx, "获取搜索总数失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("search count retrieval failed: %v", err))
	}

	// 获取搜索类型分布
	var typeStats []struct {
		Type  string
		Count int64
	}
	if err := r.db.WithContext(ctx).
		Model(&models.SearchHistory{}).
		Select("type, COUNT(*) as count").
		Where("user_id = ? AND created_at > ?", userID, startTime).
		Group("type").
		Find(&typeStats).Error; err != nil {
		r.logger.Error(ctx, "获取搜索类型分布失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("search type distribution retrieval failed: %v", err))
	}

	// 获取最常搜索的关键词
	var topKeywords []struct {
		Keyword string
		Count   int64
	}
	if err := r.db.WithContext(ctx).
		Model(&models.SearchHistory{}).
		Select("keyword, COUNT(*) as count").
		Where("user_id = ? AND created_at > ?", userID, startTime).
		Group("keyword").
		Order("count DESC").
		Limit(10).
		Find(&topKeywords).Error; err != nil {
		r.logger.Error(ctx, "获取热门关键词失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("popular keywords retrieval failed: %v", err))
	}

	statistics := map[string]interface{}{
		"total_searches": totalSearches,
		"type_stats":     typeStats,
		"top_keywords":   topKeywords,
		"period_days":    days,
	}

	return statistics, nil
}
