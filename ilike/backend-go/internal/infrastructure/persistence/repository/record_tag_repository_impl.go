package repository

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/persistence/models"

	"gitee.com/heiyee/platforms/pkg/logiface"

	"gorm.io/gorm"
)

// RecordTagRepositoryImpl 记录标签关联仓储实现
type RecordTagRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewRecordTagRepository 创建记录标签关联仓储实例
func NewRecordTagRepository(db *gorm.DB, logger logiface.Logger) *RecordTagRepositoryImpl {
	return &RecordTagRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// AddTagsToRecord 为记录添加标签
func (r *RecordTagRepositoryImpl) AddTagsToRecord(ctx context.Context, recordID int64, tagIDs []int64) error {
	if len(tagIDs) == 0 {
		return nil
	}

	// 准备批量插入数据
	recordTags := make([]models.RecordTag, len(tagIDs))
	for i, tagID := range tagIDs {
		recordTags[i] = models.RecordTag{
			RecordID: recordID,
			TagID:    tagID,
		}
	}

	// 批量插入，忽略重复记录
	if err := r.db.WithContext(ctx).Create(&recordTags).Error; err != nil {
		r.logger.Error(ctx, "为记录添加标签失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("record tag addition failed: %v", err))
	}

	// 更新标签使用次数
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id IN ?", tagIDs).
		UpdateColumn("use_count", gorm.Expr("use_count + 1")).Error; err != nil {
		r.logger.Error(ctx, "更新标签使用次数失败", logiface.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	return nil
}

// RemoveTagsFromRecord 从记录中移除标签
func (r *RecordTagRepositoryImpl) RemoveTagsFromRecord(ctx context.Context, recordID int64, tagIDs []int64) error {
	if len(tagIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Where("record_id = ? AND tag_id IN ?", recordID, tagIDs).
		Delete(&models.RecordTag{}).Error; err != nil {
		r.logger.Error(ctx, "从记录中移除标签失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("record tag removal failed: %v", err))
	}

	// 更新标签使用次数
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id IN ? AND use_count > 0", tagIDs).
		UpdateColumn("use_count", gorm.Expr("use_count - 1")).Error; err != nil {
		r.logger.Error(ctx, "更新标签使用次数失败", logiface.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	return nil
}

// ReplaceRecordTags 替换记录的所有标签
func (r *RecordTagRepositoryImpl) ReplaceRecordTags(ctx context.Context, recordID int64, tagIDs []int64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前标签
		var currentTags []models.RecordTag
		if err := tx.Where("record_id = ?", recordID).Find(&currentTags).Error; err != nil {
			return errors.NewStorageError(fmt.Sprintf("current tags retrieval failed: %v", err))
		}

		// 删除所有现有标签关联
		if err := tx.Where("record_id = ?", recordID).Delete(&models.RecordTag{}).Error; err != nil {
			return errors.NewStorageError(fmt.Sprintf("existing tag associations deletion failed: %v", err))
		}

		// 更新旧标签使用次数
		if len(currentTags) > 0 {
			oldTagIDs := make([]int64, len(currentTags))
			for i, tag := range currentTags {
				oldTagIDs[i] = tag.TagID
			}
			if err := tx.Model(&models.Tag{}).
				Where("id IN ? AND use_count > 0", oldTagIDs).
				UpdateColumn("use_count", gorm.Expr("use_count - 1")).Error; err != nil {
				r.logger.Error(ctx, "更新旧标签使用次数失败", logiface.Error(err))
			}
		}

		// 添加新标签
		if len(tagIDs) > 0 {
			recordTags := make([]models.RecordTag, len(tagIDs))
			for i, tagID := range tagIDs {
				recordTags[i] = models.RecordTag{
					RecordID: recordID,
					TagID:    tagID,
				}
			}

			if err := tx.Create(&recordTags).Error; err != nil {
				return errors.NewStorageError(fmt.Sprintf("new tag associations addition failed: %v", err))
			}

			// 更新新标签使用次数
			if err := tx.Model(&models.Tag{}).
				Where("id IN ?", tagIDs).
				UpdateColumn("use_count", gorm.Expr("use_count + 1")).Error; err != nil {
				r.logger.Error(ctx, "更新新标签使用次数失败", logiface.Error(err))
			}
		}

		return nil
	})
}

// GetRecordTags 获取记录的标签列表
func (r *RecordTagRepositoryImpl) GetRecordTags(ctx context.Context, recordID int64) ([]int64, error) {
	var recordTags []models.RecordTag
	if err := r.db.WithContext(ctx).
		Where("record_id = ?", recordID).
		Find(&recordTags).Error; err != nil {
		r.logger.Error(ctx, "获取记录标签失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("record tags retrieval failed: %v", err))
	}

	tagIDs := make([]int64, len(recordTags))
	for i, tag := range recordTags {
		tagIDs[i] = tag.TagID
	}

	return tagIDs, nil
}

// GetTagRecords 获取标签关联的记录列表
func (r *RecordTagRepositoryImpl) GetTagRecords(ctx context.Context, tagID int64, limit, offset int) ([]int64, int64, error) {
	var total int64
	var recordTags []models.RecordTag

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&models.RecordTag{}).
		Where("tag_id = ?", tagID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取标签记录总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tag records count retrieval failed: %v", err))
	}

	// 获取记录列表
	if err := r.db.WithContext(ctx).
		Where("tag_id = ?", tagID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&recordTags).Error; err != nil {
		r.logger.Error(ctx, "获取标签记录列表失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tag records list retrieval failed: %v", err))
	}

	recordIDs := make([]int64, len(recordTags))
	for i, tag := range recordTags {
		recordIDs[i] = tag.RecordID
	}

	return recordIDs, total, nil
}

// DeleteRecordTags 删除记录的所有标签关联
func (r *RecordTagRepositoryImpl) DeleteRecordTags(ctx context.Context, recordID int64) error {
	// 获取当前标签
	var currentTags []models.RecordTag
	if err := r.db.WithContext(ctx).Where("record_id = ?", recordID).Find(&currentTags).Error; err != nil {
		r.logger.Error(ctx, "获取当前标签失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("current tags retrieval failed: %v", err))
	}

	if len(currentTags) == 0 {
		return nil
	}

	// 删除标签关联
	if err := r.db.WithContext(ctx).
		Where("record_id = ?", recordID).
		Delete(&models.RecordTag{}).Error; err != nil {
		r.logger.Error(ctx, "删除记录标签关联失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("record tag associations deletion failed: %v", err))
	}

	// 更新标签使用次数
	tagIDs := make([]int64, len(currentTags))
	for i, tag := range currentTags {
		tagIDs[i] = tag.TagID
	}

	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id IN ? AND use_count > 0", tagIDs).
		UpdateColumn("use_count", gorm.Expr("use_count - 1")).Error; err != nil {
		r.logger.Error(ctx, "更新标签使用次数失败", logiface.Error(err))
		// 这里不返回错误，因为主要操作已经成功
	}

	return nil
}

// BatchDeleteRecordTags 批量删除记录的标签关联
func (r *RecordTagRepositoryImpl) BatchDeleteRecordTags(ctx context.Context, recordIDs []int64) error {
	if len(recordIDs) == 0 {
		return nil
	}

	// 获取所有要删除的标签关联
	var recordTags []models.RecordTag
	if err := r.db.WithContext(ctx).
		Where("record_id IN ?", recordIDs).
		Find(&recordTags).Error; err != nil {
		r.logger.Error(ctx, "获取要删除的标签关联失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("tag associations to delete retrieval failed: %v", err))
	}

	if len(recordTags) == 0 {
		return nil
	}

	// 删除标签关联
	if err := r.db.WithContext(ctx).
		Where("record_id IN ?", recordIDs).
		Delete(&models.RecordTag{}).Error; err != nil {
		r.logger.Error(ctx, "批量删除记录标签关联失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("batch record tag associations deletion failed: %v", err))
	}

	// 统计每个标签的使用次数变化
	tagCountMap := make(map[int64]int)
	for _, tag := range recordTags {
		tagCountMap[tag.TagID]++
	}

	// 更新标签使用次数
	for tagID, count := range tagCountMap {
		if err := r.db.WithContext(ctx).Model(&models.Tag{}).
			Where("id = ? AND use_count >= ?", tagID, count).
			UpdateColumn("use_count", gorm.Expr("use_count - ?", count)).Error; err != nil {
			r.logger.Error(ctx, "更新标签使用次数失败", logiface.Error(err))
			// 这里不返回错误，因为主要操作已经成功
		}
	}

	return nil
}
