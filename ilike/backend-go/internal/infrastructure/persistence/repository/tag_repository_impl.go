package repository

import (
	"context"
	"fmt"
	"gitee.com/heiyee/platforms/pkg/usercontext"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/tag/entity"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/persistence/models"

	"gitee.com/heiyee/platforms/pkg/logiface"

	"gorm.io/gorm"
)

// TagRepositoryImpl 标签仓储实现
type TagRepositoryImpl struct {
	db          *gorm.DB
	logger      logiface.Logger
	idGenerator *external.IDGeneratorClient
}

// NewTagRepository 创建标签仓储实例
func NewTagRepository(db *gorm.DB, logger logiface.Logger, idGenerator *external.IDGeneratorClient) repository.TagRepository {
	return &TagRepositoryImpl{
		db:          db,
		logger:      logger,
		idGenerator: idGenerator,
	}
}

// Create 创建标签
func (r *TagRepositoryImpl) Create(ctx context.Context, tag *entity.Tag) (*entity.Tag, error) {
	// 生成ID
	if tag.ID == 0 {
		tenantId, _ := usercontext.GetTenantID(ctx)
		id, err := r.idGenerator.GenerateID(ctx, external.IDBizTag, tenantId)
		if err != nil {
			r.logger.Error(ctx, "生成标签ID失败", logiface.Error(err))
			return nil, errors.NewIntegrationFailedError(fmt.Sprintf("tag ID generation failed: %v", err))
		}
		tag.ID = id
	}

	// 转换用户ID
	var userIDPtr *int64
	if tag.UserID != 0 {
		userIDPtr = &tag.UserID
	}

	// 转换IsSystem为int8
	var isSystem int8
	if tag.IsSystem {
		isSystem = 1
	}

	model := &models.Tag{
		ID:         int64(tag.ID),
		TagID:      tag.ID,
		Name:       tag.Name,
		UserID:     userIDPtr,
		UsageCount: int(tag.UseCount),
		IsSystem:   isSystem,
	}

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		r.logger.Error(ctx, "创建标签失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("tag creation failed: %v", err))
	}

	return r.modelToEntity(model), nil
}

// GetByID 根据ID获取标签
func (r *TagRepositoryImpl) GetByID(ctx context.Context, id int64) (*entity.Tag, error) {
	var model models.Tag
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "获取标签失败", logiface.Error(err), logiface.Int64("tag_id", id))
		return nil, errors.NewStorageError(fmt.Sprintf("tag retrieval failed: %v", err))
	}
	return r.modelToEntity(&model), nil
}

// Update 更新标签
func (r *TagRepositoryImpl) Update(ctx context.Context, tag *entity.Tag) (*entity.Tag, error) {
	// 转换用户ID
	var userIDPtr *int64
	if tag.UserID != 0 {
		userIDPtr = &tag.UserID
	}

	// 转换IsSystem为int8
	var isSystem int8
	if tag.IsSystem {
		isSystem = 1
	}

	model := &models.Tag{
		ID:         int64(tag.ID),
		TagID:      tag.ID,
		Name:       tag.Name,
		UserID:     userIDPtr,
		UsageCount: int(tag.UseCount),
		IsSystem:   isSystem,
	}

	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		r.logger.Error(ctx, "更新标签失败", logiface.Error(err), logiface.Int64("tag_id", tag.ID))
		return nil, errors.NewStorageError(fmt.Sprintf("tag update failed: %v", err))
	}

	return r.modelToEntity(model), nil
}

// Delete 删除标签
func (r *TagRepositoryImpl) Delete(ctx context.Context, id int64) error {
	if err := r.db.WithContext(ctx).Delete(&models.Tag{}, id).Error; err != nil {
		r.logger.Error(ctx, "删除标签失败", logiface.Error(err), logiface.Int64("tag_id", id))
		return errors.NewStorageError(fmt.Sprintf("tag deletion failed: %v", err))
	}
	return nil
}

// GetByUserID 获取用户创建的标签列表
func (r *TagRepositoryImpl) GetByUserID(ctx context.Context, userID int64, filters *repository.TagFilters) ([]*entity.Tag, int64, error) {
	var tags []models.Tag
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Tag{}).Where("user_id = ?", userID)

	// 应用过滤器
	if filters.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+filters.Keyword+"%")
	}
	// 移除Color过滤器，因为新模型不再包含color字段

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取用户标签总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("user tags count retrieval failed: %v", err))
	}

	// 应用分页和排序
	if filters.Size > 0 {
		offset := (filters.Page - 1) * filters.Size
		query = query.Offset(offset).Limit(filters.Size)
	}

	orderBy := "created_at"
	if filters.OrderBy != "" {
		orderBy = filters.OrderBy
	}
	direction := "DESC"
	if filters.Direction != "" {
		direction = filters.Direction
	}
	query = query.Order(fmt.Sprintf("%s %s", orderBy, direction))

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "获取用户标签列表失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("user tags list retrieval failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, total, nil
}

// GetSystemTags 获取系统标签列表
func (r *TagRepositoryImpl) GetSystemTags(ctx context.Context, filters *repository.TagFilters) ([]*entity.Tag, int64, error) {
	var tags []models.Tag
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Tag{}).Where("is_system = ? AND is_active = ?", true, true)

	// 应用过滤器
	if filters.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+filters.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取系统标签总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("system tags count retrieval failed: %v", err))
	}

	// 应用分页和排序
	if filters.Size > 0 {
		offset := (filters.Page - 1) * filters.Size
		query = query.Offset(offset).Limit(filters.Size)
	}

	orderBy := "usage_count"
	if filters.OrderBy != "" {
		orderBy = filters.OrderBy
	}
	direction := "DESC"
	if filters.Direction != "" {
		direction = filters.Direction
	}
	query = query.Order(fmt.Sprintf("%s %s", orderBy, direction))

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "获取系统标签列表失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("system tags list retrieval failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, total, nil
}

// GetPopularTags 获取热门标签
func (r *TagRepositoryImpl) GetPopularTags(ctx context.Context, limit int) ([]*entity.Tag, error) {
	var tags []models.Tag

	query := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("is_active = ?", true).
		Order("use_count DESC").
		Limit(limit)

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "获取热门标签失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("popular tags retrieval failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, nil
}

// List 获取标签列表
func (r *TagRepositoryImpl) List(ctx context.Context, filters *repository.TagFilters) ([]*entity.Tag, int64, error) {
	var tags []models.Tag
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Tag{}).Where("is_active = ?", true)

	// 应用过滤器
	if filters.UserID > 0 {
		query = query.Where("user_id = ?", filters.UserID)
	}
	if filters.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+filters.Keyword+"%")
	}
	// 移除Color过滤器，因为新模型不再包含color字段

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取标签总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tags count retrieval failed: %v", err))
	}

	// 应用分页和排序
	if filters.Size > 0 {
		offset := (filters.Page - 1) * filters.Size
		query = query.Offset(offset).Limit(filters.Size)
	}

	orderBy := "created_at"
	if filters.OrderBy != "" {
		orderBy = filters.OrderBy
	}
	direction := "DESC"
	if filters.Direction != "" {
		direction = filters.Direction
	}
	query = query.Order(fmt.Sprintf("%s %s", orderBy, direction))

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "获取标签列表失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tags list retrieval failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, total, nil
}

// Search 搜索标签
func (r *TagRepositoryImpl) Search(ctx context.Context, keyword string, filters *repository.TagFilters) ([]*entity.Tag, int64, error) {
	var tags []models.Tag
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Tag{}).Where("is_active = ?", true)

	// 搜索条件
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 应用过滤器
	if filters.UserID > 0 {
		query = query.Where("user_id = ?", filters.UserID)
	}
	if filters.Color != "" {
		query = query.Where("color = ?", filters.Color)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "搜索标签总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tag search count failed: %v", err))
	}

	// 应用分页和排序
	if filters.Size > 0 {
		offset := (filters.Page - 1) * filters.Size
		query = query.Offset(offset).Limit(filters.Size)
	}

	orderBy := "usage_count"
	if filters.OrderBy != "" {
		orderBy = filters.OrderBy
	}
	direction := "DESC"
	if filters.Direction != "" {
		direction = filters.Direction
	}
	query = query.Order(fmt.Sprintf("%s %s", orderBy, direction))

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "搜索标签失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("tag search failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, total, nil
}

// SearchByName 根据名称搜索标签
func (r *TagRepositoryImpl) SearchByName(ctx context.Context, name string, limit int) ([]*entity.Tag, error) {
	var tags []models.Tag

	query := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("is_active = ? AND name LIKE ?", true, "%"+name+"%").
		Order("use_count DESC").
		Limit(limit)

	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "根据名称搜索标签失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("tag search by name failed: %v", err))
	}

	// 转换为实体
	entities := make([]*entity.Tag, len(tags))
	for i, tag := range tags {
		entities[i] = r.modelToEntity(&tag)
	}

	return entities, nil
}

// CountByUserID 统计用户标签数量
func (r *TagRepositoryImpl) CountByUserID(ctx context.Context, userID int64) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("user_id = ? AND is_active = ?", userID, true).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "统计用户标签数量失败", logiface.Error(err))
		return 0, errors.NewStorageError(fmt.Sprintf("user tag count statistics failed: %v", err))
	}
	return count, nil
}

func (r *TagRepositoryImpl) CountTotal(ctx context.Context) (int64, error) {
	return 0, nil
}

func (r *TagRepositoryImpl) CountByUsage(ctx context.Context, minUsage int64) (int64, error) {
	return 0, nil
}

func (r *TagRepositoryImpl) IncrementUsage(ctx context.Context, id int64) error {
	return nil
}

func (r *TagRepositoryImpl) DecrementUsage(ctx context.Context, id int64) error {
	return nil
}

func (r *TagRepositoryImpl) UpdateUsageCount(ctx context.Context, id int64, count int64) error {
	return nil
}

func (r *TagRepositoryImpl) ExistsByName(ctx context.Context, name string, excludeID ...int64) (bool, error) {
	return false, nil
}

func (r *TagRepositoryImpl) ExistsByID(ctx context.Context, id int64) (bool, error) {
	return false, nil
}

func (r *TagRepositoryImpl) GetByName(ctx context.Context, name string) (*entity.Tag, error) {
	return nil, nil
}

func (r *TagRepositoryImpl) GetByIDs(ctx context.Context, ids []int64) ([]*entity.Tag, error) {
	return []*entity.Tag{}, nil
}

func (r *TagRepositoryImpl) BatchCreate(ctx context.Context, tags []*entity.Tag) ([]*entity.Tag, error) {
	return []*entity.Tag{}, nil
}

func (r *TagRepositoryImpl) BatchUpdate(ctx context.Context, tags []*entity.Tag) error {
	return nil
}

func (r *TagRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	return nil
}

// modelToEntity 将数据库模型转换为实体
func (r *TagRepositoryImpl) modelToEntity(model *models.Tag) *entity.Tag {
	// 处理用户ID
	var userID int64
	if model.UserID != nil {
		userID = *model.UserID
	}

	// 转换IsSystem为bool
	isSystem := model.IsSystem == 1

	return entity.TagFromPersistence(
		int64(model.ID),
		model.Name,
		"", // description - 不再使用
		"", // color - 不再使用
		userID,
		int64(model.UsageCount),
		isSystem,
		true, // isActive - 默认为true
		model.CreatedAt,
		model.UpdatedAt,
	)
}
