package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/file/entity"
	fileRepo "gitee.com/heiyee/platforms/ilike-backend/internal/domain/file/repository"
)

// FileRepositoryImpl 文件仓储实现
type FileRepositoryImpl struct {
	db *gorm.DB
}

// NewFileRepository 创建文件仓储实现
func NewFileRepository(db *gorm.DB) fileRepo.FileRepository {
	return &FileRepositoryImpl{
		db: db,
	}
}

// Create 创建文件记录
func (r *FileRepositoryImpl) Create(ctx context.Context, file *entity.File) error {
	if err := file.ValidateUpload(); err != nil {
		return err
	}

	now := time.Now()
	file.CreatedAt = now
	file.UpdatedAt = now

	if err := r.db.WithContext(ctx).Create(file).Error; err != nil {
		return fmt.Errorf("创建文件记录失败: %w", err)
	}

	return nil
}

// GetByID 通过ID获取文件
func (r *FileRepositoryImpl) GetByID(ctx context.Context, id uint64) (*entity.File, error) {
	var file entity.File

	err := r.db.WithContext(ctx).
		Where("id = ? AND deleted_at IS NULL", id).
		First(&file).Error

	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("文件不存在")
	}
	if err != nil {
		return nil, fmt.Errorf("获取文件失败: %w", err)
	}

	return &file, nil
}

// GetByHashValue 通过哈希值获取文件
func (r *FileRepositoryImpl) GetByHashValue(ctx context.Context, hashValue string) (*entity.File, error) {
	var file entity.File

	err := r.db.WithContext(ctx).
		Where("hash_value = ? AND deleted_at IS NULL", hashValue).
		First(&file).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil // 返回nil表示未找到，不是错误
	}
	if err != nil {
		return nil, fmt.Errorf("通过哈希值获取文件失败: %w", err)
	}

	return &file, nil
}

// GetByUserID 获取用户的文件列表
func (r *FileRepositoryImpl) GetByUserID(ctx context.Context, userID uint64, limit, offset int) ([]*entity.File, error) {
	var files []*entity.File

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&files).Error

	if err != nil {
		return nil, fmt.Errorf("获取用户文件列表失败: %w", err)
	}

	return files, nil
}

// Update 更新文件信息
func (r *FileRepositoryImpl) Update(ctx context.Context, file *entity.File) error {
	file.UpdatedAt = time.Now()

	err := r.db.WithContext(ctx).
		Where("id = ? AND deleted_at IS NULL", file.ID).
		Updates(file).Error

	if err != nil {
		return fmt.Errorf("更新文件信息失败: %w", err)
	}

	return nil
}

// Delete 软删除文件
func (r *FileRepositoryImpl) Delete(ctx context.Context, id uint64) error {
	now := time.Now()

	err := r.db.WithContext(ctx).
		Model(&entity.File{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", now).Error

	if err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}

	return nil
}

// HardDelete 硬删除文件记录
func (r *FileRepositoryImpl) HardDelete(ctx context.Context, id uint64) error {
	err := r.db.WithContext(ctx).
		Unscoped().
		Delete(&entity.File{}, id).Error

	if err != nil {
		return fmt.Errorf("硬删除文件失败: %w", err)
	}

	return nil
}

// CountByUserID 获取用户文件总数
func (r *FileRepositoryImpl) CountByUserID(ctx context.Context, userID uint64) (int64, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&entity.File{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("获取用户文件总数失败: %w", err)
	}

	return count, nil
}

// GetPublicFiles 获取公开文件列表
func (r *FileRepositoryImpl) GetPublicFiles(ctx context.Context, limit, offset int) ([]*entity.File, error) {
	var files []*entity.File

	err := r.db.WithContext(ctx).
		Where("is_public = ? AND deleted_at IS NULL", true).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&files).Error

	if err != nil {
		return nil, fmt.Errorf("获取公开文件列表失败: %w", err)
	}

	return files, nil
}
