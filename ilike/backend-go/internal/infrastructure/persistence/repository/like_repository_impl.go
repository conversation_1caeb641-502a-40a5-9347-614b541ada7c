package repository

import (
	"context"
	"database/sql"
	"fmt"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/record"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"time"
)

// LikeRepositoryImpl 点赞仓储实现
type LikeRepositoryImpl struct {
	db     *sql.DB
	logger logiface.Logger
}

// NewLikeRepository 创建点赞仓储实例
func NewLikeRepository(db *sql.DB, logger logiface.Logger) record.LikeRepository {
	return &LikeRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建点赞记录
func (r *LikeRepositoryImpl) Create(ctx context.Context, like *record.Like) (*record.Like, error) {
	query := `INSERT INTO record_likes (user_id, record_id) VALUES (?, ?)`
	result, err := r.db.ExecContext(ctx, query, like.UserID, like.RecordID)
	if err != nil {
		r.logger.Error(ctx, "Failed to create like record",
			logiface.Error(err),
			logiface.Int64("user_id", like.UserID),
			logiface.Int64("record_id", like.RecordID),
		)
		return nil, errors.NewStorageError(fmt.Sprintf("like creation failed: %v", err))
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("last insert ID retrieval failed: %v", err))
	}

	like.ID = id
	like.CreatedAt = time.Now()

	r.logger.Info(ctx, "Like record created successfully",
		logiface.Int64("like_id", like.ID),
		logiface.Int64("user_id", like.UserID),
		logiface.Int64("record_id", like.RecordID),
	)

	return like, nil
}

// Delete 删除点赞记录
func (r *LikeRepositoryImpl) Delete(ctx context.Context, userID, recordID int64, likeType string) error {
	query := `DELETE FROM record_likes WHERE user_id = ? AND record_id = ? AND type = ?`
	result, err := r.db.ExecContext(ctx, query, userID, recordID, likeType)
	if err != nil {
		r.logger.Error(ctx, "Failed to delete like record",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("record_id", recordID),
		)
		return errors.NewStorageError(fmt.Sprintf("like deletion failed: %v", err))
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return errors.NewRecordNotLikedError(recordID)
	}

	r.logger.Info(ctx, "Like record deleted successfully",
		logiface.Int64("user_id", userID),
		logiface.Int64("record_id", recordID),
	)

	return nil
}

// GetByUserAndRecord 获取用户对特定记录的点赞
func (r *LikeRepositoryImpl) GetByUserAndRecord(ctx context.Context, userID, recordID int64, likeType string) (*record.Like, error) {
	query := `SELECT id, user_id, record_id, type, created_at FROM record_likes WHERE user_id = ? AND record_id = ? AND type = ?`

	var like record.Like
	err := r.db.QueryRowContext(ctx, query, userID, recordID, likeType).Scan(
		&like.ID, &like.UserID, &like.RecordID, &like.Type, &like.CreatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.NewRecordNotLikedError(recordID)
		}
		r.logger.Error(ctx, "Failed to get like record",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("record_id", recordID),
		)
		return nil, errors.NewStorageError(fmt.Sprintf("like retrieval failed: %v", err))
	}

	return &like, nil
}

// GetByRecord 获取记录的点赞列表
func (r *LikeRepositoryImpl) GetByRecord(ctx context.Context, recordID int64, likeType string, limit, offset int) ([]*record.Like, int64, error) {
	// 获取总数
	countQuery := `SELECT COUNT(*) FROM record_likes WHERE record_id = ? AND type = ?`
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, recordID, likeType).Scan(&total)
	if err != nil {
		r.logger.Error(ctx, "Failed to get like count by record",
			logiface.Error(err),
			logiface.Int64("record_id", recordID),
		)
		return nil, 0, errors.NewStorageError(fmt.Sprintf("like count retrieval failed: %v", err))
	}

	// 获取点赞列表
	query := `SELECT id, user_id, record_id, type, created_at FROM record_likes WHERE record_id = ? AND type = ? ORDER BY created_at DESC LIMIT ? OFFSET ?`
	rows, err := r.db.QueryContext(ctx, query, recordID, likeType, limit, offset)
	if err != nil {
		r.logger.Error(ctx, "Failed to get likes by record",
			logiface.Error(err),
			logiface.Int64("record_id", recordID),
		)
		return nil, 0, errors.NewStorageError(fmt.Sprintf("likes retrieval failed: %v", err))
	}
	defer rows.Close()

	var likes []*record.Like
	for rows.Next() {
		var like record.Like
		err := rows.Scan(&like.ID, &like.UserID, &like.RecordID, &like.Type, &like.CreatedAt)
		if err != nil {
			return nil, 0, errors.NewStorageError(fmt.Sprintf("like scan failed: %v", err))
		}
		likes = append(likes, &like)
	}

	r.logger.Info(ctx, "Likes retrieved by record",
		logiface.Int64("record_id", recordID),
		logiface.Int("count", len(likes)),
		logiface.Int64("total", total),
	)

	return likes, total, nil
}

// GetByUser 获取用户的点赞列表
func (r *LikeRepositoryImpl) GetByUser(ctx context.Context, userID int64, likeType string, limit, offset int) ([]*record.Like, int64, error) {
	// 获取总数
	countQuery := `SELECT COUNT(*) FROM record_likes WHERE user_id = ? AND type = ?`
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, userID, likeType).Scan(&total)
	if err != nil {
		r.logger.Error(ctx, "Failed to get like count by user",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		return nil, 0, errors.NewStorageError(fmt.Sprintf("like count retrieval failed: %v", err))
	}

	// 获取点赞列表
	query := `SELECT id, user_id, record_id, type, created_at FROM record_likes WHERE user_id = ? AND type = ? ORDER BY created_at DESC LIMIT ? OFFSET ?`
	rows, err := r.db.QueryContext(ctx, query, userID, likeType, limit, offset)
	if err != nil {
		r.logger.Error(ctx, "Failed to get likes by user",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		return nil, 0, errors.NewStorageError(fmt.Sprintf("likes retrieval failed: %v", err))
	}
	defer rows.Close()

	var likes []*record.Like
	for rows.Next() {
		var like record.Like
		err := rows.Scan(&like.ID, &like.UserID, &like.RecordID, &like.Type, &like.CreatedAt)
		if err != nil {
			return nil, 0, errors.NewStorageError(fmt.Sprintf("like scan failed: %v", err))
		}
		likes = append(likes, &like)
	}

	r.logger.Info(ctx, "Likes retrieved by user",
		logiface.Int64("user_id", userID),
		logiface.Int("count", len(likes)),
		logiface.Int64("total", total),
	)

	return likes, total, nil
}

// CountByRecord 获取记录的点赞数量
func (r *LikeRepositoryImpl) CountByRecord(ctx context.Context, recordID int64, likeType string) (int64, error) {
	query := `SELECT COUNT(*) FROM record_likes WHERE record_id = ? AND type = ?`
	var count int64
	err := r.db.QueryRowContext(ctx, query, recordID, likeType).Scan(&count)
	if err != nil {
		r.logger.Error(ctx, "Failed to count likes by record",
			logiface.Error(err),
			logiface.Int64("record_id", recordID),
		)
		return 0, errors.NewStorageError(fmt.Sprintf("like count failed: %v", err))
	}
	return count, nil
}
