package repository

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/record"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/persistence/models"

	"gitee.com/heiyee/platforms/pkg/logiface"

	"gorm.io/gorm"
)

// WishlistRepositoryImpl 愿望清单仓储实现
type WishlistRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewWishlistRepository 创建愿望清单仓储实例
func NewWishlistRepository(db *gorm.DB, logger logiface.Logger) record.WishlistRepository {
	return &WishlistRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Add 添加记录到愿望清单
func (r *WishlistRepositoryImpl) Add(ctx context.Context, userID, recordID int64) error {
	wishlist := &models.Wishlist{
		UserID:   userID,
		RecordID: recordID,
	}

	if err := r.db.WithContext(ctx).Create(wishlist).Error; err != nil {
		r.logger.Error(ctx, "添加到愿望清单失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("wishlist addition failed: %v", err))
	}

	return nil
}

// Remove 从愿望清单移除记录
func (r *WishlistRepositoryImpl) Remove(ctx context.Context, userID, recordID int64) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND record_id = ?", userID, recordID).
		Delete(&models.Wishlist{}).Error; err != nil {
		r.logger.Error(ctx, "从愿望清单移除失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("wishlist removal failed: %v", err))
	}

	return nil
}

// Exists 检查记录是否在愿望清单中
func (r *WishlistRepositoryImpl) Exists(ctx context.Context, userID, recordID int64) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.Wishlist{}).
		Where("user_id = ? AND record_id = ?", userID, recordID).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "检查愿望清单状态失败", logiface.Error(err))
		return false, errors.NewStorageError(fmt.Sprintf("wishlist status check failed: %v", err))
	}

	return count > 0, nil
}

// GetByUserID 获取用户愿望清单记录
func (r *WishlistRepositoryImpl) GetByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*record.Record, int64, error) {
	var total int64
	var records []*record.Record

	// 获取总数
	if err := r.db.WithContext(ctx).
		Model(&models.Wishlist{}).
		Where("user_id = ?", userID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取愿望清单总数失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("wishlist count retrieval failed: %v", err))
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询记录
	var recordModels []models.Record
	if err := r.db.WithContext(ctx).
		Table("records r").
		Select("r.*").
		Joins("INNER JOIN wishlists w ON r.id = w.record_id").
		Where("w.user_id = ?", userID).
		Order("w.created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&recordModels).Error; err != nil {
		r.logger.Error(ctx, "获取愿望清单记录失败", logiface.Error(err))
		return nil, 0, errors.NewStorageError(fmt.Sprintf("wishlist records retrieval failed: %v", err))
	}

	// 转换为实体
	records = make([]*record.Record, len(recordModels))
	for i, model := range recordModels {
		records[i] = r.modelToEntity(&model)
	}

	return records, total, nil
}

// GetRecordIDs 获取用户愿望清单中的记录ID列表
func (r *WishlistRepositoryImpl) GetRecordIDs(ctx context.Context, userID int64) ([]int64, error) {
	var recordIDs []int64

	if err := r.db.WithContext(ctx).
		Model(&models.Wishlist{}).
		Where("user_id = ?", userID).
		Pluck("record_id", &recordIDs).Error; err != nil {
		r.logger.Error(ctx, "获取愿望清单记录ID失败", logiface.Error(err))
		return nil, errors.NewStorageError(fmt.Sprintf("wishlist record IDs retrieval failed: %v", err))
	}

	return recordIDs, nil
}

// Count 获取用户愿望清单数量
func (r *WishlistRepositoryImpl) Count(ctx context.Context, userID int64) (int64, error) {
	var count int64

	if err := r.db.WithContext(ctx).
		Model(&models.Wishlist{}).
		Where("user_id = ?", userID).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "获取愿望清单数量失败", logiface.Error(err))
		return 0, errors.NewStorageError(fmt.Sprintf("wishlist count retrieval failed: %v", err))
	}

	return count, nil
}

// BatchAdd 批量添加到愿望清单
func (r *WishlistRepositoryImpl) BatchAdd(ctx context.Context, userID int64, recordIDs []int64) error {
	if len(recordIDs) == 0 {
		return nil
	}

	// 准备批量插入数据
	wishlists := make([]models.Wishlist, len(recordIDs))
	for i, recordID := range recordIDs {
		wishlists[i] = models.Wishlist{
			UserID:   userID,
			RecordID: recordID,
		}
	}

	// 批量插入，忽略重复记录
	if err := r.db.WithContext(ctx).
		Create(&wishlists).Error; err != nil {
		r.logger.Error(ctx, "批量添加到愿望清单失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("batch wishlist addition failed: %v", err))
	}

	return nil
}

// BatchRemove 批量从愿望清单移除
func (r *WishlistRepositoryImpl) BatchRemove(ctx context.Context, userID int64, recordIDs []int64) error {
	if len(recordIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND record_id IN ?", userID, recordIDs).
		Delete(&models.Wishlist{}).Error; err != nil {
		r.logger.Error(ctx, "批量从愿望清单移除失败", logiface.Error(err))
		return errors.NewStorageError(fmt.Sprintf("batch wishlist removal failed: %v", err))
	}

	return nil
}

// modelToEntity 将数据库模型转换为实体
func (r *WishlistRepositoryImpl) modelToEntity(model *models.Record) *record.Record {
	return &record.Record{
		ID:        int64(model.ID),
		UserID:    int64(model.UserID),
		Title:     model.Title,
		Content:   model.Content,
		Type:      model.Type,
		Status:    string(model.Status),
		LikeCount: model.LikeCount,
		ViewCount: model.ViewCount,
		CreatedAt: model.CreatedAt,
		UpdatedAt: model.UpdatedAt,
	}
}
