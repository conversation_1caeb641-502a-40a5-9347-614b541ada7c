package middleware

import (
	"reflect"
	"strings"

	"gitee.com/heiyee/platforms/ilike-backend/pkg/response"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

var validate *validator.Validate

func init() {
	validate = validator.New()
	
	// 注册自定义字段名映射函数
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
}

// ValidationErrorDetail 验证错误详情
type ValidationErrorDetail struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ParseValidationErrors 解析验证错误并返回规范的错误响应
func ParseValidationErrors(err error) []response.ErrorDetail {
	var errors []response.ErrorDetail
	
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			field := getFieldName(e)
			message := getValidationMessage(e)
			
			errors = append(errors, response.ErrorDetail{
				Field:   field,
				Message: message,
			})
		}
	} else {
		// 处理非validator错误
		errors = append(errors, response.ErrorDetail{
			Field:   "request",
			Message: "请求格式错误: " + err.Error(),
		})
	}
	
	return errors
}

// getFieldName 获取字段名（优先使用JSON标签）
func getFieldName(e validator.FieldError) string {
	return e.Field()
}

// getValidationMessage 获取友好的验证错误消息
func getValidationMessage(e validator.FieldError) string {
	field := e.Field()
	
	switch e.Tag() {
	case "required":
		return field + "为必填字段"
	case "min":
		if e.Kind() == reflect.String {
			return field + "长度不能少于" + e.Param() + "个字符"
		}
		return field + "最小值为" + e.Param()
	case "max":
		if e.Kind() == reflect.String {
			return field + "长度不能超过" + e.Param() + "个字符"
		}
		return field + "最大值为" + e.Param()
	case "email":
		return field + "必须是有效的邮箱地址"
	case "url":
		return field + "必须是有效的URL地址"
	case "numeric":
		return field + "必须是数字"
	case "alpha":
		return field + "只能包含字母"
	case "alphanum":
		return field + "只能包含字母和数字"
	case "oneof":
		return field + "的值必须是以下之一: " + e.Param()
	case "gte":
		return field + "的值必须大于等于" + e.Param()
	case "lte":
		return field + "的值必须小于等于" + e.Param()
	case "gt":
		return field + "的值必须大于" + e.Param()
	case "lt":
		return field + "的值必须小于" + e.Param()
	case "len":
		return field + "的长度必须为" + e.Param() + "个字符"
	case "unique":
		return field + "的值必须唯一"
	case "dive":
		return field + "数组元素验证失败"
	default:
		return field + "格式不正确"
	}
}

// ValidateStruct 验证结构体
func ValidateStruct(obj interface{}) []response.ErrorDetail {
	err := validate.Struct(obj)
	if err == nil {
		return nil
	}
	return ParseValidationErrors(err)
}

// HandleValidationError 处理验证错误的Gin中间件辅助函数
func HandleValidationError(c *gin.Context, err error) {
	errors := ParseValidationErrors(err)
	response.ValidationError(c, errors)
}

// ValidateJSON 验证JSON请求体的中间件函数
func ValidateJSON(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBindJSON(obj); err != nil {
			HandleValidationError(c, err)
			c.Abort()
			return
		}
		
		// 执行struct验证
		if errors := ValidateStruct(obj); len(errors) > 0 {
			response.ValidationError(c, errors)
			c.Abort()
			return
		}
		
		c.Set("validated_request", obj)
		c.Next()
	}
}

// ValidateQuery 验证查询参数的中间件函数
func ValidateQuery(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBindQuery(obj); err != nil {
			HandleValidationError(c, err)
			c.Abort()
			return
		}
		
		// 执行struct验证
		if errors := ValidateStruct(obj); len(errors) > 0 {
			response.ValidationError(c, errors)
			c.Abort()
			return
		}
		
		c.Set("validated_query", obj)
		c.Next()
	}
}

// ValidateForm 验证表单数据的中间件函数
func ValidateForm(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBind(obj); err != nil {
			HandleValidationError(c, err)
			c.Abort()
			return
		}
		
		// 执行struct验证
		if errors := ValidateStruct(obj); len(errors) > 0 {
			response.ValidationError(c, errors)
			c.Abort()
			return
		}
		
		c.Set("validated_form", obj)
		c.Next()
	}
}

// GetValidator 获取验证器实例
func GetValidator() *validator.Validate {
	return validate
}