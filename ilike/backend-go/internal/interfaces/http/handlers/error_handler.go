package handlers

import (
	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	commonResponse "gitee.com/heiyee/platforms/ilike-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// HandleILikeError 处理iLike模块的业务错误
// 将iLike特定的错误码映射到HTTP状态码和响应格式
func HandleILikeError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// 检查是否为iLike模块的错误
	if ilikeErr, ok := err.(*errors.ILikeError); ok {
		// 根据具体错误类型提供更精确的处理
		switch ilikeErr.GetCode() {
		// 通用错误 - 资源不存在
		case errors.CodeResourceNotFound:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 通用错误 - 资源已存在
		case errors.CodeResourceAlreadyExists:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 通用错误 - 权限不足
		case errors.CodePermissionDenied:
			commonResponse.Forbidden(c)

		// 通用错误 - 验证失败
		case errors.CodeValidationFailed,
			errors.CodeRecordInvalidTitle,
			errors.CodeRecordInvalidContent,
			errors.CodeRecordInvalidType,
			errors.CodeRecordInvalidSchema,
			errors.CodeTagInvalidName,
			errors.CodeCommentInvalidContent,
			errors.CodeFileInvalidFormat:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 通用错误 - 操作不被允许
		case errors.CodeOperationNotAllowed,
			errors.CodeRecordAlreadyLiked,
			errors.CodeRecordNotLiked,
			errors.CodeCommentAlreadyLiked,
			errors.CodeCommentNotLiked:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 标签相关错误
		case errors.CodeTagAlreadyAssigned,
			errors.CodeTagNotAssigned,
			errors.CodeTagTooMany:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 文件相关错误
		case errors.CodeFileUploadFailed,
			errors.CodeFileTooLarge,
			errors.CodeFileNotFound:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 系统错误 - 频率限制
		case errors.CodeRateLimitExceeded:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 系统错误 - 服务不可用
		case errors.CodeServiceUnavailable:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())

		// 系统错误 - 存储、缓存、集成失败
		case errors.CodeStorageError,
			errors.CodeCacheError,
			errors.CodeIntegrationFailed:
			commonResponse.Error(c, err)

		// 默认处理 - 使用iLike错误码直接返回
		default:
			commonResponse.BusinessError(c, ilikeErr.GetCode(), ilikeErr.GetMessage())
		}
		return
	}

	// 如果不是iLike模块的错误，按通用错误处理
	commonResponse.Error(c, err)
}
