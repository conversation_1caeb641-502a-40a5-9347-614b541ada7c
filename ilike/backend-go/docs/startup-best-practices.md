# 微服务启动流程最佳实践

## 概述

本文档总结了基于 `users`、`email`、`ilike` 三个项目的启动流程分析，并提供了一个统一的最佳实践启动流程设计。

## 三个项目启动流程分析

### 1. users 项目 (最完整)
**优点：**
- ✅ 完整的优雅关闭机制
- ✅ 同时支持 HTTP 和 gRPC 服务
- ✅ 服务注册到 Nacos
- ✅ 完整的依赖注入容器
- ✅ 配置热更新支持
- ✅ 结构化错误处理

**缺点：**
- ❌ 代码结构较为复杂
- ❌ 缺少启动阶段的状态检查
- ❌ 错误处理不够统一

### 2. email 项目 (中等复杂度)
**优点：**
- ✅ 依赖注入容器设计良好
- ✅ 配置热更新
- ✅ gRPC 客户端管理
- ✅ 日志级别动态调整

**缺点：**
- ❌ 缺少优雅关闭超时控制
- ❌ 错误处理不够完善
- ❌ 缺少健康检查

### 3. ilike 项目 (最简单)
**优点：**
- ✅ 代码简洁
- ✅ 依赖注入容器
- ✅ 中间件配置完整

**缺点：**
- ❌ 缺少服务注册
- ❌ 缺少配置热更新
- ❌ 优雅关闭机制简单

## 最佳实践启动流程设计

### 核心原则

1. **生命周期管理**：明确的应用生命周期（初始化 → 启动 → 运行 → 关闭）
2. **优雅关闭**：确保所有资源正确释放
3. **错误处理**：统一的错误处理和日志记录
4. **健康检查**：提供健康检查端点
5. **配置管理**：支持配置热更新
6. **可观测性**：完整的日志、监控、链路追踪

### 启动流程架构

```go
type Application struct {
    config        *config.Config
    logger        logiface.Logger
    container     *container.DependencyContainer
    httpServer    *http.Server
    otelShutdown  func(context.Context) error
    ctx           context.Context
    cancel        context.CancelFunc
}
```

### 启动步骤

1. **解析命令行参数**
   ```go
   flag.Parse()
   ```

2. **加载配置**
   ```go
   cfg, err := config.LoadConfig()
   ```

3. **初始化日志系统**
   ```go
   logiface.InitLogger(logConfig)
   ```

4. **初始化 OpenTelemetry**
   ```go
   shutdown, err := otel.InitTracerProvider(ServiceName, cfg.Otel.Endpoint)
   ```

5. **启动配置热更新监听**
   ```go
   go config.ListenNacosConfigChange(ServiceName)
   ```

6. **初始化依赖注入容器**
   ```go
   container := container.NewDependencyContainer(cfg, logger)
   container.Initialize(ctx)
   ```

7. **初始化 gRPC 客户端管理器**
   ```go
   grpcregistry.InitGlobalManager(logger)
   grpcregistry.BatchSubscribeServices(cfg.GRPCSubscriptions, logger)
   ```

8. **构建 HTTP 服务器**
   ```go
   router := gin.New()
   httpmiddleware.SetupCommonMiddleware(router, middlewareConfig)
   routes.SetupRoutes(router, handlers...)
   ```

9. **启动服务**
   ```go
   go httpServer.ListenAndServe()
   ```

10. **等待关闭信号**
    ```go
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit
    ```

11. **优雅关闭**
    ```go
    // 1. 关闭 HTTP 服务器
    httpServer.Shutdown(ctx)
    
    // 2. 关闭依赖注入容器
    container.Close()
    
    // 3. 关闭 gRPC 客户端管理器
    grpcregistry.GetGlobalManager().Close()
    
    // 4. 关闭 OpenTelemetry
    otelShutdown(ctx)
    ```

### 健康检查实现

```go
func (app *Application) healthCheck(c *gin.Context) {
    // 检查数据库连接
    if app.container.Infrastructure.DB != nil {
        sqlDB, err := app.container.Infrastructure.DB.DB()
        if err != nil || sqlDB.Ping() != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "unhealthy",
                "error":  "database connection failed",
            })
            return
        }
    }

    // 检查 gRPC 客户端连接
    if app.container.Infrastructure.UsersClient != nil {
        if err := app.container.Infrastructure.UsersClient.Health(app.ctx); err != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "unhealthy",
                "error":  "grpc client connection failed",
            })
            return
        }
    }

    c.JSON(http.StatusOK, gin.H{
        "status":    "healthy",
        "service":   ServiceName,
        "timestamp": time.Now().Unix(),
    })
}
```

### 错误处理策略

1. **初始化阶段错误**：使用 `log.Fatalf` 终止程序
2. **运行时错误**：记录日志但不终止程序
3. **关闭阶段错误**：记录日志但继续关闭流程
4. **错误包装**：使用 `fmt.Errorf("context: %w", err)` 包装错误

### 配置管理

1. **环境变量**：支持环境变量覆盖配置
2. **配置文件**：支持 YAML/TOML 配置文件
3. **配置热更新**：通过 Nacos 实现配置热更新
4. **配置验证**：启动时验证配置有效性

### 日志管理

1. **结构化日志**：使用 JSON 格式
2. **日志级别**：支持动态调整日志级别
3. **日志分类**：应用日志、访问日志、错误日志分离
4. **链路追踪**：集成 OpenTelemetry 链路追踪

### 监控指标

1. **应用指标**：启动时间、运行时间、请求数
2. **系统指标**：CPU、内存、磁盘使用率
3. **业务指标**：业务相关的自定义指标
4. **健康状态**：服务健康状态监控

## 实施建议

### 1. 统一启动框架

建议创建一个统一的启动框架，可以被所有微服务复用：

```go
// pkg/startup/application.go
type Application interface {
    Initialize() error
    Start() error
    WaitForShutdown()
    Shutdown() error
}
```

### 2. 配置模板

为不同类型的服务提供配置模板：
- HTTP 服务配置模板
- gRPC 服务配置模板
- 混合服务配置模板

### 3. 健康检查标准

定义统一的健康检查标准：
- `/health` - 基础健康检查
- `/health/ready` - 就绪检查
- `/health/live` - 存活检查

### 4. 优雅关闭超时

设置合理的优雅关闭超时时间：
- HTTP 服务器：30 秒
- gRPC 服务器：30 秒
- 数据库连接：10 秒
- 外部服务连接：5 秒

### 5. 错误码规范

定义统一的错误码规范：
- 0: 成功
- 1000-1999: 客户端错误
- 2000-2999: 认证授权错误
- 3000-3999: 业务逻辑错误
- 4000-4999: 资源不存在
- 5000-5999: 服务器内部错误

## 总结

通过分析三个项目的启动流程，我们设计了一个统一的最佳实践启动流程。这个流程具有以下特点：

1. **完整性**：涵盖了微服务启动的所有关键环节
2. **可维护性**：清晰的代码结构和错误处理
3. **可观测性**：完整的日志、监控、链路追踪
4. **可靠性**：优雅关闭和健康检查
5. **可扩展性**：支持配置热更新和服务发现

这个设计可以作为所有微服务项目的标准启动流程模板。 