# iLike Backend Go API 接口文档 - 第二部分

## 5. 评论管理 (Comments)

#### 5.1 创建评论

**接口**: `POST /api/ilike/comments/create`

**描述**: 创建新评论（需要认证）

**请求体**:
```json
{
  "record_id": 1,
  "content": "评论内容",
  "parent_id": 0
}
```

**字段说明**:
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| record_id | int64 | 是 | 记录ID |
| content | string | 是 | 评论内容 |
| parent_id | int64 | 否 | 父评论ID，用于回复 |

#### 5.2 获取评论详情

**接口**: `GET /api/ilike/comments/detail`

**描述**: 获取指定评论的详细信息

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 评论ID |

#### 5.3 获取记录评论列表

**接口**: `GET /api/ilike/comments/list`

**描述**: 获取指定记录的评论列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| record_id | string | 是 | 记录ID |
| parent_id | string | 否 | 父评论ID |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 5.4 更新评论

**接口**: `POST /api/ilike/comments/update`

**描述**: 更新评论内容（需要认证）

**请求体**:
```json
{
  "id": 1,
  "content": "更新后的评论内容"
}
```

#### 5.5 删除评论

**接口**: `POST /api/ilike/comments/delete`

**描述**: 删除评论（需要认证）

**请求体**:
```json
{
  "id": 1
}
```

#### 5.6 点赞评论

**接口**: `POST /api/ilike/comments/like`

**描述**: 为评论点赞（需要认证）

**请求体**:
```json
{
  "comment_id": 1
}
```

#### 5.7 取消评论点赞

**接口**: `POST /api/ilike/comments/unlike`

**描述**: 取消评论点赞（需要认证）

**请求体**:
```json
{
  "comment_id": 1
}
```

#### 5.8 获取用户评论

**接口**: `GET /api/ilike/comments/user`

**描述**: 获取用户的评论列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

### 6. 标签管理 (Tags)

#### 6.1 创建标签

**接口**: `POST /api/ilike/tags/create`

**描述**: 创建新标签（需要认证）

**请求体**:
```json
{
  "name": "标签名称",
  "description": "标签描述",
  "color": "#1890ff"
}
```

**字段说明**:
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 是 | 标签名称 |
| description | string | 否 | 标签描述 |
| color | string | 否 | 标签颜色 |

#### 6.2 获取标签详情

**接口**: `GET /api/ilike/tags/detail`

**描述**: 获取指定标签的详细信息（需要认证）

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 标签ID |

#### 6.3 更新标签

**接口**: `POST /api/ilike/tags/update`

**描述**: 更新标签信息（需要认证）

**请求体**:
```json
{
  "id": 1,
  "name": "更新后的标签名称",
  "description": "更新后的描述",
  "color": "#52c41a"
}
```

#### 6.4 删除标签

**接口**: `POST /api/ilike/tags/delete`

**描述**: 删除标签（需要认证）

**请求体**:
```json
{
  "id": 1
}
```

#### 6.5 获取用户标签列表

**接口**: `GET /api/ilike/tags/my`

**描述**: 获取当前用户的标签列表（需要认证）

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

### 7. 分类管理 (Categories)

#### 7.1 获取分类列表

**接口**: `GET /api/ilike/categories/list`

**描述**: 获取所有分类列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 7.2 获取分类详情

**接口**: `GET /api/ilike/categories/detail`

**描述**: 获取指定分类的详细信息

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 分类ID |

#### 7.3 根据代码获取分类

**接口**: `GET /api/ilike/categories/get-by-code`

**描述**: 根据分类代码获取分类信息

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| code | string | 是 | 分类代码 |

#### 7.4 创建分类

**接口**: `POST /api/ilike/categories/create`

**描述**: 创建新分类（需要认证）

**请求体**:
```json
{
  "name": "分类名称",
  "code": "category_code",
  "description": "分类描述",
  "parent_id": 0,
  "sort": 1
}
```

#### 7.5 更新分类

**接口**: `POST /api/ilike/categories/update`

**描述**: 更新分类信息（需要认证）

**请求体**:
```json
{
  "id": 1,
  "name": "更新后的分类名称",
  "code": "updated_code",
  "description": "更新后的描述",
  "parent_id": 0,
  "sort": 2
}
```

#### 7.6 删除分类

**接口**: `POST /api/ilike/categories/delete`

**描述**: 删除分类（需要认证）

**请求体**:
```json
{
  "id": 1
}
```

### 8. 搜索功能 (Search)

#### 8.1 通用搜索

**接口**: `GET /api/ilike/search`

**描述**: 通用搜索功能

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.2 搜索记录

**接口**: `GET /api/ilike/search/records`

**描述**: 专门搜索记录

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.3 搜索标签

**接口**: `GET /api/ilike/search/tags`

**描述**: 搜索标签

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.4 获取搜索建议

**接口**: `GET /api/ilike/search/suggestions`

**描述**: 获取搜索建议

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| limit | int | 否 | 建议数量，默认 10 |

#### 8.5 根据标签搜索记录

**接口**: `GET /api/ilike/search/records-by-tag`

**描述**: 根据标签搜索记录

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| tag_id | int64 | 是 | 标签ID |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.6 根据分类搜索记录

**接口**: `GET /api/ilike/search/records-by-category`

**描述**: 根据分类搜索记录

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| category_id | int64 | 是 | 分类ID |
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.7 获取热门搜索关键词

**接口**: `GET /api/ilike/search/popular-keywords`

**描述**: 获取热门搜索关键词

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| limit | int | 否 | 关键词数量，默认 10 |

#### 8.8 获取搜索历史

**接口**: `GET /api/ilike/search/history`

**描述**: 获取用户搜索历史（需要认证）

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

#### 8.9 删除搜索历史

**接口**: `DELETE /api/ilike/search/history`

**描述**: 删除搜索历史（需要认证）

**请求体**:
```json
{
  "keyword": "要删除的关键词"
}
```

#### 8.10 清空搜索历史

**接口**: `POST /api/ilike/search/history/clear`

**描述**: 清空所有搜索历史（需要认证）

#### 8.11 获取搜索统计

**接口**: `GET /api/ilike/search/statistics`

**描述**: 获取搜索统计信息（需要认证）

### 9. 管理接口 (Admin)

#### 9.1 记录类型管理

##### 获取记录类型列表

**接口**: `GET /api/admin/record-types`

**描述**: 获取所有记录类型

##### 根据代码获取记录类型

**接口**: `GET /api/admin/record-types/by-code`

**描述**: 根据代码获取记录类型

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| code | string | 是 | 记录类型代码 |

##### 创建记录类型

**接口**: `POST /api/admin/record-types`

**描述**: 创建新的记录类型（需要认证）

**请求体**:
```json
{
  "name": "记录类型名称",
  "code": "record_type_code",
  "description": "记录类型描述",
  "icon": "icon-name",
  "color": "#1890ff",
  "sort": 1,
  "is_active": true
}
```

##### 更新记录类型

**接口**: `POST /api/admin/record-types/update`

**描述**: 更新记录类型（需要认证）

**请求体**:
```json
{
  "id": 1,
  "name": "更新后的名称",
  "code": "updated_code",
  "description": "更新后的描述",
  "icon": "updated-icon",
  "color": "#52c41a",
  "sort": 2,
  "is_active": true
}
```

##### 删除记录类型

**接口**: `POST /api/admin/record-types/delete`

**描述**: 删除记录类型（需要认证）

**请求体**:
```json
{
  "id": 1
}
```

#### 9.2 分类管理

##### 获取分类树

**接口**: `GET /api/admin/categories/tree`

**描述**: 获取分类树结构

##### 获取分类子节点

**接口**: `GET /api/admin/categories/children`

**描述**: 获取指定分类的子节点

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| parent_id | int64 | 否 | 父分类ID，不传则获取根分类 |

#### 9.3 愿望清单管理

##### 获取愿望清单数量

**接口**: `GET /api/ilike/wishlist/count`

**描述**: 获取用户愿望清单中的记录数量（需要认证）

##### 检查是否在愿望清单中

**接口**: `GET /api/ilike/wishlist/status`

**描述**: 检查指定记录是否在用户的愿望清单中（需要认证）

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| record_id | int64 | 是 | 记录ID |

##### 批量添加到愿望清单

**接口**: `POST /api/ilike/wishlist/batch/add`

**描述**: 批量添加记录到愿望清单（需要认证）

**请求体**:
```json
{
  "record_ids": [1, 2, 3]
}
```

##### 批量从愿望清单移除

**接口**: `POST /api/ilike/wishlist/batch/remove`

**描述**: 批量从愿望清单移除记录（需要认证）

**请求体**:
```json
{
  "record_ids": [1, 2, 3]
}
```

## 数据模型补充

### CategoryDTO (分类数据传输对象)

```json
{
  "id": 1,
  "name": "分类名称",
  "code": "category_code",
  "description": "分类描述",
  "parent_id": 0,
  "sort": 1,
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### CommentDTO (评论数据传输对象)

```json
{
  "id": 1,
  "record_id": 1,
  "user_id": 123,
  "content": "评论内容",
  "parent_id": 0,
  "like_count": 5,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### RecordTypeDTO (记录类型数据传输对象)

```json
{
  "id": 1,
  "name": "记录类型名称",
  "code": "record_type_code",
  "description": "记录类型描述",
  "icon": "icon-name",
  "color": "#1890ff",
  "sort": 1,
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 部署配置

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| SERVER_PORT | 服务端口 | 8086 |
| SERVER_ENV | 运行环境 | dev |
| DATABASE_HOST | 数据库主机 | ************** |
| DATABASE_PORT | 数据库端口 | 3308 |
| DATABASE_NAME | 数据库名称 | platforms-ilike |
| REDIS_HOST | Redis主机 | 127.0.0.1 |
| REDIS_PORT | Redis端口 | 6379 |

### 配置文件

配置文件位于 `configs/` 目录下：

- `app.toml`: 基础配置
- `app.dev.toml`: 开发环境配置
- `app.prod.toml`: 生产环境配置

### 启动命令

```bash
# 开发环境
go run cmd/main.go

# 生产环境
./bin/platforms-ilike
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的记录管理功能
- 实现搜索、评论、点赞功能
- 实现标签和分类管理
- 实现愿望清单功能 