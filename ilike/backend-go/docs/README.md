# iLike Backend Go 模块文档

## 概述

iLike Backend Go 是一个基于 Go 语言开发的微服务后端系统，采用 DDD（领域驱动设计）架构，提供记录管理、搜索、评论、点赞等功能。

## 文档结构

### 1. API 接口文档

- **[完整 API 文档](api-documentation.md)** - 包含基础接口、记录管理、点赞、愿望清单等功能的详细 API 文档
- **[API 文档第二部分](api-documentation-part2.md)** - 包含评论、标签、分类、搜索和管理接口的详细 API 文档

### 2. 项目文档

- **[项目 README](../README.md)** - 项目基础信息和快速开始指南
- **[数据库安装指南](../DATABASE_INSTALL.md)** - 数据库安装和初始化说明
- **[重构报告](../REFACTORING_REPORT.md)** - 代码重构的详细报告
- **[实现完成报告](../IMPLEMENTATION_COMPLETION_REPORT.md)** - 功能实现完成情况报告
- **[迁移计划](../IMPLEMENTATION_PLAN.md)** - 从 Java 到 Go 的迁移计划
- **[迁移检查清单](../MIGRATION_CHECKLIST.md)** - 迁移过程中的检查清单
- **[迁移完成报告](../MIGRATION_COMPLETE.md)** - 迁移完成情况报告
- **[进度跟踪](../PROGRESS_TRACKING.md)** - 项目进度跟踪文档
- **[项目状态](../PROJECT_STATUS.md)** - 当前项目状态
- **[任务完成总结](../TASK_COMPLETION_SUMMARY.md)** - 任务完成情况总结
- **[任务列表](../TASK_LIST.md)** - 待完成任务列表

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.0+

### 启动服务

```bash
# 1. 克隆项目
git clone <repository-url>
cd ilike/backend-go

# 2. 安装依赖
go mod download

# 3. 配置数据库
# 参考 DATABASE_INSTALL.md

# 4. 启动服务
go run cmd/main.go
```

### 配置说明

配置文件位于 `configs/` 目录：

- `app.toml` - 基础配置
- `app.dev.toml` - 开发环境配置  
- `app.prod.toml` - 生产环境配置

## 核心功能

### 1. 记录管理
- 创建、更新、删除记录
- 记录列表查询和分页
- 按类型筛选记录
- 记录标签管理

### 2. 点赞系统
- 记录点赞/取消点赞
- 评论点赞/取消点赞
- 点赞状态查询

### 3. 愿望清单
- 添加/移除愿望清单
- 愿望清单查询
- 批量操作

### 4. 评论系统
- 创建、更新、删除评论
- 评论列表查询
- 评论回复功能
- 评论点赞

### 5. 标签管理
- 创建、更新、删除标签
- 标签列表查询
- 标签颜色配置

### 6. 分类管理
- 分类树结构
- 分类 CRUD 操作
- 分类层级管理

### 7. 搜索功能
- 通用搜索
- 记录搜索
- 标签搜索
- 搜索建议
- 搜索历史

### 8. 管理接口
- 记录类型管理
- 分类管理
- 愿望清单管理

## 技术架构

### 架构模式
- **DDD (领域驱动设计)** - 按业务领域组织代码
- **Clean Architecture** - 清晰的分层架构
- **依赖注入** - 使用依赖注入容器管理依赖

### 技术栈
- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **配置中心**: Nacos
- **服务发现**: Nacos
- **监控**: OpenTelemetry
- **日志**: Zap

### 项目结构
```
ilike/backend-go/
├── cmd/                    # 应用入口
├── configs/               # 配置文件
├── docs/                  # 文档
├── internal/              # 内部代码
│   ├── application/       # 应用层
│   ├── domain/           # 领域层
│   ├── infrastructure/    # 基础设施层
│   └── interfaces/        # 接口层
├── pkg/                   # 公共包
└── scripts/              # 脚本文件
```

## API 接口概览

### 公开接口
- `GET /health` - 健康检查
- `GET /api/ilike/records/list` - 获取记录列表
- `GET /api/ilike/records/detail` - 获取记录详情
- `GET /api/ilike/records/by-type` - 按类型获取记录
- `GET /api/ilike/categories/list` - 获取分类列表
- `GET /api/ilike/search` - 通用搜索

### 需要认证的接口
- `POST /api/ilike/records/create` - 创建记录
- `POST /api/ilike/records/update` - 更新记录
- `POST /api/ilike/records/delete` - 删除记录
- `POST /api/ilike/records/like` - 点赞记录
- `POST /api/ilike/records/unlike` - 取消点赞
- `POST /api/ilike/comments/create` - 创建评论
- `POST /api/ilike/tags/create` - 创建标签

### 管理接口
- `GET /api/admin/record-types` - 获取记录类型
- `POST /api/admin/record-types` - 创建记录类型
- `GET /api/admin/categories/tree` - 获取分类树

## 开发规范

### 代码规范
1. **代码组织**: 采用 DDD 架构，按领域模块组织代码
2. **错误处理**: 统一使用错误码和错误消息
3. **日志记录**: 使用结构化日志，包含请求ID和用户信息
4. **接口设计**: RESTful API 设计，统一响应格式
5. **认证授权**: JWT Token 认证，基于角色的权限控制
6. **数据验证**: 使用 validator 进行请求参数验证
7. **测试**: 单元测试覆盖率要求 80% 以上

### 响应格式
所有 API 接口都使用统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 部署指南

### 开发环境
```bash
go run cmd/main.go
```

### 生产环境
```bash
./bin/platforms-ilike
```

### Docker 部署
```bash
docker build -t ilike-backend .
docker run -p 8086:8086 ilike-backend
```

## 监控和日志

### 日志配置
- 应用日志: `log/app`
- 访问日志: `log/access`
- 错误日志: `log/error`

### 监控指标
- OpenTelemetry 分布式追踪
- 请求延迟监控
- 错误率监控
- 资源使用监控

## 常见问题

### Q: 如何修改数据库配置？
A: 编辑 `configs/app.toml` 文件中的 `[database.mysql]` 部分。

### Q: 如何添加新的 API 接口？
A: 在 `internal/interfaces/http/handler/` 目录下创建新的处理器，并在 `routes.go` 中注册路由。

### Q: 如何调试服务？
A: 使用 `go run cmd/main.go` 启动服务，日志会输出到控制台。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。 