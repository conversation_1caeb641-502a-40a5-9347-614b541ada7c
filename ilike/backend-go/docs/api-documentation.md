# iLike Backend Go API 接口文档

## 概述

iLike Backend Go 是一个基于 Go 语言开发的微服务后端系统，采用 DDD（领域驱动设计）架构，提供记录管理、搜索、评论、点赞等功能。

### 基础信息

- **服务名称**: platforms-ilike
- **默认端口**: 8086
- **API 前缀**: `/api/ilike`
- **管理接口前缀**: `/api/admin`
- **认证方式**: JWT Token
- **响应格式**: JSON

### 统一响应格式

所有 API 接口都使用统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

- `code`: 状态码，0 表示成功，其他值表示错误
- `message`: 响应消息
- `data`: 响应数据

## 认证与权限

### 认证中间件

需要认证的接口使用 `RequireAuthedMiddleware()` 中间件，会自动从请求头中获取 JWT Token 并验证用户身份。

### 用户上下文

认证成功后，用户信息会存储在请求上下文中，可通过 `usercontext.GetUserID()` 获取用户ID。

## API 接口列表

### 1. 健康检查

#### GET /health

检查服务健康状态

**响应示例**:
```json
{
  "status": "ok"
}
```

### 2. 记录管理 (Records)

#### 2.1 获取记录列表

**接口**: `GET /api/ilike/records/list`

**描述**: 获取记录列表，支持分页和筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |
| type | string | 否 | 记录类型筛选 |
| keyword | string | 否 | 关键词搜索 |
| sort_by | string | 否 | 排序字段 |
| sort_order | string | 否 | 排序方向 (asc/desc) |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "user_id": 123,
        "title": "记录标题",
        "content": "记录内容",
        "type": "note",
        "is_private": false,
        "like_count": 5,
        "tags": [
          {
            "id": 1,
            "name": "标签1",
            "description": "标签描述",
            "color": "#1890ff",
            "use_count": 10
          }
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 100
  }
}
```

#### 2.2 获取记录详情

**接口**: `GET /api/ilike/records/detail`

**描述**: 获取指定记录的详细信息

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 记录ID |

#### 2.3 按类型获取记录

**接口**: `GET /api/ilike/records/by-type`

**描述**: 根据记录类型获取记录列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| type_code | string | 是 | 记录类型代码 |
| cursor | int64 | 否 | 游标，用于分页 |
| limit | int | 否 | 限制数量，默认 20，最大 100 |
| sort_by | string | 否 | 排序字段 |
| sort_order | string | 否 | 排序方向 |

#### 2.4 创建记录

**接口**: `POST /api/ilike/records/create`

**描述**: 创建新记录（需要认证）

**请求体**:
```json
{
  "title": "记录标题",
  "content": "记录内容",
  "type": "note",
  "is_private": false,
  "tag_ids": [1, 2, 3]
}
```

**字段说明**:
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| title | string | 是 | 记录标题 |
| content | string | 否 | 记录内容 |
| type | string | 是 | 记录类型 |
| is_private | bool | 否 | 是否私有，默认 false |
| tag_ids | []int64 | 否 | 标签ID列表 |

#### 2.5 更新记录

**接口**: `POST /api/ilike/records/update`

**描述**: 更新记录信息（需要认证）

**请求体**:
```json
{
  "id": 1,
  "title": "更新后的标题",
  "content": "更新后的内容",
  "type": "note",
  "is_private": false,
  "tag_ids": [1, 2, 3]
}
```

#### 2.6 删除记录

**接口**: `POST /api/ilike/records/delete`

**描述**: 删除记录（需要认证）

**请求体**:
```json
{
  "id": 1
}
```

#### 2.7 为记录添加标签

**接口**: `POST /api/ilike/records/:id/tags/add`

**描述**: 为指定记录添加标签（需要认证）

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 记录ID |

**请求体**:
```json
{
  "tag_ids": [1, 2, 3]
}
```

#### 2.8 移除记录标签

**接口**: `POST /api/ilike/records/:id/tags/remove`

**描述**: 移除指定记录的标签（需要认证）

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 记录ID |

**请求体**:
```json
{
  "tag_ids": [1, 2, 3]
}
```

#### 2.9 获取记录标签

**接口**: `GET /api/ilike/records/:id/tags`

**描述**: 获取指定记录的标签列表（需要认证）

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 记录ID |

### 3. 点赞管理 (Likes)

#### 3.1 点赞记录

**接口**: `POST /api/ilike/records/like`

**描述**: 为记录点赞（需要认证）

**请求体**:
```json
{
  "record_id": 1,
  "type": "record"
}
```

**字段说明**:
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| record_id | int64 | 是 | 记录ID |
| type | string | 是 | 点赞类型 |

#### 3.2 取消点赞

**接口**: `POST /api/ilike/records/unlike`

**描述**: 取消记录点赞（需要认证）

**请求体**:
```json
{
  "record_id": 1,
  "type": "record"
}
```

### 4. 愿望清单管理 (Wishlist)

#### 4.1 添加到愿望清单

**接口**: `POST /api/ilike/records/wishlist/add`

**描述**: 将记录添加到愿望清单（需要认证）

**请求体**:
```json
{
  "record_id": 1
}
```

#### 4.2 从愿望清单移除

**接口**: `POST /api/ilike/records/wishlist/remove`

**描述**: 从愿望清单移除记录（需要认证）

**请求体**:
```json
{
  "record_id": 1
}
```

#### 4.3 获取愿望清单

**接口**: `GET /api/ilike/records/wishlist`

**描述**: 获取用户的愿望清单（需要认证）

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | int | 否 | 页码，默认 1 |
| size | int | 否 | 每页大小，默认 20 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 数据模型

### RecordDTO (记录数据传输对象)

```json
{
  "id": 1,
  "user_id": 123,
  "title": "记录标题",
  "content": "记录内容",
  "type": "note",
  "is_private": false,
  "like_count": 5,
  "tag_ids": [1, 2, 3],
  "tags": [
    {
      "id": 1,
      "name": "标签名称",
      "description": "标签描述",
      "color": "#1890ff",
      "use_count": 10
    }
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### TagDTO (标签数据传输对象)

```json
{
  "id": 1,
  "name": "标签名称",
  "description": "标签描述",
  "color": "#1890ff",
  "use_count": 10
}
```

## 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **配置中心**: Nacos
- **服务发现**: Nacos
- **监控**: OpenTelemetry
- **日志**: Zap
- **架构**: DDD (领域驱动设计)

## 开发规范

1. **代码组织**: 采用 DDD 架构，按领域模块组织代码
2. **错误处理**: 统一使用错误码和错误消息
3. **日志记录**: 使用结构化日志，包含请求ID和用户信息
4. **接口设计**: RESTful API 设计，统一响应格式
5. **认证授权**: JWT Token 认证，基于角色的权限控制
6. **数据验证**: 使用 validator 进行请求参数验证
7. **测试**: 单元测试覆盖率要求 80% 以上 