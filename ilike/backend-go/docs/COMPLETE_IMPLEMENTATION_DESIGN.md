# iLike Backend-Go 完整实现设计方案

## 🎯 设计目标

基于现有DDD架构，完整实现所有缺失功能，确保代码质量、可维护性和扩展性，最终达到100%功能完成度。

## 🏗️ 架构设计原则

1. **DDD一致性**: 严格遵循领域驱动设计分层架构
2. **向后兼容**: 不影响现有功能和数据结构
3. **渐进式实现**: 分阶段实施，降低风险
4. **测试驱动**: 先写测试，再实现功能
5. **文档完整**: 同步更新API文档和代码注释

## 📋 完整实施路线图

### 阶段一：基础设施准备 (2-3小时)
**目标**: 建立新功能的基础设施和通用组件

#### 1.1 共享工具类创建
```go
// internal/pkg/fileutils/file_validator.go
package fileutils

type FileValidator struct {
    MaxSize     int64
    AllowedTypes []string
}

func (v *FileValidator) Validate(file *multipart.FileHeader) error {
    // 文件类型、大小验证
}

// internal/pkg/pagination/pagination.go
package pagination

type Pagination struct {
    Page     int `json:"page"`
    PageSize int `json:"page_size"`
    Total    int64 `json:"total"`
}
```

#### 1.2 错误码定义扩展
```go
// pkg/common/errors/error_codes.go
const (
    // 文件相关错误码
    ErrFileTooLarge    = 4001001
    ErrInvalidFileType = 4001002
    ErrFileNotFound    = 4041001
    
    // 评论相关错误码
    ErrCommentNotFound     = 4042001
    ErrInvalidComment      = 4002001
    ErrCommentNotAllowed   = 4032001
    
    // 用户配置相关错误码
    ErrProfileNotFound = 4043001
)
```

### 阶段二：用户配置管理 (8-10小时)
**目标**: 实现用户个人资料和偏好设置管理

#### 2.1 领域层设计

**实体 (Entity)**
```go
// internal/domain/user/entity/user_profile.go
package entity

type UserProfile struct {
    UserID      uint
    AvatarURL   string
    Bio         string
    Preferences map[string]interface{}
    Privacy     PrivacySettings
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

type PrivacySettings struct {
    ProfileVisible bool
    RecordsPublic  bool
    AllowComments  bool
}
```

**值对象 (Value Object)**
```go
// internal/domain/user/value_object/preference.go
package valueobject

type UserPreference struct {
    Key   string
    Value string
    Type  string // "string", "bool", "number"
}
```

**仓储接口**
```go
// internal/domain/user/repository/user_profile_repository.go
package repository

type UserProfileRepository interface {
    FindByUserID(userID uint) (*entity.UserProfile, error)
    Save(profile *entity.UserProfile) error
    UpdateAvatar(userID uint, avatarURL string) error
    UpdatePreferences(userID uint, preferences map[string]interface{}) error
}
```

#### 2.2 基础设施层实现

**仓储实现**
```go
// internal/infrastructure/persistence/user_profile_repository.go
package persistence

type UserProfileRepositoryImpl struct {
    db *gorm.DB
}

func (r *UserProfileRepositoryImpl) FindByUserID(userID uint) (*entity.UserProfile, error) {
    var model models.UserProfile
    if err := r.db.Where("user_id = ?", userID).First(&model).Error; err != nil {
        return nil, err
    }
    return r.toEntity(&model), nil
}
```

#### 2.3 应用层服务

```go
// internal/application/user/service/user_profile_service.go
package service

type UserProfileApplicationService struct {
    repo repository.UserProfileRepository
    fileService FileService
}

func (s *UserProfileApplicationService) UpdateProfile(cmd *UpdateProfileCommand) error {
    profile, err := s.repo.FindByUserID(cmd.UserID)
    if err != nil {
        return err
    }
    
    profile.Bio = cmd.Bio
    profile.Privacy = cmd.Privacy
    
    return s.repo.Save(profile)
}

func (s *UserProfileApplicationService) UploadAvatar(cmd *UploadAvatarCommand) (string, error) {
    // 文件上传逻辑
    // 更新用户头像URL
}
```

#### 2.4 接口层实现

**HTTP处理器**
```go
// internal/interfaces/http/handler/user_profile_handler.go
package handler

type UserProfileHandler struct {
    service *service.UserProfileApplicationService
}

func (h *UserProfileHandler) GetProfile(c *gin.Context) {
    userID := usercontext.GetUserID(c)
    profile, err := h.service.GetProfile(userID)
    if err != nil {
        response.Error(c, err)
        return
    }
    response.Success(c, profile)
}

func (h *UserProfileHandler) UpdateProfile(c *gin.Context) {
    var req dto.UpdateProfileRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, err)
        return
    }
    
    userID := usercontext.GetUserID(c)
    err := h.service.UpdateProfile(&service.UpdateProfileCommand{
        UserID:      userID,
        Bio:         req.Bio,
        Privacy:     req.Privacy,
    })
    
    if err != nil {
        response.Error(c, err)
        return
    }
    
    response.Success(c, nil)
}
```

### 阶段三：文件管理系统 (16-20小时)
**目标**: 实现完整的文件上传、管理和权限控制系统

#### 3.1 领域层设计

**实体**
```go
// internal/domain/file/entity/file.go
package entity

type File struct {
    ID          uint
    UserID      uint
    RecordID    *uint
    Filename    string
    FilePath    string
    FileSize    int64
    FileType    string
    MimeType    string
    Hash        string
    IsPublic    bool
    Description string
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

type FileMetadata struct {
    Width     *int
    Height    *int
    Duration  *int
    Location  *Location
}

type Location struct {
    Latitude  float64
    Longitude float64
    Address   string
}
```

**仓储接口**
```go
// internal/domain/file/repository/file_repository.go
package repository

type FileRepository interface {
    FindByID(id uint) (*entity.File, error)
    FindByUserID(userID uint, pagination *pagination.Pagination) ([]*entity.File, error)
    FindByRecordID(recordID uint) ([]*entity.File, error)
    Save(file *entity.File) error
    Delete(id uint) error
    UpdatePermissions(id uint, isPublic bool) error
    FindByHash(hash string) (*entity.File, error)
}
```

#### 3.2 文件存储策略

**存储接口**
```go
// internal/domain/file/service/storage_service.go
package service

type StorageService interface {
    Upload(file *multipart.FileHeader, path string) (string, error)
    Download(path string) (io.ReadCloser, error)
    Delete(path string) error
    GetURL(path string) string
}

// 本地存储实现
// S3存储实现 (未来扩展)
```

#### 3.3 应用层服务

```go
// internal/application/file/service/file_application_service.go
package service

type FileApplicationService struct {
    fileRepo    repository.FileRepository
    storageService domain.StorageService
    validator   *fileutils.FileValidator
}

func (s *FileApplicationService) UploadFile(cmd *UploadFileCommand) (*FileDTO, error) {
    // 1. 验证文件
    if err := s.validator.Validate(cmd.File); err != nil {
        return nil, err
    }
    
    // 2. 检查重复文件
    hash := s.calculateHash(cmd.File)
    if existing, _ := s.fileRepo.FindByHash(hash); existing != nil {
        return s.toDTO(existing), nil // 返回已存在的文件
    }
    
    // 3. 上传到存储
    filePath, err := s.storageService.Upload(cmd.File, s.generatePath(cmd.UserID))
    if err != nil {
        return nil, err
    }
    
    // 4. 保存到数据库
    file := &entity.File{
        UserID:   cmd.UserID,
        RecordID: cmd.RecordID,
        Filename: cmd.File.Filename,
        FilePath: filePath,
        FileSize: cmd.File.Size,
        FileType: cmd.FileType,
        Hash:     hash,
        IsPublic: cmd.IsPublic,
    }
    
    if err := s.fileRepo.Save(file); err != nil {
        s.storageService.Delete(filePath) // 回滚
        return nil, err
    }
    
    return s.toDTO(file), nil
}
```

#### 3.4 文件上传中间件

```go
// internal/interfaces/http/middleware/upload_middleware.go
package middleware

func UploadLimiter(maxSize int64) gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
        
        if err := c.Request.ParseMultipartForm(maxSize); err != nil {
            response.Error(c, errors.New("文件过大"))
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 阶段四：评论系统 (12-15小时)
**目标**: 实现完整的评论管理，包括层级评论、点赞和审核

#### 4.1 领域层设计

**实体**
```go
// internal/domain/comment/entity/comment.go
package entity

type Comment struct {
    ID        uint
    UserID    uint
    RecordID  uint
    ParentID  *uint
    Content   string
    IsDeleted bool
    LikeCount int
    CreatedAt time.Time
    UpdatedAt time.Time
    
    // 关联关系
    User     *entity.User
    Replies  []*Comment
}

type CommentTree struct {
    RootComments []*CommentNode
}

type CommentNode struct {
    Comment *Comment
    Replies []*CommentNode
}
```

**仓储接口**
```go
// internal/domain/comment/repository/comment_repository.go
package repository

type CommentRepository interface {
    FindByID(id uint) (*entity.Comment, error)
    FindByRecordID(recordID uint, pagination *pagination.Pagination) ([]*entity.Comment, error)
    FindReplies(parentID uint) ([]*entity.Comment, error)
    Save(comment *entity.Comment) error
    Delete(id uint) error
    UpdateLikeCount(id uint, increment int) error
    FindByUserID(userID uint, pagination *pagination.Pagination) ([]*entity.Comment, error)
}
```

#### 4.2 评论树构建服务

```go
// internal/application/comment/service/comment_tree_builder.go
package service

type CommentTreeBuilder struct {
    repo repository.CommentRepository
}

func (b *CommentTreeBuilder) BuildCommentTree(recordID uint) (*entity.CommentTree, error) {
    comments, err := b.repo.FindByRecordID(recordID, nil)
    if err != nil {
        return nil, err
    }
    
    return b.buildTree(comments), nil
}

func (b *CommentTreeBuilder) buildTree(comments []*entity.Comment) *entity.CommentTree {
    commentMap := make(map[uint]*entity.CommentNode)
    var rootComments []*entity.CommentNode
    
    for _, comment := range comments {
        node := &entity.CommentNode{Comment: comment}
        commentMap[comment.ID] = node
        
        if comment.ParentID == nil {
            rootComments = append(rootComments, node)
        } else if parent, exists := commentMap[*comment.ParentID]; exists {
            parent.Replies = append(parent.Replies, node)
        }
    }
    
    return &entity.CommentTree{RootComments: rootComments}
}
```

#### 4.3 评论应用服务

```go
// internal/application/comment/service/comment_application_service.go
package service

type CommentApplicationService struct {
    commentRepo repository.CommentRepository
    recordRepo  record.Repository
    userService *user.UserService
}

func (s *CommentApplicationService) CreateComment(cmd *CreateCommentCommand) (*CommentDTO, error) {
    // 1. 验证记录存在
    if _, err := s.recordRepo.FindByID(cmd.RecordID); err != nil {
        return nil, err
    }
    
    // 2. 验证父评论存在
    if cmd.ParentID != nil {
        if _, err := s.commentRepo.FindByID(*cmd.ParentID); err != nil {
            return nil, err
        }
    }
    
    // 3. 创建评论
    comment := &entity.Comment{
        UserID:   cmd.UserID,
        RecordID: cmd.RecordID,
        ParentID: cmd.ParentID,
        Content:  cmd.Content,
    }
    
    if err := s.commentRepo.Save(comment); err != nil {
        return nil, err
    }
    
    // 4. 更新记录评论数
    s.updateRecordCommentCount(cmd.RecordID)
    
    return s.toDTO(comment), nil
}

func (s *CommentApplicationService) GetCommentsWithReplies(query *GetCommentsQuery) (*CommentTreeDTO, error) {
    tree, err := s.treeBuilder.BuildCommentTree(query.RecordID)
    if err != nil {
        return nil, err
    }
    
    return s.toTreeDTO(tree), nil
}
```

### 阶段五：集成与测试 (4-6小时)

#### 5.1 路由注册

```go
// internal/interfaces/http/routes/routes.go
func RegisterRoutes(router *gin.Engine, container *container.DependencyContainer) {
    // 现有路由...
    
    // 用户配置路由
    profileHandler := handler.NewUserProfileHandler(container.UserProfileService)
    api.POST("/profile", profileHandler.UpdateProfile)
    api.GET("/profile", profileHandler.GetProfile)
    api.POST("/profile/avatar", profileHandler.UploadAvatar)
    
    // 文件管理路由
    fileHandler := handler.NewFileHandler(container.FileService)
    api.POST("/files/upload", middleware.UploadLimiter(10<<20), fileHandler.UploadFile)
    api.GET("/files", fileHandler.GetUserFiles)
    api.GET("/files/:id", fileHandler.DownloadFile)
    api.POST("/files/:id/permissions", fileHandler.UpdatePermissions)
    api.POST("/files/:id/delete", fileHandler.DeleteFile)
    
    // 评论路由
    commentHandler := handler.NewCommentHandler(container.CommentService)
    api.POST("/comments", commentHandler.CreateComment)
    api.GET("/comments/record/:recordId", commentHandler.GetComments)
    api.POST("/comments/:id", commentHandler.UpdateComment)
    api.POST("/comments/:id/delete", commentHandler.DeleteComment)
    api.POST("/comments/:id/like", commentHandler.LikeComment)
}
```

#### 5.2 数据库迁移脚本

```sql
-- V3__add_missing_indexes.sql
-- 为文件表添加索引
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_files_record_id ON files(record_id);
CREATE INDEX IF NOT EXISTS idx_files_hash ON files(hash);
CREATE INDEX IF NOT EXISTS idx_files_is_public ON files(is_public);

-- 为评论表添加索引
CREATE INDEX IF NOT EXISTS idx_comments_record_id ON comments(record_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at);

-- 为用户配置表添加索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
```

#### 5.3 测试策略

**单元测试模板**
```go
// internal/application/file/service/file_application_service_test.go
package service_test

func TestFileApplicationService_UploadFile(t *testing.T) {
    tests := []struct {
        name     string
        setup    func() *service.UploadFileCommand
        wantErr  bool
        errCode  int
    }{
        {
            name: "successful upload",
            setup: func() *service.UploadFileCommand {
                return &service.UploadFileCommand{
                    UserID:   1,
                    File:     createTestFile("test.jpg", 1024),
                    IsPublic: true,
                }
            },
            wantErr: false,
        },
        {
            name: "file too large",
            setup: func() *service.UploadFileCommand {
                return &service.UploadFileCommand{
                    UserID:   1,
                    File:     createTestFile("large.jpg", 10*1024*1024),
                    IsPublic: true,
                }
            },
            wantErr: true,
            errCode: errors.ErrFileTooLarge,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试实现...
        })
    }
}
```

## 🔧 技术实施清单

### 文件创建清单

#### 用户配置模块
```
internal/domain/user/entity/user_profile.go
internal/domain/user/repository/user_profile_repository.go
internal/domain/user/value_object/preference.go
internal/infrastructure/persistence/user_profile_repository.go
internal/application/user/service/user_profile_service.go
internal/application/user/dto/profile_dto.go
internal/interfaces/http/handler/user_profile_handler.go
```

#### 文件管理模块
```
internal/domain/file/entity/file.go
internal/domain/file/repository/file_repository.go
internal/domain/file/service/storage_service.go
internal/infrastructure/persistence/file_repository.go
internal/infrastructure/services/local_storage_service.go
internal/application/file/service/file_application_service.go
internal/application/file/dto/file_dto.go
internal/interfaces/http/handler/file_handler.go
internal/interfaces/http/middleware/upload_middleware.go
```

#### 评论系统模块
```
internal/domain/comment/entity/comment.go
internal/domain/comment/repository/comment_repository.go
internal/infrastructure/persistence/comment_repository.go
internal/application/comment/service/comment_application_service.go
internal/application/comment/service/comment_tree_builder.go
internal/application/comment/dto/comment_dto.go
internal/interfaces/http/handler/comment_handler.go
```

### 配置更新

#### 配置文件更新
```yaml
# configs/app.yaml
file:
  upload:
    max_size: 10485760  # 10MB
    allowed_types: ["jpg", "jpeg", "png", "gif", "pdf", "txt", "md"]
    storage_path: "./uploads"
  
  storage:
    type: "local"  # local, s3 (未来扩展)
    local:
      base_path: "./uploads"
    
  permissions:
    default_public: false
    max_files_per_user: 1000

comment:
  moderation:
    enabled: true
    max_depth: 3
    auto_approve: true
```

## 📊 验证与部署

### 验证清单
- [ ] 所有新功能单元测试通过
- [ ] 集成测试覆盖所有API端点
- [ ] 数据库迁移脚本执行成功
- [ ] 性能测试 (文件上传/下载)
- [ ] 安全测试 (权限验证、文件类型检查)
- [ ] 负载测试 (评论系统并发)

### 部署步骤
1. **数据库更新**: 执行迁移脚本
2. **配置更新**: 更新配置文件
3. **代码部署**: 部署新代码
4. **功能验证**: 运行测试脚本
5. **监控设置**: 设置错误监控和性能监控

### 回滚计划
- 数据库迁移可回滚
- 新功能可开关控制
- 保留旧版本兼容

## 🎯 预期成果

完成本设计方案后，iLike Backend-Go将达到：

- **100% 功能完成度**: 所有数据库表都有对应完整实现
- **完整的用户功能**: 文件管理、社交互动、个性化配置
- **生产就绪**: 完整的测试覆盖、监控、错误处理
- **架构一致性**: 保持DDD架构风格统一
- **扩展能力**: 为未来功能扩展打下坚实基础