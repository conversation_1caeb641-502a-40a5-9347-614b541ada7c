/// HTTP常量配置
/// 统一管理所有HTTP相关的常量配置
class HttpConstants {
  // 私有构造函数，防止实例化
  HttpConstants._();

  // 基础配置
  static const String apiVersion = 'v1';
  static const String apiPrefix = '/api/$apiVersion';
  static const String appId = 'q6N-TLt5Q1UtGnq5B5dXkA';
  
  // 超时配置
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // 请求头配置
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-App-Id': appId,
  };
  
  // 表单请求头配置
  static const Map<String, String> formHeaders = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json',
    'X-App-Id': appId,
  };
  
  // 文件上传请求头配置
  static const Map<String, String> multipartHeaders = {
    'Accept': 'application/json',
    'X-App-Id': appId,
  };
  
  /// 获取带认证的请求头
  static Map<String, String> getHeadersWithAuth(String? token) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
  
  /// 获取带认证的表单请求头
  static Map<String, String> getFormHeadersWithAuth(String? token) {
    final headers = Map<String, String>.from(formHeaders);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
  
  /// 获取带认证的文件上传请求头
  static Map<String, String> getMultipartHeadersWithAuth(String? token) {
    final headers = Map<String, String>.from(multipartHeaders);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
} 