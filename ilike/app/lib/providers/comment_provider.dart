import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/comment.dart';
import '../services/comment_service.dart';

class CommentProvider extends ChangeNotifier {
  final CommentService _commentService = CommentService();
  final List<Comment> _comments = [];
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMore = true;

  // Getters
  List<Comment> get comments => List.unmodifiable(_comments);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMore => _hasMore;

  /// 加载评论列表
  Future<void> loadComments(int recordId, {bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      _comments.clear();
      _currentPage = 1;
      _hasMore = true;
      _error = null;
    }

    if (!_hasMore) return;

    _setLoading(true);

    try {
      final commentsData = await _commentService.getCommentsByRecord(recordId);

      if (refresh) {
        _comments.clear();
      }

      // 将Map转换为Comment对象
      final newComments = commentsData.map((data) => Comment.fromJson(data)).toList();
      _comments.addAll(newComments);
      _hasMore = newComments.length >= 20;
      _currentPage++;

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading comments: $e');
      _setError('加载评论失败：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 加载回复列表
  Future<List<Comment>> loadReplies(int parentId) async {
    try {
      final commentsData = await _commentService.getCommentList(parentId: parentId);
      return commentsData.map((data) => Comment.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error loading replies: $e');
      throw Exception('加载回复失败：$e');
    }
  }

  /// 创建评论
  Future<Comment> createComment({
    required int recordId,
    required String content,
    int? parentId,
  }) async {
    try {
      final commentData = {
        'record_id': recordId,
        'content': content,
        if (parentId != null) 'parent_id': parentId,
      };

      final newCommentData = await _commentService.createComment(commentData);
      final newComment = Comment.fromJson(newCommentData);

      // 如果是顶级评论，添加到列表开头
      if (parentId == null || parentId == 0) {
        _comments.insert(0, newComment);
        notifyListeners();
      }

      return newComment;
    } catch (e) {
      debugPrint('Error creating comment: $e');
      throw Exception('创建评论失败：$e');
    }
  }

  /// 更新评论
  Future<void> updateComment({
    required int commentId,
    required String content,
  }) async {
    try {
      final updatedCommentData = await _commentService.updateComment(commentId, {'content': content});
      final updatedComment = Comment.fromJson(updatedCommentData);

      // 更新本地评论
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        _comments[index] = updatedComment;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating comment: $e');
      throw Exception('更新评论失败：$e');
    }
  }

  /// 删除评论
  Future<void> deleteComment(int commentId) async {
    try {
      await _commentService.deleteComment(commentId);

      // 从本地列表移除
      _comments.removeWhere((c) => c.id == commentId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting comment: $e');
      throw Exception('删除评论失败：$e');
    }
  }

  /// 点赞评论
  Future<void> likeComment(int commentId) async {
    try {
      await _commentService.likeComment(commentId);

      // 更新本地点赞数
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        final comment = _comments[index];
        _comments[index] = Comment(
          id: comment.id,
          userId: comment.userId,
          recordId: comment.recordId,
          parentId: comment.parentId,
          content: comment.content,
          likeCount: comment.likeCount + 1,
          replyCount: comment.replyCount,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error liking comment: $e');
      throw Exception('点赞失败：$e');
    }
  }

  /// 取消点赞评论
  Future<void> unlikeComment(int commentId) async {
    try {
      await _commentService.unlikeComment(commentId);

      // 更新本地点赞数
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        final comment = _comments[index];
        _comments[index] = Comment(
          id: comment.id,
          userId: comment.userId,
          recordId: comment.recordId,
          parentId: comment.parentId,
          content: comment.content,
          likeCount: comment.likeCount > 0 ? comment.likeCount - 1 : 0,
          replyCount: comment.replyCount,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error unliking comment: $e');
      throw Exception('取消点赞失败：$e');
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误状态
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 刷新评论列表
  Future<void> refresh(int recordId) async {
    await loadComments(recordId, refresh: true);
  }
}