import 'package:flutter/material.dart';
import 'package:ilike/utils/form_validation_handler.dart';

/// 表单验证混入，提供统一的表单验证错误处理功能
mixin FormValidationMixin<T extends StatefulWidget> on State<T> {
  /// 表单字段错误映射
  Map<String, String> _fieldErrors = {};

  /// 获取字段错误映射
  Map<String, String> get fieldErrors => _fieldErrors;

  /// 设置字段错误
  void setFieldErrors(Map<String, String> errors) {
    setState(() {
      _fieldErrors = errors;
    });
  }

  /// 清除字段错误
  void clearFieldErrors(GlobalKey<FormState> formKey) {
    FormValidationHandler.clearFieldErrors(
      formKey,
      _fieldErrors,
      () {
        setState(() {
          _fieldErrors = {};
        });
      },
    );
  }

  /// 处理API异常，检查是否为表单验证错误
  bool handleApiException(dynamic error, GlobalKey<FormState> formKey) {
    if (FormValidationHandler.isValidationError(error)) {
      final fieldErrors = FormValidationHandler.handleValidationError(error);
      setFieldErrors(fieldErrors);
      // 显示字段错误
      FormValidationHandler.showFieldErrors(context, _fieldErrors, formKey);
      return true; // 表示已处理表单验证错误
    }
    return false; // 表示不是表单验证错误
  }

  /// 获取字段错误消息
  String? getFieldError(String fieldName) {
    return FormValidationHandler.getFieldError(_fieldErrors, fieldName);
  }

  /// 检查字段是否有错误
  bool hasFieldError(String fieldName) {
    return FormValidationHandler.hasFieldError(_fieldErrors, fieldName);
  }

  /// 创建字段验证器
  String? Function(String?)? createFieldValidator(
    String fieldName,
    String? Function(String?)? clientValidator,
  ) {
    return FormValidationHandler.createFieldValidator(
      _fieldErrors,
      fieldName,
      clientValidator,
    );
  }

  /// 检查是否有任何字段错误
  bool get hasAnyFieldError {
    return FormValidationHandler.hasAnyFieldError(_fieldErrors);
  }

  /// 获取所有字段错误消息
  List<String> get allFieldErrors {
    return FormValidationHandler.getAllFieldErrors(_fieldErrors);
  }
} 