import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ilike/services/upload_service.dart';
import 'dart:io';

class FileUploadWidget extends StatefulWidget {
  final Function(UploadResult)? onUploadSuccess;
  final Function(String)? onUploadError;
  final bool showPreview;
  final double? maxWidth;
  final double? maxHeight;
  final int imageQuality;
  final String? initialImageUrl;
  final String? placeholderText;
  final IconData? placeholderIcon;

  const FileUploadWidget({
    Key? key,
    this.onUploadSuccess,
    this.onUploadError,
    this.showPreview = true,
    this.maxWidth,
    this.maxHeight,
    this.imageQuality = 80,
    this.initialImageUrl,
    this.placeholderText,
    this.placeholderIcon,
  }) : super(key: key);

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  bool _isUploading = false;
  String? _currentImageUrl;
  final UploadService _uploadService = UploadService();

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;
  }

  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: widget.maxWidth,
        maxHeight: widget.maxHeight,
        imageQuality: widget.imageQuality,
      );

      if (image != null) {
        await _uploadFile(File(image.path));
      }
    } catch (e) {
      widget.onUploadError?.call('选择图片失败: $e');
    }
  }

  Future<void> _uploadFile(File file) async {
    setState(() {
      _isUploading = true;
    });

    try {
      final result = await _uploadService.uploadFile(file);
      
      setState(() {
        _currentImageUrl = result.fileUrl;
        _isUploading = false;
      });

      widget.onUploadSuccess?.call(result);
    } catch (e) {
      setState(() {
        _isUploading = false;
      });
      widget.onUploadError?.call('上传失败: $e');
    }
  }

  void _showImagePicker() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: Icon(Icons.photo_camera),
                title: Text('拍照'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('从相册选择'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _isUploading ? null : _showImagePicker,
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: _isUploading
            ? _buildLoadingWidget()
            : _buildContentWidget(),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            strokeWidth: 2,
          ),
          SizedBox(height: 8),
          Text(
            '上传中...',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    if (_currentImageUrl != null && widget.showPreview) {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              _currentImageUrl!,
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildPlaceholderWidget();
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.edit,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    } else {
      return _buildPlaceholderWidget();
    }
  }

  Widget _buildPlaceholderWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          widget.placeholderIcon ?? Icons.add_a_photo,
          size: 32,
          color: Colors.grey.shade400,
        ),
        SizedBox(height: 8),
        Text(
          widget.placeholderText ?? '上传图片',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}

class FileUploadButton extends StatelessWidget {
  final Function(UploadResult)? onUploadSuccess;
  final Function(String)? onUploadError;
  final String buttonText;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;

  const FileUploadButton({
    Key? key,
    this.onUploadSuccess,
    this.onUploadError,
    this.buttonText = '上传文件',
    this.icon,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => _showFilePicker(context),
      icon: Icon(icon ?? Icons.upload_file),
      label: Text(buttonText),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
      ),
    );
  }

  void _showFilePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: Icon(Icons.photo_camera),
                title: Text('拍照'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadFile(context, ImageSource.camera);
                },
              ),
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('从相册选择'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadFile(context, ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickAndUploadFile(BuildContext context, ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (image != null) {
        final uploadService = UploadService();
        final result = await uploadService.uploadFile(File(image.path));
        onUploadSuccess?.call(result);
      }
    } catch (e) {
      onUploadError?.call('上传失败: $e');
    }
  }
} 