import 'package:flutter/material.dart';
import 'package:ilike/services/upload_service.dart';
import 'package:ilike/widgets/file_upload_widget.dart';
import 'dart:io';

class UploadExample extends StatefulWidget {
  const UploadExample({Key? key}) : super(key: key);

  @override
  State<UploadExample> createState() => _UploadExampleState();
}

class _UploadExampleState extends State<UploadExample> {
  final UploadService _uploadService = UploadService();
  List<UploadResult> _uploadedFiles = [];
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('文件上传示例'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像上传示例
            Text(
              '头像上传',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            FileUploadWidget(
              onUploadSuccess: (result) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('头像上传成功: ${result.fileUrl}')),
                );
              },
              onUploadError: (error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('头像上传失败: $error')),
                );
              },
              placeholderText: '上传头像',
              placeholderIcon: Icons.person,
            ),
            
            SizedBox(height: 32),
            
            // 文件上传按钮示例
            Text(
              '文件上传按钮',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            FileUploadButton(
              onUploadSuccess: (result) {
                setState(() {
                  _uploadedFiles.add(result);
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('文件上传成功: ${result.fileName}')),
                );
              },
              onUploadError: (error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('文件上传失败: $error')),
                );
              },
              buttonText: '上传图片',
              icon: Icons.upload_file,
            ),
            
            SizedBox(height: 32),
            
            // 批量上传示例
            Text(
              '批量上传',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isUploading ? null : _batchUpload,
              child: _isUploading 
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('上传中...'),
                    ],
                  )
                : Text('批量上传测试文件'),
            ),
            
            SizedBox(height: 32),
            
            // 已上传文件列表
            if (_uploadedFiles.isNotEmpty) ...[
              Text(
                '已上传文件',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 16),
              ..._uploadedFiles.map((file) => _buildFileItem(file)),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _batchUpload() async {
    setState(() {
      _isUploading = true;
    });

    try {
      // 创建一些测试文件（这里只是示例，实际应该从用户选择）
      final results = await _uploadService.uploadFiles([]);
      
      setState(() {
        _uploadedFiles.addAll(results);
        _isUploading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('批量上传完成，成功上传 ${results.length} 个文件')),
      );
    } catch (e) {
      setState(() {
        _isUploading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('批量上传失败: $e')),
      );
    }
  }

  Widget _buildFileItem(UploadResult file) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(Icons.file_present),
        title: Text(file.fileName),
        subtitle: Text('${(file.fileSize / 1024).toStringAsFixed(1)} KB'),
        trailing: IconButton(
          icon: Icon(Icons.delete),
          onPressed: () {
            setState(() {
              _uploadedFiles.remove(file);
            });
          },
        ),
        onTap: () {
          // 可以在这里添加预览文件的功能
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('文件URL: ${file.fileUrl}')),
          );
        },
      ),
    );
  }
}

// 使用示例
class UploadServiceExample {
  static Future<void> uploadSingleFile() async {
    final uploadService = UploadService();
    
    try {
      // 这里应该从用户选择文件
      // final file = await pickFile();
      // final result = await uploadService.uploadFile(file);
      // print('上传成功: ${result.fileUrl}');
    } catch (e) {
      print('上传失败: $e');
    }
  }

  static Future<void> uploadMultipleFiles() async {
    final uploadService = UploadService();
    
    try {
      // 这里应该从用户选择多个文件
      // final files = await pickMultipleFiles();
      // final results = await uploadService.uploadFiles(files);
      // print('批量上传成功: ${results.length} 个文件');
    } catch (e) {
      print('批量上传失败: $e');
    }
  }

  static Future<void> getUploadToken() async {
    final uploadService = UploadService();
    
    try {
      final token = await uploadService.getUploadToken();
      print('获取上传令牌成功: $token');
    } catch (e) {
      print('获取上传令牌失败: $e');
    }
  }
} 