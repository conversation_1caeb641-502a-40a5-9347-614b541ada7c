import 'package:flutter/foundation.dart';
import 'http/http_client_export.dart';
import 'auth_service.dart';
import 'records_service.dart';
import 'category_service.dart';
import 'schema_service.dart';
import 'comment_service.dart';

/// 服务工厂
/// 提供统一的服务访问接口
class ServiceFactory {
  // 单例实例
  static ServiceFactory? _instance;
  static ServiceFactory get instance => _instance ??= ServiceFactory._();
  
  ServiceFactory._();

  // 服务实例缓存
  AuthService? _authService;
  RecordsService? _recordsService;
  CategoryService? _categoryService;
  SchemaService? _schemaService;
  CommentService? _commentService;

  // HTTP客户端实例
  HttpClient? _userServiceClient;
  HttpClient? _iLikeServiceClient;

  /// 获取用户服务HTTP客户端
  HttpClient get userServiceClient {
    _userServiceClient ??= HttpClientFactory.getUserServiceClient();
    return _userServiceClient!;
  }

  /// 获取iLike服务HTTP客户端
  HttpClient get iLikeServiceClient {
    _iLikeServiceClient ??= HttpClientFactory.getILikeServiceClient();
    return _iLikeServiceClient!;
  }

  /// 获取认证服务
  AuthService get authService {
    _authService ??= AuthService(httpClient: userServiceClient);
    return _authService!;
  }

  /// 获取记录服务
  RecordsService get recordsService {
    _recordsService ??= RecordsService(httpClient: iLikeServiceClient);
    return _recordsService!;
  }

  /// 获取分类服务
  CategoryService get categoryService {
    _categoryService ??= CategoryService(httpClient: iLikeServiceClient);
    return _categoryService!;
  }

  /// 获取Schema服务
  SchemaService get schemaService {
    _schemaService ??= SchemaService(httpClient: iLikeServiceClient);
    return _schemaService!;
  }

  /// 获取评论服务
  CommentService get commentService {
    _commentService ??= CommentService(httpClient: iLikeServiceClient);
    return _commentService!;
  }

  /// 创建自定义认证服务
  AuthService createAuthService({HttpClient? httpClient}) {
    return AuthService(httpClient: httpClient ?? userServiceClient);
  }

  /// 创建自定义记录服务
  RecordsService createRecordsService({HttpClient? httpClient}) {
    return RecordsService(httpClient: httpClient ?? iLikeServiceClient);
  }

  /// 创建自定义分类服务
  CategoryService createCategoryService({HttpClient? httpClient}) {
    return CategoryService(httpClient: httpClient ?? iLikeServiceClient);
  }

  /// 创建自定义Schema服务
  SchemaService createSchemaService({HttpClient? httpClient}) {
    return SchemaService(httpClient: httpClient ?? iLikeServiceClient);
  }

  /// 创建自定义评论服务
  CommentService createCommentService({HttpClient? httpClient}) {
    return CommentService(httpClient: httpClient ?? iLikeServiceClient);
  }

  /// 清除所有服务缓存
  void clearCache() {
    _authService = null;
    _recordsService = null;
    _categoryService = null;
    _schemaService = null;
    _commentService = null;
    _userServiceClient = null;
    _iLikeServiceClient = null;
  }

  /// 关闭所有HTTP客户端
  void dispose() {
    HttpClientFactory.closeAll();
    clearCache();
  }

  /// 测试所有服务连接
  Future<Map<String, bool>> testAllServices() async {
    final results = <String, bool>{};
    
    try {
      debugPrint('开始测试所有服务连接...');
      
      // 测试认证服务
      try {
        await authService.getCaptcha();
        results['authService'] = true;
        debugPrint('✅ 认证服务连接正常');
      } catch (e) {
        results['authService'] = false;
        debugPrint('❌ 认证服务连接失败: $e');
      }
      
      // 测试记录服务
      try {
        await recordsService.getAllRecords(limit: 1);
        results['recordsService'] = true;
        debugPrint('✅ 记录服务连接正常');
      } catch (e) {
        results['recordsService'] = false;
        debugPrint('❌ 记录服务连接失败: $e');
      }
      
      // 测试分类服务
      try {
        await categoryService.getAllCategories();
        results['categoryService'] = true;
        debugPrint('✅ 分类服务连接正常');
      } catch (e) {
        results['categoryService'] = false;
        debugPrint('❌ 分类服务连接失败: $e');
      }
      
      // 测试Schema服务
      try {
        await schemaService.getAllSchemas();
        results['schemaService'] = true;
        debugPrint('✅ Schema服务连接正常');
      } catch (e) {
        results['schemaService'] = false;
        debugPrint('❌ Schema服务连接失败: $e');
      }
      
      // 测试评论服务
      try {
        await commentService.getCommentList(limit: 1);
        results['commentService'] = true;
        debugPrint('✅ 评论服务连接正常');
      } catch (e) {
        results['commentService'] = false;
        debugPrint('❌ 评论服务连接失败: $e');
      }
      
      debugPrint('服务连接测试完成');
    } catch (e) {
      debugPrint('服务连接测试异常: $e');
    }
    
    return results;
  }
} 