import 'dart:io';
import 'package:flutter/foundation.dart';
import 'http/http_client_export.dart';

/// 上传结果
class UploadResult {
  final String fileUrl;
  final String fileName;
  final int fileSize;
  final String mimeType;

  UploadResult({
    required this.fileUrl,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
  });

  factory UploadResult.fromJson(Map<String, dynamic> json) {
    return UploadResult(
      fileUrl: json['file_url'] ?? '',
      fileName: json['file_name'] ?? '',
      fileSize: json['file_size'] ?? 0,
      mimeType: json['mime_type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'file_url': fileUrl,
      'file_name': fileName,
      'file_size': fileSize,
      'mime_type': mimeType,
    };
  }
}

/// 上传服务
/// 负责处理文件上传相关功能
class UploadService {
  final HttpClient _httpClient;

  UploadService({HttpClient? httpClient})
      : _httpClient = httpClient ?? HttpClientFactory.getILikeServiceClient();

  // 上传文件
  Future<UploadResult> uploadFile(File file) async {
    try {
      // 这里需要实现文件上传逻辑
      // 由于新的HTTP客户端不直接支持文件上传，需要扩展或使用其他方式
      
      // 临时实现：返回模拟数据
      return UploadResult(
        fileUrl: 'https://example.com/uploads/${file.path.split('/').last}',
        fileName: file.path.split('/').last,
        fileSize: await file.length(),
        mimeType: 'application/octet-stream',
      );
    } catch (e) {
      debugPrint('文件上传失败: $e');
      throw HttpError(
        statusCode: 500,
        message: '文件上传失败: $e',
      );
    }
  }

  // 上传图片
  Future<UploadResult> uploadImage(File imageFile) async {
    try {
      // 这里需要实现图片上传逻辑
      // 由于新的HTTP客户端不直接支持文件上传，需要扩展或使用其他方式
      
      // 临时实现：返回模拟数据
      return UploadResult(
        fileUrl: 'https://example.com/images/${imageFile.path.split('/').last}',
        fileName: imageFile.path.split('/').last,
        fileSize: await imageFile.length(),
        mimeType: 'image/jpeg',
      );
    } catch (e) {
      debugPrint('图片上传失败: $e');
      throw HttpError(
        statusCode: 500,
        message: '图片上传失败: $e',
      );
    }
  }

  // 删除文件
  Future<bool> deleteFile(String fileUrl) async {
    try {
      final response = await _httpClient.post<dynamic>(
        '/api/upload/delete',
        data: {'file_url': fileUrl},
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '删除文件失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('删除文件失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('删除文件异常: $e');
      rethrow;
    }
  }

  // 获取上传配置
  Future<Map<String, dynamic>> getUploadConfig() async {
    try {
      final response = await _httpClient.get<dynamic>('/api/upload/config');

      if (response.isSuccess) {
        return {'success': true, 'data': response.data};
      } else {
        return {
          'success': false,
          'message': response.errorMessage ?? '获取上传配置失败',
        };
      }
    } on HttpError catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      debugPrint('获取上传配置失败: $e');
      return {'success': false, 'message': '获取上传配置失败，请稍后重试'};
    }
  }

  // 上传多个文件
  Future<List<UploadResult>> uploadFiles(List<File> files) async {
    try {
      final results = <UploadResult>[];
      for (final file in files) {
        final result = await uploadFile(file);
        results.add(result);
      }
      return results;
    } catch (e) {
      debugPrint('批量上传文件失败: $e');
      throw HttpError(
        statusCode: 500,
        message: '批量上传文件失败: $e',
      );
    }
  }

  // 获取上传令牌
  Future<String> getUploadToken() async {
    try {
      final response = await _httpClient.get<dynamic>('/api/upload/token');

      if (response.isSuccess) {
        final data = response.data;
        if (data is Map<String, dynamic> && data['token'] != null) {
          return data['token'] as String;
        }
        throw HttpError(
          statusCode: 500,
          message: '获取上传令牌失败：响应格式错误',
        );
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '获取上传令牌失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('获取上传令牌失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取上传令牌异常: $e');
      throw HttpError(
        statusCode: 500,
        message: '获取上传令牌失败: $e',
      );
    }
  }
} 