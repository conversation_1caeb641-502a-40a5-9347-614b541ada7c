import 'dart:io';
import 'package:flutter/foundation.dart';

import 'http/http_client_export.dart';

/// 头像服务
/// 负责处理头像上传和删除
class AvatarService {
  final HttpClient _httpClient;

  AvatarService({HttpClient? httpClient})
      : _httpClient = httpClient ?? HttpClientFactory.getILikeServiceClient();

  /// 上传头像
  Future<Map<String, dynamic>> uploadAvatar(File imageFile) async {
    try {
      // 这里需要实现文件上传逻辑
      // 由于新的HTTP客户端不直接支持文件上传，需要扩展或使用其他方式
      
      // 临时实现：返回模拟数据
      return {
        'success': true,
        'data': {
          'avatar_url': 'https://example.com/avatar.jpg',
          'file_name': imageFile.path.split('/').last,
          'file_size': await imageFile.length(),
          'mime_type': 'image/jpeg',
        },
      };
    } catch (e) {
      debugPrint('头像上传失败: $e');
      return {
        'success': false,
        'message': '头像上传失败: $e',
      };
    }
  }

  /// 删除头像
  Future<Map<String, dynamic>> deleteAvatar() async {
    try {
      final response = await _httpClient.post<dynamic>(
        '/api/avatar/delete',
        data: {},
      );

      if (response.isSuccess) {
        return {
          'success': true,
          'message': '头像删除成功',
        };
      } else {
        return {
          'success': false,
          'message': response.errorMessage ?? '头像删除失败',
        };
      }
    } catch (e) {
      debugPrint('头像删除失败: $e');
      return {
        'success': false,
        'message': '头像删除失败: $e',
      };
    }
  }
} 