import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import 'http/http_client_export.dart';

/// 评论服务
/// 负责处理评论相关的API请求
class CommentService {
  final HttpClient _httpClient;

  CommentService({HttpClient? httpClient})
      : _httpClient = httpClient ?? HttpClientFactory.getILikeServiceClient();

  // 创建评论
  Future<Map<String, dynamic>> createComment(Map<String, dynamic> commentData) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.create,
        data: commentData,
      );

      if (response.isSuccess) {
        final data = response.data;
        if (data is Map<String, dynamic>) {
          return data;
        }
        return {};
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '创建评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('创建评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('创建评论异常: $e');
      rethrow;
    }
  }

  // 获取评论详情
  Future<Map<String, dynamic>> getCommentDetail(int commentId) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.detail,
        data: {'id': commentId},
      );

      if (response.isSuccess) {
        final data = response.data;
        if (data is Map<String, dynamic>) {
          return data;
        }
        return {};
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '获取评论详情失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('获取评论详情失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取评论详情异常: $e');
      rethrow;
    }
  }

  // 获取评论列表
  Future<List<Map<String, dynamic>>> getCommentList({
    int? recordId,
    int? parentId,
    int? cursor,
    int limit = 20,
  }) async {
    try {
      final body = <String, dynamic>{
        'limit': limit,
      };
      if (recordId != null) {
        body['record_id'] = recordId;
      }
      if (parentId != null) {
        body['parent_id'] = parentId;
      }
      if (cursor != null) {
        body['cursor'] = cursor;
      }

      final response = await _httpClient.post<dynamic>(
        ApiComment.list,
        data: body,
      );

      if (response.isSuccess) {
        final data = response.data;
        if (data is List) {
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        }
        return [];
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '获取评论列表失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('获取评论列表失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取评论列表异常: $e');
      rethrow;
    }
  }

  // 更新评论
  Future<Map<String, dynamic>> updateComment(int commentId, Map<String, dynamic> commentData) async {
    try {
      final data = Map<String, dynamic>.from(commentData);
      data['id'] = commentId;

      final response = await _httpClient.post<dynamic>(
        ApiComment.update,
        data: data,
      );

      if (response.isSuccess) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          return responseData;
        }
        return {};
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '更新评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('更新评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('更新评论异常: $e');
      rethrow;
    }
  }

  // 删除评论
  Future<bool> deleteComment(int commentId) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.delete,
        data: {'id': commentId},
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '删除评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('删除评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('删除评论异常: $e');
      rethrow;
    }
  }

  // 点赞评论
  Future<bool> likeComment(int commentId) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.like,
        data: {'id': commentId},
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '点赞评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('点赞评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('点赞评论异常: $e');
      rethrow;
    }
  }

  // 取消点赞评论
  Future<bool> unlikeComment(int commentId) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.unlike,
        data: {'id': commentId},
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '取消点赞评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('取消点赞评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('取消点赞评论异常: $e');
      rethrow;
    }
  }

  // 获取用户评论
  Future<List<Map<String, dynamic>>> getUserComments({
    int? userId,
    int? cursor,
    int limit = 20,
  }) async {
    try {
      final body = <String, dynamic>{
        'limit': limit,
      };
      if (userId != null) {
        body['user_id'] = userId;
      }
      if (cursor != null) {
        body['cursor'] = cursor;
      }

      final response = await _httpClient.post<dynamic>(
        ApiComment.user,
        data: body,
      );

      if (response.isSuccess) {
        final data = response.data;
        if (data is List) {
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        }
        return [];
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '获取用户评论失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('获取用户评论失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取用户评论异常: $e');
      rethrow;
    }
  }

  // 根据记录ID获取评论列表
  Future<List<Map<String, dynamic>>> getCommentsByRecord(int recordId) async {
    try {
      final response = await _httpClient.post<dynamic>(
        ApiComment.list,
        data: {'record_id': recordId},
      );

      if (response.isSuccess) {
        final data = response.data;
        if (data is List) {
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        }
        return [];
      } else {
        throw HttpError(
          statusCode: response.statusCode,
          message: response.errorMessage ?? '获取评论列表失败',
        );
      }
    } on HttpError catch (e) {
      debugPrint('获取评论列表失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取评论列表异常: $e');
      rethrow;
    }
  }

} 