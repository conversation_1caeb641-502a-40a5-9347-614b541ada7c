# 统一文件上传功能

## 概述

本项目实现了统一的文件上传功能，使用 users 服务的 `/upload-token` 接口接入 OSS 客户端上传能力。支持单文件上传、批量上传、头像上传等功能。

## 功能特点

- ✅ 使用 users 服务的 `/upload-token` 接口获取上传令牌
- ✅ 支持 OSS 客户端直传，减少服务器压力
- ✅ 自动生成 UUID 文件名，保持文件后缀
- ✅ 支持多种文件类型（图片、文档、PDF等）
- ✅ 提供完整的错误处理和进度指示
- ✅ 支持批量上传
- ✅ 提供可复用的 UI 组件

## 核心组件

### 1. UploadService

主要的文件上传服务类，提供以下功能：

```dart
final uploadService = UploadService();

// 上传单个文件
final result = await uploadService.uploadFile(file);

// 批量上传文件
final results = await uploadService.uploadFiles(files);

// 获取上传令牌
final token = await uploadService.getUploadToken();
```

### 2. FileUploadWidget

可复用的文件上传组件：

```dart
FileUploadWidget(
  onUploadSuccess: (result) {
    print('上传成功: ${result.fileUrl}');
  },
  onUploadError: (error) {
    print('上传失败: $error');
  },
  placeholderText: '上传图片',
  placeholderIcon: Icons.add_a_photo,
)
```

### 3. FileUploadButton

文件上传按钮组件：

```dart
FileUploadButton(
  onUploadSuccess: (result) {
    print('上传成功: ${result.fileName}');
  },
  onUploadError: (error) {
    print('上传失败: $error');
  },
  buttonText: '上传文件',
  icon: Icons.upload_file,
)
```

## 使用示例

### 1. 头像上传

```dart
import 'package:ilike/services/avatar_service.dart';

final avatarService = AvatarService();

// 上传头像
final result = await avatarService.uploadAvatar(imageFile);
if (result['success']) {
  print('头像上传成功: ${result['data']['avatar_url']}');
} else {
  print('头像上传失败: ${result['message']}');
}
```

### 2. 个人资料页面集成

```dart
// 在 ProfileAvatar 组件中使用
ProfileAvatar(
  avatarUrl: user?.avatarUrl,
  username: user?.username ?? '用户',
  radius: 35,
  onImageSelected: (file) async {
    final result = await avatarService.uploadAvatar(file);
    if (result['success']) {
      // 刷新用户信息
      await authProvider.validateToken();
    }
  },
)
```

### 3. 通用文件上传

```dart
import 'package:ilike/services/upload_service.dart';

final uploadService = UploadService();

// 上传单个文件
try {
  final result = await uploadService.uploadFile(file);
  print('文件上传成功: ${result.fileUrl}');
  print('文件名: ${result.fileName}');
  print('文件大小: ${result.fileSize} bytes');
  print('MIME类型: ${result.mimeType}');
} catch (e) {
  print('文件上传失败: $e');
}
```

## API 接口说明

### 获取上传令牌

**接口**: `GET /api/user/upload-token`

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "access_key_id": "your_access_key_id",
    "access_key_secret": "your_access_key_secret",
    "security_token": "your_security_token",
    "bucket": "your_bucket_name",
    "endpoint": "oss-cn-hangzhou.aliyuncs.com",
    "prefix": "uploads/2024/01",
    "expire_at": "2024-01-01T12:00:00Z"
  }
}
```

### 上传流程

1. **获取上传令牌**: 调用 `/api/user/upload-token` 获取 OSS 上传凭证
2. **检查令牌有效期**: 验证 `expire_at` 是否过期
3. **生成文件名**: 使用 UUID + 原文件后缀生成唯一文件名
4. **构建上传路径**: 使用 `prefix` + 文件名构建完整路径
5. **上传到 OSS**: 使用获取的凭证直接上传到 OSS
6. **返回结果**: 返回文件访问 URL 和元信息

## 文件命名规则

- 使用 UUID v4 生成唯一文件名
- 保持原文件的后缀名
- 格式: `{uuid}.{extension}`
- 示例: `550e8400-e29b-41d4-a716-************.jpg`

## 支持的文件类型

| 文件类型 | 扩展名 | MIME 类型 |
|---------|--------|-----------|
| JPEG 图片 | .jpg, .jpeg | image/jpeg |
| PNG 图片 | .png | image/png |
| GIF 图片 | .gif | image/gif |
| WebP 图片 | .webp | image/webp |
| PDF 文档 | .pdf | application/pdf |
| Word 文档 | .doc | application/msword |
| Word 文档 | .docx | application/vnd.openxmlformats-officedocument.wordprocessingml.document |
| Excel 表格 | .xls | application/vnd.ms-excel |
| Excel 表格 | .xlsx | application/vnd.openxmlformats-officedocument.spreadsheetml.sheet |
| 文本文件 | .txt | text/plain |
| 其他文件 | 其他 | application/octet-stream |

## 错误处理

### 常见错误

1. **令牌过期**: 重新获取上传令牌
2. **网络错误**: 检查网络连接
3. **文件格式不支持**: 检查文件类型
4. **文件过大**: 检查文件大小限制
5. **OSS 错误**: 检查 OSS 配置

### 错误处理示例

```dart
try {
  final result = await uploadService.uploadFile(file);
  // 处理成功结果
} catch (e) {
  if (e.toString().contains('令牌已过期')) {
    // 重新获取令牌
    await uploadService.getUploadToken();
  } else if (e.toString().contains('网络错误')) {
    // 提示用户检查网络
    showNetworkErrorDialog();
  } else {
    // 其他错误处理
    showErrorDialog(e.toString());
  }
}
```

## 性能优化

1. **图片压缩**: 上传前自动压缩图片
2. **分片上传**: 大文件支持分片上传
3. **断点续传**: 支持上传中断后继续
4. **并发控制**: 批量上传时控制并发数

## 安全考虑

1. **令牌有效期**: 上传令牌有过期时间
2. **文件类型验证**: 服务端验证文件类型
3. **文件大小限制**: 服务端限制文件大小
4. **访问权限控制**: OSS 访问权限控制

## 依赖项

确保在 `pubspec.yaml` 中添加以下依赖：

```yaml
dependencies:
  uuid: ^4.3.3
  image_picker: ^1.1.2
  http: ^1.1.0
  path: ^1.8.3
```

## 注意事项

1. **网络权限**: 确保应用有网络访问权限
2. **存储权限**: 确保应用有文件读写权限
3. **相机权限**: 使用拍照功能时需要相机权限
4. **相册权限**: 使用相册选择功能时需要相册权限

## 更新日志

### v1.0.0
- 初始版本发布
- 支持单文件上传
- 支持批量上传
- 提供可复用 UI 组件
- 集成头像上传功能 