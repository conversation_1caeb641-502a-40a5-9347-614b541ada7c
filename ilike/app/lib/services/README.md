# Flutter 统一HTTP请求实现

## 概述

本项目实现了一个基于Dio的统一HTTP客户端，遵循Flutter最佳实践，提供了完整的错误处理、重试机制、拦截器、缓存等功能。

## 架构设计

### 核心组件

1. **HttpClient** - 统一的HTTP客户端
2. **BaseService** - 基础服务类，提供通用API调用方法
3. **BasePaginatedService** - 带分页功能的基础服务类
4. **ApiResponse** - 统一响应结构
5. **CacheManager** - 缓存管理器

### 特性

- ✅ 统一的响应格式处理
- ✅ 完整的错误处理机制
- ✅ 自动重试机制
- ✅ 请求/响应拦截器
- ✅ 本地缓存支持
- ✅ 文件上传支持
- ✅ 分页数据支持
- ✅ 结构化日志记录
- ✅ 类型安全的API调用

## 快速开始

### 1. 初始化

在应用启动时初始化环境配置：

```dart
void main() {
  // 初始化环境配置
  EnvManager.init();
  
  runApp(MyApp());
}
```

### 2. 设置认证令牌

```dart
// 设置认证令牌
HttpClient.instance.setAuthToken('your-auth-token');

// 或者通过服务工厂设置所有服务
ServiceFactory.setAuthToken('your-auth-token');
```

### 3. 基本使用

#### 直接使用HttpClient

```dart
final httpClient = HttpClient.instance;

// GET请求
final response = await httpClient.get<Map<String, dynamic>>(
  '/api/v1/users',
  queryParameters: {'page': 1, 'size': 10},
);

if (response.isSuccess) {
  final users = response.data;
  print('获取用户成功: $users');
} else {
  print('获取用户失败: ${response.message}');
}
```

#### 使用基础服务类

```dart
class UserService extends BaseService {
  /// 获取用户信息（返回完整响应）
  Future<ApiResponse<User>> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }

  /// 获取用户信息（只返回数据）
  Future<User> getUserProfileData() async {
    return handleApiCallData(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }
}
```

### 4. 错误处理

```dart
// 使用完整响应（推荐）
try {
  final response = await userService.getUserProfile();
  
  if (response.isSuccess) {
    final user = response.data!;
    print('用户信息: ${user.name}');
    print('请求ID: ${response.requestId}');
    print('响应时间: ${response.timestamp}');
  } else {
    print('业务错误: ${response.message}');
    if (response.hasErrors) {
      response.errors!.forEach((error) {
        print('字段错误: ${error.field} - ${error.message}');
      });
    }
  }
} on ApiException catch (e) {
  // 处理业务逻辑错误
  print('业务错误: ${e.message}');
  if (e.fieldErrors != null) {
    // 处理字段错误
    e.fieldErrors!.forEach((error) {
      print('${error.field}: ${error.message}');
    });
  }
} on NetworkException catch (e) {
  // 处理网络错误
  print('网络错误: ${e.message}');
} catch (e) {
  // 处理其他错误
  print('未知错误: $e');
}

// 使用简化方式（只获取数据）
try {
  final user = await userService.getUserProfileData();
  print('用户信息: ${user.name}');
} catch (e) {
  print('错误: $e');
}
```

### 5. 分页数据

```dart
class RecordService extends BasePaginatedService {
  /// 获取记录列表（返回完整响应）
  Future<ApiResponse<PaginatedData<Record>>> getRecords({
    PaginationParams? pagination,
    String? search,
  }) async {
    final additionalParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      additionalParams['search'] = search;
    }

    return handleApiCall(() async {
      final response = await get<PaginatedData<Record>>(
        '/api/v1/records',
        queryParameters: {
          ...?pagination?.toJson(),
          ...additionalParams,
        },
        converter: (data) => PaginatedData.fromJson(data, Record.fromJson),
      );
      return response;
    }, errorMessage: '获取记录失败');
  }
}

// 使用分页
final pagination = PaginationParams(page: 1, pageSize: 20);
final response = await recordService.getRecords(pagination: pagination);

if (response.isSuccess) {
  final records = response.data!;
  print('总记录数: ${records.total}');
  print('当前页: ${records.page}');
  print('总页数: ${records.totalPages}');
  print('是否有下一页: ${records.hasNextPage}');
  print('请求ID: ${response.requestId}');
} else {
  print('获取记录失败: ${response.message}');
}
```

### 6. 文件上传

```dart
class UploadService extends BaseService {
  Future<String> uploadImage(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        '/api/v1/upload/image',
        filePath: filePath,
        fieldName: 'image',
        converter: (data) => data,
      );
      return response.data!['url'] as String;
    }, errorMessage: '上传图片失败');
  }
}
```

### 7. 缓存使用

```dart
// 启用缓存的请求
final response = await httpClient.get<Map<String, dynamic>>(
  '/api/v1/categories',
  config: RequestConfig(
    enableCache: true,
    cacheDuration: Duration(hours: 1),
  ),
);

// 清除缓存
await httpClient.clearCache();
```

### 8. 带加载提示的API调用

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        try {
          final user = await userService.callWithLoading(
            context,
            () => userService.getUserProfile(),
            errorMessage: '获取用户信息失败',
          );
          // 处理成功响应
        } catch (e) {
          // 错误已经在callWithLoading中处理
        }
      },
      child: Text('获取用户信息'),
    );
  }
}
```

## 配置说明

### 环境配置

```dart
// 开发环境
static const EnvConfig dev = EnvConfig(
  type: EnvType.dev,
  serviceEndpoints: ServiceEndpoints.separated,
  enableLogging: true,
  enablePerformanceMonitoring: true,
  cacheDurationHours: 1,
  imageCdnBaseUrl: 'http://localhost:8081/images',
);

// 生产环境
static const EnvConfig prod = EnvConfig(
  type: EnvType.prod,
  serviceEndpoints: ServiceEndpoints(
    userServiceUrl: 'https://api.ilike-app.com',
    contentServiceUrl: 'https://api.ilike-app.com',
  ),
  enableLogging: false,
  enablePerformanceMonitoring: true,
  cacheDurationHours: 24,
  imageCdnBaseUrl: 'https://cdn.ilike-app.com/images',
);
```

### API配置

```dart
class ApiConfig {
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  static Map<String, String> getHeaders({String? token}) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Tenant-Code': 'ilike',
    };
    
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }
}
```

## 最佳实践

### 1. 服务类设计

```dart
// 推荐：使用单例模式
class UserService extends BaseService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();
  
  // 业务方法
  Future<User> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }
}
```

### 2. 模型类设计

```dart
class User {
  final int id;
  final String name;
  final String email;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
```

### 3. 错误处理

```dart
// 在UI层统一处理错误
void handleApiError(BuildContext context, dynamic error) {
  String message = '请求失败';
  
  if (error is ApiException) {
    message = error.message;
  } else if (error is NetworkException) {
    message = error.message;
  }
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
    ),
  );
}
```

### 4. 状态管理

```dart
class UserProvider extends ChangeNotifier {
  User? _user;
  bool _loading = false;
  String? _error;

  User? get user => _user;
  bool get loading => _loading;
  String? get error => _error;

  Future<void> loadUser() async {
    _loading = true;
    _error = null;
    notifyListeners();

    try {
      _user = await ServiceFactory.userService.getUserProfile();
    } catch (e) {
      _error = e is ApiException ? e.message : '加载用户信息失败';
    } finally {
      _loading = false;
      notifyListeners();
    }
  }
}
```

## 高级功能

### 1. 自定义拦截器

```dart
class CustomInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加自定义请求头
    options.headers['X-Custom-Header'] = 'custom-value';
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 处理响应
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理错误
    handler.next(err);
  }
}

// 添加到HttpClient
HttpClient.instance.dio.interceptors.add(CustomInterceptor());
```

### 2. 请求配置

```dart
final config = RequestConfig(
  connectTimeout: Duration(seconds: 10),
  receiveTimeout: Duration(seconds: 30),
  enableCache: true,
  cacheDuration: Duration(minutes: 30),
  retryCount: 3,
  retryDelay: Duration(seconds: 1),
  enableLogging: true,
);

final response = await httpClient.get<Map<String, dynamic>>(
  '/api/v1/data',
  config: config,
);
```

### 3. 批量操作

```dart
class BatchService extends BaseService {
  Future<List<User>> getUsersByIds(List<int> ids) async {
    return handleApiCall(() async {
      final response = await post<List<dynamic>>(
        '/api/v1/users/batch',
        data: {'ids': ids},
        converter: (data) => (data as List).map((item) => User.fromJson(item)).toList(),
      );
      return response;
    }, errorMessage: '批量获取用户失败');
  }
}
```

## 测试

### 单元测试

```dart
void main() {
  group('UserService Tests', () {
    late UserService userService;

    setUp(() {
      userService = UserService();
    });

    test('should get user profile successfully', () async {
      // Mock HTTP client
      // Test implementation
    });
  });
}
```

### 集成测试

```dart
void main() {
  group('HTTP Client Integration Tests', () {
    test('should handle network errors gracefully', () async {
      // Test network error handling
    });

    test('should retry failed requests', () async {
      // Test retry mechanism
    });
  });
}
```

## 故障排除

### 常见问题

1. **网络超时**
   - 检查网络连接
   - 调整超时配置
   - 启用重试机制

2. **认证失败**
   - 检查token是否有效
   - 确认token格式正确
   - 检查服务器认证配置

3. **缓存问题**
   - 清除缓存：`await httpClient.clearCache()`
   - 检查缓存配置
   - 确认缓存键生成逻辑

4. **响应解析错误**
   - 检查响应格式是否符合预期
   - 确认converter函数正确
   - 查看服务器返回的原始数据

### 调试技巧

1. **启用详细日志**
   ```dart
   EnvManager.init(config: EnvConfig.dev);
   ```

2. **查看请求详情**
   ```dart
   HttpClient.instance.logger.d('Request details');
   ```

3. **检查响应数据**
   ```dart
   print('Response: ${response.data}');
   ```

## 更新日志

### v1.0.0
- 初始版本
- 基础HTTP客户端实现
- 统一响应格式处理
- 错误处理机制
- 缓存支持
- 文件上传功能
- 分页数据支持

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License 