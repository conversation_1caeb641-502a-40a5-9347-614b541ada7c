# API响应使用指南

## 概述

本HTTP客户端设计为返回完整的`ApiResponse<T>`对象，而不是只返回数据。这样设计的好处是：

1. **完整的响应信息** - 可以访问分页参数、元数据、请求ID等
2. **字段级错误处理** - 可以获取具体的字段验证错误
3. **业务逻辑控制** - 可以根据响应码进行不同的业务处理
4. **调试信息** - 可以获取请求ID、时间戳等调试信息

## 响应结构

```dart
class ApiResponse<T> {
  final int code;           // 业务状态码
  final String message;     // 响应消息
  final T? data;           // 响应数据
  final Map<String, dynamic>? meta;  // 元数据（分页、请求ID等）
  final List<FieldError>? errors;    // 字段错误
  final String? requestId;  // 请求ID
  final int timestamp;      // 时间戳

  bool get isSuccess => code == 0;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}
```

## 使用方式

### 1. 获取完整响应（推荐）

```dart
class UserService extends BaseService {
  /// 获取用户信息（返回完整响应）
  Future<ApiResponse<User>> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }
}

// 使用示例
final userService = UserService();

try {
  final response = await userService.getUserProfile();
  
  if (response.isSuccess) {
    final user = response.data!;
    print('用户信息: ${user.name}');
    print('请求ID: ${response.requestId}');
    print('响应时间: ${response.timestamp}');
  } else {
    print('业务错误: ${response.message}');
    if (response.hasErrors) {
      response.errors!.forEach((error) {
        print('字段错误: ${error.field} - ${error.message}');
      });
    }
  }
} catch (e) {
  print('网络错误: $e');
}
```

### 2. 分页数据处理

```dart
class RecordService extends BasePaginatedService {
  /// 获取记录列表（返回完整响应）
  Future<ApiResponse<PaginatedData<Record>>> getRecords({
    PaginationParams? pagination,
    String? search,
  }) async {
    final additionalParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      additionalParams['search'] = search;
    }

    return handleApiCall(() async {
      final response = await get<PaginatedData<Record>>(
        '/api/v1/records',
        queryParameters: {
          ...?pagination?.toJson(),
          ...additionalParams,
        },
        converter: (data) => PaginatedData.fromJson(data, Record.fromJson),
      );
      return response;
    }, errorMessage: '获取记录失败');
  }
}

// 使用示例
final recordService = RecordService();

try {
  final pagination = PaginationParams(page: 1, pageSize: 20);
  final response = await recordService.getRecords(pagination: pagination);
  
  if (response.isSuccess) {
    final recordsData = response.data!;
    print('总记录数: ${recordsData.total}');
    print('当前页: ${recordsData.page}');
    print('总页数: ${recordsData.totalPages}');
    print('是否有下一页: ${recordsData.hasNextPage}');
    
    for (final record in recordsData.list) {
      print('记录: ${record.title}');
    }
  } else {
    print('获取记录失败: ${response.message}');
  }
} catch (e) {
  print('网络错误: $e');
}
```

### 3. 字段错误处理

```dart
class AuthService extends BaseService {
  /// 用户注册（返回完整响应）
  Future<ApiResponse<User>> register({
    required String username,
    required String email,
    required String password,
  }) async {
    return handleApiCall(() async {
      final response = await post<User>(
        '/api/v1/auth/register',
        data: {
          'username': username,
          'email': email,
          'password': password,
        },
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '注册失败');
  }
}

// 使用示例
final authService = AuthService();

try {
  final response = await authService.register(
    username: '',
    email: 'invalid-email',
    password: '123',
  );
  
  if (response.isSuccess) {
    print('注册成功: ${response.data!.name}');
  } else {
    print('注册失败: ${response.message}');
    
    // 处理字段错误
    if (response.hasErrors) {
      final fieldErrors = <String, String>{};
      for (final error in response.errors!) {
        fieldErrors[error.field] = error.message;
      }
      
      // 显示字段错误
      if (fieldErrors.containsKey('username')) {
        print('用户名错误: ${fieldErrors['username']}');
      }
      if (fieldErrors.containsKey('email')) {
        print('邮箱错误: ${fieldErrors['email']}');
      }
      if (fieldErrors.containsKey('password')) {
        print('密码错误: ${fieldErrors['password']}');
      }
    }
  }
} catch (e) {
  print('网络错误: $e');
}
```

### 4. 只获取数据（简化场景）

对于只需要数据的简单场景，可以使用`handleApiCallData`方法：

```dart
class SimpleService extends BaseService {
  /// 获取简单数据（只返回数据）
  Future<User> getUserSimple() async {
    return handleApiCallData(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }
}

// 使用示例
final simpleService = SimpleService();

try {
  final user = await simpleService.getUserSimple();
  print('用户: ${user.name}');
} catch (e) {
  print('错误: $e');
}
```

## 错误处理最佳实践

### 1. 分层错误处理

```dart
Future<void> handleUserOperation() async {
  try {
    final response = await userService.getUserProfile();
    
    if (response.isSuccess) {
      // 处理成功响应
      handleSuccess(response.data!);
    } else {
      // 处理业务错误
      handleBusinessError(response);
    }
  } on ApiException catch (e) {
    // 处理API异常
    handleApiException(e);
  } on NetworkException catch (e) {
    // 处理网络异常
    handleNetworkException(e);
  } catch (e) {
    // 处理其他异常
    handleUnknownException(e);
  }
}

void handleSuccess(User user) {
  print('操作成功: ${user.name}');
}

void handleBusinessError(ApiResponse<User> response) {
  print('业务错误: ${response.message}');
  
  if (response.hasErrors) {
    // 处理字段错误
    for (final error in response.errors!) {
      showFieldError(error.field, error.message);
    }
  }
}

void handleApiException(ApiException e) {
  print('API异常: ${e.message}');
  if (e.fieldErrors != null) {
    for (final error in e.fieldErrors!) {
      showFieldError(error.field, error.message);
    }
  }
}

void handleNetworkException(NetworkException e) {
  print('网络异常: ${e.message}');
  showNetworkError(e.message);
}

void handleUnknownException(dynamic e) {
  print('未知异常: $e');
  showGenericError('操作失败，请重试');
}
```

### 2. UI层错误处理

```dart
class UserProfileWidget extends StatefulWidget {
  @override
  _UserProfileWidgetState createState() => _UserProfileWidgetState();
}

class _UserProfileWidgetState extends State<UserProfileWidget> {
  User? _user;
  bool _loading = false;
  String? _error;
  Map<String, String> _fieldErrors = {};

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      _loading = true;
      _error = null;
      _fieldErrors.clear();
    });

    try {
      final response = await userService.getUserProfile();
      
      if (response.isSuccess) {
        setState(() {
          _user = response.data!;
          _loading = false;
        });
      } else {
        setState(() {
          _error = response.message;
          _loading = false;
        });
        
        // 处理字段错误
        if (response.hasErrors) {
          final fieldErrors = <String, String>{};
          for (final error in response.errors!) {
            fieldErrors[error.field] = error.message;
          }
          setState(() {
            _fieldErrors = fieldErrors;
          });
        }
      }
    } on ApiException catch (e) {
      setState(() {
        _error = e.message;
        _loading = false;
      });
      
      if (e.fieldErrors != null) {
        final fieldErrors = <String, String>{};
        for (final error in e.fieldErrors!) {
          fieldErrors[error.field] = error.message;
        }
        setState(() {
          _fieldErrors = fieldErrors;
        });
      }
    } on NetworkException catch (e) {
      setState(() {
        _error = '网络错误: ${e.message}';
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = '未知错误: $e';
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(_error!, style: TextStyle(color: Colors.red)),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadUserProfile,
              child: Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_user == null) {
      return Center(child: Text('暂无数据'));
    }

    return Column(
      children: [
        // 显示字段错误
        if (_fieldErrors.isNotEmpty) ...[
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.red[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _fieldErrors.entries.map((entry) {
                return Text(
                  '${entry.key}: ${entry.value}',
                  style: TextStyle(color: Colors.red),
                );
              }).toList(),
            ),
          ),
        ],
        
        // 显示用户信息
        ListTile(
          leading: CircleAvatar(child: Text(_user!.name[0])),
          title: Text(_user!.name),
          subtitle: Text(_user!.email),
        ),
      ],
    );
  }
}
```

## 总结

1. **推荐使用完整响应** - 大多数情况下应该使用`handleApiCall`返回完整的`ApiResponse`
2. **简化场景使用数据** - 简单场景可以使用`handleApiCallData`只返回数据
3. **正确处理错误** - 分层处理不同类型的错误
4. **利用元数据** - 充分利用分页、请求ID等元数据信息
5. **字段级错误处理** - 正确处理字段验证错误，提供更好的用户体验

这样的设计既保证了灵活性，又提供了完整的错误处理能力，是Flutter应用中处理HTTP请求的最佳实践。 