import 'package:logger/logger.dart';
import '../core/interfaces.dart';
import '../interceptors/auth_interceptor.dart';
import '../interceptors/logging_interceptor.dart';
import '../interceptors/error_interceptor.dart';
import '../adapters/logger_adapter.dart';
import '../adapters/token_provider_adapter.dart';
import '../../token_manager.dart';
import '../../../config/api_config.dart';
import '../../../config/env_config.dart';
import '../../../config/http_constants.dart';

/// HTTP配置实现
class HttpConfig implements IHttpConfig {
  @override
  final String baseUrl;
  
  @override
  final Duration timeout;
  
  @override
  final Map<String, String> defaultHeaders;
  
  @override
  final List<IHttpInterceptor> interceptors;

  const HttpConfig({
    required this.baseUrl,
    this.timeout = const Duration(seconds: 30),
    this.defaultHeaders = const {},
    this.interceptors = const [],
  });

  /// 创建默认配置
  factory HttpConfig.defaultConfig({
    required String baseUrl,
    required ITokenProvider tokenProvider,
    ILogger? logger,
  }) {
    final defaultLogger = logger ?? LoggerAdapter(Logger());
    
    return HttpConfig(
      baseUrl: baseUrl,
      timeout: HttpConstants.connectTimeout, // 使用统一的超时配置
      defaultHeaders: HttpConstants.defaultHeaders, // 使用统一的请求头配置
      interceptors: [
        AuthInterceptor(tokenProvider),
        LoggingInterceptor(defaultLogger),
        ErrorInterceptor(),
      ],
    );
  }

  /// 创建默认配置（使用TokenManager）
  factory HttpConfig.defaultConfigWithTokenManager({
    required String baseUrl,
    required TokenManager tokenManager,
    ILogger? logger,
  }) {
    final tokenProvider = TokenProviderAdapter(tokenManager);
    return HttpConfig.defaultConfig(
      baseUrl: baseUrl,
      tokenProvider: tokenProvider,
      logger: logger,
    );
  }

  /// 创建用户服务配置
  factory HttpConfig.userService({
    required ITokenProvider tokenProvider,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfig(
      baseUrl: EnvManager.userApiBaseUrl, // 使用EnvManager的用户服务URL
      tokenProvider: tokenProvider,
      logger: logger,
    );
  }

  /// 创建用户服务配置（使用TokenManager）
  factory HttpConfig.userServiceWithTokenManager({
    required TokenManager tokenManager,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfigWithTokenManager(
      baseUrl: EnvManager.userApiBaseUrl, // 使用EnvManager的用户服务URL
      tokenManager: tokenManager,
      logger: logger,
    );
  }

  /// 创建iLike服务配置
  factory HttpConfig.iLikeService({
    required ITokenProvider tokenProvider,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfig(
      baseUrl: EnvManager.contentApiBaseUrl, // 使用EnvManager的内容服务URL
      tokenProvider: tokenProvider,
      logger: logger,
    );
  }

  /// 创建iLike服务配置（使用TokenManager）
  factory HttpConfig.iLikeServiceWithTokenManager({
    required TokenManager tokenManager,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfigWithTokenManager(
      baseUrl: EnvManager.contentApiBaseUrl, // 使用EnvManager的内容服务URL
      tokenManager: tokenManager,
      logger: logger,
    );
  }

  /// 创建OSS服务配置
  factory HttpConfig.ossService({
    required ITokenProvider tokenProvider,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfig(
      baseUrl: EnvManager.ossApiBaseUrl, // 使用EnvManager的OSS服务URL
      tokenProvider: tokenProvider,
      logger: logger,
    );
  }

  /// 创建OSS服务配置（使用TokenManager）
  factory HttpConfig.ossServiceWithTokenManager({
    required TokenManager tokenManager,
    ILogger? logger,
  }) {
    return HttpConfig.defaultConfigWithTokenManager(
      baseUrl: EnvManager.ossApiBaseUrl, // 使用EnvManager的OSS服务URL
      tokenManager: tokenManager,
      logger: logger,
    );
  }
} 