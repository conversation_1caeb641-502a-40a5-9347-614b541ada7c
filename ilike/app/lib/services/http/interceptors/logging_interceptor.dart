import '../core/interfaces.dart';

/// 日志拦截器
class LoggingInterceptor implements IHttpInterceptor {
  final ILogger logger;

  LoggingInterceptor(this.logger);

  @override
  Future<HttpRequest> onRequest(HttpRequest request) async {
    logger.info('HTTP Request: ${request.method} ${request.url}');
    if (request.body != null) {
      logger.debug('Request Body: ${request.body}');
    }
    return request;
  }

  @override
  Future<HttpResponse> onResponse(HttpResponse response) async {
    logger.info('HTTP Response: ${response.statusCode}');
    if (response.data != null) {
      logger.debug('Response Data: ${response.data}');
    }
    return response;
  }

  @override
  Future<HttpError> onError(HttpError error) async {
    logger.error('HTTP Error: ${error.message}', error.originalError);
    return error;
  }
} 