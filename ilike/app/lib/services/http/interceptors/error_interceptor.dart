import '../core/interfaces.dart';

/// 错误拦截器
class ErrorInterceptor implements IHttpInterceptor {
  @override
  Future<HttpRequest> onRequest(HttpRequest request) async {
    return request;
  }

  @override
  Future<HttpResponse> onResponse(HttpResponse response) async {
    return response;
  }

  @override
  Future<HttpError> onError(HttpError error) async {
    // 统一错误处理逻辑
    switch (error.statusCode) {
      case 401:
        // 处理未授权 - 可以在这里触发重新登录
        break;
      case 403:
        // 处理禁止访问
        break;
      case 404:
        // 处理资源不存在
        break;
      case 500:
        // 处理服务器错误
        break;
      case 502:
      case 503:
      case 504:
        // 处理网关错误
        break;
    }
    return error;
  }
} 