import '../core/interfaces.dart';

/// 认证拦截器
class AuthInterceptor implements IHttpInterceptor {
  final ITokenProvider tokenProvider;

  AuthInterceptor(this.tokenProvider);

  @override
  Future<HttpRequest> onRequest(HttpRequest request) async {
    final token = await tokenProvider.getToken();
    if (token != null) {
      final headers = Map<String, String>.from(request.headers);
      headers['Authorization'] = 'Bearer $token';
      return request.copyWith(headers: headers);
    }
    return request;
  }

  @override
  Future<HttpResponse> onResponse(HttpResponse response) async {
    return response;
  }

  @override
  Future<HttpError> onError(HttpError error) async {
    // 如果是401错误，清除token
    if (error.statusCode == 401) {
      await tokenProvider.clearToken();
    }
    return error;
  }
} 