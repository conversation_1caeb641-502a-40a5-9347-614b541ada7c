

/// HTTP请求类
class HttpRequest {
  final String method;
  final Uri url;
  final Map<String, String> headers;
  final dynamic body;
  final Duration? timeout;

  HttpRequest({
    required this.method,
    required this.url,
    required this.headers,
    this.body,
    this.timeout,
  });

  HttpRequest copyWith({
    String? method,
    Uri? url,
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
  }) {
    return HttpRequest(
      method: method ?? this.method,
      url: url ?? this.url,
      headers: headers ?? this.headers,
      body: body ?? this.body,
      timeout: timeout ?? this.timeout,
    );
  }
}

/// HTTP响应类
class HttpResponse<T> {
  final int statusCode;
  final Map<String, String> headers;
  final T? data;
  final String? errorMessage;
  final String? requestId;

  HttpResponse({
    required this.statusCode,
    required this.headers,
    this.data,
    this.errorMessage,
    this.requestId,
  });

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
  bool get isClientError => statusCode >= 400 && statusCode < 500;
  bool get isServerError => statusCode >= 500;
}

/// HTTP错误类
class HttpError {
  final int? statusCode;
  final String message;
  final String? requestId;
  final dynamic originalError;

  HttpError({
    this.statusCode,
    required this.message,
    this.requestId,
    this.originalError,
  });
}

/// HTTP客户端接口
abstract class IHttpClient {
  Future<HttpResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  });

  Future<HttpResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  });

  Future<HttpResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  });

  Future<HttpResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  });

  Future<HttpResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  });
}

/// HTTP拦截器接口
abstract class IHttpInterceptor {
  Future<HttpRequest> onRequest(HttpRequest request);
  Future<HttpResponse> onResponse(HttpResponse response);
  Future<HttpError> onError(HttpError error);
}

/// HTTP配置接口
abstract class IHttpConfig {
  String get baseUrl;
  Duration get timeout;
  Map<String, String> get defaultHeaders;
  List<IHttpInterceptor> get interceptors;
}

/// Token提供者接口
abstract class ITokenProvider {
  Future<String?> getToken();
  Future<void> setToken(String token);
  Future<void> clearToken();
}

/// 日志记录器接口
abstract class ILogger {
  void info(String message);
  void warn(String message);
  void error(String message, [dynamic error, StackTrace? stackTrace]);
  void debug(String message);
} 