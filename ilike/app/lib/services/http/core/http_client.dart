import 'package:http/http.dart' as http;

import 'interfaces.dart';
import '../builders/request_builder.dart';
import '../handlers/response_handler.dart';

/// HTTP客户端实现
class HttpClient implements IHttpClient {
  final IHttpConfig config;
  final List<IHttpInterceptor> interceptors;
  final http.Client _client;

  HttpClient({
    required this.config,
    required this.interceptors,
  }) : _client = http.Client();

  @override
  Future<HttpResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _executeRequest<T>(
      method: 'GET',
      path: path,
      queryParameters: queryParameters,
      headers: headers,
      timeout: timeout,
    );
  }

  @override
  Future<HttpResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _executeRequest<T>(
      method: 'POST',
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      timeout: timeout,
    );
  }

  @override
  Future<HttpResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _executeRequest<T>(
      method: 'PUT',
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      timeout: timeout,
    );
  }

  @override
  Future<HttpResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _executeRequest<T>(
      method: 'DELETE',
      path: path,
      queryParameters: queryParameters,
      headers: headers,
      timeout: timeout,
    );
  }

  @override
  Future<HttpResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return _executeRequest<T>(
      method: 'PATCH',
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      timeout: timeout,
    );
  }

  /// 执行HTTP请求的核心方法
  Future<HttpResponse<T>> _executeRequest<T>({
    required String method,
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    try {
      // 构建请求
      final request = RequestBuilder.buildRequest(
        baseUrl: config.baseUrl,
        path: path,
        method: method,
        data: data,
        queryParameters: queryParameters,
        headers: headers,
        defaultHeaders: config.defaultHeaders,
        timeout: timeout ?? config.timeout,
      );

      // 执行请求拦截器
      HttpRequest interceptedRequest = request;
      for (final interceptor in interceptors) {
        interceptedRequest = await interceptor.onRequest(interceptedRequest);
      }

      // 执行HTTP请求
      http.Response response;
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(
            interceptedRequest.url,
            headers: interceptedRequest.headers,
          ).timeout(interceptedRequest.timeout ?? config.timeout);
          break;
        case 'POST':
          response = await _client.post(
            interceptedRequest.url,
            headers: interceptedRequest.headers,
            body: interceptedRequest.body,
          ).timeout(interceptedRequest.timeout ?? config.timeout);
          break;
        case 'PUT':
          response = await _client.put(
            interceptedRequest.url,
            headers: interceptedRequest.headers,
            body: interceptedRequest.body,
          ).timeout(interceptedRequest.timeout ?? config.timeout);
          break;
        case 'DELETE':
          response = await _client.delete(
            interceptedRequest.url,
            headers: interceptedRequest.headers,
          ).timeout(interceptedRequest.timeout ?? config.timeout);
          break;
        case 'PATCH':
          response = await _client.patch(
            interceptedRequest.url,
            headers: interceptedRequest.headers,
            body: interceptedRequest.body,
          ).timeout(interceptedRequest.timeout ?? config.timeout);
          break;
        default:
          throw HttpError(
            message: 'Unsupported HTTP method: $method',
            statusCode: 400,
          );
      }

      // 处理响应
      final httpResponse = ResponseHandler.handleResponse<T>(response);

      // 执行响应拦截器
      HttpResponse interceptedResponse = httpResponse;
      for (final interceptor in interceptors) {
        interceptedResponse = await interceptor.onResponse(interceptedResponse);
      }

      return interceptedResponse as HttpResponse<T>;
    } catch (error) {
      // 处理错误
      final httpError = ResponseHandler.handleError(error);
      
      // 执行错误拦截器
      HttpError interceptedError = httpError;
      for (final interceptor in interceptors) {
        interceptedError = await interceptor.onError(interceptedError);
      }

      throw interceptedError;
    }
  }

  /// 关闭HTTP客户端
  void close() {
    _client.close();
  }
} 