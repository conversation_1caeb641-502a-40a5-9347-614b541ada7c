import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../core/interfaces.dart';

/// 响应处理器
class ResponseHandler {
  /// 处理HTTP响应
  static HttpResponse<T> handleResponse<T>(http.Response response) {
    try {
      // 解析响应体
      dynamic data;
      if (response.body.isNotEmpty) {
        try {
          data = json.decode(response.body);
        } catch (e) {
          // 如果不是JSON格式，直接使用原始响应体
          data = response.body;
        }
      }

      // 检查HTTP状态码
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return HttpResponse<T>(
          statusCode: response.statusCode,
          headers: response.headers,
          data: data,
          requestId: response.headers['x-request-id'],
        );
      } else {
        // 处理HTTP错误
        String errorMessage = 'HTTP ${response.statusCode}';
        if (data is Map<String, dynamic> && data['message'] != null) {
          errorMessage = data['message'];
        }
        
        throw HttpError(
          statusCode: response.statusCode,
          message: errorMessage,
          requestId: response.headers['x-request-id'],
        );
      }
    } catch (e) {
      if (e is HttpError) {
        rethrow;
      }
      throw HttpError(
        statusCode: response.statusCode,
        message: 'Failed to parse response: $e',
        requestId: response.headers['x-request-id'],
        originalError: e,
      );
    }
  }

  /// 处理错误
  static HttpError handleError(dynamic error) {
    if (error is HttpError) {
      return error;
    }
    
    if (error is http.ClientException) {
      return HttpError(
        message: 'Network error: ${error.message}',
        originalError: error,
      );
    }
    
    if (error is SocketException) {
      return HttpError(
        message: 'Connection failed: ${error.message}',
        originalError: error,
      );
    }
    
    if (error is TimeoutException) {
      return HttpError(
        message: 'Request timeout',
        originalError: error,
      );
    }
    
    return HttpError(
      message: 'Unknown error: $error',
      originalError: error,
    );
  }
} 