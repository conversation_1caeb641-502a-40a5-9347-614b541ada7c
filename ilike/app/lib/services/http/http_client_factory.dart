import 'package:logger/logger.dart';
import 'core/http_client.dart';
import 'core/interfaces.dart';
import 'config/http_config.dart';
import 'adapters/logger_adapter.dart';
import '../token_manager.dart';

/// HTTP客户端工厂
class HttpClientFactory {
  static HttpClient? _userServiceClient;
  static HttpClient? _iLikeServiceClient;

  /// 获取用户服务HTTP客户端
  static HttpClient getUserServiceClient() {
    if (_userServiceClient == null) {
      final config = HttpConfig.userServiceWithTokenManager(
        tokenManager: TokenManager.instance,
        logger: LoggerAdapter(Logger()),
      );
      _userServiceClient = HttpClient(
        config: config,
        interceptors: config.interceptors,
      );
    }
    return _userServiceClient!;
  }

  /// 获取iLike服务HTTP客户端
  static HttpClient getILikeServiceClient() {
    if (_iLikeServiceClient == null) {
      final config = HttpConfig.iLikeServiceWithTokenManager(
        tokenManager: TokenManager.instance,
        logger: LoggerAdapter(Logger()),
      );
      _iLikeServiceClient = HttpClient(
        config: config,
        interceptors: config.interceptors,
      );
    }
    return _iLikeServiceClient!;
  }

  /// 创建自定义HTTP客户端
  static HttpClient createClient({
    required String baseUrl,
    required TokenManager tokenManager,
    ILogger? logger,
    List<IHttpInterceptor>? additionalInterceptors,
  }) {
    final config = HttpConfig.defaultConfigWithTokenManager(
      baseUrl: baseUrl,
      tokenManager: tokenManager,
      logger: logger,
    );

    final interceptors = List<IHttpInterceptor>.from(config.interceptors);
    if (additionalInterceptors != null) {
      interceptors.addAll(additionalInterceptors);
    }

    return HttpClient(
      config: config,
      interceptors: interceptors,
    );
  }

  /// 关闭所有HTTP客户端
  static void closeAll() {
    _userServiceClient?.close();
    _iLikeServiceClient?.close();
    _userServiceClient = null;
    _iLikeServiceClient = null;
  }
} 