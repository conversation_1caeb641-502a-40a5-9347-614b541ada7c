import 'dart:convert';
import '../core/interfaces.dart';

/// 请求构建器
class RequestBuilder {
  /// 构建HTTP请求
  static HttpRequest buildRequest({
    required String baseUrl,
    required String path,
    required String method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    Map<String, String>? defaultHeaders,
    Duration? timeout,
  }) {
    // 构建URL
    final url = _buildUrl(baseUrl, path, queryParameters);
    
    // 构建请求头
    final requestHeaders = _buildHeaders(headers, defaultHeaders);
    
    // 构建请求体
    final requestBody = _buildBody(data, requestHeaders);
    
    return HttpRequest(
      method: method,
      url: url,
      headers: requestHeaders,
      body: requestBody,
      timeout: timeout,
    );
  }

  /// 构建完整URL
  static Uri _buildUrl(String baseUrl, String path, Map<String, dynamic>? queryParameters) {
    final uri = Uri.parse(baseUrl);
    
    // 正确处理路径拼接
    String fullPath;
    if (path.startsWith('/')) {
      // 如果path以/开头，直接拼接
      fullPath = path;
    } else {
      // 如果path不以/开头，需要添加/
      fullPath = '/$path';
    }
    
    // 构建完整URL
    final fullUrl = '$baseUrl$fullPath';
    final fullUri = Uri.parse(fullUrl);
    
    final queryParams = <String, String>{};
    if (queryParameters != null) {
      queryParameters.forEach((key, value) {
        if (value != null) {
          queryParams[key] = value.toString();
        }
      });
    }

    return Uri(
      scheme: fullUri.scheme,
      host: fullUri.host,
      port: fullUri.port,
      path: fullUri.path,
      queryParameters: queryParams.isNotEmpty ? queryParams : null,
    );
  }

  /// 构建请求头
  static Map<String, String> _buildHeaders(
    Map<String, String>? customHeaders,
    Map<String, String>? defaultHeaders,
  ) {
    final headers = <String, String>{};
    
    // 添加默认请求头
    if (defaultHeaders != null) {
      headers.addAll(defaultHeaders);
    }
    
    // 添加自定义请求头（会覆盖默认请求头）
    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }
    
    return headers;
  }

  /// 构建请求体
  static dynamic _buildBody(dynamic data, Map<String, String> headers) {
    if (data == null) return null;
    
    final contentType = headers['Content-Type']?.toLowerCase();
    
    if (contentType?.contains('application/json') == true) {
      return json.encode(data);
    } else if (contentType?.contains('application/x-www-form-urlencoded') == true) {
      if (data is Map<String, dynamic>) {
        return Uri(queryParameters: data.map((key, value) => MapEntry(key, value.toString()))).query;
      }
      return data.toString();
    }
    
    return data;
  }
} 