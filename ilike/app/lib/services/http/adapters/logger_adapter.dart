import 'package:logger/logger.dart';
import '../core/interfaces.dart';

/// Logger适配器
class LoggerAdapter implements ILogger {
  final Logger _logger;

  LoggerAdapter(this._logger);

  @override
  void info(String message) {
    _logger.i(message);
  }

  @override
  void warn(String message) {
    _logger.w(message);
  }

  @override
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  @override
  void debug(String message) {
    _logger.d(message);
  }
} 