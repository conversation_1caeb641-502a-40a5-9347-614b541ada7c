import '../core/interfaces.dart';
import '../../token_manager.dart';

/// TokenProvider适配器
class TokenProviderAdapter implements ITokenProvider {
  final TokenManager _tokenManager;

  TokenProviderAdapter(this._tokenManager);

  @override
  Future<String?> getToken() async {
    final tokenInfo = _tokenManager.tokenInfo;
    if (tokenInfo != null && !tokenInfo.isExpired) {
      return tokenInfo.accessToken;
    }
    return null;
  }

  @override
  Future<void> setToken(String token) async {
    // TokenManager不直接支持设置单个token，这里可以扩展
    // 或者通过其他方式处理
  }

  @override
  Future<void> clearToken() async {
    await _tokenManager.logout();
  }
} 