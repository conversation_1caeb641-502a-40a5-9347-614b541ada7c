import 'package:flutter/material.dart';
import 'package:ilike/services/http/http_client_export.dart';

/// 表单验证错误处理工具
class FormValidationHandler {
  /// 检查是否为表单验证错误（错误码10001）
  static bool isValidationError(dynamic error) {
    if (error is HttpError) {
      // 注意：HttpError没有code字段，这里需要根据实际情况调整
      // 暂时返回false，因为HttpError没有业务错误码概念
      return false;
    }
    return false;
  }

  /// 从API异常中提取字段错误信息
  static Map<String, String> extractFieldErrors(dynamic error) {
    if (error is HttpError) {
      // 注意：HttpError没有fieldErrors字段，这里需要根据实际情况调整
      // 暂时返回空映射
      return {};
    }
    return {};
  }

  /// 处理表单验证错误，返回字段错误映射
  static Map<String, String> handleValidationError(dynamic error) {
    if (isValidationError(error)) {
      return extractFieldErrors(error);
    }
    return {};
  }

  /// 显示表单验证错误到对应的表单字段
  static void showFieldErrors(
    BuildContext context,
    Map<String, String> fieldErrors,
    GlobalKey<FormState> formKey,
  ) {
    // 强制表单重新验证以显示字段错误
    formKey.currentState?.validate();
  }

  /// 清除表单字段错误
  static void clearFieldErrors(
    GlobalKey<FormState> formKey,
    Map<String, String> fieldErrors,
    VoidCallback onClear,
  ) {
    fieldErrors.clear();
    onClear();
    // 重新验证表单以清除错误显示
    formKey.currentState?.validate();
  }

  /// 获取字段错误消息
  static String? getFieldError(Map<String, String> fieldErrors, String fieldName) {
    return fieldErrors[fieldName];
  }

  /// 检查字段是否有错误
  static bool hasFieldError(Map<String, String> fieldErrors, String fieldName) {
    return fieldErrors.containsKey(fieldName) && fieldErrors[fieldName]!.isNotEmpty;
  }

  /// 获取所有字段错误消息
  static List<String> getAllFieldErrors(Map<String, String> fieldErrors) {
    return fieldErrors.values.toList();
  }

  /// 检查是否有任何字段错误
  static bool hasAnyFieldError(Map<String, String> fieldErrors) {
    return fieldErrors.isNotEmpty;
  }

  /// 创建表单字段验证器，支持服务端错误优先显示
  static String? Function(String?)? createFieldValidator(
    Map<String, String> fieldErrors,
    String fieldName,
    String? Function(String?)? clientValidator,
  ) {
    return (value) {
      // 优先显示服务端返回的字段错误
      if (hasFieldError(fieldErrors, fieldName)) {
        return getFieldError(fieldErrors, fieldName);
      }
      
      // 如果没有服务端错误，使用客户端验证器
      if (clientValidator != null) {
        return clientValidator(value);
      }
      
      return null;
    };
  }
} 