// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Comment _$CommentFromJson(Map<String, dynamic> json) => Comment(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      recordId: (json['record_id'] as num).toInt(),
      parentId: (json['parent_id'] as num?)?.toInt() ?? 0,
      content: json['content'] as String,
      likeCount: (json['like_count'] as num?)?.toInt() ?? 0,
      replyCount: (json['reply_count'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$CommentToJson(Comment instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'record_id': instance.recordId,
      'parent_id': instance.parentId,
      'content': instance.content,
      'like_count': instance.likeCount,
      'reply_count': instance.replyCount,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

CommentCreateRequest _$CommentCreateRequestFromJson(
        Map<String, dynamic> json) =>
    CommentCreateRequest(
      recordId: (json['record_id'] as num).toInt(),
      parentId: (json['parent_id'] as num?)?.toInt(),
      content: json['content'] as String,
    );

Map<String, dynamic> _$CommentCreateRequestToJson(
        CommentCreateRequest instance) =>
    <String, dynamic>{
      'record_id': instance.recordId,
      'parent_id': instance.parentId,
      'content': instance.content,
    };

CommentListResponse _$CommentListResponseFromJson(Map<String, dynamic> json) =>
    CommentListResponse(
      comments: (json['comments'] as List<dynamic>)
          .map((e) => Comment.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      totalPages: (json['total_pages'] as num).toInt(),
    );

Map<String, dynamic> _$CommentListResponseToJson(
        CommentListResponse instance) =>
    <String, dynamic>{
      'comments': instance.comments,
      'total': instance.total,
      'page': instance.page,
      'size': instance.size,
      'total_pages': instance.totalPages,
    };
