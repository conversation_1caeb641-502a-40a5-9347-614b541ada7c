import 'package:json_annotation/json_annotation.dart';

part 'comment.g.dart';

@JsonSerializable()
class Comment {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'record_id')
  final int recordId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'parent_id')
  final int parentId;
  final String content;
  @Json<PERSON>ey(name: 'like_count')
  final int likeCount;
  @<PERSON>son<PERSON>ey(name: 'reply_count')
  final int replyCount;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  Comment({
    required this.id,
    required this.userId,
    required this.recordId,
    this.parentId = 0,
    required this.content,
    this.likeCount = 0,
    this.replyCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
  Map<String, dynamic> toJson() => _$CommentToJson(this);

  bool get isReply => parentId > 0;
}

@JsonSerializable()
class CommentCreateRequest {
  @Json<PERSON>ey(name: 'record_id')
  final int recordId;
  @JsonKey(name: 'parent_id')
  final int? parentId;
  final String content;

  CommentCreateRequest({
    required this.recordId,
    this.parentId,
    required this.content,
  });

  factory CommentCreateRequest.fromJson(Map<String, dynamic> json) => _$CommentCreateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CommentCreateRequestToJson(this);
}

@JsonSerializable()
class CommentListResponse {
  final List<Comment> comments;
  final int total;
  final int page;
  final int size;
  @JsonKey(name: 'total_pages')
  final int totalPages;

  CommentListResponse({
    required this.comments,
    required this.total,
    required this.page,
    required this.size,
    required this.totalPages,
  });

  factory CommentListResponse.fromJson(Map<String, dynamic> json) => _$CommentListResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CommentListResponseToJson(this);
}