<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iLike 移动端动态表单组件设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f2f2f7;
            color: #000;
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow-x: hidden;
        }

        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* iOS 状态栏 */
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .bar {
            width: 3px;
            background: #fff;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #fff;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #fff;
            border-radius: 0 1px 1px 0;
        }

        .battery-level {
            position: absolute;
            left: 1px;
            top: 1px;
            height: 8px;
            width: 18px;
            background: #fff;
            border-radius: 1px;
        }

        /* 导航栏 */
        .nav-bar {
            height: 56px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 0.5px solid #c6c6c8;
            position: relative;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
        }

        .back-button {
            position: absolute;
            left: 16px;
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
        }

        /* 主内容区域 */
        .content {
            padding: 0;
            padding-bottom: 84px; /* 为底部导航预留空间 */
        }

        .section {
            background: #fff;
            margin-bottom: 16px;
        }

        .section-header {
            padding: 16px 16px 8px 16px;
            background: #f2f2f7;
            border-bottom: 0.5px solid #c6c6c8;
        }

        .section-title {
            font-size: 13px;
            font-weight: 600;
            color: #6d6d70;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-group {
            background: #fff;
        }

        .field-item {
            padding: 12px 16px;
            border-bottom: 0.5px solid #c6c6c8;
            position: relative;
        }

        .field-item:last-child {
            border-bottom: none;
        }

        .field-label {
            font-size: 16px;
            color: #000;
            margin-bottom: 8px;
            font-weight: 400;
        }

        .field-input {
            width: 100%;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #000;
            padding: 0;
            outline: none;
            font-family: inherit;
        }

        .field-input::placeholder {
            color: #8e8e93;
        }

        /* iOS 样式的输入框 */
        .ios-input-group {
            background: #f2f2f7;
            border-radius: 10px;
            padding: 12px 16px;
            margin-bottom: 16px;
        }

        .ios-input {
            background: transparent;
            border: none;
            font-size: 16px;
            width: 100%;
            color: #000;
            outline: none;
        }

        .ios-input::placeholder {
            color: #8e8e93;
        }

        /* 选择器样式 */
        .selector-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .selector-option {
            padding: 8px 16px;
            background: #f2f2f7;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            color: #000;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .selector-option.selected {
            background: #007AFF;
            color: #fff;
        }

        .selector-option:active {
            transform: scale(0.95);
        }

        /* 开关样式 */
        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ios-switch {
            position: relative;
            width: 51px;
            height: 31px;
            background: #e9e9ea;
            border-radius: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .ios-switch.on {
            background: #34c759;
        }

        .switch-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 27px;
            height: 27px;
            background: #fff;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .ios-switch.on .switch-handle {
            transform: translateX(20px);
        }

        /* 滑块样式 */
        .slider-container {
            margin: 16px 0;
        }

        .slider-track {
            width: 100%;
            height: 4px;
            background: #e9e9ea;
            border-radius: 2px;
            position: relative;
            margin: 16px 0;
        }

        .slider-fill {
            height: 100%;
            background: #007AFF;
            border-radius: 2px;
            width: 60%;
        }

        .slider-thumb {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            border: 1px solid #e9e9ea;
        }

        .slider-value {
            text-align: center;
            font-size: 16px;
            color: #007AFF;
            font-weight: 600;
        }

        /* 文件上传区域 */
        .upload-area {
            background: #f2f2f7;
            border-radius: 10px;
            padding: 24px;
            text-align: center;
            border: 2px dashed #c6c6c8;
            margin: 8px 0;
        }

        .upload-icon {
            font-size: 32px;
            margin-bottom: 8px;
            opacity: 0.6;
        }

        .upload-text {
            color: #8e8e93;
            font-size: 14px;
        }

        /* 图片预览 */
        .image-preview {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 8px;
        }

        .preview-image {
            width: 60px;
            height: 60px;
            background: #f2f2f7;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .preview-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-image {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 20px;
            height: 20px;
            background: #ff3b30;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
            cursor: pointer;
        }

        /* 位置选择器 */
        .location-display {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
        }

        .location-icon {
            color: #ff3b30;
            font-size: 16px;
        }

        .location-text {
            color: #007AFF;
            text-decoration: none;
        }

        /* 评分组件 */
        .rating-container {
            display: flex;
            gap: 4px;
            margin: 8px 0;
        }

        .star {
            font-size: 24px;
            color: #e9e9ea;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .star.filled {
            color: #ffcc02;
        }

        /* 标签输入 */
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            background: #e1f5fe;
            color: #0277bd;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tag-remove {
            color: #0277bd;
            cursor: pointer;
            font-size: 14px;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 414px;
            max-width: 100vw;
            height: 84px;
            background: #f8f9fa;
            border-top: 0.5px solid #c6c6c8;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            color: #8e8e93;
            text-decoration: none;
            min-width: 60px;
            padding: 4px;
        }

        .nav-item.active {
            color: #007AFF;
        }

        .nav-icon {
            font-size: 24px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .field-item {
            animation: fadeIn 0.3s ease-out;
        }

        /* 响应式调整 */
        @media (max-width: 414px) {
            .mobile-container {
                max-width: 100vw;
                box-shadow: none;
            }
            
            .bottom-nav {
                width: 100vw;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #000;
                color: #fff;
            }
            
            .mobile-container {
                background: #1c1c1e;
            }
            
            .nav-bar {
                background: #2c2c2e;
                border-bottom-color: #38383a;
            }
            
            .section {
                background: #1c1c1e;
            }
            
            .section-header {
                background: #2c2c2e;
                border-bottom-color: #38383a;
            }
            
            .field-item {
                border-bottom-color: #38383a;
            }
            
            .ios-input-group {
                background: #2c2c2e;
            }
            
            .upload-area {
                background: #2c2c2e;
                border-color: #38383a;
            }
            
            .bottom-nav {
                background: #2c2c2e;
                border-top-color: #38383a;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <div class="signal-bars">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
                <span>中国移动</span>
            </div>
            <div>9:41</div>
            <div class="status-right">
                <span>100%</span>
                <div class="battery">
                    <div class="battery-level"></div>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <a href="#" class="back-button">‹ 返回</a>
            <div class="nav-title">创建记录</div>
        </div>

        <!-- 主内容 -->
        <div class="content">
            <!-- 基础信息 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">基础信息</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">标题</div>
                        <input type="text" class="field-input" placeholder="请输入标题" value="我的旅行记录">
                    </div>
                    <div class="field-item">
                        <div class="field-label">描述</div>
                        <textarea class="field-input" placeholder="请输入描述" rows="3" style="resize: none;">这次旅行真的很棒，风景美丽，食物美味...</textarea>
                    </div>
                </div>
            </div>

            <!-- 详细信息 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">详细信息</div>
                </div>
                <div class="field-group">
                    <!-- 价格字段 -->
                    <div class="field-item">
                        <div class="field-label">总费用</div>
                        <div style="display: flex; align-items: center;">
                            <span style="color: #8e8e93; margin-right: 8px;">¥</span>
                            <input type="number" class="field-input" placeholder="0.00" value="2850.00" step="0.01">
                        </div>
                    </div>

                    <!-- 日期字段 -->
                    <div class="field-item">
                        <div class="field-label">出发日期</div>
                        <input type="date" class="field-input" value="2024-03-15">
                    </div>

                    <!-- 时间字段 -->
                    <div class="field-item">
                        <div class="field-label">出发时间</div>
                        <input type="time" class="field-input" value="09:30">
                    </div>

                    <!-- 数量字段 -->
                    <div class="field-item">
                        <div class="field-label">人数</div>
                        <input type="number" class="field-input" placeholder="请输入人数" value="2" min="1" max="99">
                    </div>
                </div>
            </div>

            <!-- 选择类型 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">类型选择</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">旅行类型</div>
                        <div class="selector-container">
                            <button class="selector-option selected">休闲旅游</button>
                            <button class="selector-option">商务出行</button>
                            <button class="selector-option">探险旅行</button>
                            <button class="selector-option">文化旅游</button>
                        </div>
                    </div>

                    <div class="field-item">
                        <div class="field-label">交通方式（多选）</div>
                        <div class="selector-container">
                            <button class="selector-option selected">飞机</button>
                            <button class="selector-option selected">高铁</button>
                            <button class="selector-option">汽车</button>
                            <button class="selector-option">轮船</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评分和开关 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">评价设置</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">满意度评分</div>
                        <div class="rating-container">
                            <span class="star filled">★</span>
                            <span class="star filled">★</span>
                            <span class="star filled">★</span>
                            <span class="star filled">★</span>
                            <span class="star">★</span>
                        </div>
                    </div>

                    <div class="field-item">
                        <div class="switch-container">
                            <div class="field-label">公开显示</div>
                            <div class="ios-switch on" onclick="toggleSwitch(this)">
                                <div class="switch-handle"></div>
                            </div>
                        </div>
                    </div>

                    <div class="field-item">
                        <div class="switch-container">
                            <div class="field-label">允许评论</div>
                            <div class="ios-switch on" onclick="toggleSwitch(this)">
                                <div class="switch-handle"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置和媒体 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">位置和媒体</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">位置</div>
                        <div class="location-display">
                            <span class="location-icon">📍</span>
                            <a href="#" class="location-text">北京市朝阳区三里屯</a>
                        </div>
                    </div>

                    <div class="field-item">
                        <div class="field-label">照片</div>
                        <div class="upload-area" onclick="selectImages()">
                            <div class="upload-icon">📷</div>
                            <div class="upload-text">点击添加照片</div>
                        </div>
                        <div class="image-preview">
                            <div class="preview-image">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjZjJmMmY3Ii8+CjxwYXRoIGQ9Im0yMCAyMCA0IDQgOC04IDggOCIgc3Ryb2tlPSIjOGU4ZTkzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+" alt="预览图">
                                <div class="remove-image">×</div>
                            </div>
                            <div class="preview-image">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjZjJmMmY3Ii8+CjxwYXRoIGQ9Im0yMCAyMCA0IDQgOC04IDggOCIgc3Ryb2tlPSIjOGU4ZTkzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+" alt="预览图">
                                <div class="remove-image">×</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">标签</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">添加标签</div>
                        <input type="text" class="field-input" placeholder="输入标签后按回车" id="tagInput">
                        <div class="tag-container">
                            <div class="tag">
                                旅行
                                <span class="tag-remove">×</span>
                            </div>
                            <div class="tag">
                                美食
                                <span class="tag-remove">×</span>
                            </div>
                            <div class="tag">
                                摄影
                                <span class="tag-remove">×</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">高级设置</div>
                </div>
                <div class="field-group">
                    <div class="field-item">
                        <div class="field-label">重要程度</div>
                        <div class="slider-container">
                            <div class="slider-track">
                                <div class="slider-fill" style="width: 60%;">
                                    <div class="slider-thumb"></div>
                                </div>
                            </div>
                            <div class="slider-value">3 / 5</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="#" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">🔍</div>
                <div class="nav-label">搜索</div>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon">➕</div>
                <div class="nav-label">创建</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">❤️</div>
                <div class="nav-label">收藏</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </a>
        </div>
    </div>

    <script>
        // 开关切换
        function toggleSwitch(element) {
            element.classList.toggle('on');
        }

        // 选择器切换
        document.addEventListener('DOMContentLoaded', function() {
            // 单选按钮
            const singleSelectors = document.querySelectorAll('.selector-container')[0];
            if (singleSelectors) {
                singleSelectors.addEventListener('click', function(e) {
                    if (e.target.classList.contains('selector-option')) {
                        // 移除其他选中状态
                        singleSelectors.querySelectorAll('.selector-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        // 添加当前选中状态
                        e.target.classList.add('selected');
                    }
                });
            }

            // 多选按钮
            const multiSelectors = document.querySelectorAll('.selector-container')[1];
            if (multiSelectors) {
                multiSelectors.addEventListener('click', function(e) {
                    if (e.target.classList.contains('selector-option')) {
                        e.target.classList.toggle('selected');
                    }
                });
            }

            // 评分点击
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    stars.forEach((s, i) => {
                        if (i <= index) {
                            s.classList.add('filled');
                        } else {
                            s.classList.remove('filled');
                        }
                    });
                });
            });

            // 标签输入
            const tagInput = document.getElementById('tagInput');
            const tagContainer = tagInput.nextElementSibling;
            
            tagInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && this.value.trim()) {
                    const tagText = this.value.trim();
                    const tag = document.createElement('div');
                    tag.className = 'tag';
                    tag.innerHTML = `
                        ${tagText}
                        <span class="tag-remove">×</span>
                    `;
                    tagContainer.appendChild(tag);
                    this.value = '';
                }
            });

            // 删除标签
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('tag-remove')) {
                    e.target.parentElement.remove();
                }
            });

            // 删除图片
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-image')) {
                    e.target.parentElement.remove();
                }
            });
        });

        // 选择图片
        function selectImages() {
            // 模拟选择图片
            alert('这里会打开图片选择器');
        }

        // 滑块拖拽
        document.addEventListener('DOMContentLoaded', function() {
            const sliderThumbs = document.querySelectorAll('.slider-thumb');
            sliderThumbs.forEach(thumb => {
                let isDragging = false;
                const track = thumb.closest('.slider-track');
                const fill = track.querySelector('.slider-fill');
                const valueDisplay = track.nextElementSibling;

                thumb.addEventListener('mousedown', startDrag);
                thumb.addEventListener('touchstart', startDrag);

                function startDrag(e) {
                    isDragging = true;
                    document.addEventListener('mousemove', drag);
                    document.addEventListener('mouseup', stopDrag);
                    document.addEventListener('touchmove', drag);
                    document.addEventListener('touchend', stopDrag);
                    e.preventDefault();
                }

                function drag(e) {
                    if (!isDragging) return;
                    
                    const rect = track.getBoundingClientRect();
                    const clientX = e.touches ? e.touches[0].clientX : e.clientX;
                    const x = Math.max(0, Math.min(rect.width, clientX - rect.left));
                    const percentage = (x / rect.width) * 100;
                    
                    fill.style.width = percentage + '%';
                    const value = Math.round((percentage / 100) * 5);
                    valueDisplay.textContent = `${value} / 5`;
                }

                function stopDrag() {
                    isDragging = false;
                    document.removeEventListener('mousemove', drag);
                    document.removeEventListener('mouseup', stopDrag);
                    document.removeEventListener('touchmove', drag);
                    document.removeEventListener('touchend', stopDrag);
                }
            });
        });
    </script>
</body>
</html>