# API 概览

## 核心 API 设计原则

遵循 platforms 项目的 API 设计规范：
- 仅使用 GET（查询）和 POST（操作）方法
- 所有参数通过查询字符串或请求体传递
- 统一的响应格式：`{ code, message, data, errors, meta }`

## 主要服务接口

### 1. 项目管理 API
- `POST /api/projects/create` - 创建新项目
- `GET /api/projects/list` - 获取项目列表
- `GET /api/projects/status` - 获取项目状态
- `POST /api/projects/cancel` - 取消项目执行

### 2. 任务管理 API
- `GET /api/tasks/list` - 获取任务列表
- `GET /api/tasks/status` - 获取任务状态
- `POST /api/tasks/retry` - 重试失败任务
- `GET /api/tasks/logs` - 获取任务日志

### 3. 质量管理 API
- `GET /api/quality/report` - 获取质量报告
- `GET /api/quality/metrics` - 获取质量指标
- `POST /api/quality/gate` - 执行质量门禁检查

### 4. 监控 API
- `GET /api/monitoring/health` - 健康检查
- `GET /api/monitoring/metrics` - 获取系统指标
- `GET /api/monitoring/dashboard` - 获取监控仪表板数据

## 响应格式示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "project_id": "proj_001",
    "status": "running",
    "progress": 65
  },
  "errors": [],
  "meta": {
    "timestamp": "2025-07-25T10:30:00Z",
    "request_id": "req_123456"
  }
}
```

## 错误码规范

- 2000xx - 项目管理相关错误
- 2001xx - 任务管理相关错误  
- 2002xx - 质量管理相关错误
- 2003xx - AI 服务相关错误
- 2009xx - 系统通用错误