# 开发指南

## 环境配置

### 必要环境
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- Git 2.0+
- Docker (可选，用于容器化部署)

### 依赖服务
- Nacos 配置中心
- platforms-pkg 共享库

## 项目初始化

### 1. 克隆项目
```bash
cd /Users/<USER>/personal/platforms/auto-coding-system
```

### 2. 初始化 Go 模块
```bash
go mod init auto-coding-system
```

### 3. 配置依赖
```bash
# 添加本地 platforms-pkg 依赖
go mod edit -replace platforms-pkg=../pkg

# 安装依赖
go mod tidy
```

### 4. 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE auto_coding_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行迁移脚本
go run cmd/migrate/main.go
```

## 代码规范

### 目录结构规范
遵循 DDD 分层架构：
```
internal/
├── domain/          # 实体、值对象、聚合根、领域服务
├── application/     # 应用服务、用例、DTO
├── infrastructure/  # 数据访问、外部服务、技术实现
└── interfaces/      # HTTP handlers、gRPC 服务、消息处理
```

### 命名规范
- 包名：小写，简洁明了
- 接口名：以 Interface 结尾或使用动词形式
- 结构体：PascalCase
- 方法：PascalCase（公开）、camelCase（私有）
- 常量：大写下划线分隔

### 错误处理规范
使用 platforms-pkg/common/errors 统一错误处理：
```go
import "platforms-pkg/common/errors"

// 业务错误
err := errors.NewBusinessError(200001, "项目创建失败", details)

// 系统错误
err := errors.NewSystemError("database connection failed", originalErr)
```

## 开发流程

### 1. 功能开发流程
1. 在 domain 层定义实体和业务规则
2. 在 application 层实现用例逻辑
3. 在 infrastructure 层实现技术细节
4. 在 interfaces 层实现 API 接口
5. 编写单元测试和集成测试

### 2. 测试规范
```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./internal/domain/...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 3. 代码质量检查
```bash
# 代码格式化
go fmt ./...

# 静态分析
golangci-lint run

# 安全扫描
gosec ./...
```

## 调试指南

### 日志配置
使用 platforms-pkg/logiface 统一日志接口：
```go
import "platforms-pkg/logiface"

logger := logiface.GetLogger("auto-coding-system")
logger.Info("Processing task", "task_id", taskID)
```

### 性能分析
```bash
# CPU 性能分析
go tool pprof http://localhost:8080/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:8080/debug/pprof/heap
```

## 部署指南

### 本地开发部署
```bash
# 启动服务
./scripts/start.sh

# 或直接运行
go run cmd/main.go
```

### Docker 部署
```bash
# 构建镜像
docker build -t auto-coding-system .

# 运行容器
docker run -d -p 8080:8080 auto-coding-system
```

## 常见问题

### Q: 如何添加新的 AI 角色？
A: 1. 在 domain/agents 中定义角色接口
   2. 在 infrastructure/ai 中实现具体逻辑
   3. 在 application/orchestrator 中注册角色

### Q: 如何扩展任务类型？
A: 1. 在 domain/tasks 中定义任务类型
   2. 实现对应的任务处理器
   3. 更新任务分发逻辑

### Q: 如何配置不同的 LLM 模型？
A: 在 configs/config.yaml 中配置模型参数，支持 OpenAI、Claude、本地模型等。