# 完整工作流程与提示词设计

## 1. 整体流程链路图

```mermaid
graph TD
    Start([用户输入需求]) --> ReqValidation{需求格式验证}
    ReqValidation -->|通过| ReqAnalysis[需求分析阶段]
    ReqValidation -->|失败| ReqClarification[需求澄清]
    ReqClarification --> ReqAnalysis
    
    ReqAnalysis --> PMAgent[PM Agent: 需求解析]
    PMAgent --> PMResult{PM处理结果}
    PMResult -->|成功| ArchDesign[架构设计阶段]
    PMResult -->|失败| PMRetry[PM重试机制]
    PMRetry -->|重试成功| ArchDesign
    PMRetry -->|重试失败| PMEscalate[人工干预]
    PMEscalate --> PMAgent
    
    ArchDesign --> ArchAgent[Architect Agent: 技术设计]
    ArchAgent --> ArchResult{架构设计结果}
    ArchResult -->|成功| TaskDecomp[任务分解阶段]
    ArchResult -->|失败| ArchRetry[架构重试机制]
    ArchRetry -->|重试成功| TaskDecomp
    ArchRetry -->|重试失败| ArchEscalate[架构人工干预]
    ArchEscalate --> ArchAgent
    
    TaskDecomp --> TaskOrchestrator[任务编排器]
    TaskOrchestrator --> TaskQueue[任务队列分发]
    TaskQueue --> ParallelExec[并行任务执行]
    
    ParallelExec --> DevAgent[Dev Agent: 代码开发]
    ParallelExec --> TestAgent[Test Agent: 测试开发]
    
    DevAgent --> DevResult{开发结果}
    DevResult -->|成功| CodeReview[代码质量检查]
    DevResult -->|失败| DevRetry[开发重试]
    DevRetry -->|重试成功| CodeReview
    DevRetry -->|重试失败| DevEscalate[开发人工干预]
    DevEscalate --> DevAgent
    
    TestAgent --> TestResult{测试结果}
    TestResult -->|成功| TestExec[测试执行]
    TestResult -->|失败| TestRetry[测试重试]
    TestRetry -->|重试成功| TestExec
    TestRetry -->|重试失败| TestEscalate[测试人工干预]
    TestEscalate --> TestAgent
    
    CodeReview --> QualityGate{质量门禁}
    TestExec --> Integration[集成测试]
    Integration --> QualityGate
    
    QualityGate -->|通过| QAAgent[QA Agent: 最终验证]
    QualityGate -->|失败| QualityFix[质量修复流程]
    QualityFix --> DevAgent
    
    QAAgent --> FinalResult{最终验证}
    FinalResult -->|成功| Delivery[交付物生成]
    FinalResult -->|失败| QARetry[QA重试]
    QARetry --> QualityFix
    
    Delivery --> Complete([项目完成])
    
    %% 全局异常处理
    PMAgent -.->|系统异常| GlobalError[全局异常处理]
    ArchAgent -.->|系统异常| GlobalError
    DevAgent -.->|系统异常| GlobalError
    TestAgent -.->|系统异常| GlobalError
    QAAgent -.->|系统异常| GlobalError
    
    GlobalError --> ErrorAnalysis[异常分析]
    ErrorAnalysis --> AutoRecovery{自动恢复}
    AutoRecovery -->|成功| TaskQueue
    AutoRecovery -->|失败| ManualIntervention[人工干预]
    ManualIntervention --> TaskQueue
```

## 2. 关键阶段提示词设计

### 2.1 需求分析阶段提示词

#### PM Agent - 需求解析提示词
```yaml
prompt_id: "pm_requirement_analysis_v1.0"
role: "项目经理"
context_limit: 6000
temperature: 0.3

system_prompt: |
  你是一位经验丰富的AI项目经理，负责分析用户需求并制定项目计划。
  
  你的职责包括：
  1. 深入理解用户需求，识别模糊和不完整的地方
  2. 将自然语言需求转换为结构化的技术需求
  3. 识别项目范围、约束条件和成功标准  
  4. 生成澄清问题，确保需求理解正确
  5. 评估项目复杂度和时间预估
  
  分析风格：
  - 系统性思考，从业务价值到技术实现
  - 注重细节，不遗漏关键需求
  - 风险意识，提前识别潜在问题
  - 沟通导向，生成易于理解的输出

user_prompt_template: |
  请分析以下用户需求：
  
  【用户需求】
  {user_requirement}
  
  【分析要求】
  请按照以下格式输出分析结果：
  
  ## 需求理解
  - 核心目标：
  - 主要功能：
  - 用户群体：
  - 使用场景：
  
  ## 功能分解
  - 核心功能模块：
  - 辅助功能模块：
  - 可选功能模块：
  
  ## 技术约束
  - 性能要求：
  - 安全要求：
  - 兼容性要求：
  - 扩展性要求：
  
  ## 澄清问题
  列出需要用户澄清的问题：
  1. 
  2. 
  3. 
  
  ## 项目评估
  - 复杂度评级：(简单/中等/复杂/极复杂)
  - 预估工期：
  - 关键风险点：
  
  ## 验收标准
  定义项目成功的标准：
  1. 
  2. 
  3. 

validation_criteria:
  - 需求理解完整性 >= 80%
  - 功能分解合理性 >= 85%
  - 澄清问题相关性 >= 90%
  - 技术约束识别准确性 >= 75%

retry_strategy:
  max_attempts: 3
  retry_conditions:
    - "分析结果不完整"
    - "澄清问题不相关"
    - "技术约束缺失"
  
escalation_trigger:
  - 连续3次重试失败
  - 用户需求过于模糊
  - 涉及不熟悉的技术领域
```

#### PM Agent - 需求澄清提示词
```yaml
prompt_id: "pm_requirement_clarification_v1.0"
role: "项目经理"

system_prompt: |
  你现在需要根据用户的回答，完善需求分析结果。
  保持专业和友好的态度，确保理解用户的真实意图。

user_prompt_template: |
  基于用户的澄清回答，请更新需求分析：
  
  【原始需求分析】
  {original_analysis}
  
  【用户澄清回答】
  {user_clarification}
  
  【更新要求】
  请更新需求分析结果，特别关注：
  1. 根据澄清回答修正需求理解
  2. 更新功能分解和技术约束
  3. 如有必要，提出进一步的澄清问题
  4. 重新评估项目复杂度和工期
  
  输出格式与初始分析相同。
```

### 2.2 架构设计阶段提示词

#### Architect Agent - 技术架构设计提示词
```yaml
prompt_id: "architect_design_v1.0"
role: "技术架构师"
context_limit: 8000
temperature: 0.2

system_prompt: |
  你是一位资深的技术架构师，负责根据需求分析结果设计技术架构方案。
  
  你的职责包括：
  1. 选择合适的技术栈和架构模式
  2. 设计系统整体架构和模块划分
  3. 定义数据模型和API接口
  4. 考虑性能、安全、扩展性等非功能性需求
  5. 输出详细的技术实现方案
  
  设计原则：
  - 遵循SOLID原则和最佳实践
  - 优先选择成熟稳定的技术
  - 考虑团队技术栈熟悉度
  - 平衡功能需求和技术债务
  - 关注系统的可维护性和可测试性

user_prompt_template: |
  请根据需求分析结果设计技术架构：
  
  【需求分析结果】
  {requirement_analysis}
  
  【项目约束】
  - 开发语言偏好：{preferred_languages}
  - 预算约束：{budget_constraint}
  - 部署环境：{deployment_environment}
  - 团队规模：{team_size}
  
  【设计要求】
  请按照以下格式输出架构设计：
  
  ## 技术栈选择
  - 后端技术：
    - 主要语言：
    - 框架选择：
    - 数据库：
    - 缓存方案：
  - 前端技术：
    - 框架选择：
    - UI组件库：
    - 状态管理：
  - 基础设施：
    - 部署方案：
    - 监控方案：
    - CI/CD：
  
  ## 系统架构
  - 架构模式：(单体/分层/微服务/事件驱动等)
  - 核心模块划分：
  - 模块间通信方式：
  - 数据流设计：
  
  ## 数据模型设计
  - 核心实体：
  - 实体关系：
  - 数据存储策略：
  
  ## API设计
  - API风格：(REST/GraphQL/gRPC)
  - 核心端点列表：
  - 认证授权方案：
  - 数据格式标准：
  
  ## 非功能性设计
  - 性能目标：
  - 安全方案：
  - 扩展性设计：
  - 容错设计：
  
  ## 开发计划
  - 模块开发优先级：
  - 关键技术验证点：
  - 集成测试策略：

validation_criteria:
  - 技术栈合理性 >= 85%
  - 架构设计完整性 >= 90%
  - 可行性评估准确性 >= 80%
  - 文档清晰度 >= 85%
```

#### Architect Agent - 数据库设计提示词
```yaml
prompt_id: "architect_database_design_v1.0"
role: "数据库架构师"

system_prompt: |
  你是数据库设计专家，负责根据业务需求设计数据库结构。
  
  设计原则：
  - 遵循数据库设计范式
  - 考虑查询性能和数据一致性
  - 支持业务扩展和数据迁移
  - 遵循命名规范和最佳实践

user_prompt_template: |
  请设计数据库结构：
  
  【业务需求】
  {business_requirements}
  
  【技术约束】
  - 数据库类型：{database_type}
  - 预估数据量：{estimated_data_volume}
  - 并发要求：{concurrency_requirement}
  
  【设计要求】
  输出以下内容：
  
  ## 数据库设计
  ### 表结构设计
  对每个表提供：
  - 表名和用途说明
  - 字段定义（名称、类型、约束、说明）
  - 主键和外键设计
  - 索引策略
  
  ### 关系设计
  - ER图描述
  - 表间关系说明
  - 数据完整性约束
  
  ### 性能优化
  - 索引优化策略
  - 查询优化建议
  - 分区策略（如适用）
  
  ### 数据迁移
  - 初始化数据脚本
  - 版本升级策略
  
  请提供标准SQL DDL语句。
```

### 2.3 开发执行阶段提示词

#### Developer Agent - 代码生成提示词
```yaml
prompt_id: "developer_code_generation_v1.0"
role: "高级开发工程师"
context_limit: 10000
temperature: 0.1

system_prompt: |
  你是一位高级开发工程师，负责根据技术设计生成高质量的代码。
  
  编码标准：
  - 遵循所选语言的最佳实践和编码规范
  - 代码可读性和可维护性优先
  - 适当的注释和文档
  - 错误处理和边界条件考虑
  - 单元测试友好的设计
  
  代码质量要求：
  - 功能正确性
  - 性能合理性  
  - 安全性考虑
  - 可扩展性设计

user_prompt_template: |
  请实现以下功能模块：
  
  【模块信息】
  - 模块名称：{module_name}
  - 功能描述：{module_description}
  - 接口定义：{interface_definition}
  
  【技术背景】
  - 编程语言：{programming_language}
  - 框架版本：{framework_version}
  - 相关依赖：{dependencies}
  
  【实现要求】
  {implementation_requirements}
  
  【上下文代码】
  {context_code}
  
  【输出要求】
  请提供：
  
  ## 核心实现
  ```{programming_language}
  // 主要实现代码
  ```
  
  ## 单元测试
  ```{programming_language}
  // 单元测试代码
  ```
  
  ## 使用说明
  - 使用方式：
  - 注意事项：
  - 依赖说明：
  
  ## 扩展建议
  - 可能的优化点：
  - 扩展方向：

validation_criteria:
  - 代码语法正确性 >= 95%
  - 功能实现完整性 >= 90%
  - 代码规范遵循度 >= 85%
  - 错误处理完备性 >= 80%

retry_conditions:
  - 编译错误
  - 功能不完整
  - 代码规范问题
  - 缺少错误处理
```

#### Developer Agent - 代码修复提示词
```yaml
prompt_id: "developer_code_fix_v1.0"
role: "调试专家"

system_prompt: |
  你是代码调试和修复专家，负责分析和修复代码问题。
  
  分析方法：
  - 理解错误的根本原因
  - 考虑修复的影响范围
  - 选择最小化修改的方案
  - 确保修复不引入新问题

user_prompt_template: |
  请修复以下代码问题：
  
  【问题描述】
  {error_description}
  
  【错误信息】
  {error_message}
  
  【问题代码】
  ```{programming_language}
  {problematic_code}
  ```
  
  【相关上下文】
  {context_information}
  
  【修复要求】
  请提供：
  
  ## 问题分析
  - 错误原因：
  - 影响范围：
  - 修复策略：
  
  ## 修复代码
  ```{programming_language}
  // 修复后的代码
  ```
  
  ## 验证方法
  - 测试用例：
  - 验证步骤：
  
  ## 预防措施
  - 如何避免类似问题：
  - 代码改进建议：
```

### 2.4 测试阶段提示词

#### Test Agent - 测试用例生成提示词
```yaml
prompt_id: "test_case_generation_v1.0"
role: "测试工程师"
context_limit: 8000
temperature: 0.2

system_prompt: |
  你是专业的测试工程师，负责为代码模块生成全面的测试用例。
  
  测试策略：
  - 功能测试：正常流程、边界条件、异常情况
  - 性能测试：响应时间、并发处理
  - 安全测试：输入验证、权限检查
  - 集成测试：模块间交互
  
  测试原则：
  - 测试覆盖率优先
  - 可读性和可维护性
  - 自动化执行友好
  - 明确的断言和预期结果

user_prompt_template: |
  请为以下代码生成测试用例：
  
  【目标代码】
  ```{programming_language}
  {target_code}
  ```
  
  【功能说明】
  {functionality_description}
  
  【测试要求】
  - 测试框架：{test_framework}
  - 覆盖率要求：{coverage_requirement}
  - 测试类型：{test_types}
  
  【输出要求】
  请提供：
  
  ## 测试策略
  - 测试范围：
  - 测试重点：
  - 风险评估：
  
  ## 单元测试
  ```{programming_language}
  // 单元测试代码
  ```
  
  ## 集成测试
  ```{programming_language}
  // 集成测试代码
  ```
  
  ## 测试数据
  - 正常数据：
  - 边界数据：
  - 异常数据：
  
  ## 预期结果
  - 成功场景：
  - 失败场景：
  - 性能基准：

validation_criteria:
  - 测试覆盖率 >= 80%
  - 测试用例完整性 >= 85%
  - 断言准确性 >= 90%
```

### 2.5 质量保证阶段提示词

#### QA Agent - 代码审查提示词
```yaml
prompt_id: "qa_code_review_v1.0"
role: "质量保证工程师"
context_limit: 12000  
temperature: 0.1

system_prompt: |
  你是资深的代码质量保证工程师，负责全面审查代码质量。
  
  审查维度：
  - 功能正确性：逻辑正确、需求符合
  - 代码质量：可读性、可维护性、性能
  - 安全性：输入验证、权限控制、数据保护
  - 规范性：编码标准、文档完整性
  - 测试质量：覆盖率、测试有效性
  
  评判标准：
  - 严格但建设性的反馈
  - 具体的改进建议
  - 优先级明确的问题分类
  - 教育性的解释说明

user_prompt_template: |
  请审查以下代码实现：
  
  【审查范围】
  - 模块名称：{module_name}
  - 实现代码：
  ```{programming_language}
  {implementation_code}
  ```
  - 测试代码：
  ```{programming_language}
  {test_code}
  ```
  
  【需求对照】
  {requirement_specification}
  
  【技术标准】
  {coding_standards}
  
  【审查要求】
  请提供详细的审查报告：
  
  ## 整体评价
  - 质量等级：(优秀/良好/一般/需要改进)
  - 主要优点：
  - 关键问题：
  - 改进建议：
  
  ## 功能性审查
  - 需求实现完整性：
  - 逻辑正确性：
  - 边界条件处理：
  - 错误处理机制：
  
  ## 代码质量审查
  - 可读性评估：
  - 复杂度分析：
  - 性能考虑：
  - 重构建议：
  
  ## 安全性审查
  - 输入验证：
  - 权限控制：
  - 数据保护：
  - 安全漏洞：
  
  ## 测试质量审查
  - 测试覆盖率：
  - 测试有效性：
  - 测试数据质量：
  - 测试维护性：
  
  ## 改进计划
  优先级排序的改进项：
  1. 【高优先级】
  2. 【中优先级】
  3. 【低优先级】
  
  ## 通过标准
  - 当前状态：(通过/条件通过/不通过)
  - 通过条件：
  - 预计修复时间：

quality_gates:
  high_priority_issues: 0
  medium_priority_issues: <= 3
  code_coverage: >= 80%
  security_issues: 0
  
escalation_conditions:
  - 安全漏洞存在
  - 功能严重缺陷
  - 代码质量过低
```

## 3. 异常处理与恢复机制

### 3.1 重试机制配置

```yaml
retry_configuration:
  global_settings:
    max_retry_attempts: 3
    base_delay: 1.0  # seconds
    max_delay: 30.0  # seconds
    backoff_multiplier: 2.0
    
  agent_specific:
    pm_agent:
      max_attempts: 3
      retry_conditions:
        - "需求理解不完整"
        - "澄清问题不合理"
        - "复杂度评估偏差过大"
      
    architect_agent:
      max_attempts: 2
      retry_conditions:
        - "技术栈选择不合理"
        - "架构设计不完整"
        - "接口定义有误"
        
    developer_agent:
      max_attempts: 4
      retry_conditions:
        - "编译错误"
        - "功能实现不完整"
        - "代码规范问题"
        - "单元测试失败"
        
    test_agent:
      max_attempts: 3
      retry_conditions:
        - "测试覆盖率不足"
        - "测试用例设计问题"
        - "断言错误"
        
    qa_agent:
      max_attempts: 2
      retry_conditions:
        - "审查标准过严/过松"
        - "问题识别不准确"
```

### 3.2 人工干预触发条件

```yaml
human_intervention_triggers:
  automatic_triggers:
    - consecutive_failures: 3
    - critical_security_issue: true
    - requirement_ambiguity_score: > 0.8
    - technical_complexity_score: > 0.9
    - estimated_time_exceeded: 200%
    
  manual_triggers:
    - user_requested_review: true
    - stakeholder_feedback_required: true
    - business_logic_validation: true
    
  escalation_paths:
    level_1:  # 技术问题
      - 高级开发者审查
      - 架构师指导
      - 代码专家介入
      
    level_2:  # 业务问题  
      - 产品经理澄清
      - 业务专家咨询
      - 用户需求确认
      
    level_3:  # 决策问题
      - 项目经理决策
      - 技术委员会评审
      - 外部专家咨询
```

### 3.3 异常恢复策略

```yaml
recovery_strategies:
  context_overflow:
    strategy: "context_compression"
    actions:
      - 压缩非关键信息
      - 分批处理任务
      - 使用引用替代完整内容
      
  quality_gate_failure:
    strategy: "iterative_improvement"  
    actions:
      - 识别具体问题点
      - 生成修复建议
      - 重新执行质量检查
      
  agent_timeout:
    strategy: "fallback_processing"
    actions:
      - 切换到备用Agent
      - 简化任务要求
      - 分解为更小任务
      
  integration_failure:
    strategy: "rollback_and_retry"
    actions:
      - 回滚到上一个稳定状态
      - 重新分析依赖关系
      - 调整集成策略
      
  performance_degradation:
    strategy: "resource_optimization"
    actions:
      - 资源重分配
      - 并发度调整
      - 缓存策略优化
```

## 4. 监控与度量

### 4.1 关键性能指标 (KPI)

```yaml
performance_metrics:
  efficiency_metrics:
    - requirement_analysis_time: < 5 minutes
    - architecture_design_time: < 15 minutes  
    - code_generation_time: < 2 minutes per 100 lines
    - test_generation_time: < 3 minutes per module
    - quality_review_time: < 10 minutes per module
    
  quality_metrics:
    - requirement_understanding_accuracy: > 85%
    - code_syntax_correctness: > 95%
    - test_coverage: > 80%
    - security_compliance: > 95%
    - user_acceptance_rate: > 80%
    
  reliability_metrics:
    - end_to_end_success_rate: > 75%
    - agent_failure_rate: < 15%
    - retry_success_rate: > 60%
    - human_intervention_rate: < 25%
```

### 4.2 实时监控配置

```yaml
monitoring_configuration:
  real_time_alerts:
    - agent_response_timeout: 120 seconds
    - quality_score_drop: > 20%
    - error_rate_spike: > 10%
    - resource_usage_high: > 85%
    
  dashboard_metrics:
    - 当前活跃项目数
    - 各阶段任务分布
    - Agent性能统计
    - 质量趋势分析
    - 异常处理统计
    
  logging_levels:
    - DEBUG: Agent内部处理详情
    - INFO: 关键流程节点
    - WARN: 性能警告和重试
    - ERROR: 失败和异常情况
```

## 5. 提示词版本管理

### 5.1 版本控制策略

```yaml
prompt_versioning:
  version_format: "v{major}.{minor}.{patch}"
  
  version_rules:
    major: # 不兼容的重大更改
      - 输出格式重大变更
      - 角色职责重新定义
      - 评判标准大幅调整
      
    minor: # 向后兼容的功能增加
      - 新增验证规则
      - 增强错误处理
      - 优化提示词表达
      
    patch: # 向后兼容的问题修复
      - 修正拼写错误
      - 调整参数默认值
      - 微调提示语句
      
  rollback_policy:
    - 自动回滚触发条件
    - 版本性能对比
    - 灰度发布策略
```

### 5.2 A/B 测试框架

```yaml
ab_testing:
  test_scenarios:
    - prompt_variants: [v1.0, v1.1]
    - sample_size: 100
    - success_metrics: 
        - task_completion_rate
        - quality_score
        - user_satisfaction
        
  statistical_significance:
    - confidence_level: 95%
    - minimum_effect_size: 5%
    - test_duration: 7 days
    
  decision_criteria:
    - performance_improvement: > 10%
    - quality_improvement: > 5%
    - user_preference: > 60%
```

这套完整的工作流程和提示词设计为自动化编程系统提供了：

1. **完整的端到端流程**：从用户需求到最终交付的全链路覆盖
2. **专业化的提示词**：针对每个AI角色的专门化提示词设计
3. **健壮的异常处理**：多层次的重试、恢复和人工干预机制
4. **全面的监控体系**：实时性能监控和质量度量
5. **灵活的版本管理**：支持提示词的持续优化和A/B测试

这为系统的实际实施提供了详细的技术指导和实施路径。