package mcp

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// Manager MCP数据管理器
type Manager struct {
	basePath string
	mu       sync.RWMutex
	cache    map[string]interface{}
}

// ProjectStatus 项目状态
type ProjectStatus struct {
	ID           string                    `json:"id"`
	Name         string                    `json:"name"`
	Status       string                    `json:"status"`
	Progress     int                       `json:"progress"`
	CurrentPhase string                    `json:"current_phase"`
	Phases       map[string]PhaseInfo      `json:"phases"`
	CreatedAt    time.Time                 `json:"created_at"`
	UpdatedAt    time.Time                 `json:"updated_at"`
	Requirement  string                    `json:"user_requirement"`
	Metadata     map[string]interface{}    `json:"metadata,omitempty"`
}

// PhaseInfo 阶段信息
type PhaseInfo struct {
	Status   string `json:"status"`
	Progress int    `json:"progress"`
}

// TaskData 任务数据
type TaskData struct {
	TaskID      string                 `json:"task_id"`
	TaskType    string                 `json:"task_type"`
	AssignedTo  string                 `json:"assigned_to"`
	ProjectID   string                 `json:"project_id"`
	Description string                 `json:"description"`
	InputData   map[string]interface{} `json:"input_data"`
	Status      string                 `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Result      interface{}            `json:"result,omitempty"`
}

// NewManager 创建MCP管理器
func NewManager(basePath string) *Manager {
	return &Manager{
		basePath: basePath,
		cache:    make(map[string]interface{}),
	}
}

// SaveData 保存数据
func (m *Manager) SaveData(category, filename string, data interface{}) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 创建目录
	dirPath := filepath.Join(m.basePath, category)
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 序列化数据
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化数据失败: %w", err)
	}

	// 保存文件
	filePath := filepath.Join(dirPath, filename)
	if err := os.WriteFile(filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	// 更新缓存
	cacheKey := filepath.Join(category, filename)
	m.cache[cacheKey] = data

	return nil
}

// LoadData 加载数据
func (m *Manager) LoadData(category, filename string, target interface{}) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 先检查缓存
	cacheKey := filepath.Join(category, filename)
	if cached, exists := m.cache[cacheKey]; exists {
		// 将缓存数据复制到目标
		jsonData, err := json.Marshal(cached)
		if err != nil {
			return fmt.Errorf("序列化缓存数据失败: %w", err)
		}
		return json.Unmarshal(jsonData, target)
	}

	// 从文件加载
	filePath := filepath.Join(m.basePath, category, filename)
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取文件失败: %w", err)
	}

	if err := json.Unmarshal(data, target); err != nil {
		return fmt.Errorf("反序列化数据失败: %w", err)
	}

	// 更新缓存
	m.cache[cacheKey] = target

	return nil
}

// CreateProject 创建项目
func (m *Manager) CreateProject(name, requirement string) (*ProjectStatus, error) {
	projectID := fmt.Sprintf("project_%d", time.Now().Unix())
	
	project := &ProjectStatus{
		ID:           projectID,
		Name:         name,
		Status:       "INITIALIZED",
		Progress:     0,
		CurrentPhase: "requirement_analysis",
		Phases: map[string]PhaseInfo{
			"requirement_analysis": {Status: "pending", Progress: 0},
			"architecture_design":  {Status: "pending", Progress: 0},
			"development":          {Status: "pending", Progress: 0},
			"testing":              {Status: "pending", Progress: 0},
			"quality_assurance":    {Status: "pending", Progress: 0},
		},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Requirement: requirement,
		Metadata:    make(map[string]interface{}),
	}

	if err := m.SaveData("projects", fmt.Sprintf("%s.json", projectID), project); err != nil {
		return nil, fmt.Errorf("保存项目失败: %w", err)
	}

	return project, nil
}

// GetProject 获取项目
func (m *Manager) GetProject(projectID string) (*ProjectStatus, error) {
	var project ProjectStatus
	if err := m.LoadData("projects", fmt.Sprintf("%s.json", projectID), &project); err != nil {
		return nil, fmt.Errorf("加载项目失败: %w", err)
	}
	return &project, nil
}

// UpdateProject 更新项目
func (m *Manager) UpdateProject(project *ProjectStatus) error {
	project.UpdatedAt = time.Now()
	return m.SaveData("projects", fmt.Sprintf("%s.json", project.ID), project)
}

// ListProjects 列出所有项目
func (m *Manager) ListProjects() ([]*ProjectStatus, error) {
	projectsDir := filepath.Join(m.basePath, "projects")
	
	// 检查目录是否存在
	if _, err := os.Stat(projectsDir); os.IsNotExist(err) {
		return []*ProjectStatus{}, nil
	}

	entries, err := os.ReadDir(projectsDir)
	if err != nil {
		return nil, fmt.Errorf("读取项目目录失败: %w", err)
	}

	var projects []*ProjectStatus
	for _, entry := range entries {
		if !entry.IsDir() && filepath.Ext(entry.Name()) == ".json" {
			var project ProjectStatus
			if err := m.LoadData("projects", entry.Name(), &project); err != nil {
				continue // 跳过损坏的文件
			}
			projects = append(projects, &project)
		}
	}

	return projects, nil
}

// CreateTask 创建任务
func (m *Manager) CreateTask(taskType, assignedTo, projectID, description string, inputData map[string]interface{}) (*TaskData, error) {
	taskID := fmt.Sprintf("%s_%d", taskType, time.Now().Unix())
	
	task := &TaskData{
		TaskID:      taskID,
		TaskType:    taskType,
		AssignedTo:  assignedTo,
		ProjectID:   projectID,
		Description: description,
		InputData:   inputData,
		Status:      "assigned",
		CreatedAt:   time.Now(),
	}

	if err := m.SaveData("tasks", fmt.Sprintf("%s.json", taskID), task); err != nil {
		return nil, fmt.Errorf("保存任务失败: %w", err)
	}

	return task, nil
}

// UpdateTask 更新任务
func (m *Manager) UpdateTask(task *TaskData) error {
	return m.SaveData("tasks", fmt.Sprintf("%s.json", task.TaskID), task)
}

// GetTask 获取任务
func (m *Manager) GetTask(taskID string) (*TaskData, error) {
	var task TaskData
	if err := m.LoadData("tasks", fmt.Sprintf("%s.json", taskID), &task); err != nil {
		return nil, fmt.Errorf("加载任务失败: %w", err)
	}
	return &task, nil
}

// SaveAgentResult 保存Agent结果
func (m *Manager) SaveAgentResult(agentType, projectID string, result interface{}) error {
	filename := fmt.Sprintf("%s_%s_result.json", projectID, agentType)
	return m.SaveData("agent_results", filename, result)
}

// LoadAgentResult 加载Agent结果
func (m *Manager) LoadAgentResult(agentType, projectID string, target interface{}) error {
	filename := fmt.Sprintf("%s_%s_result.json", projectID, agentType)
	return m.LoadData("agent_results", filename, target)
}

// GetBasePath 获取基础路径
func (m *Manager) GetBasePath() string {
	return m.basePath
}

// ClearCache 清除缓存
func (m *Manager) ClearCache() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.cache = make(map[string]interface{})
}