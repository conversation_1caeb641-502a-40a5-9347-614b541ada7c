# AI Multi-Agent Collaborative Auto-Coding System

基于AI多代理协作的无监督自动化编程系统，通过PM、架构师、开发者、测试和QA五个AI角色的协同工作，实现从需求到代码的全流程自动化。

## 🚀 系统特性

- **多角色协作**：5个专业AI角色分工合作，模拟真实团队开发流程
- **进程内通信**：高效的Agent间通信机制，确保协作流畅
- **完整工作流**：从需求分析到质量评估的端到端自动化
- **状态管理**：基于MCP的项目状态和数据持久化
- **MVP架构**：简化技术栈，专注核心功能验证

## 📋 AI角色说明

| 角色 | 职责 | 主要功能 |
|------|------|----------|
| **PM Agent** | 项目经理 | 需求分析、项目规划、进度管理 |
| **Architect Agent** | 架构师 | 系统架构设计、技术选型、风险评估 |
| **Developer Agent** | 开发专家 | 代码生成、模块开发、技术实现 |
| **Test Agent** | 测试专家 | 测试策略、用例生成、质量验证 |
| **QA Agent** | 质量专家 | 代码审查、质量评估、发布建议 |

## 🏗️ 系统架构

```
auto-coding-system/
├── cmd/                    # 程序入口
│   └── main.go            # CLI主程序
├── internal/              # 核心实现
│   ├── agents/           # AI代理实现
│   │   ├── pm_agent.go
│   │   ├── architect_agent.go
│   │   ├── developer_agent.go
│   │   ├── test_agent.go
│   │   └── qa_agent.go
│   ├── coordinator/      # 系统协调器
│   │   └── system_coordinator.go
│   └── mcp/             # 数据管理
│       └── manager.go
├── mcp-data/            # 项目数据存储
│   ├── projects/        # 项目状态
│   └── agent_results/   # Agent执行结果
└── docs/               # 项目文档
```

## 🔧 技术栈

- **语言**: Go 1.21+
- **架构**: DDD分层架构 + 单体应用（MVP版本）
- **通信**: 进程内通信（基于interface和channel）
- **存储**: 文件系统 + JSON格式（MCP协议）
- **配置**: 环境变量 + 配置文件

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Go版本 >= 1.21
go version

# 克隆项目
git clone <repository-url>
cd auto-coding-system
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 运行系统

#### 完整工作流程
```bash
go run cmd/main.go run "开发一个简单的任务管理系统"
```

#### 查看项目列表
```bash
go run cmd/main.go list
```

#### 查看项目状态
```bash
go run cmd/main.go status <project_id>
```

#### 单独执行Agent
```bash
go run cmd/main.go agent <pm|architect|developer|test|qa> <project_id>
```

## 📖 使用示例

### 创建新项目
```bash
$ go run cmd/main.go run "开发一个博客系统"

🚀 启动自动化编程系统（集成版本）
==================================================
✅ 已注册Agent: pm
✅ 已注册Agent: architect  
✅ 已注册Agent: developer
✅ 已注册Agent: test
✅ 已注册Agent: qa

🎯 开始执行完整的项目开发流程
📋 需求: 开发一个博客系统

🚀 开始执行完整工作流程...
📋 已创建项目: 自动化编程项目 (ID: project_1234567890)

🤖 执行阶段 1/5: pm Agent
📋 [PM Agent] 开始需求分析...
✅ [PM Agent] 需求分析完成，耗时: 5ms
✅ pm Agent 执行完成: completed

# ... 其他Agent执行过程 ...

🎉 项目开发完成！
📊 项目ID: project_1234567890
🎯 最终状态: COMPLETED
📈 完成度: 100%
⭐ 质量评分: B+ (82/100)
```

### 查看项目详情
```bash
$ go run cmd/main.go status project_1234567890

📊 查看项目状态 (项目ID: project_1234567890)
📋 项目名称: 自动化编程项目
🎯 当前阶段: completed
📈 整体进度: 100%
📊 状态: COMPLETED

📋 各阶段详情:
  requirement_analysis: completed (100%)
  architecture_design: completed (100%)
  development: completed (100%)
  testing: completed (100%)
  quality_assurance: completed (100%)
```

## 🏃‍♂️ 工作流程

系统采用5阶段顺序执行的工作流程：

```mermaid
graph LR
    A[需求输入] --> B[PM Agent]
    B --> C[Architect Agent]
    C --> D[Developer Agent]
    D --> E[Test Agent]
    E --> F[QA Agent]
    F --> G[项目完成]
```

1. **需求分析阶段** - PM Agent分析需求，生成项目计划
2. **架构设计阶段** - Architect Agent设计系统架构
3. **开发实现阶段** - Developer Agent生成代码实现
4. **测试验证阶段** - Test Agent设计测试用例
5. **质量评估阶段** - QA Agent进行质量检查

## 📊 输出产物

每个Agent执行完成后会生成相应的产物：

- **PM Agent**: 需求分析报告、项目计划、验收标准
- **Architect Agent**: 系统架构设计、技术选型、数据库设计
- **Developer Agent**: 代码实现、API设计、模块结构
- **Test Agent**: 测试策略、测试用例、覆盖率报告
- **QA Agent**: 质量评估报告、改进建议、发布评估

## 🔧 配置说明

### 环境变量
创建 `.env` 文件配置相关参数：
```bash
# MCP数据存储路径
MCP_DATA_PATH=./mcp-data

# 日志级别
LOG_LEVEL=info

# AI服务配置（如需要）
# OPENAI_API_KEY=your-api-key
# ANTHROPIC_API_KEY=your-api-key
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./internal/agents
go test ./internal/coordinator
go test ./internal/mcp
```

### 集成测试
```bash
# 完整工作流程测试
go run cmd/main.go run "开发一个测试项目"
```

## 📝 开发指南

### 添加新的Agent
1. 在 `internal/agents/` 下创建新的Agent文件
2. 实现 `Agent` 接口
3. 在 `cmd/main.go` 中注册新Agent
4. 更新工作流程配置

### 扩展功能
- 支持更多编程语言
- 增加Web管理界面
- 实现分布式部署
- 增加更多AI模型支持

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🎯 发展路线图

- [x] MVP版本：基础多Agent协作系统
- [ ] v1.1：Web管理界面
- [ ] v1.2：支持多种编程语言
- [ ] v1.3：分布式部署支持
- [ ] v2.0：微服务架构重构

## 📞 联系我们

- 项目地址：[GitHub Repository](https://github.com/your-org/auto-coding-system)
- 问题反馈：[Issues](https://github.com/your-org/auto-coding-system/issues)
- 讨论交流：[Discussions](https://github.com/your-org/auto-coding-system/discussions)

---

**注意**: 这是一个MVP版本的演示系统，主要用于验证AI多代理协作的技术可行性。在生产环境使用前，建议进行充分的测试和安全评估。