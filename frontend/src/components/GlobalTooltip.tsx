import React from 'react';
import { Tooltip, TooltipProps } from 'antd';
import { useTheme } from '../contexts/ThemeContext';
import { componentConfig } from '../styles/theme';

interface GlobalTooltipProps extends Omit<TooltipProps, 'color'> {
  children: React.ReactNode;
}

/**
 * 全局统一的 Tooltip 组件
 * 使用项目统一的颜色配置，支持暗色/亮色主题
 */
const GlobalTooltip: React.FC<GlobalTooltipProps> = ({ 
  children, 
  title,
  placement = 'top',
  ...props 
}) => {
  const { isDarkMode } = useTheme();
  
  // 根据主题模式选择颜色
  const tooltipColor = isDarkMode 
    ? componentConfig.tooltip.darkColor 
    : componentConfig.tooltip.lightColor;

  return (
    <Tooltip
      title={title}
      placement={placement}
      color={tooltipColor}
      {...props}
    >
      {children}
    </Tooltip>
  );
};

export default GlobalTooltip; 