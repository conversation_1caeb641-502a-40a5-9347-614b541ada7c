import React from 'react';
import { Menu } from 'antd';
import { AsyncIcon } from '../utils/iconLoader';
import { SimpleMenuItem } from '../hooks/useSimpleMenu';

interface SimpleMenuProps {
  menuItems: SimpleMenuItem[];
  selectedKeys: string[];
  openKeys: string[];
  onMenuClick: (key: string) => void;
  onOpenChange?: (openKeys: string[]) => void;
  theme?: 'light' | 'dark';
  mode?: 'vertical' | 'horizontal' | 'inline';
  style?: React.CSSProperties;
}

const SimpleMenu: React.FC<SimpleMenuProps> = ({
  menuItems,
  selectedKeys,
  openKeys,
  onMenuClick,
  onOpenChange,
  theme = 'light',
  mode = 'inline',
  style,
}) => {
  // 渲染菜单项
  const renderMenuItems = (items: SimpleMenuItem[]): React.ReactNode[] => {
    return items.map(item => {
      // 分割线
      if (item.type === 'divider') {
        return <Menu.Divider key={item.key} />;
      }

      // 子菜单
      if (item.type === 'submenu' && item.children && item.children.length > 0) {
        return (
          <Menu.SubMenu
            key={item.key}
            icon={item.icon ? <AsyncIcon iconName={item.icon} /> : null}
            title={item.label}
          >
            {renderMenuItems(item.children)}
          </Menu.SubMenu>
        );
      }

      // 普通菜单项
      return (
        <Menu.Item
          key={item.key}
          icon={item.icon ? <AsyncIcon iconName={item.icon} /> : null}
          onClick={() => onMenuClick(item.key)}
        >
          {item.label}
        </Menu.Item>
      );
    });
  };

  return (
    <Menu
      mode={mode}
      theme={theme}
      selectedKeys={selectedKeys}
      openKeys={openKeys}
      onOpenChange={onOpenChange}
      style={{
        background: 'transparent',
        border: 'none',
        fontSize: 14,
        ...style,
      }}
    >
      {renderMenuItems(menuItems)}
    </Menu>
  );
};

export default SimpleMenu;
