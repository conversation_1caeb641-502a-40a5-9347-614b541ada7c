import React, { useState } from 'react';
import { Upload, message, Modal, Image } from 'antd';
import { PlusOutlined, LoadingOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload';
import { FileUploadService } from '../services/fileUpload';
import { SUCCESS } from '../constants/errorCodes';

interface ImageUploadProps {
  value?: string; // 图片URL
  onChange?: (url: string) => void;
  appId?: string;
  sceneCode?: string;
  disabled?: boolean;
  placeholder?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  appId,
  sceneCode = 'app_icon', // 默认场景码为应用图标
  disabled = false,
  placeholder = '点击上传图片'
}) => {
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }
    
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    
    return true;
  };

  const handleUpload = async (file: RcFile) => {
    setLoading(true);
    try {
      const response = await FileUploadService.uploadFile(file, sceneCode, appId);
      if (response.code === SUCCESS && response.data) {
        const imageUrl = response.data.access_url;
        onChange?.(imageUrl);
        message.success('图片上传成功');
      } else {
        message.error(response.message || '上传失败');
      }
    } catch (error) {
      console.error('Upload error:', error);
      message.error('上传失败');
    } finally {
      setLoading(false);
    }
    return false; // 阻止默认上传行为
  };

  const handlePreview = () => {
    if (value) {
      setPreviewImage(value);
      setPreviewVisible(true);
    }
  };

  const handleRemove = () => {
    onChange?.('');
    message.success('图片已移除');
  };

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{placeholder}</div>
    </div>
  );

  // 如果已有图片，显示图片预览
  const imagePreview = value ? (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      <img
        src={value}
        alt="预览"
        style={{ 
          width: '100%', 
          height: '100%', 
          objectFit: 'cover',
          borderRadius: '6px'
        }}
      />
      {!disabled && (
        <div 
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transition: 'opacity 0.3s',
            borderRadius: '6px'
          }}
          className="image-overlay"
          onMouseEnter={(e) => {
            (e.currentTarget as HTMLDivElement).style.opacity = '1';
          }}
          onMouseLeave={(e) => {
            (e.currentTarget as HTMLDivElement).style.opacity = '0';
          }}
        >
          <EyeOutlined 
            style={{ color: 'white', fontSize: 16, marginRight: 8, cursor: 'pointer' }}
            onClick={handlePreview}
          />
          <DeleteOutlined 
            style={{ color: 'white', fontSize: 16, cursor: 'pointer' }}
            onClick={handleRemove}
          />
        </div>
      )}
    </div>
  ) : null;

  return (
    <>
      <Upload
        name="file"
        listType="picture-card"
        className="image-uploader"
        showUploadList={false}
        beforeUpload={beforeUpload}
        customRequest={({ file }) => handleUpload(file as RcFile)}
        disabled={disabled || loading}
        style={{ width: 104, height: 104 }}
      >
        {value ? imagePreview : uploadButton}
      </Upload>
      
      <Modal 
        open={previewVisible} 
        title="图片预览" 
        footer={null} 
        onCancel={() => setPreviewVisible(false)}
        width={800}
        centered
      >
        <Image 
          alt="预览" 
          style={{ width: '100%' }} 
          src={previewImage} 
          preview={false}
        />
      </Modal>
      
      <style>{`
        .image-uploader .ant-upload {
          border-color: #d9d9d9;
          border-style: dashed;
        }
        
        .image-uploader .ant-upload:hover {
          border-color: #1890ff;
        }
        
        .image-uploader .ant-upload-select-picture-card i {
          font-size: 32px;
          color: #999;
        }
        
        .image-uploader .ant-upload-select-picture-card .ant-upload-text {
          margin-top: 8px;
          color: #666;
          font-size: 14px;
        }
      `}</style>
    </>
  );
};

export default ImageUpload;