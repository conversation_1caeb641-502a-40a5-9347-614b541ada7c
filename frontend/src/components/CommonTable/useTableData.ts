import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { 
  TableState, 
  QueryParams, 
  FetchFunction, 
  CreateFunction, 
  UpdateFunction, 
  DeleteFunction, 
  BatchDeleteFunction,
  TableMethods,
  BaseRecord,
  ApiResponse,
  ExportConfig
} from './types';

// 常量定义
const SUCCESS_CODE = 0;
const DEFAULT_PAGE_SIZE = 10;

// Hook选项
interface UseTableDataOptions<T extends BaseRecord> {
  fetchFn: FetchFunction<T>;
  createFn?: CreateFunction<T>;
  updateFn?: UpdateFunction<T>;
  deleteFn?: DeleteFunction;
  batchDeleteFn?: BatchDeleteFunction;
  initialParams?: Partial<QueryParams>;
  autoFetch?: boolean;
  onError?: (error: any) => void;
}

// Hook返回值
interface UseTableDataReturn<T extends BaseRecord> {
  state: TableState<T>;
  methods: TableMethods<T>;
  loading: boolean;
}

export const useTableData = <T extends BaseRecord>({
  fetchFn,
  createFn,
  updateFn,
  deleteFn,
  batchDeleteFn,
  initialParams = {},
  autoFetch = true,
  onError
}: UseTableDataOptions<T>): UseTableDataReturn<T> => {
  // 状态管理
  const [state, setState] = useState<TableState<T>>({
    data: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: DEFAULT_PAGE_SIZE,
      total: 0,
      ...initialParams.pagination
    },
    filters: initialParams.filters || {},
    sorter: initialParams.sorter || {},
    search: initialParams.search || '',
    selectedRowKeys: [],
    selectedRows: [],
    stats: {}
  });

  // 请求参数缓存
  const paramsRef = useRef<QueryParams>({
    pagination: state.pagination,
    filters: state.filters,
    sorter: state.sorter,
    search: state.search,
    ...initialParams
  });

  // 错误处理
  const handleError = useCallback((error: any) => {
    console.error('Table operation error:', error);
    if (onError) {
      onError(error);
    } else {
      message.error(error?.message || '操作失败');
    }
  }, [onError]);

  // 数据获取
  const fetchData = useCallback(async (params?: Partial<QueryParams>) => {
    if (!fetchFn) return;

    const mergedParams = {
      ...paramsRef.current,
      ...params
    };

    setState(prev => ({ ...prev, loading: true }));

    try {
      const response: ApiResponse<T[]> = await fetchFn(mergedParams);
      
      if (response.code === SUCCESS_CODE) {
        const data = response.data || [];
        const pagination = response.meta?.pagination || {};
        
        setState(prev => ({
          ...prev,
          data,
          loading: false,
          pagination: {
            ...prev.pagination,
            total: (pagination as any).total || data.length,
            current: (pagination as any).current || mergedParams.pagination.current,
            pageSize: (pagination as any).pageSize || mergedParams.pagination.pageSize
          }
        }));
        
        // 更新参数缓存
        paramsRef.current = mergedParams;
      } else {
        throw new Error(response.message || '获取数据失败');
      }
    } catch (error) {
      setState(prev => ({ ...prev, loading: false, data: [] }));
      handleError(error);
    }
  }, [fetchFn, handleError]);

  // 刷新数据
  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  // 搜索
  const search = useCallback((keyword: string) => {
    setState(prev => ({ 
      ...prev, 
      search: keyword,
      pagination: { ...prev.pagination, current: 1 }
    }));
    
    fetchData({
      search: keyword,
      pagination: { ...state.pagination, current: 1 }
    });
  }, [fetchData, state.pagination]);

  // 筛选
  const filter = useCallback((filters: Record<string, any>) => {
    setState(prev => ({ 
      ...prev, 
      filters,
      pagination: { ...prev.pagination, current: 1 }
    }));
    
    fetchData({
      filters,
      pagination: { ...state.pagination, current: 1 }
    });
  }, [fetchData, state.pagination]);

  // 重置筛选
  const resetFilters = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      filters: {},
      search: '',
      pagination: { ...prev.pagination, current: 1 }
    }));
    
    fetchData({
      filters: {},
      search: '',
      pagination: { ...state.pagination, current: 1 }
    });
  }, [fetchData, state.pagination]);

  // 排序
  const sort = useCallback((sorter: { field?: string; order?: 'ascend' | 'descend' }) => {
    setState(prev => ({ 
      ...prev, 
      sorter,
      pagination: { ...prev.pagination, current: 1 }
    }));
    
    fetchData({
      sorter,
      pagination: { ...state.pagination, current: 1 }
    });
  }, [fetchData, state.pagination]);

  // 分页
  const paginate = useCallback((pagination: { current: number; pageSize: number }) => {
    setState(prev => ({ ...prev, pagination: { ...prev.pagination, ...pagination } }));
    fetchData({ pagination });
  }, [fetchData]);

  // 选择行
  const setSelection = useCallback((selectedRowKeys: any[], selectedRows: T[]) => {
    setState(prev => ({ ...prev, selectedRowKeys, selectedRows }));
  }, []);

  // 获取选中行
  const getSelectedRows = useCallback(() => {
    return state.selectedRows;
  }, [state.selectedRows]);

  // 清空选择
  const clearSelection = useCallback(() => {
    setState(prev => ({ ...prev, selectedRowKeys: [], selectedRows: [] }));
  }, []);

  // 创建记录
  const create = useCallback(async (data: Partial<T>) => {
    if (!createFn) throw new Error('Create function not provided');
    
    try {
      const response = await createFn(data);
      if (response.code === SUCCESS_CODE) {
        message.success('创建成功');
        await refresh();
        return response;
      } else {
        throw new Error(response.message || '创建失败');
      }
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [createFn, refresh, handleError]);

  // 更新记录
  const update = useCallback(async (id: string | number, data: Partial<T>) => {
    if (!updateFn) throw new Error('Update function not provided');
    
    try {
      const response = await updateFn(id, data);
      if (response.code === SUCCESS_CODE) {
        message.success('更新成功');
        await refresh();
        return response;
      } else {
        throw new Error(response.message || '更新失败');
      }
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [updateFn, refresh, handleError]);

  // 删除记录
  const deleteRecord = useCallback(async (id: string | number) => {
    if (!deleteFn) throw new Error('Delete function not provided');
    
    try {
      const response = await deleteFn(id);
      if (response.code === SUCCESS_CODE) {
        message.success('删除成功');
        await refresh();
        return response;
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [deleteFn, refresh, handleError]);

  // 批量删除
  const batchDelete = useCallback(async (ids: (string | number)[]) => {
    if (!batchDeleteFn) throw new Error('Batch delete function not provided');
    
    try {
      const response = await batchDeleteFn(ids);
      if (response.code === SUCCESS_CODE) {
        message.success(`成功删除 ${ids.length} 条记录`);
        await refresh();
        clearSelection();
        return response;
      } else {
        throw new Error(response.message || '批量删除失败');
      }
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [batchDeleteFn, refresh, clearSelection, handleError]);

  // 导出数据
  const exportData = useCallback(async (config?: any) => {
    // 获取所有数据进行导出
    try {
      const allDataParams = {
        ...paramsRef.current,
        pagination: { current: 1, pageSize: 10000 } // 获取大量数据用于导出
      };
      
      const response = await fetchFn(allDataParams);
      if (response.code === SUCCESS_CODE) {
        const data = response.data || [];
        
        // 这里可以集成导出库，如 xlsx 或 csv-parse
        console.log('Export data:', data, config);
        message.success('导出成功');
        
        return data;
      } else {
        throw new Error(response.message || '导出失败');
      }
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [fetchFn, handleError]);

  // 组装方法对象
  const methods: TableMethods<T> = {
    refresh,
    search,
    filter,
    resetFilters,
    getSelectedRows,
    clearSelection,
    exportData: async (config?: ExportConfig) => {
      await exportData(config);
    },
    // 扩展方法
    create: create ? async (data: Partial<T>) => {
      await create(data);
    } : undefined,
    update: update ? async (id: string | number, data: Partial<T>) => {
      await update(id, data);
    } : undefined,
    delete: deleteRecord ? async (id: string | number) => {
      await deleteRecord(id);
    } : undefined,
    batchDelete: batchDelete ? async (ids: (string | number)[]) => {
      await batchDelete(ids);
    } : undefined,
    sort,
    paginate,
    setSelection
  };

  // 初始化时自动获取数据
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch, fetchFn]); // 仅在初始化时执行

  return {
    state,
    methods,
    loading: state.loading
  };
};

// 导出类型和Hook
export type { UseTableDataOptions, UseTableDataReturn };
export default useTableData;