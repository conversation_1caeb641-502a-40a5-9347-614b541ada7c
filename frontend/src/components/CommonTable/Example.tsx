import React, { useRef } from 'react';
import { message } from 'antd';
import { 
  UserOutlined, 
  EditOutlined, 
  DeleteOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import CommonTable from './index';
import { TableConfig, TableMethods } from './types';

// 模拟用户数据类型
interface User {
  id: number;
  name: string;
  email: string;
  status: 'active' | 'inactive';
  department: string;
  role: string;
  avatar?: string;
  created_at: string;
  login_count: number;
}

// 模拟API函数
const mockUsers: User[] = [
  {
    id: 1,
    name: '张三',
    email: 'zhang<PERSON>@example.com',
    status: 'active',
    department: '技术部',
    role: '开发工程师',
    created_at: '2024-01-15 09:30:00',
    login_count: 156
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    status: 'inactive',
    department: '产品部',
    role: '产品经理',
    created_at: '2024-02-20 14:20:00',
    login_count: 89
  },
  // ... 更多用户数据
];

// 模拟获取用户列表
const fetchUsers = async (params: any) => {
  console.log('Fetch users with params:', params);
  
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const { pagination, filters, search, sorter } = params;
  let filteredData = [...mockUsers];
  
  // 搜索过滤
  if (search) {
    filteredData = filteredData.filter(user => 
      user.name.includes(search) || user.email.includes(search)
    );
  }
  
  // 状态过滤
  if (filters.status) {
    filteredData = filteredData.filter(user => user.status === filters.status);
  }
  
  // 部门过滤
  if (filters.department) {
    filteredData = filteredData.filter(user => user.department === filters.department);
  }
  
  // 排序
  if (sorter?.field && sorter?.order) {
    filteredData.sort((a, b) => {
      const aVal = a[sorter.field as keyof User];
      const bVal = b[sorter.field as keyof User];
      
      if (sorter.order === 'ascend') {
        return (aVal ?? 0) > (bVal ?? 0) ? 1 : -1;
      } else {
        return (aVal ?? 0) < (bVal ?? 0) ? 1 : -1;
      }
    });
  }
  
  // 分页
  const start = (pagination.current - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  const pageData = filteredData.slice(start, end);
  
  return {
    code: 0,
    message: 'success',
    data: pageData,
    meta: {
      pagination: {
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: filteredData.length
      }
    }
  };
};

// 模拟创建用户
const createUser = async (userData: Partial<User>) => {
  console.log('Create user:', userData);
  await new Promise(resolve => setTimeout(resolve, 300));
  return { code: 0, message: '创建成功' };
};

// 模拟更新用户
const updateUser = async (id: string | number, userData: Partial<User>) => {
  console.log('Update user:', id, userData);
  await new Promise(resolve => setTimeout(resolve, 300));
  return { code: 0, message: '更新成功' };
};

// 模拟删除用户
const deleteUser = async (id: string | number) => {
  console.log('Delete user:', id);
  await new Promise(resolve => setTimeout(resolve, 300));
  return { code: 0, message: '删除成功' };
};

// =========================== JSON配置示例 ===========================
const userTableConfig: TableConfig<User> = {
  name: 'user-table',
  title: '用户管理',
  
  // 数据源配置
  dataSource: {
    fetch: fetchUsers,
    create: createUser,
    update: updateUser,
    delete: deleteUser,
  },
  
  // 列配置
  columns: [
    {
      key: 'user_info',
      title: '用户信息',
      dataIndex: 'name',
      renderType: 'avatar',
      avatarConfig: {
        fallback: (record) => record.name.charAt(0),
        shape: 'circle',
        size: 40
      },
      searchable: true,
      width: 200
    },
    {
      key: 'email',
      title: '邮箱',
      dataIndex: 'email',
      renderType: 'text',
      copyable: true,
      width: 200
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      renderType: 'status',
      statusMap: {
        active: { text: '正常', color: 'success' },
        inactive: { text: '禁用', color: 'default' }
      },
      filterable: true,
      width: 100
    },
    {
      key: 'department',
      title: '部门',
      dataIndex: 'department',
      renderType: 'text',
      width: 120
    },
    {
      key: 'role',
      title: '角色',
      dataIndex: 'role',
      renderType: 'text',
      width: 120
    },
    {
      key: 'login_count',
      title: '登录次数',
      dataIndex: 'login_count',
      renderType: 'number',
      sortable: true,
      width: 100
    },
    {
      key: 'created_at',
      title: '创建时间',
      dataIndex: 'created_at',
      renderType: 'datetime',
      sortable: true,
      width: 180
    },
    {
      key: 'actions',
      title: '操作',
      renderType: 'actions',
      actionsConfig: [
        {
          key: 'edit',
          label: '编辑',
          icon: <EditOutlined />,
          type: 'text',
          onClick: (record) => {
            message.info(`编辑用户：${record.name}`);
          }
        },
        {
          key: 'delete',
          label: '删除',
          icon: <DeleteOutlined />,
          type: 'text',
          danger: true,
          confirm: {
            title: '确认删除',
            content: '删除后无法恢复，确定要删除这个用户吗？'
          },
          onClick: (record) => {
            message.success(`删除用户：${record.name}`);
          }
        }
      ],
      width: 120,
      fixed: 'right'
    }
  ],
  
  // 功能配置
  features: {
    pagination: {
      defaultPageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true
    },
    search: {
      placeholder: '搜索用户名或邮箱',
      fields: ['name', 'email']
    },
    filters: [
      {
        field: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '正常', value: 'active', color: '#52c41a' },
          { label: '禁用', value: 'inactive', color: '#d9d9d9' }
        ]
      },
      {
        field: 'department',
        label: '部门',
        type: 'select',
        options: [
          { label: '技术部', value: '技术部' },
          { label: '产品部', value: '产品部' },
          { label: '设计部', value: '设计部' },
          { label: '运营部', value: '运营部' }
        ]
      },
      {
        field: 'created_date',
        label: '创建时间',
        type: 'dateRange',
        dateFormat: 'YYYY-MM-DD'
      }
    ],
    selection: {
      type: 'checkbox',
      preserveSelectedRowKeys: true
    },
    stats: [
      {
        title: '总用户数',
        value: (data) => data.length,
        icon: <UserOutlined />,
        color: '#1890ff'
      },
      {
        title: '活跃用户',
        value: (data) => data.filter(u => u.status === 'active').length,
        icon: <CheckCircleOutlined />,
        color: '#52c41a'
      },
      {
        title: '禁用用户',
        value: (data) => data.filter(u => u.status === 'inactive').length,
        icon: <CloseCircleOutlined />,
        color: '#f5222d'
      },
      {
        title: '平均登录',
        value: (data) => {
          const total = data.reduce((sum, u) => sum + u.login_count, 0);
          return Math.round(total / data.length);
        },
        icon: <TeamOutlined />,
        color: '#722ed1',
        suffix: '次'
      }
    ],
    export: true,
    refresh: true
  },
  
  // 表格级操作
  actions: [
    {
      key: 'add',
      label: '新增用户',
      type: 'primary',
      icon: <UserOutlined />,
      onClick: () => {
        message.info('打开新增用户弹窗');
      }
    }
  ],
  
  // 样式配置
  style: {
    size: 'middle',
    bordered: false,
    scroll: { x: 1200 }
  }
};

// =========================== 使用示例组件 ===========================
const TableExample: React.FC = () => {
  const tableRef = useRef<TableMethods<User>>(null);
  
  // 处理状态变化
  const handleStateChange = (state: any) => {
    console.log('Table state changed:', state);
  };
  
  // 处理选择变化
  const handleSelectionChange = (selectedRowKeys: any[], selectedRows: User[]) => {
    console.log('Selection changed:', selectedRowKeys, selectedRows);
  };
  
  // 自定义工具栏渲染
  const renderCustomToolbar = (defaultToolbar: React.ReactNode) => {
    return (
      <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
        {defaultToolbar}
      </div>
    );
  };
  
  return (
    <div style={{ padding: 24 }}>
      <h1>通用表格组件示例</h1>
      
      {/* 方式1：使用JSON配置 */}
      <div style={{ marginBottom: 32 }}>
        <h2>方式1：JSON配置</h2>
        <CommonTable<User>
          config={userTableConfig}
          tableRef={tableRef}
          onStateChange={handleStateChange}
          onSelectionChange={handleSelectionChange}
        />
      </div>
      
      {/* 方式2：Props配置 */}
      <div style={{ marginBottom: 32 }}>
        <h2>方式2：Props配置</h2>
        <CommonTable<User>
          title="用户列表 (Props配置)"
          dataSource={{ fetch: fetchUsers }}
          columns={[
            {
              key: 'name',
              title: '姓名',
              dataIndex: 'name',
              renderType: 'text'
            },
            {
              key: 'email',
              title: '邮箱',
              dataIndex: 'email',
              renderType: 'text'
            },
            {
              key: 'status',
              title: '状态',
              dataIndex: 'status',
              renderType: 'status',
              statusMap: {
                active: { text: '正常', color: 'success' },
                inactive: { text: '禁用', color: 'default' }
              }
            }
          ]}
          filters={[
            {
              field: 'status',
              label: '状态',
              type: 'select',
              options: [
                { label: '正常', value: 'active' },
                { label: '禁用', value: 'inactive' }
              ]
            }
          ]}
        />
      </div>
      
      {/* 方式3：混合配置 */}
      <div style={{ marginBottom: 32 }}>
        <h2>方式3：混合配置 (JSON + Props覆盖)</h2>
        <CommonTable<User>
          config={userTableConfig}
          title="用户管理 (覆盖标题)"
          renderToolbar={renderCustomToolbar}
          style={{ border: '2px solid #1890ff' }}
        />
      </div>
    </div>
  );
};

export default TableExample;
export { userTableConfig, fetchUsers, createUser, updateUser, deleteUser };