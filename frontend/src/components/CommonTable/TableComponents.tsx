import React from 'react';
import { <PERSON>, Row, Col, Statistic, Space, Button, Tooltip, Dropdown, Menu, Input } from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  ExportOutlined,
  DeleteOutlined,
  SettingOutlined,
  DownloadOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { StatsConfig, TableAction } from './types';

// =========================== 统计组件 ===========================
interface TableStatsProps {
  stats: StatsConfig[];
  data?: any[];
  style?: React.CSSProperties;
}

const TableStats: React.FC<TableStatsProps> = ({ stats, data = [], style }) => {
  // 计算统计值
  const calculateValue = (config: StatsConfig): string | number => {
    if (typeof config.value === 'function') {
      return config.value(data);
    }
    return config.value;
  };

  // 格式化数值
  const formatValue = (value: string | number, config: StatsConfig): string => {
    if (typeof value === 'string') return value;
    
    const num = Number(value);
    if (isNaN(num)) return '0';
    
    let formatted = config.precision !== undefined ? 
      num.toFixed(config.precision) : 
      num.toLocaleString('zh-CN');
    
    if (config.suffix) {
      formatted += config.suffix;
    }
    
    return formatted;
  };

  return (
    <Card style={{ marginBottom: 16, ...style }}>
      <Row gutter={[24, 16]}>
        {stats.map((stat, index) => {
          const value = calculateValue(stat);
          const formattedValue = formatValue(value, stat);
          
          return (
            <Col key={index} xs={24} sm={12} md={8} lg={6}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 16
                }}
              >
                {/* 图标 */}
                {stat.icon && (
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: 8,
                      backgroundColor: stat.color ? `${stat.color}15` : '#f0f0f0',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: 20,
                      color: stat.color || '#666',
                      flexShrink: 0
                    }}
                  >
                    {stat.icon}
                  </div>
                )}
                
                {/* 统计内容 */}
                <div style={{ minWidth: 0 }}>
                  <Statistic
                    title={stat.title}
                    value={formattedValue}
                    valueStyle={{
                      color: stat.color,
                      fontSize: 24,
                      fontWeight: 600
                    }}
                  />
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

// =========================== 工具栏组件 ===========================
interface TableToolbarProps {
  title?: string;
  actions?: TableAction[];
  
  // 内置操作
  showRefresh?: boolean;
  showDensity?: boolean;
  showColumnSettings?: boolean;
  showExport?: boolean;
  
  // 批量操作
  selectedCount?: number;
  onBatchDelete?: () => void;
  
  // 事件回调
  onRefresh?: () => void;
  onExport?: () => void;
  onDensityChange?: (density: 'small' | 'middle' | 'large') => void;
  onColumnSettingsChange?: (columns: string[]) => void;
  
  // 样式
  style?: React.CSSProperties;
  extra?: React.ReactNode;
}

const TableToolbar: React.FC<TableToolbarProps> = ({
  title,
  actions = [],
  showRefresh = true,
  showDensity = false,
  showColumnSettings = false,
  showExport = false,
  selectedCount = 0,
  onBatchDelete,
  onRefresh,
  onExport,
  onDensityChange,
  onColumnSettingsChange,
  style,
  extra
}) => {
  
  // 渲染操作按钮
  const renderAction = (action: TableAction) => {
    // 检查可见性
    if (typeof action.visible === 'boolean' && !action.visible) return null;
    
    // 检查禁用状态
    const disabled = typeof action.disabled === 'boolean' ? action.disabled : false;
    
    return (
      <Tooltip key={action.key} title={action.label}>
        <Button
          type={action.type || 'default'}
          icon={action.icon}
          disabled={disabled}
          loading={action.loading}
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      </Tooltip>
    );
  };

  // 密度设置菜单
  const densityMenu = (
    <Menu
      onClick={({ key }) => onDensityChange?.(key as 'small' | 'middle' | 'large')}
      items={[
        { key: 'small', label: '紧凑' },
        { key: 'middle', label: '默认' },
        { key: 'large', label: '宽松' }
      ]}
    />
  );

  // 更多操作菜单
  const moreMenu = (
    <Menu
      items={[
        showColumnSettings && {
          key: 'columnSettings',
          label: '列设置',
          icon: <SettingOutlined />,
          onClick: () => console.log('列设置')
        },
        showExport && {
          key: 'export',
          label: '导出数据',
          icon: <DownloadOutlined />,
          onClick: onExport
        }
      ].filter(Boolean) as any[]}
    />
  );

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 16,
        ...style
      }}
    >
      {/* 左侧：标题和批量操作 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
        {title && (
          <h3 style={{ margin: 0, fontSize: 16, fontWeight: 600 }}>
            {title}
          </h3>
        )}
        
        {selectedCount > 0 && onBatchDelete && (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ color: '#666', fontSize: 14 }}>
              已选择 {selectedCount} 项
            </span>
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={onBatchDelete}
            >
              批量删除
            </Button>
          </div>
        )}
      </div>

      {/* 右侧：操作按钮 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <Space>
          {/* 自定义操作 */}
          {actions.map(renderAction)}
          
          {/* 刷新按钮 */}
          {showRefresh && (
            <Tooltip title="刷新">
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
              />
            </Tooltip>
          )}
          
          {/* 密度设置 */}
          {showDensity && (
            <Dropdown overlay={densityMenu} trigger={['click']}>
              <Button icon={<SettingOutlined />} />
            </Dropdown>
          )}
          
          {/* 更多操作 */}
          {(showColumnSettings || showExport) && (
            <Dropdown overlay={moreMenu} trigger={['click']}>
              <Button icon={<MoreOutlined />} />
            </Dropdown>
          )}
          
          {/* 额外内容 */}
          {extra}
        </Space>
      </div>
    </div>
  );
};

// =========================== 搜索栏组件 ===========================
interface TableSearchProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  loading?: boolean;
  style?: React.CSSProperties;
  
  // 高级搜索
  showAdvanced?: boolean;
  onAdvancedSearch?: () => void;
}

const TableSearch: React.FC<TableSearchProps> = ({
  placeholder = '请输入搜索关键词',
  value,
  onChange,
  onSearch,
  loading,
  style,
  showAdvanced,
  onAdvancedSearch
}) => {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: 8,
        marginBottom: 16,
        ...style
      }}
    >
      <Input.Search
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        onSearch={onSearch}
        loading={loading}
        allowClear
        style={{ flex: 1 }}
      />
      
      {showAdvanced && (
        <Button onClick={onAdvancedSearch}>
          高级搜索
        </Button>
      )}
    </div>
  );
};

export { TableStats, TableToolbar, TableSearch };
export type { TableStatsProps, TableToolbarProps, TableSearchProps };