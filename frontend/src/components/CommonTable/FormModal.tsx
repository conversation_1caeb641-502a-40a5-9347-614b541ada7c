import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  Upload,
  Row,
  Col,
  Button,
  Space,
  message
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { BaseRecord } from './types';

const { Option } = Select;
const { TextArea } = Input;

// 表单项类型
export type FormItemType = 
  | 'input' 
  | 'textarea'
  | 'password'
  | 'number'
  | 'select'
  | 'multiSelect'
  | 'date'
  | 'datetime'
  | 'switch'
  | 'upload'
  | 'custom';

// 表单项配置
export interface FormItemConfig {
  field: string;
  label: string;
  type: FormItemType;
  
  // 通用属性
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  tooltip?: string;
  span?: number; // 栅格占位
  
  // 验证规则
  rules?: any[];
  
  // Select相关
  options?: Array<{ label: string; value: any; disabled?: boolean }>;
  mode?: 'multiple' | 'tags';
  remote?: {
    url: string;
    valueField: string;
    labelField: string;
    searchField?: string;
  };
  
  // Number相关
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  
  // Date相关
  format?: string;
  showTime?: boolean;
  
  // Upload相关
  uploadConfig?: {
    action: string;
    listType?: 'text' | 'picture' | 'picture-card';
    maxCount?: number;
    accept?: string;
  };
  
  // 自定义渲染
  render?: (form: any, value: any, onChange: (value: any) => void) => React.ReactNode;
  
  // 依赖字段（用于联动）
  dependencies?: string[];
  
  // 显示控制
  visible?: boolean | ((values: any) => boolean);
  
  // 默认值
  defaultValue?: any;
  
  // 格式化（用于编辑时回显）
  formatter?: (value: any) => any;
  
  // 解析（用于提交时处理）
  parser?: (value: any) => any;
}

// 表单模态框配置
export interface FormModalConfig<T extends BaseRecord = BaseRecord> {
  title?: string | ((isEdit: boolean, record?: T) => string);
  width?: number;
  destroyOnHidden?: boolean;
  maskClosable?: boolean;
  
  // 表单配置
  formItems: FormItemConfig[];
  labelCol?: any;
  wrapperCol?: any;
  layout?: 'horizontal' | 'vertical' | 'inline';
  
  // 提交配置
  onSubmit: (values: any, isEdit: boolean, record?: T) => Promise<void>;
  
  // 表单处理
  beforeSubmit?: (values: any, isEdit: boolean, record?: T) => any;
  afterSubmit?: (result: any, isEdit: boolean, record?: T) => void;
  
  // 验证配置
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  
  // 自定义渲染
  renderFooter?: (
    form: any,
    isEdit: boolean,
    loading: boolean,
    onCancel: () => void,
    onSubmit: () => void
  ) => React.ReactNode;
}

// 表单模态框方法
export interface FormModalMethods<T extends BaseRecord = BaseRecord> {
  open: (record?: T) => void;
  close: () => void;
  submit: () => Promise<void>;
  reset: () => void;
  setFieldsValue: (values: any) => void;
  getFieldsValue: () => any;
  validateFields: () => Promise<any>;
}

// 表单模态框属性
export interface FormModalProps<T extends BaseRecord = BaseRecord> {
  config: FormModalConfig<T>;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
  onSuccess?: (result: any, isEdit: boolean, record?: T) => void;
  onError?: (error: any) => void;
}

// 表单项渲染器
class FormItemRenderer {
  
  static renderFormItem(config: FormItemConfig, form: any) {
    const { field, type, ...itemProps } = config;
    
    // 处理依赖字段
    const formItemProps: any = {
      name: field,
      label: config.label,
      rules: config.rules,
      tooltip: config.tooltip,
      required: config.required,
      hidden: typeof config.visible === 'boolean' ? !config.visible : false,
    };
    
    // 添加依赖字段
    if (config.dependencies) {
      formItemProps.dependencies = config.dependencies;
      formItemProps.shouldUpdate = (prevValues: any, currentValues: any) => {
        return config.dependencies!.some(dep => prevValues[dep] !== currentValues[dep]);
      };
    }
    
    const commonProps = {
      placeholder: config.placeholder,
      disabled: config.disabled,
      style: { width: '100%' }
    };
    
    // 根据类型渲染不同组件
    let component: React.ReactNode;
    
    switch (type) {
      case 'input':
        component = <Input {...commonProps} />;
        break;
        
      case 'textarea':
        component = (
          <TextArea 
            {...commonProps}
            rows={4}
            showCount
            maxLength={500}
          />
        );
        break;
        
      case 'password':
        component = <Input.Password {...commonProps} />;
        break;
        
      case 'number':
        component = (
          <InputNumber
            {...commonProps}
            min={config.min}
            max={config.max}
            step={config.step}
            precision={config.precision}
          />
        );
        break;
        
      case 'select':
        component = (
          <Select
            {...commonProps}
            mode={config.mode}
            showSearch
            optionFilterProp="children"
            loading={false} // TODO: 处理远程加载
          >
            {config.options?.map(option => (
              <Option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;
        
      case 'multiSelect':
        component = (
          <Select
            {...commonProps}
            mode="multiple"
            showSearch
            optionFilterProp="children"
          >
            {config.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;
        
      case 'date':
        component = (
          <DatePicker
            {...commonProps}
            format={config.format || 'YYYY-MM-DD'}
            showTime={config.showTime}
          />
        );
        break;
        
      case 'datetime':
        component = (
          <DatePicker
            {...commonProps}
            format={config.format || 'YYYY-MM-DD HH:mm:ss'}
            showTime
          />
        );
        break;
        
      case 'switch':
        component = <Switch />;
        formItemProps.valuePropName = 'checked';
        break;
        
      case 'upload':
        const uploadConfig = (config.uploadConfig || {}) as {
          action: string;
          listType?: 'text' | 'picture' | 'picture-card';
          maxCount?: number;
          accept?: string;
        };
        component = (
          <Upload
            action={uploadConfig.action || ''}
            listType={uploadConfig.listType || 'text'}
            maxCount={uploadConfig.maxCount}
            accept={uploadConfig.accept}
          >
            <Button icon={<UploadOutlined />}>上传文件</Button>
          </Upload>
        );
        formItemProps.valuePropName = 'fileList';
        break;
        
      case 'custom':
        if (config.render) {
          const currentValue = form.getFieldValue(field);
          const onChange = (value: any) => form.setFieldsValue({ [field]: value });
          component = config.render(form, currentValue, onChange);
        } else {
          component = <Input {...commonProps} />;
        }
        break;
        
      default:
        component = <Input {...commonProps} />;
    }
    
    // 处理显示/隐藏逻辑
    if (typeof config.visible === 'function') {
      return (
        <Form.Item shouldUpdate noStyle>
          {({ getFieldsValue }) => {
            const values = getFieldsValue();
            const visible = typeof config.visible === 'function' ? config.visible(values) : config.visible;
            
            return visible ? (
              <Form.Item {...formItemProps}>
                {component}
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
      );
    }
    
    return (
      <Form.Item {...formItemProps}>
        {component}
      </Form.Item>
    );
  }
}

// 通用表单模态框组件
const FormModal = forwardRef<FormModalMethods, FormModalProps>(({
  config,
  visible = false,
  onVisibleChange,
  onSuccess,
  onError
}, ref) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [internalVisible, setInternalVisible] = useState(visible);
  
  // 同步外部visible状态
  useEffect(() => {
    setInternalVisible(visible);
  }, [visible]);
  
  // 处理可见性变化
  const handleVisibleChange = (newVisible: boolean) => {
    setInternalVisible(newVisible);
    onVisibleChange?.(newVisible);
    
    if (!newVisible) {
      // 关闭时重置状态
      setTimeout(() => {
        form.resetFields();
        setCurrentRecord(null);
        setIsEdit(false);
      }, 300);
    }
  };
  
  // 打开表单
  const open = (record?: any) => {
    setCurrentRecord(record);
    setIsEdit(!!record);
    
    if (record) {
      // 格式化数据用于表单回显
      const formData = { ...record };
      
      config.formItems.forEach(item => {
        if (item.formatter && formData[item.field] !== undefined) {
          formData[item.field] = item.formatter(formData[item.field]);
        }
        
        // 处理日期类型
        if ((item.type === 'date' || item.type === 'datetime') && formData[item.field]) {
          formData[item.field] = dayjs(formData[item.field]);
        }
      });
      
      form.setFieldsValue(formData);
    } else {
      // 新增时设置默认值
      const defaultValues: any = {};
      config.formItems.forEach(item => {
        if (item.defaultValue !== undefined) {
          defaultValues[item.field] = item.defaultValue;
        }
      });
      form.setFieldsValue(defaultValues);
    }
    
    handleVisibleChange(true);
  };
  
  // 关闭表单
  const close = () => {
    handleVisibleChange(false);
  };
  
  // 提交表单
  const submit = async () => {
    try {
      setLoading(true);
      
      // 验证表单
      const values = await form.validateFields();
      
      // 处理表单数据
      const processedValues = { ...values };
      
      config.formItems.forEach(item => {
        if (processedValues[item.field] !== undefined) {
          // 处理日期类型
          if ((item.type === 'date' || item.type === 'datetime') && processedValues[item.field]) {
            processedValues[item.field] = processedValues[item.field].format(
              item.format || (item.type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss')
            );
          }
          
          // 应用解析器
          if (item.parser) {
            processedValues[item.field] = item.parser(processedValues[item.field]);
          }
        }
      });
      
      // 前置处理
      const finalValues = config.beforeSubmit 
        ? config.beforeSubmit(processedValues, isEdit, currentRecord)
        : processedValues;
      
      // 调用提交函数
      const result = await config.onSubmit(finalValues, isEdit, currentRecord);
      
      // 后置处理
      config.afterSubmit?.(result, isEdit, currentRecord);
      
      // 成功回调
      onSuccess?.(result, isEdit, currentRecord);
      
      // 关闭弹窗
      close();
      
    } catch (error) {
      console.error('Form submit error:', error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };
  
  // 重置表单
  const reset = () => {
    form.resetFields();
  };
  
  // 暴露方法
  useImperativeHandle(ref, () => ({
    open,
    close,
    submit,
    reset,
    setFieldsValue: form.setFieldsValue,
    getFieldsValue: form.getFieldsValue,
    validateFields: form.validateFields
  }));
  
  // 获取标题
  const getTitle = () => {
    if (typeof config.title === 'function') {
      return config.title(isEdit, currentRecord);
    }
    if (config.title) {
      return config.title;
    }
    return isEdit ? '编辑' : '新增';
  };
  
  // 渲染表单项
  const renderFormItems = () => {
    const items = config.formItems.map(item => ({
      ...item,
      key: item.field
    }));
    
    // 按行分组
    const rows: FormItemConfig[][] = [];
    let currentRow: FormItemConfig[] = [];
    let currentRowSpan = 0;
    
    items.forEach(item => {
      const span = item.span || 24;
      
      if (currentRowSpan + span > 24) {
        // 新起一行
        if (currentRow.length > 0) {
          rows.push(currentRow);
        }
        currentRow = [item];
        currentRowSpan = span;
      } else {
        currentRow.push(item);
        currentRowSpan += span;
      }
    });
    
    if (currentRow.length > 0) {
      rows.push(currentRow);
    }
    
    return rows.map((row, rowIndex) => (
      <Row key={rowIndex} gutter={16}>
        {row.map(item => (
          <Col key={item.field} span={item.span || 24}>
            {FormItemRenderer.renderFormItem(item, form)}
          </Col>
        ))}
      </Row>
    ));
  };
  
  // 渲染底部按钮
  const renderFooter = () => {
    if (config.renderFooter) {
      return config.renderFooter(form, isEdit, loading, close, submit);
    }
    
    return (
      <Space>
        <Button onClick={close}>
          取消
        </Button>
        <Button 
          type="primary" 
          loading={loading}
          onClick={submit}
        >
          {isEdit ? '更新' : '创建'}
        </Button>
      </Space>
    );
  };
  
  return (
    <Modal
      title={getTitle()}
      open={internalVisible}
      onCancel={close}
      footer={renderFooter()}
      width={config.width || 600}
      destroyOnHidden={config.destroyOnHidden !== false}
      maskClosable={config.maskClosable !== false}
    >
      <Form
        form={form}
        layout={config.layout || 'vertical'}
        labelCol={config.labelCol}
        wrapperCol={config.wrapperCol}
        validateTrigger={config.validateTrigger || 'onChange'}
        preserve={false}
      >
        {renderFormItems()}
      </Form>
    </Modal>
  );
});

FormModal.displayName = 'FormModal';

export default FormModal;
export { FormItemRenderer };