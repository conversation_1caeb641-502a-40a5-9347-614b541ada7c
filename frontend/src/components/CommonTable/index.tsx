import React, { useImperativeHandle, useMemo, useCallback, useEffect, useRef, useState } from 'react';
import { Table, Card, Empty, Spin, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { 
  CommonTableProps, 
  TableConfig, 
  TableMethods, 
  BaseRecord,
  TableState,
  CrudModalConfig
} from './types';
import { useTableData } from './useTableData';
import { ConfigParser } from './ConfigParser';
import { TableStats, TableToolbar, TableSearch } from './TableComponents';
import TableFilters from './TableFilters';
import FormModal, { FormModalMethods } from './FormModal';

const { confirm } = Modal;

// 通用表格组件
function CommonTable<T extends BaseRecord = BaseRecord>({
  // JSON配置
  config,
  
  // Props配置（用于覆盖或扩展JSON配置）
  title,
  dataSource,
  columns,
  actions,
  filters,
  stats,
  modals,
  
  // 外部刷新
  refreshTrigger,
  refreshDeps = [],
  onRefresh,
  
  // 事件回调
  onStateChange,
  onSelectionChange,
  onCreateSuccess,
  onUpdateSuccess,
  onDeleteSuccess,
  
  // 表格实例引用
  tableRef,
  
  // 自定义渲染
  renderToolbar,
  renderFilters,
  renderStats,
  
  // 样式
  className,
  style,
  
  // Antd Table 原生属性
  ...antdTableProps
}: CommonTableProps<T>) {
  
  // 模态框引用
  const createModalRef = useRef<FormModalMethods<T>>(null);
  const editModalRef = useRef<FormModalMethods<T>>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailRecord, setDetailRecord] = useState<T | null>(null);
  
  // 合并配置：JSON配置 + Props配置，Props优先级更高
  const mergedConfig: TableConfig<T> = useMemo(() => {
    const propsConfig = {
      title,
      dataSource,
      columns,
      actions,
      filters,
      stats,
      modals
    };
    
    return ConfigParser.mergeConfig(config, propsConfig);
  }, [config, title, dataSource, columns, actions, filters, stats, modals]);
  
  // 验证配置
  useEffect(() => {
    if (!ConfigParser.validateConfig(mergedConfig)) {
      console.error('CommonTable: 无效的配置', mergedConfig);
    }
  }, [mergedConfig]);
  
  // 使用数据Hook
  const { state, methods, loading } = useTableData<T>({
    fetchFn: mergedConfig.dataSource.fetch,
    createFn: mergedConfig.dataSource.create,
    updateFn: mergedConfig.dataSource.update,
    deleteFn: mergedConfig.dataSource.delete,
    batchDeleteFn: mergedConfig.dataSource.batchDelete,
    autoFetch: true
  });
  
  // 外部刷新机制
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      const timer = setTimeout(() => {
        methods.refresh();
        onRefresh?.();
      }, mergedConfig.refresh?.delay || 0);
      
      return () => clearTimeout(timer);
    }
  }, [refreshTrigger, methods, onRefresh, mergedConfig.refresh?.delay]);
  
  // 依赖数组刷新
  useEffect(() => {
    if (refreshDeps.length > 0) {
      methods.refresh();
      onRefresh?.();
    }
  }, refreshDeps);
  
  // CRUD操作方法
  const handleCreate = useCallback(async (data: Partial<T>) => {
    try {
      const result = await methods.create!(data);
      onCreateSuccess?.(result, data);
      return result;
    } catch (error) {
      throw error;
    }
  }, [methods, onCreateSuccess]);
  
  const handleUpdate = useCallback(async (id: string | number, data: Partial<T>) => {
    try {
      const result = await methods.update!(id, data);
      onUpdateSuccess?.(result, id, data);
      return result;
    } catch (error) {
      throw error;
    }
  }, [methods, onUpdateSuccess]);
  
  const handleDelete = useCallback(async (id: string | number) => {
    try {
      const result = await methods.delete!(id);
      onDeleteSuccess?.(result, id);
      return result;
    } catch (error) {
      throw error;
    }
  }, [methods, onDeleteSuccess]);
  
  // 模态框操作方法
  const openCreateModal = useCallback(() => {
    createModalRef.current?.open();
  }, []);
  
  const openEditModal = useCallback((record: T) => {
    editModalRef.current?.open(record);
  }, []);
  
  const openDetailModal = useCallback((record: T) => {
    setDetailRecord(record);
    setDetailModalVisible(true);
  }, []);
  
  // 删除确认
  const confirmDelete = useCallback((record: T) => {
    const deleteConfig = mergedConfig.modals?.delete;
    const title = typeof deleteConfig?.title === 'function' 
      ? deleteConfig.title(record) 
      : (deleteConfig?.title || '确认删除');
    const content = typeof deleteConfig?.content === 'function'
      ? deleteConfig.content(record)
      : (deleteConfig?.content || '删除后无法恢复，确定要删除吗？');
    
    confirm({
      title,
      content,
      icon: <ExclamationCircleOutlined />,
      okText: deleteConfig?.okText || '确定',
      cancelText: deleteConfig?.cancelText || '取消',
      okType: 'danger',
      onOk: () => handleDelete(record.id)
    });
  }, [mergedConfig.modals?.delete, handleDelete]);
  
  // 扩展方法对象
  const extendedMethods: TableMethods<T> = useMemo(() => ({
    ...methods,
    create: mergedConfig.dataSource.create ? handleCreate : undefined,
    update: mergedConfig.dataSource.update ? handleUpdate : undefined,
    delete: mergedConfig.dataSource.delete ? handleDelete : undefined,
    openCreateModal: mergedConfig.modals?.create ? openCreateModal : undefined,
    openEditModal: mergedConfig.modals?.edit ? openEditModal : undefined,
    openDetailModal: mergedConfig.modals?.detail ? openDetailModal : undefined,
  }), [
    methods, 
    mergedConfig.dataSource, 
    mergedConfig.modals,
    handleCreate, 
    handleUpdate, 
    handleDelete,
    openCreateModal,
    openEditModal,
    openDetailModal
  ]);
  
  // 暴露方法给父组件
  useImperativeHandle(tableRef, () => extendedMethods, [extendedMethods]);
  
  // 状态变化回调
  useEffect(() => {
    onStateChange?.(state);
  }, [state, onStateChange]);
  
  // 选择变化回调
  useEffect(() => {
    onSelectionChange?.(state.selectedRowKeys, state.selectedRows);
  }, [state.selectedRowKeys, state.selectedRows, onSelectionChange]);
  
  // 解析Antd表格列，注入CRUD操作
  const antdColumns = useMemo(() => {
    if (!mergedConfig.columns) return [];
    
    // 处理列配置，为操作列注入CRUD方法
    const processedColumns = mergedConfig.columns.map(col => {
      if (col.renderType === 'actions' && col.actionsConfig) {
        // 为操作按钮注入内置CRUD方法
        const processedActionsConfig = col.actionsConfig.map(action => {
          let processedAction = { ...action };
          
          // 注入编辑方法
          if (action.key === 'edit' && mergedConfig.modals?.edit) {
            const originalOnClick = action.onClick;
            processedAction.onClick = (record, index) => {
              openEditModal(record);
              originalOnClick?.(record, index);
            };
          }
          
          // 注入删除方法
          if (action.key === 'delete' && mergedConfig.dataSource.delete) {
            processedAction.onClick = (record, index) => {
              confirmDelete(record);
            };
          }
          
          // 注入详情方法
          if (action.key === 'detail' && mergedConfig.modals?.detail) {
            const originalOnClick = action.onClick;
            processedAction.onClick = (record, index) => {
              openDetailModal(record);
              originalOnClick?.(record, index);
            };
          }
          
          return processedAction;
        });
        
        return {
          ...col,
          actionsConfig: processedActionsConfig
        };
      }
      
      return col;
    });
    
    return ConfigParser.parseColumns(processedColumns);
  }, [mergedConfig.columns, mergedConfig.modals, mergedConfig.dataSource, openEditModal, openDetailModal, confirmDelete]);
  
  // 处理表格级操作，注入新增方法
  const processedActions = useMemo(() => {
    if (!mergedConfig.actions) return [];
    
    return mergedConfig.actions.map(action => {
      if (action.key === 'add' && mergedConfig.modals?.create) {
        return {
          ...action,
          onClick: () => {
            openCreateModal();
            action.onClick?.();
          }
        };
      }
      return action;
    });
  }, [mergedConfig.actions, mergedConfig.modals?.create, openCreateModal]);
  
  // 表格行选择配置
  const rowSelection = useMemo(() => {
    const selectionConfig = mergedConfig.features?.selection;
    if (!selectionConfig) return undefined;
    
    const baseSelection = typeof selectionConfig === 'boolean' ? {} : selectionConfig;
    
    return {
      type: baseSelection.type || 'checkbox',
      selectedRowKeys: state.selectedRowKeys,
      preserveSelectedRowKeys: baseSelection.preserveSelectedRowKeys,
      onChange: (selectedRowKeys: any[], selectedRows: T[]) => {
        extendedMethods.setSelection(selectedRowKeys, selectedRows);
        baseSelection.onChange?.(selectedRowKeys, selectedRows);
      }
    };
  }, [mergedConfig.features?.selection, state.selectedRowKeys, extendedMethods]);
  
  // 分页配置
  const paginationConfig = useMemo(() => {
    const paginationFeature = mergedConfig.features?.pagination;
    if (!paginationFeature) return false;
    
    const paginationSettings = typeof paginationFeature === 'boolean' ? {} : paginationFeature;
    
    return {
      current: state.pagination.current,
      pageSize: state.pagination.pageSize,
      total: state.pagination.total,
      showSizeChanger: paginationSettings.showSizeChanger !== false,
      showQuickJumper: paginationSettings.showQuickJumper !== false,
      showTotal: paginationSettings.showTotal !== false ? 
        (total: number, range: [number, number]) => 
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条` : 
        undefined,
      pageSizeOptions: paginationSettings.pageSizeOptions || ['10', '20', '50', '100'],
      onChange: (page: number, pageSize: number) => {
        extendedMethods.paginate({ current: page, pageSize });
      }
    };
  }, [mergedConfig.features?.pagination, state.pagination, extendedMethods]);
  
  // 处理表格变化（排序、筛选等）
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    if (sorter && sorter.field) {
      extendedMethods.sort({
        field: sorter.field,
        order: sorter.order
      });
    }
  }, [extendedMethods]);
  
  // 处理搜索
  const handleSearch = useCallback((keyword: string) => {
    extendedMethods.search(keyword);
  }, [extendedMethods]);
  
  // 处理筛选
  const handleFilter = useCallback((filterValues: Record<string, any>) => {
    extendedMethods.filter(filterValues);
  }, [extendedMethods]);
  
  // 处理批量删除
  const handleBatchDelete = useCallback(async () => {
    if (state.selectedRowKeys.length === 0) return;
    
    confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${state.selectedRowKeys.length} 条记录吗？`,
      icon: <ExclamationCircleOutlined />,
      okType: 'danger',
      onOk: async () => {
        try {
          await extendedMethods.batchDelete!(state.selectedRowKeys);
        } catch (error) {
          console.error('批量删除失败:', error);
        }
      }
    });
  }, [state.selectedRowKeys, extendedMethods]);
  
  // 渲染统计区域
  const renderStatsSection = () => {
    const statsConfig = mergedConfig.features?.stats;
    if (!statsConfig || !Array.isArray(statsConfig)) return null;
    
    const statsContent = (
      <TableStats 
        stats={statsConfig} 
        data={state.data}
      />
    );
    
    return renderStats ? renderStats(statsContent) : statsContent;
  };
  
  // 渲染工具栏区域
  const renderToolbarSection = () => {
    const toolbarContent = (
      <TableToolbar
        title={mergedConfig.title}
        actions={processedActions}
        selectedCount={state.selectedRowKeys.length}
        onBatchDelete={extendedMethods.batchDelete ? handleBatchDelete : undefined}
        onRefresh={extendedMethods.refresh}
        onExport={extendedMethods.exportData}
        showRefresh={mergedConfig.features?.refresh !== false}
        showExport={!!mergedConfig.features?.export}
      />
    );
    
    return renderToolbar ? renderToolbar(toolbarContent) : toolbarContent;
  };
  
  // 渲染搜索区域
  const renderSearchSection = () => {
    const searchConfig = mergedConfig.features?.search;
    if (!searchConfig) return null;
    
    const searchSettings = typeof searchConfig === 'boolean' ? {} : searchConfig;
    
    return (
      <TableSearch
        placeholder={searchSettings.placeholder}
        value={state.search}
        onChange={(value) => extendedMethods.search(value)}
        onSearch={handleSearch}
        loading={loading}
        style={{ marginBottom: 16 }}
      />
    );
  };
  
  // 渲染筛选区域
  const renderFiltersSection = () => {
    const filtersConfig = mergedConfig.features?.filters;
    if (!filtersConfig || !Array.isArray(filtersConfig)) return null;
    
    const filtersContent = (
      <TableFilters
        filters={filtersConfig}
        values={state.filters}
        loading={loading}
        onChange={handleFilter}
        onReset={extendedMethods.resetFilters}
      />
    );
    
    return renderFilters ? renderFilters(filtersContent) : filtersContent;
  };
  
  // 表格样式配置
  const tableStyle = useMemo(() => {
    const styleConfig = mergedConfig.style || {};
    return {
      size: styleConfig.size || 'middle',
      bordered: styleConfig.bordered,
      showHeader: styleConfig.showHeader !== false,
      tableLayout: styleConfig.tableLayout,
      scroll: styleConfig.scroll
    };
  }, [mergedConfig.style]);
  
  // 行配置
  const rowConfig = useMemo(() => {
    const rowConfigSettings = mergedConfig.rowConfig || {};
    
    return {
      rowKey: rowConfigSettings.key || 'id',
      rowClassName: rowConfigSettings.className,
      onRow: (record: T, index?: number) => ({
        onClick: rowConfigSettings.onClick ? 
          () => rowConfigSettings.onClick!(record, index || 0) : 
          undefined,
        onDoubleClick: rowConfigSettings.onDoubleClick ? 
          () => rowConfigSettings.onDoubleClick!(record, index || 0) : 
          undefined
      })
    };
  }, [mergedConfig.rowConfig]);
  
  // 空状态配置
  const emptyConfig = useMemo(() => {
    const emptySettings = mergedConfig.emptyConfig;
    if (!emptySettings) return undefined;
    
    return (
      <Empty
        image={emptySettings.image}
        description={emptySettings.description || '暂无数据'}
      />
    );
  }, [mergedConfig.emptyConfig]);
  
  // 获取模态框配置
  const getModalConfig = (type: 'create' | 'edit') => {
    const modalConfig = mergedConfig.modals?.[type];
    if (!modalConfig || typeof modalConfig === 'boolean') return null;
    return modalConfig;
  };
  
  return (
    <div 
      className={`common-table ${className || ''}`}
      style={style}
    >
      {/* 统计区域 */}
      {renderStatsSection()}
      
      {/* 工具栏区域 */}
      {renderToolbarSection()}
      
      {/* 搜索区域 */}
      {renderSearchSection()}
      
      {/* 筛选区域 */}  
      {renderFiltersSection()}
      
      {/* 表格区域 */}
      <Card>
        <Spin spinning={loading}>
          <Table<T>
            {...antdTableProps}
            {...tableStyle}
            {...rowConfig}
            columns={antdColumns}
            dataSource={state.data}
            loading={false} // 使用外层Spin
            pagination={paginationConfig}
            rowSelection={rowSelection}
            onChange={handleTableChange}
            locale={{
              emptyText: emptyConfig
            }}
          />
        </Spin>
      </Card>
      
      {/* 新增模态框 */}
      {getModalConfig('create') && (
        <FormModal
          ref={createModalRef as any}
          config={{
            ...getModalConfig('create')!,
            onSubmit: async (values: any, isEdit: boolean) => {
              await handleCreate(values);
            }
          } as any}
          onSuccess={() => {
            // 创建成功后刷新列表
            extendedMethods.refresh();
          }}
        />
      )}
      
      {/* 编辑模态框 */}
      {getModalConfig('edit') && (
        <FormModal
          ref={editModalRef as any}
          config={{
            ...getModalConfig('edit')!,
            onSubmit: async (values: any, isEdit: boolean, record: any) => {
              await handleUpdate(record!.id, values);
            }
          } as any}
          onSuccess={() => {
            // 编辑成功后刷新列表
            extendedMethods.refresh();
          }}
        />
      )}
      
      {/* 详情模态框 */}
      {mergedConfig.modals?.detail && (
        <Modal
          title={
            typeof mergedConfig.modals.detail.title === 'function'
              ? mergedConfig.modals.detail.title(detailRecord!)
              : (mergedConfig.modals.detail.title || '详情')
          }
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={null}
          width={mergedConfig.modals.detail.width || 600}
        >
          {detailRecord && mergedConfig.modals.detail.render(detailRecord)}
        </Modal>
      )}
    </div>
  );
}

export default CommonTable;
export type { CommonTableProps, TableConfig, TableMethods };