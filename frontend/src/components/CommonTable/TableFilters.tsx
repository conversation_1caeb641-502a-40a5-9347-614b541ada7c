import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  Form
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined
} from '@ant-design/icons';
import { FilterConfig } from './types';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface TableFiltersProps {
  filters: FilterConfig[];
  values: Record<string, any>;
  loading?: boolean;
  onChange: (values: Record<string, any>) => void;
  onSearch?: () => void;
  onReset?: () => void;
  
  // 样式配置
  cardStyle?: React.CSSProperties;
  showCard?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

const TableFilters: React.FC<TableFiltersProps> = ({
  filters,
  values,
  loading,
  onChange,
  onSearch,
  onReset,
  cardStyle,
  showCard = true,
  collapsible = false,
  defaultCollapsed = false
}) => {
  const [form] = Form.useForm();
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  // 同步表单值
  useEffect(() => {
    form.setFieldsValue(values);
  }, [values, form]);

  // 处理表单值变化
  const handleValuesChange = (changedValues: Record<string, any>, allValues: Record<string, any>) => {
    // 清理undefined值
    const cleanValues = Object.entries(allValues).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    onChange(cleanValues);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onChange({});
    onReset?.();
  };

  // 处理搜索
  const handleSearch = () => {
    onSearch?.();
  };

  // 渲染单个筛选器
  const renderFilter = (filter: FilterConfig) => {
    const commonProps = {
      placeholder: filter.placeholder,
      allowClear: true,
      style: { width: '100%' }
    };

    switch (filter.type) {
      case 'input':
        return (
          <Form.Item
            key={filter.field}
            name={filter.field}
            label={filter.label}
          >
            <Input
              {...commonProps}
              prefix={<SearchOutlined />}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item
            key={filter.field}
            name={filter.field}
            label={filter.label}
          >
            <Select {...commonProps}>
              {filter.options?.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.color && (
                    <span
                      style={{
                        display: 'inline-block',
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: option.color,
                        marginRight: 8
                      }}
                    />
                  )}
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'multiSelect':
        return (
          <Form.Item
            key={filter.field}
            name={filter.field}
            label={filter.label}
          >
            <Select
              {...commonProps}
              mode={filter.mode || 'multiple'}
            >
              {filter.options?.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.color && (
                    <span
                      style={{
                        display: 'inline-block',
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: option.color,
                        marginRight: 8
                      }}
                    />
                  )}
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'dateRange':
        return (
          <Form.Item
            key={filter.field}
            name={filter.field}
            label={filter.label}
          >
            <RangePicker
              style={{ width: '100%' }}
              format={filter.dateFormat || 'YYYY-MM-DD'}
              showTime={filter.showTime}
            />
          </Form.Item>
        );

      case 'numberRange':
        const { min, max, step, precision } = filter.numberConfig || {};
        return (
          <Form.Item
            key={filter.field}
            name={filter.field}
            label={filter.label}
          >
            <Input.Group compact>
              <InputNumber
                style={{ width: '50%' }}
                placeholder="最小值"
                min={min}
                max={max}
                step={step}
                precision={precision}
              />
              <InputNumber
                style={{ width: '50%' }}
                placeholder="最大值"
                min={min}
                max={max}
                step={step}
                precision={precision}
              />
            </Input.Group>
          </Form.Item>
        );

      default:
        return null;
    }
  };

  // 可见的筛选器
  const visibleFilters = useMemo(() => {
    if (!collapsible || !collapsed) {
      return filters;
    }
    // 如果折叠，只显示前3个筛选器
    return filters.slice(0, 3);
  }, [filters, collapsible, collapsed]);

  // 筛选器内容
  const filtersContent = (
    <Form
      form={form}
      layout="vertical"
      onValuesChange={handleValuesChange}
      initialValues={values}
    >
      <Row gutter={[16, 16]} align="middle">
        {visibleFilters.map(filter => (
          <Col 
            key={filter.field}
            xs={24} 
            sm={12} 
            md={8} 
            lg={filter.span || 6}
            xl={filter.span || 6}
          >
            {renderFilter(filter)}
          </Col>
        ))}
        
        {/* 操作按钮 */}
        <Col xs={24} sm={12} md={8} lg={6} xl={6}>
          <Space style={{ marginTop: collapsible ? 0 : 24 }}>
            {onSearch && (
              <Button
                type="primary"
                icon={<SearchOutlined />}
                loading={loading}
                onClick={handleSearch}
              >
                搜索
              </Button>
            )}
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置
            </Button>
            {collapsible && (
              <Button
                type="link"
                icon={collapsed ? <DownOutlined /> : <UpOutlined />}
                onClick={() => setCollapsed(!collapsed)}
              >
                {collapsed ? '展开' : '收起'}
              </Button>
            )}
          </Space>
        </Col>
      </Row>
      
      {/* 展开/收起提示 */}
      {collapsible && collapsed && filters.length > 3 && (
        <Row>
          <Col span={24} style={{ textAlign: 'center', marginTop: 8 }}>
            <Button
              type="link"
              size="small"
              icon={<DownOutlined />}
              onClick={() => setCollapsed(false)}
            >
              展开更多筛选条件 ({filters.length - 3})
            </Button>
          </Col>
        </Row>
      )}
    </Form>
  );

  // 如果不显示卡片，直接返回内容
  if (!showCard) {
    return filtersContent;
  }

  // 包装在卡片中
  return (
    <Card 
      style={{ 
        marginBottom: 16,
        ...cardStyle
      }}
      bodyStyle={{ paddingBottom: 16 }}
    >
      {filtersContent}
    </Card>
  );
};

export default TableFilters;

// 导出筛选器类型
export type { FilterConfig };