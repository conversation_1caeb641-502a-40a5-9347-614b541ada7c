# CommonTable 集成指南

这份指南展示了如何在实际项目中集成和使用 CommonTable 组件。

## 📖 快速集成

### 1. 基础使用

最简单的使用方式，只需要提供数据获取函数和列配置：

```tsx
import React from 'react';
import { CommonTable, TableConfig } from '@/components/CommonTable/export';

interface Product {
  id: string;
  name: string;
  price: number;
  status: 'active' | 'inactive';
  created_at: string;
}

const ProductList: React.FC = () => {
  const tableConfig: TableConfig<Product> = {
    name: 'product-list',
    title: '产品列表',
    
    dataSource: {
      fetch: async (params) => {
        const response = await fetch(`/api/products?${new URLSearchParams(params)}`);
        return response.json();
      }
    },
    
    columns: [
      {
        key: 'name',
        title: '产品名称',
        dataIndex: 'name',
        renderType: 'text'
      },
      {
        key: 'price',
        title: '价格',
        dataIndex: 'price',
        renderType: 'number',
        format: '¥0,0.00'
      },
      {
        key: 'status',
        title: '状态',
        dataIndex: 'status',
        renderType: 'status',
        statusMap: {
          active: { text: '上架', color: 'success' },
          inactive: { text: '下架', color: 'default' }
        }
      }
    ],
    
    features: {
      pagination: true,
      search: true
    }
  };

  return <CommonTable config={tableConfig} />;
};
```

### 2. 带有CRUD操作的完整示例

```tsx
import React, { useRef } from 'react';
import { message } from 'antd';
import { CommonTable, TableConfig, TableMethods } from '@/components/CommonTable/export';

const UserManagement: React.FC = () => {
  const tableRef = useRef<TableMethods<User>>(null);

  // API 函数
  const userApi = {
    fetch: async (params: any) => {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      return response.json();
    },
    
    create: async (data: any) => {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },  
        body: JSON.stringify(data)
      });
      const result = await response.json();
      if (result.code === 200) {
        message.success('创建成功');
      }
      return result;
    },
    
    update: async (id: string, data: any) => {
      const response = await fetch(`/api/users/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      const result = await response.json();
      if (result.code === 200) {
        message.success('更新成功');
      }
      return result;
    },
    
    delete: async (id: string) => {
      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE'
      });
      const result = await response.json();
      if (result.code === 200) {
        message.success('删除成功');
      }
      return result;
    }
  };

  const tableConfig: TableConfig<User> = {
    name: 'user-management',
    title: '用户管理',
    
    dataSource: userApi,
    
    columns: [
      {
        key: 'name',
        title: '姓名',
        dataIndex: 'name',
        renderType: 'text',
        searchable: true
      },
      {
        key: 'email', 
        title: '邮箱',
        dataIndex: 'email',
        renderType: 'text'
      },
      {
        key: 'status',
        title: '状态',
        dataIndex: 'status',
        renderType: 'status',
        statusMap: {
          active: { text: '正常', color: 'success' },
          inactive: { text: '禁用', color: 'default' }
        }
      },
      {
        key: 'actions',
        title: '操作',
        renderType: 'actions',
        actionsConfig: [
          { key: 'edit', label: '编辑', onClick: () => {} },
          { key: 'delete', label: '删除', onClick: () => {} }
        ]
      }
    ],
    
    // CRUD 模态框配置
    modals: {
      create: {
        title: '新增用户',
        formItems: [
          {
            field: 'name',
            label: '姓名',
            type: 'input',
            required: true,
            rules: [{ required: true, message: '请输入姓名' }]
          },
          {
            field: 'email',
            label: '邮箱',
            type: 'input',
            required: true,
            rules: [
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '邮箱格式不正确' }
            ]
          },
          {
            field: 'status',
            label: '状态',
            type: 'select',
            options: [
              { label: '正常', value: 'active' },
              { label: '禁用', value: 'inactive' }
            ]
          }
        ],
        onSubmit: userApi.create
      },
      
      edit: {
        title: '编辑用户',
        formItems: [
          {
            field: 'name',
            label: '姓名',
            type: 'input',
            required: true
          },
          {
            field: 'email',
            label: '邮箱',
            type: 'input',
            required: true,
            disabled: true // 编辑时不允许修改邮箱
          },
          {
            field: 'status',
            label: '状态',
            type: 'select',
            options: [
              { label: '正常', value: 'active' },
              { label: '禁用', value: 'inactive' }
            ]
          }
        ],
        onSubmit: userApi.update
      }
    },
    
    actions: [
      {
        key: 'add',
        label: '新增用户',
        type: 'primary',
        onClick: () => {}
      }
    ],
    
    features: {
      pagination: true,
      search: true,
      selection: true
    }
  };

  return (
    <CommonTable 
      config={tableConfig}
      tableRef={tableRef}
      onCreateSuccess={() => {
        // 创建成功后的回调
        console.log('用户创建成功');
      }}
      onUpdateSuccess={() => {
        // 更新成功后的回调
        console.log('用户更新成功');
      }}
    />
  );
};
```

## 🔄 外部刷新机制

### 1. 触发器刷新

```tsx
const MyComponent: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };
  
  return (
    <div>
      <Button onClick={handleRefresh}>刷新表格</Button>
      <CommonTable
        config={tableConfig}
        refreshTrigger={refreshTrigger}
        onRefresh={() => {
          console.log('表格已刷新');
        }}
      />
    </div>
  );
};
```

### 2. 依赖数组刷新

```tsx
const MyComponent: React.FC = () => {
  const [filters, setFilters] = useState({ status: 'active' });
  
  return (
    <CommonTable
      config={tableConfig}
      refreshDeps={[filters]} // 当filters变化时自动刷新
      onRefresh={() => {
        console.log('因依赖变化而刷新');
      }}
    />
  );
};
```

### 3. 通过实例方法刷新

```tsx
const MyComponent: React.FC = () => {
  const tableRef = useRef<TableMethods>(null);
  
  const handleManualRefresh = () => {
    tableRef.current?.refresh();
  };
  
  return (
    <div>
      <Button onClick={handleManualRefresh}>手动刷新</Button>
      <CommonTable
        config={tableConfig}
        tableRef={tableRef}
      />
    </div>
  );
};
```

## 🎨 自定义渲染

### 1. 自定义工具栏

```tsx
<CommonTable
  config={tableConfig}
  renderToolbar={(defaultToolbar) => (
    <div className="custom-toolbar">
      <div className="toolbar-left">
        <h3>自定义标题</h3>
      </div>
      <div className="toolbar-right">
        {defaultToolbar}
        <Button type="primary">自定义按钮</Button>
      </div>
    </div>
  )}
/>
```

### 2. 自定义筛选器

```tsx
<CommonTable
  config={tableConfig}
  renderFilters={(defaultFilters) => (
    <Card title="高级筛选" style={{ marginBottom: 16 }}>
      {defaultFilters}
      <div style={{ marginTop: 16 }}>
        <Button>自定义筛选操作</Button>
      </div>
    </Card>
  )}
/>
```

### 3. 自定义列渲染

```tsx
const columns = [
  {
    key: 'custom',
    title: '自定义列',
    render: (value: any, record: any, index: number) => (
      <div className="custom-cell">
        <Avatar src={record.avatar} />
        <span>{record.name}</span>
        <Tag color="blue">{record.role}</Tag>
      </div>
    )
  }
];
```

## 📊 实时数据和WebSocket集成

```tsx
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

const RealTimeTable: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  useEffect(() => {
    const socket = io('ws://localhost:3001');
    
    // 监听数据更新事件
    socket.on('dataUpdated', (data) => {
      console.log('Received real-time update:', data);
      setRefreshTrigger(prev => prev + 1);
    });
    
    return () => socket.disconnect();
  }, []);
  
  return (
    <CommonTable
      config={tableConfig}
      refreshTrigger={refreshTrigger}
    />
  );
};
```

## 🔐 权限控制集成

```tsx
import { useAuth } from '@/hooks/useAuth';

const ProtectedTable: React.FC = () => {
  const { hasPermission } = useAuth();
  
  const tableConfig: TableConfig = {
    // ... 其他配置
    
    actions: [
      {
        key: 'add',
        label: '新增',
        type: 'primary',
        visible: hasPermission('user:create'), // 权限控制
        onClick: () => {}
      }
    ],
    
    columns: [
      // ... 其他列
      {
        key: 'actions',
        title: '操作',
        renderType: 'actions',
        actionsConfig: [
          {
            key: 'edit',
            label: '编辑',
            visible: (record) => hasPermission('user:update') && record.status === 'active',
            onClick: () => {}
          },
          {
            key: 'delete',
            label: '删除',
            visible: hasPermission('user:delete'),
            onClick: () => {}
          }
        ]
      }
    ]
  };
  
  return <CommonTable config={tableConfig} />;
};
```

## 🚀 性能优化建议

### 1. 大数据量处理

```tsx
const tableConfig: TableConfig = {
  // ... 其他配置
  
  features: {
    pagination: {
      defaultPageSize: 20, // 适中的页面大小
      pageSizeOptions: ['20', '50', '100']
    }
  },
  
  style: {
    scroll: { y: 400 } // 固定高度，启用滚动
  }
};
```

### 2. 搜索防抖

```tsx
const tableConfig: TableConfig = {
  features: {
    search: {
      debounceMs: 500 // 500ms防抖
    }
  }
};
```

### 3. 缓存策略

```tsx
// 在数据获取函数中实现缓存
const fetchWithCache = async (params: any) => {
  const cacheKey = JSON.stringify(params);
  const cached = localStorage.getItem(cacheKey);
  
  if (cached && Date.now() - JSON.parse(cached).timestamp < 5 * 60 * 1000) {
    return JSON.parse(cached).data;
  }
  
  const result = await fetch('/api/data', {
    method: 'POST',
    body: JSON.stringify(params)
  }).then(res => res.json());
  
  localStorage.setItem(cacheKey, JSON.stringify({
    data: result,
    timestamp: Date.now()
  }));
  
  return result;
};
```

## 🎯 最佳实践总结

1. **配置驱动**: 优先使用JSON配置，只在特殊情况下使用Props覆盖
2. **类型安全**: 充分利用TypeScript类型系统，定义完整的数据模型
3. **错误处理**: 在API函数中实现完善的错误处理机制
4. **用户反馈**: 合理使用message、notification等给用户反馈
5. **权限控制**: 在配置层面就实现权限控制，而不是在渲染层
6. **性能优化**: 合理设置分页、防抖、缓存等性能优化策略
7. **响应式设计**: 考虑不同屏幕尺寸的适配
8. **可维护性**: 保持配置的清晰和模块化

这样的设计让CommonTable既强大又易用，能够满足大部分业务场景的需求。