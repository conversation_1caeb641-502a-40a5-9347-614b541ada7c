# CommonTable 通用表格组件

一个功能强大、高度可配置的React表格组件，基于Ant Design Table构建，同时支持JSON配置和Props定制。

## ✨ 特性

- 🚀 **开箱即用**：内置常用功能，零配置快速使用
- 📦 **配置驱动**：通过JSON配置实现复杂表格功能
- 🎨 **高度定制**：支持Props覆盖，满足特殊需求
- 🔧 **TypeScript**：完整的类型支持和智能提示
- 📱 **响应式**：适配各种屏幕尺寸
- ⚡ **高性能**：内置缓存和优化策略
- 🔌 **可扩展**：支持自定义渲染器和插件

## 📦 安装

```bash
# 组件已内置在项目中，直接使用即可
import { CommonTable } from '@/components/CommonTable/export';
```

## 🚀 快速开始

### 1. JSON配置方式（推荐）

```tsx
import React from 'react';
import { CommonTable, TableConfig } from '@/components/CommonTable/export';

// 定义表格配置
const tableConfig: TableConfig<User> = {
  name: 'user-table',
  title: '用户管理',
  
  // 数据源
  dataSource: {
    fetch: fetchUsers,
    create: createUser,
    update: updateUser,
    delete: deleteUser,
  },
  
  // 列定义
  columns: [
    {
      key: 'name',
      title: '姓名',
      dataIndex: 'name',
      renderType: 'avatar',
      searchable: true
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      renderType: 'status',
      statusMap: {
        active: { text: '正常', color: 'success' },
        inactive: { text: '禁用', color: 'default' }
      }
    },
    {
      key: 'actions',
      title: '操作',
      renderType: 'actions',
      actionsConfig: [
        {
          key: 'edit',
          label: '编辑',
          onClick: (record) => console.log('edit', record)
        }
      ]
    }
  ],
  
  // 功能配置
  features: {
    pagination: true,
    search: true,
    filters: [
      {
        field: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '正常', value: 'active' },
          { label: '禁用', value: 'inactive' }
        ]
      }
    ],
    selection: true,
    stats: [
      {
        title: '总数',
        value: (data) => data.length,
        icon: <UserOutlined />
      }
    ]
  }
};

// 使用组件
const UserPage = () => (
  <CommonTable config={tableConfig} />
);
```

### 2. Props配置方式

```tsx
import React from 'react';
import { CommonTable } from '@/components/CommonTable/export';

const UserPage = () => (
  <CommonTable
    title="用户列表"
    dataSource={{ fetch: fetchUsers }}
    columns={[
      {
        key: 'name',
        title: '姓名',
        dataIndex: 'name',
        renderType: 'text'
      }
    ]}
    filters={[
      {
        field: 'status',
        label: '状态',
        type: 'select',
        options: [{ label: '正常', value: 'active' }]
      }
    ]}
  />
);
```

### 3. 混合配置方式

```tsx
import React from 'react';
import { CommonTable } from '@/components/CommonTable/export';

const UserPage = () => (
  <CommonTable
    config={baseConfig}          // 基础JSON配置
    title="覆盖的标题"           // Props覆盖
    renderToolbar={(toolbar) => ( // 自定义渲染
      <div className="custom-toolbar">{toolbar}</div>
    )}
  />
);
```

## 📖 API文档

### CommonTable Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|-------|
| config | JSON配置对象 | `TableConfig<T>` | - |
| title | 表格标题 | `string` | - |
| dataSource | 数据源配置 | `DataSourceConfig` | - |
| columns | 列配置 | `TableColumnConfig[]` | - |
| actions | 表格操作 | `TableAction[]` | - |
| filters | 筛选配置 | `FilterConfig[]` | - |
| stats | 统计配置 | `StatsConfig[]` | - |
| onStateChange | 状态变化回调 | `(state) => void` | - |
| onSelectionChange | 选择变化回调 | `(keys, rows) => void` | - |
| tableRef | 表格实例引用 | `MutableRefObject` | - |
| renderToolbar | 自定义工具栏 | `(toolbar) => ReactNode` | - |
| renderFilters | 自定义筛选器 | `(filters) => ReactNode` | - |
| renderStats | 自定义统计 | `(stats) => ReactNode` | - |

### TableConfig 配置项

```typescript
interface TableConfig<T> {
  name: string;                    // 表格标识
  title?: string;                  // 表格标题
  dataSource: DataSourceConfig;    // 数据源配置
  columns: TableColumnConfig[];    // 列配置
  features?: FeaturesConfig;       // 功能配置
  actions?: TableAction[];         // 表格操作
  style?: StyleConfig;             // 样式配置
  rowConfig?: RowConfig;           // 行配置
  emptyConfig?: EmptyConfig;       // 空状态配置
  permissions?: PermissionConfig;  // 权限配置
}
```

### 列渲染类型

支持以下内置渲染类型：

- `text` - 普通文本
- `number` - 数字格式化
- `date` - 日期格式化
- `datetime` - 日期时间格式化
- `status` - 状态标签
- `avatar` - 头像信息
- `image` - 图片展示
- `link` - 链接
- `badge` - 徽章
- `progress` - 进度条
- `tags` - 标签组
- `actions` - 操作按钮
- `custom` - 自定义渲染

### 筛选器类型

支持以下筛选器类型：

- `input` - 输入框
- `select` - 下拉选择
- `multiSelect` - 多选下拉
- `dateRange` - 日期范围
- `numberRange` - 数字范围

## 🎨 高级用法

### 1. 自定义列渲染器

```tsx
const columns = [
  {
    key: 'custom',
    title: '自定义列',
    render: (value, record, index) => (
      <div className="custom-cell">
        {/* 自定义内容 */}
      </div>
    )
  }
];
```

### 2. 表格实例方法

```tsx
const tableRef = useRef<TableMethods>(null);

// 刷新数据
tableRef.current?.refresh();

// 搜索
tableRef.current?.search('关键词');

// 筛选
tableRef.current?.filter({ status: 'active' });

// 获取选中行
const selectedRows = tableRef.current?.getSelectedRows();

// 导出数据
tableRef.current?.exportData({ filename: '用户列表.xlsx' });
```

### 3. 事件处理

```tsx
const handleStateChange = (state) => {
  console.log('当前状态:', state);
};

const handleSelectionChange = (keys, rows) => {
  console.log('选中项:', keys, rows);
};

<CommonTable
  config={config}
  onStateChange={handleStateChange}
  onSelectionChange={handleSelectionChange}
/>
```

### 4. 权限控制

```tsx
const config = {
  permissions: {
    add: 'user:create',
    edit: 'user:update',
    delete: 'user:delete'
  },
  columns: [
    {
      key: 'actions',
      title: '操作',
      renderType: 'actions',
      actionsConfig: [
        {
          key: 'edit',
          label: '编辑',
          permission: 'user:update',
          onClick: handleEdit
        }
      ]
    }
  ]
};
```

## 🔧 开发指南

### 项目结构

```
CommonTable/
├── index.tsx              # 主组件
├── types.ts              # 类型定义
├── useTableData.ts       # 数据管理Hook
├── ConfigParser.tsx      # 配置解析器
├── TableFilters.tsx      # 筛选组件
├── TableComponents.tsx   # 工具组件
├── Example.tsx           # 使用示例
├── export.ts            # 导出文件
└── README.md            # 说明文档
```

### 扩展新的渲染类型

1. 在 `types.ts` 中添加新的渲染类型
2. 在 `ConfigParser.tsx` 中实现渲染器
3. 更新类型定义和文档

```typescript
// 1. 添加类型
type CellRenderType = 'text' | 'number' | 'yourNewType';

// 2. 实现渲染器
class ColumnRenderer {
  static yourNewType(value: any, config?: TableColumnConfig): ReactNode {
    // 渲染逻辑
    return <YourComponent value={value} />;
  }
}
```

## 🚀 性能优化

1. **虚拟滚动**：大数据量时自动启用
2. **智能缓存**：避免重复请求
3. **防抖搜索**：减少API调用
4. **懒加载**：按需加载数据
5. **批量操作**：提高操作效率

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 常见问题

### Q: 如何处理复杂的数据结构？

A: 使用自定义渲染器或者在数据获取时进行格式化。

### Q: 如何实现表格联动？

A: 通过 `onStateChange` 回调监听状态变化，在父组件中处理联动逻辑。

### Q: 如何优化大数据量表格？

A: 启用虚拟滚动、服务端分页、数据缓存等功能。

### Q: 如何实现自定义主题？

A: 通过CSS变量或者Ant Design主题定制功能。