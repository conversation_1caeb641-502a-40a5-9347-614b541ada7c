import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import { emailTemplateService } from '../services/email';
import { SUCCESS } from '../constants/errorCodes';

const { Option } = Select;

interface TemplateCodeSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  tenantId: number;
  appId?: string;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
}

interface TemplateCodeOption {
  code: string;
  name: string;
}

const TemplateCodeSelector: React.FC<TemplateCodeSelectorProps> = ({
  value,
  onChange,
  tenantId,
  appId,
  placeholder = "请选择模板代码",
  allowClear = true,
  disabled = false
}) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<TemplateCodeOption[]>([]);

  const fetchTemplateCodeList = async () => {
    if (!tenantId) return;
    
    setLoading(true);
    try {
      const response = await emailTemplateService.getTemplateCodeList(tenantId, appId);
      if (response.code === SUCCESS && response.data) {
        setOptions(response.data);
      } else {
        console.error('Failed to fetch template codes:', response.message);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error fetching template codes:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplateCodeList();
  }, [tenantId, appId]);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) => 
        String(option?.children || '').toLowerCase().includes(input.toLowerCase())
      }
    >
      {options.map((template, index) => (
        <Option key={template.code || `template-${index}`} value={template.code}>
          {template.name}
        </Option>
      ))}
    </Select>
  );
};

export default TemplateCodeSelector;