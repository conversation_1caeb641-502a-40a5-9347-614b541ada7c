import { useRef, useCallback } from 'react';

/**
 * 自定义Hook：防止API重复调用
 * @param apiCall 要执行的API调用函数
 * @param deps 依赖项数组
 * @returns 去重后的API调用函数
 */
export function useApiCall<T extends any[], R>(
  apiCall: (...args: T) => Promise<R>,
  deps: any[] = []
) {
  const loadingRef = useRef(false);
  const lastCallRef = useRef<string>('');

  const debouncedApiCall = useCallback(async (...args: T): Promise<R | null> => {
    // 生成调用标识符
    const callId = JSON.stringify(args);
    
    // 如果正在加载或与上次调用相同，则跳过
    if (loadingRef.current || lastCallRef.current === callId) {
      return null;
    }

    loadingRef.current = true;
    lastCallRef.current = callId;

    try {
      const result = await apiCall(...args);
      return result;
    } finally {
      loadingRef.current = false;
    }
  }, [apiCall, ...deps]);

  return debouncedApiCall;
}

/**
 * 自定义Hook：防止重复的API调用（基于key）
 * @param apiCall 要执行的API调用函数
 * @param key 用于标识调用的key
 * @returns 去重后的API调用函数
 */
export function useApiCallWithKey<T extends any[], R>(
  apiCall: (...args: T) => Promise<R>,
  key: string
) {
  const loadingRef = useRef(false);
  const lastKeyRef = useRef<string>('');

  const debouncedApiCall = useCallback(async (...args: T): Promise<R | null> => {
    // 如果正在加载或与上次调用相同，则跳过
    if (loadingRef.current || lastKeyRef.current === key) {
      return null;
    }

    loadingRef.current = true;
    lastKeyRef.current = key;

    try {
      const result = await apiCall(...args);
      return result;
    } finally {
      loadingRef.current = false;
    }
  }, [apiCall, key]);

  return debouncedApiCall;
} 