import { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import menuData from '../data/menuData.json';

// 菜单项接口
export interface MenuItem {
  key: string;
  label?: string;
  path?: string;
  icon?: string;
  type: 'menu' | 'submenu' | 'divider';
  children?: MenuItem[];
}

// 菜单状态接口
export interface MenuState {
  selectedKeys: string[];
  openKeys: string[];
}

// Hook 返回值接口
export interface UseMenuStateReturn {
  selectedKeys: string[];
  openKeys: string[];
  setOpenKeys: (keys: string[]) => void;
}

export const useMenuState = (): UseMenuStateReturn => {
  const location = useLocation();
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  // 获取菜单数据
  const menuItems = useMemo(() => {
    return menuData.menus as MenuItem[];
  }, []);

  // 扁平化菜单数据，用于查找
  const flatMenus = useMemo(() => {
    const flatten = (items: MenuItem[]): MenuItem[] => {
      const result: MenuItem[] = [];
      items.forEach(item => {
        if (item.type !== 'divider') {
          result.push(item);
          if (item.children) {
            result.push(...flatten(item.children));
          }
        }
      });
      return result;
    };
    return flatten(menuItems);
  }, [menuItems]);

  // 路径匹配函数
  const matchPath = (currentPath: string, menuPath: string): boolean => {
    if (currentPath === menuPath) return true;
    
    // 处理动态路由参数
    const currentSegments = currentPath.split('/').filter(Boolean);
    const menuSegments = menuPath.split('/').filter(Boolean);
    
    if (currentSegments.length !== menuSegments.length) return false;
    
    return menuSegments.every((segment, index) => {
      return segment.startsWith(':') || segment === currentSegments[index];
    });
  };

  // 根据路径查找菜单项
  const findMenuByPath = (path: string): MenuItem | undefined => {
    // 精确匹配
    let exactMatch = flatMenus.find(item => item.path === path);
    if (exactMatch) return exactMatch;

    // 使用路径匹配函数
    return flatMenus.find(item => item.path && matchPath(path, item.path));
  };

  // 查找菜单项的父级路径
  const findParentKeys = (targetKey: string): string[] => {
    const parents: string[] = [];
    
    const findParent = (items: MenuItem[], target: string): boolean => {
      for (const item of items) {
        if (item.children) {
          if (item.children.some(child => child.key === target)) {
            parents.push(item.key);
            return true;
          }
          if (findParent(item.children, target)) {
            parents.push(item.key);
            return true;
          }
        }
      }
      return false;
    };

    findParent(menuItems, targetKey);
    return parents.reverse();
  };

  // 计算当前选中的菜单项
  const selectedKeys = useMemo(() => {
    const currentPath = location.pathname;
    
    // 特殊处理根路径
    if (currentPath === '/' || currentPath === '') {
      return ['dashboard'];
    }
    
    const currentMenu = findMenuByPath(currentPath);
    if (currentMenu) {
      return [currentMenu.key];
    }
    
    // 如果没有找到精确匹配，尝试模糊匹配
    const pathSegments = currentPath.split('/').filter(Boolean);
    for (let i = pathSegments.length; i > 0; i--) {
      const partialPath = '/' + pathSegments.slice(0, i).join('/');
      const partialMatch = flatMenus.find(item => item.path === partialPath);
      if (partialMatch) {
        return [partialMatch.key];
      }
    }
    
    return [];
  }, [location.pathname, flatMenus]);

  // 自动设置展开的菜单项
  useEffect(() => {
    if (selectedKeys.length > 0) {
      const parentKeys = findParentKeys(selectedKeys[0]);
      setOpenKeys(parentKeys);
    }
  }, [selectedKeys, menuItems]);

  return {
    selectedKeys,
    openKeys,
    setOpenKeys,
  };
}; 