import { API_CONFIG, apiService } from '../utils/request';
import { ApiResponse } from '../types';

// 文件上传相关类型定义
export interface UploadFileRequest {
    scene_code: string;
    file_name: string;
    file_size: number;
    file_type: string;
}

export interface UploadFileResponse {
    file_id: number;
    file_name: string;
    file_size: number;
    file_type: string;
    access_url: string;
    created_at: string;
}

export interface FileInfoResponse {
    id: number;
    file_name: string;
    file_size: number;
    file_type: string;
    file_hash: string;
    access_url: string;
    is_temporary: boolean;
    is_permanent: boolean;
    expire_at?: string;
    created_at: string;
    meta: Record<string, any>;
}

export interface DeleteFileRequest {
    file_id: number;
}

export interface GetFileRequest {
    file_id: number;
}

export class FileUploadService {
    /**
     * 上传文件
     */
    static async uploadFile(file: File, sceneCode: string, appId?: string): Promise<ApiResponse<UploadFileResponse>> {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('scene_code', sceneCode);

        return await apiService.post(API_CONFIG.PREFIXES.USER + '/file/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                ...(appId ? { 'X-App-Id': appId } : {})
            },
        });
    }

    /**
     * 获取文件信息
     */
    static async getFileInfo(fileId: number, appId?: string): Promise<ApiResponse<FileInfoResponse>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/file/info', {
            file_id: fileId
        }, {
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }

    /**
     * 删除文件
     */
    static async deleteFile(fileId: number, appId?: string): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/file/delete', {
            file_id: fileId
        }, {
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }
}