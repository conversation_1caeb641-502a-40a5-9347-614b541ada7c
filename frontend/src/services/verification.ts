import { apiService, API_ENDPOINTS } from '../utils/request';
import { ApiResponse } from '../types/api';

export interface VerificationConfig {
  id?: number;
  tenant_id?: number;
  config_mode: 'static' | 'dynamic';
  
  // 静态配置字段
  purpose?: number;
  purpose_name?: string;
  
  // 动态配置字段
  business_scene?: string;
  judgment_dimension?: string;
  condition_expr?: string;
  verification_level?: number;
  require_verification?: boolean;
  
  // 通用配置字段
  target_type: number;
  target_type_name?: string;
  token_type: number;
  token_type_name?: string;
  token_length: number;
  expire_minutes: number;
  max_attempts: number;
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  template_code: string;
  priority: number;
  description?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateVerificationConfigRequest extends Omit<VerificationConfig, 'id' | 'created_at' | 'updated_at' | 'purpose_name' | 'target_type_name' | 'token_type_name'> {}

export interface UpdateVerificationConfigRequest extends Partial<CreateVerificationConfigRequest> {}

// 验证操作相关接口
export interface SendVerificationRequest {
  target: string;
  target_type: number;
  purpose?: number;
  business_scene?: string;
  token_type?: number; // 令牌类型：1=链接, 2=验证码, 3=混合模式
  extra_data?: Record<string, any>;
}

export interface SendVerificationResponse {
  request_id: string;
  expire_at: string;
  resend_interval: number;
}

export interface VerifyTokenRequest {
  token: string;
  target?: string;
  target_type?: number;
  variables?: Record<string, string>; // 额外验证参数（如混合模式的验证码）
}

export interface VerifyTokenResponse {
  is_valid: boolean;
  expire_at?: string;
  extra_data?: Record<string, any>;
}

export interface ResendVerificationRequest {
  request_id: string;
}

export interface CheckTokenStatusRequest {
  token?: string;
  request_id?: string;
}

export interface CheckTokenStatusResponse {
  status: 'pending' | 'verified' | 'expired' | 'invalid';
  expire_at?: string;
  attempts_left: number;
}

export interface VerificationStatisticsRequest {
  start_date?: string;
  end_date?: string;
  target_type?: number;
  purpose?: number;
}

export interface VerificationStatisticsResponse {
  total_sent: number;
  total_verified: number;
  success_rate: number;
  daily_stats: Array<{
    date: string;
    sent: number;
    verified: number;
  }>;
}

/**
 * 统一验证服务
 * 包含配置管理和验证操作两部分功能
 */
export class VerificationService {

  // ===== 配置管理相关方法 =====
  
  // 获取验证配置列表
  static async getConfigs(params: {
    config_mode?: string;
    page?: number;
    page_size?: number;
    keyword?: string;
    purpose?: number;
    target_type?: number;
    token_type?: number;
    is_active?: boolean;
    order_by?: string;
    order_desc?: boolean;
  }): Promise<ApiResponse<VerificationConfig[]>> {
    return apiService.get(API_ENDPOINTS.VERIFICATION.CONFIGS_LIST, { params });
  }

  // 统一配置管理 - 创建静态配置
  static async createStaticConfig(data: CreateVerificationConfigRequest): Promise<ApiResponse<VerificationConfig>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_CREATE, {
      operation: 'create_static',
      ...data
    });
  }

  // 统一配置管理 - 创建动态配置
  static async createDynamicConfig(data: CreateVerificationConfigRequest): Promise<ApiResponse<VerificationConfig>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_CREATE, {
      operation: 'create_dynamic',
      ...data
    });
  }

  // 统一配置管理 - 更新静态配置
  static async updateStaticConfig(id: number, data: UpdateVerificationConfigRequest): Promise<ApiResponse<VerificationConfig>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_UPDATE, {
      operation: 'update_static',
      id,
      ...data
    });
  }

  // 统一配置管理 - 更新动态配置
  static async updateDynamicConfig(id: number, data: UpdateVerificationConfigRequest): Promise<ApiResponse<VerificationConfig>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_UPDATE, {
      operation: 'update_dynamic',
      id,
      ...data
    });
  }

  // 统一配置管理 - 删除配置
  static async deleteConfig(id: number): Promise<ApiResponse<void>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_DELETE, {
      operation: 'delete',
      id
    });
  }

  // 复制配置
  static async copyConfigs(data: {
    source_tenant_id: number;
    target_tenant_id: number;
    overwrite?: boolean;
  }): Promise<ApiResponse<void>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_COPY, data);
  }

  // 获取有效配置
  static async getEffectiveConfig(data: {
    purpose: number;
    target_type: number;
    business_scene?: string;
  }): Promise<ApiResponse<VerificationConfig>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.CONFIGS_EFFECTIVE, data);
  }

  // ===== 验证操作相关方法 =====

  // 发送验证
  static async sendVerification(data: SendVerificationRequest): Promise<ApiResponse<SendVerificationResponse>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.SEND, data);
  }

  // 验证令牌
  static async verifyToken(data: VerifyTokenRequest): Promise<ApiResponse<VerifyTokenResponse>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.VERIFY, data);
  }

  // 重新发送验证
  static async resendVerification(data: ResendVerificationRequest): Promise<ApiResponse<SendVerificationResponse>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.RESEND, data);
  }

  // 检查令牌状态
  static async checkTokenStatus(data: CheckTokenStatusRequest): Promise<ApiResponse<CheckTokenStatusResponse>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.STATUS, data);
  }

  // 获取验证统计
  static async getStatistics(data: VerificationStatisticsRequest): Promise<ApiResponse<VerificationStatisticsResponse>> {
    return apiService.post(API_ENDPOINTS.VERIFICATION.STATISTICS, data);
  }
}