import {API_CONFIG, apiService, request} from '../utils/request';
import {API_ENDPOINTS} from '../utils/request';
import {ApiResponse} from '../types';

// 密码策略类型
export interface PasswordPolicy {
    min_length: number;
    max_length: number;
    require_uppercase: boolean;
    require_lowercase: boolean;
    require_digits: boolean;
    require_special_chars: boolean;
    forbidden_patterns: string[];
    password_history_count: number;
    expire_days: number;
}

// 注册方式类型
export interface RegistrationMethods {
    email: {
        enabled: boolean;
        require_verification: boolean;
        manual_activation: boolean; // 手动激活
        verification_template_code?: string; // 邮箱验证模板代码
    };
    phone: {
        enabled: boolean;
        require_verification: boolean;
        manual_activation: boolean; // 手动激活
        verification_template_code?: string; // 短信验证模板代码
    };
    oauth: {
        enabled: boolean;
        manual_activation: boolean; // 手动激活
    };
    admin_creation: {
        enabled: boolean;
        require_approval: boolean;
    };
}

// 租户信息类型
export interface TenantInfo {
    type: 'enterprise' | 'personal'; // 企业或个人
    service_email: string; // 客服邮箱
    address: string; // 地址
    contact_person: string; // 联系人
    contact_phone: string; // 联系电话
    website: string; // 官网地址
    custom_domain: string; // 自定义域名
    app_icon: string; // 应用图标
    description: string; // 描述
    business_license: string; // 营业执照（企业）
    password_reset_url: string; // 密码重置URL
    password_reset_request_url: string; // 申请密码重置URL
    login_url: string; // 登录URL
}

// 租户配置类型
export interface TenantConfig {
    tenant_id: number;
    config_key: string;
    config_value: string;
    config_type: string;
}

// 请求类型
export interface GetPasswordPolicyRequest {
    app_id: string;
}

export interface UpdatePasswordPolicyRequest {
    app_id: string;
    tenant_id?: number;
    policy: PasswordPolicy;
}

export interface GetRegistrationMethodsRequest {
    app_id: string;
}

export interface UpdateRegistrationMethodsRequest {
    app_id: string;
    methods: RegistrationMethods;
}

export interface GetTenantInfoRequest {
    tenant_id: number;
}

export interface UpdateTenantInfoRequest {
    app_id: string;
    tenant_id?: number;
    info: TenantInfo;
}

export interface GetTenantConfigsRequest {
    tenant_id: number;
}

export interface UpdateTenantConfigRequest {
    tenant_id: number;
    config_key: string;
    config_value: string;
}

export interface CopySystemConfigsRequest {
    tenant_id: number;
}

// 响应类型
export interface PasswordPolicyResponse {
    tenant_id: number;
    policy: PasswordPolicy;
}

export interface RegistrationMethodsResponse {
    tenant_id: number;
    methods: RegistrationMethods;
}

export interface TenantInfoResponse {
    tenant_id: number;
    info: TenantInfo;
}

export interface TenantConfigListResponse {
    configs: TenantConfig[];
    total: number;
}

export interface AppConfig {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    config_key: string;
    config_value: string;
    description: string;
    created_at: string;
    updated_at: string;
}

export interface GetAppConfigsRequest {
    tenant_id: number;
}

export interface UpdateAppConfigRequest {
    tenant_id: number;
    config_key: string;
    config_value: string;
    description?: string;
}

export interface AppConfigListResponse {
    configs: AppConfig[];
    total: number;
}

export class AppConfigService {
    /**
     * 获取密码策略
     */
    static async getPasswordPolicy(appId: string): Promise<ApiResponse<PasswordPolicyResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/app-config/password-policy', {
            params: {app_id: appId},
        });
    }

    /**
     * 更新密码策略
     */
    static async updatePasswordPolicy(data: UpdatePasswordPolicyRequest, appId?: string): Promise<ApiResponse<void>> {
        // 如果没有传递app_id，使用传入的appId参数
        if (!data.app_id && appId) {
            data.app_id = appId;
        }
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/app-config/password-policy', data);
    }

    /**
     * 获取注册方式配置
     */
    static async getRegistrationMethods(appId: string): Promise<ApiResponse<RegistrationMethodsResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/app-config/registration-methods', {
            params: {app_id: appId},
        });
    }

    /**
     * 更新注册方式配置
     */
    static async updateRegistrationMethods(data: UpdateRegistrationMethodsRequest, appId?: string): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/app-config/registration-methods', data, {
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }

    /**
     * 获取租户所有配置
     */
    static async getAppConfigs(tenantId: number, appId?: string): Promise<ApiResponse<AppConfigListResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/app-config/configs', {
            params: {tenant_id: tenantId},
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }

    /**
     * 更新租户配置
     */
    static async updateAppConfig(data: UpdateAppConfigRequest, appId?: string): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/app-config/config', data, {
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }

    /**
     * 复制系统配置到租户
     */
    static async copySystemConfigs(tenantId: number, appId?: string): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/app-config/copy-system', {
            params: {tenant_id: tenantId}
        }, {
            headers: appId ? { 'X-App-Id': appId } : undefined,
        });
    }

    /**
     * 获取租户信息配置
     */
    static async getTenantInfo(appId: string): Promise<ApiResponse<TenantInfoResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/app-config/tenant-info', {
            params: {app_id: appId},
        });
    }

    /**
     * 更新租户信息
     */
    static async updateTenantInfo(data: UpdateTenantInfoRequest, appId?: string): Promise<ApiResponse<void>> {
        // 如果没有传递app_id，使用传入的appId参数
        if (!data.app_id && appId) {
            data.app_id = appId;
        }
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/app-config/tenant-info', data);
    }
} 