# 图标异步加载系统

## 概述

本系统实现了图标的异步加载和缓存机制，避免一次性加载所有图标导致页面性能问题。

## 核心特性

- **按需加载**: 只在需要时加载图标组件
- **智能缓存**: 已加载的图标会被缓存，避免重复加载
- **队列管理**: 相同图标的并发加载请求会被队列化处理
- **错误处理**: 优雅处理图标加载失败的情况
- **预加载支持**: 支持预加载常用图标

## 使用方法

### 1. 基础使用

```tsx
import { AsyncIcon } from '../utils/iconLoader';

// 在组件中使用
const MyComponent = () => {
  return (
    <div>
      <AsyncIcon iconName="UserOutlined" />
      <AsyncIcon iconName="SettingOutlined" style={{ color: 'red' }} />
    </div>
  );
};
```

### 2. 带fallback的使用

```tsx
import { AsyncIcon } from '../utils/iconLoader';

const MyComponent = () => {
  return (
    <AsyncIcon 
      iconName="UserOutlined" 
      fallback={<span>加载中...</span>}
      style={{ fontSize: '16px' }}
    />
  );
};
```

### 3. 预加载图标

```tsx
import { preloadIcons, preloadCommonIcons } from '../utils/iconLoader';

// 预加载特定图标
await preloadIcons(['UserOutlined', 'SettingOutlined']);

// 预加载常用图标
await preloadCommonIcons();
```

### 4. 在菜单中使用

```tsx
import { AsyncIcon } from '../utils/iconLoader';

// 菜单项配置
const menuItem = {
  key: 'user',
  label: '用户管理',
  icon: <AsyncIcon iconName="UserOutlined" />,
};
```

## API 参考

### AsyncIcon 组件

```tsx
interface AsyncIconProps {
  iconName: string;           // 图标名称
  fallback?: React.ReactNode; // 加载失败时的fallback
  style?: React.CSSProperties; // 样式
  className?: string;         // CSS类名
}
```

### 工具函数

#### loadIcon(iconName: string): Promise<IconComponent>
异步加载单个图标组件

#### preloadIcons(iconNames: string[]): Promise<void>
批量预加载图标

#### preloadCommonIcons(): Promise<void>
预加载常用图标

#### getIconLoadingStatus(): { cached: string[], loading: string[], queued: string[] }
获取图标加载状态

#### clearIconCache(): void
清除图标缓存

## 性能优化

### 1. 预加载策略

- **应用启动时**: 预加载常用图标（DashboardOutlined, UserOutlined等）
- **路由变化时**: 根据路由预加载相关图标
- **用户交互时**: 按需加载其他图标

### 2. 缓存策略

- 已加载的图标会被永久缓存
- 避免重复的网络请求
- 支持并发请求的队列化处理

### 3. 错误处理

- 图标加载失败时显示fallback
- 不会阻塞其他图标的加载
- 提供详细的错误日志

## 配置说明

### 图标映射配置

在 `iconLoader.ts` 中的 `iconConfig` 对象中配置图标映射：

```typescript
const iconConfig = {
  UserOutlined: () => import('@ant-design/icons/UserOutlined'),
  SettingOutlined: () => import('@ant-design/icons/SettingOutlined'),
  // ... 更多图标
};
```

### 预加载配置

在 `iconPreloader.ts` 中配置不同场景的图标分组：

```typescript
const COMMON_ICONS = ['DashboardOutlined', 'UserOutlined', ...];
const USER_MANAGEMENT_ICONS = ['TeamOutlined', 'SolutionOutlined', ...];
const SYSTEM_MANAGEMENT_ICONS = ['SettingOutlined', 'DatabaseOutlined', ...];
```

## 最佳实践

### 1. 图标命名规范

- 使用 Ant Design 图标的完整名称
- 保持命名的一致性
- 避免使用不存在的图标名称

### 2. 性能考虑

- 优先使用常用图标
- 合理使用预加载功能
- 避免一次性加载过多图标

### 3. 错误处理

- 始终提供合适的fallback
- 监控图标加载失败的情况
- 提供用户友好的错误提示

## 迁移指南

### 从静态导入迁移

**之前:**
```tsx
import { UserOutlined } from '@ant-design/icons';

const MyComponent = () => {
  return <UserOutlined />;
};
```

**之后:**
```tsx
import { AsyncIcon } from '../utils/iconLoader';

const MyComponent = () => {
  return <AsyncIcon iconName="UserOutlined" />;
};
```

### 从图标映射迁移

**之前:**
```tsx
const iconMap = {
  UserOutlined: <UserOutlined />,
};

const MyComponent = () => {
  return iconMap['UserOutlined'];
};
```

**之后:**
```tsx
import { AsyncIcon } from '../utils/iconLoader';

const MyComponent = () => {
  return <AsyncIcon iconName="UserOutlined" />;
};
```

## 监控和调试

### 获取加载状态

```tsx
import { getIconLoadingStatus } from '../utils/iconLoader';

const status = getIconLoadingStatus();
console.log('Cached icons:', status.cached);
console.log('Loading icons:', status.loading);
console.log('Queued icons:', status.queued);
```

### 清除缓存

```tsx
import { clearIconCache } from '../utils/iconLoader';

// 在开发环境中清除缓存
if (process.env.NODE_ENV === 'development') {
  clearIconCache();
}
```

## 注意事项

1. **图标名称**: 必须与 Ant Design 图标名称完全匹配
2. **网络环境**: 在慢网络环境下，图标加载可能需要时间
3. **浏览器兼容性**: 依赖动态import，需要现代浏览器支持
4. **包大小**: 虽然按需加载，但每个图标仍会增加包大小
5. **缓存策略**: 图标会被永久缓存，直到手动清除 