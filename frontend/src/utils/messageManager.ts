import { message } from 'antd';

/**
 * 消息管理器 - 只限制错误消息的单实例
 * 防止页面弹出大量重复的错误提示
 */
class MessageManager {
  private errorMessages: Map<string, number> = new Map();
  private readonly DEBOUNCE_TIME = 3000; // 3秒内相同错误消息不重复显示

  /**
   * 显示错误消息（限制单实例）
   * @param content 错误内容
   * @param key 消息键值（可选，用于去重）
   */
  error(content: string, key?: string): void {
    const messageKey = key || content;
    const now = Date.now();
    const lastTime = this.errorMessages.get(messageKey);

    // 如果在防抖时间内，不显示重复错误消息
    if (lastTime && now - lastTime < this.DEBOUNCE_TIME) {
      return;
    }

    // 更新最后显示时间
    this.errorMessages.set(messageKey, now);

    // 显示错误消息
    message.error({
      content,
      key: messageKey, // 使用key确保相同消息只显示一个实例
      duration: 4, // 显示4秒
    });
  }

  /**
   * 显示成功消息（不限制单实例）
   * @param content 成功内容
   * @param key 消息键值（可选）
   */
  success(content: string, key?: string): void {
    const messageKey = key || content;
    message.success({
      content,
      key: messageKey,
      duration: 3,
    });
  }

  /**
   * 显示警告消息（不限制单实例）
   * @param content 警告内容
   * @param key 消息键值（可选）
   */
  warning(content: string, key?: string): void {
    const messageKey = key || content;
    message.warning({
      content,
      key: messageKey,
      duration: 4,
    });
  }

  /**
   * 显示信息消息（不限制单实例）
   * @param content 信息内容
   * @param key 消息键值（可选）
   */
  info(content: string, key?: string): void {
    const messageKey = key || content;
    message.info({
      content,
      key: messageKey,
      duration: 3,
    });
  }

  /**
   * 清除所有消息
   */
  destroy(): void {
    message.destroy();
    this.errorMessages.clear();
  }

  /**
   * 清除指定key的消息
   * @param key 消息键值
   */
  destroyByKey(key: string): void {
    message.destroy(key);
    this.errorMessages.delete(key);
  }

  /**
   * 清除错误消息记录（用于重置防抖状态）
   */
  clearErrorRecords(): void {
    this.errorMessages.clear();
  }
}

// 创建全局实例
export const messageManager = new MessageManager();

// 导出便捷方法
export const showError = (content: string, key?: string) => messageManager.error(content, key);
export const showSuccess = (content: string, key?: string) => messageManager.success(content, key);
export const showWarning = (content: string, key?: string) => messageManager.warning(content, key);
export const showInfo = (content: string, key?: string) => messageManager.info(content, key); 