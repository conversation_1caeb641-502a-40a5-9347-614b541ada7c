import * as React from 'react';
import { useState, useEffect } from 'react';

// 图标组件类型
type IconComponent = React.ComponentType<any>;

// 图标缓存
const iconCache = new Map<string, IconComponent>();

// 图标加载状态
const loadingStates = new Map<string, boolean>();

// 图标加载队列
const loadingQueue: Array<{
  iconName: string;
  resolve: (component: IconComponent) => void;
  reject: (error: Error) => void;
}> = [];

// 图标映射配置
const iconConfig: { [key: string]: () => Promise<{ default: IconComponent }> } = {
  // 基础图标 - 预加载
  DashboardOutlined: () => import('@ant-design/icons/DashboardOutlined'),
  TeamOutlined: () => import('@ant-design/icons/TeamOutlined'),
  UserOutlined: () => import('@ant-design/icons/UserOutlined'),
  ApartmentOutlined: () => import('@ant-design/icons/ApartmentOutlined'),
  SettingOutlined: () => import('@ant-design/icons/SettingOutlined'),
  
  // 用户管理相关图标 - 按需加载
  SolutionOutlined: () => import('@ant-design/icons/SolutionOutlined'),
  CrownOutlined: () => import('@ant-design/icons/CrownOutlined'),
  SafetyCertificateOutlined: () => import('@ant-design/icons/SafetyCertificateOutlined'),
  KeyOutlined: () => import('@ant-design/icons/KeyOutlined'),
  SecurityScanOutlined: () => import('@ant-design/icons/SecurityScanOutlined'),
  UserSwitchOutlined: () => import('@ant-design/icons/UserSwitchOutlined'),
  
  // 系统管理相关图标 - 按需加载
  DatabaseOutlined: () => import('@ant-design/icons/DatabaseOutlined'),
  FileTextOutlined: () => import('@ant-design/icons/FileTextOutlined'),
  FolderOutlined: () => import('@ant-design/icons/FolderOutlined'),
  
  // 通信相关图标 - 按需加载
  MailOutlined: () => import('@ant-design/icons/MailOutlined'),
  BellOutlined: () => import('@ant-design/icons/BellOutlined'),
  
  // 功能图标 - 按需加载
  ThunderboltOutlined: () => import('@ant-design/icons/ThunderboltOutlined'),
  UploadOutlined: () => import('@ant-design/icons/UploadOutlined'),
  DownloadOutlined: () => import('@ant-design/icons/DownloadOutlined'),
  BankOutlined: () => import('@ant-design/icons/BankOutlined'),
  
  // 资源管理相关图标 - 按需加载
  ApiOutlined: () => import('@ant-design/icons/ApiOutlined'),
  GlobalOutlined: () => import('@ant-design/icons/GlobalOutlined'),
  AppstoreOutlined: () => import('@ant-design/icons/AppstoreOutlined'),
  
  // 文件系统相关图标 - 按需加载
  FileOutlined: () => import('@ant-design/icons/FileOutlined'),
  BranchesOutlined: () => import('@ant-design/icons/BranchesOutlined'),
  EyeOutlined: () => import('@ant-design/icons/EyeOutlined'),
  MoreOutlined: () => import('@ant-design/icons/MoreOutlined'),
  SaveOutlined: () => import('@ant-design/icons/SaveOutlined'),
  InfoCircleOutlined: () => import('@ant-design/icons/InfoCircleOutlined'),
  
  // 编辑相关图标 - 按需加载
  EditOutlined: () => import('@ant-design/icons/EditOutlined'),
  DeleteOutlined: () => import('@ant-design/icons/DeleteOutlined'),
  PlusOutlined: () => import('@ant-design/icons/PlusOutlined'),
  ReloadOutlined: () => import('@ant-design/icons/ReloadOutlined'),
  SearchOutlined: () => import('@ant-design/icons/SearchOutlined'),
  
  // 状态图标 - 按需加载
  LockOutlined: () => import('@ant-design/icons/LockOutlined'),
  UnlockOutlined: () => import('@ant-design/icons/UnlockOutlined'),
  CheckCircleOutlined: () => import('@ant-design/icons/CheckCircleOutlined'),
  CloseCircleOutlined: () => import('@ant-design/icons/CloseCircleOutlined'),
  
  // 其他常用图标 - 按需加载
  HomeOutlined: () => import('@ant-design/icons/HomeOutlined'),
  CalendarOutlined: () => import('@ant-design/icons/CalendarOutlined'),
  TagOutlined: () => import('@ant-design/icons/TagOutlined'),
  LinkOutlined: () => import('@ant-design/icons/LinkOutlined'),
  ArrowLeftOutlined: () => import('@ant-design/icons/ArrowLeftOutlined'),
  ShareAltOutlined: () => import('@ant-design/icons/ShareAltOutlined'),
  CopyOutlined: () => import('@ant-design/icons/CopyOutlined'),
  FilterOutlined: () => import('@ant-design/icons/FilterOutlined'),
  BarsOutlined: () => import('@ant-design/icons/BarsOutlined'),
  SortAscendingOutlined: () => import('@ant-design/icons/SortAscendingOutlined'),
  ClockCircleOutlined: () => import('@ant-design/icons/ClockCircleOutlined'),
  StarOutlined: () => import('@ant-design/icons/StarOutlined'),
  CommentOutlined: () => import('@ant-design/icons/CommentOutlined'),
  HistoryOutlined: () => import('@ant-design/icons/HistoryOutlined'),
  HeartOutlined: () => import('@ant-design/icons/HeartOutlined'),
  HeartFilled: () => import('@ant-design/icons/HeartFilled'),
  EyeInvisibleOutlined: () => import('@ant-design/icons/EyeInvisibleOutlined'),
  EyeTwoTone: () => import('@ant-design/icons/EyeTwoTone'),
  TrophyOutlined: () => import('@ant-design/icons/TrophyOutlined'),
  FireOutlined: () => import('@ant-design/icons/FireOutlined'),
  RiseOutlined: () => import('@ant-design/icons/RiseOutlined'),
  PlayCircleOutlined: () => import('@ant-design/icons/PlayCircleOutlined'),
};

/**
 * 异步加载图标组件
 * @param iconName 图标名称
 * @returns Promise<IconComponent>
 */
export const loadIcon = async (iconName: string): Promise<IconComponent> => {
  // 检查缓存
  if (iconCache.has(iconName)) {
    return iconCache.get(iconName)!;
  }

  // 检查是否正在加载
  if (loadingStates.get(iconName)) {
    return new Promise((resolve, reject) => {
      loadingQueue.push({ iconName, resolve, reject });
    });
  }

  // 检查图标配置是否存在
  if (!iconConfig[iconName]) {
    throw new Error(`Icon not found: ${iconName}`);
  }

  // 标记为加载中
  loadingStates.set(iconName, true);

  try {
    // 动态导入图标
    const module = await iconConfig[iconName]();
    const IconComponent = module.default;
    
    // 缓存图标组件
    iconCache.set(iconName, IconComponent);
    
    // 处理等待队列
    const queueItems = loadingQueue.filter(item => item.iconName === iconName);
    queueItems.forEach(item => item.resolve(IconComponent));
    loadingQueue.splice(loadingQueue.findIndex(item => item.iconName === iconName), 1);
    
    return IconComponent;
  } catch (error) {
    // 处理加载失败
    const queueItems = loadingQueue.filter(item => item.iconName === iconName);
    queueItems.forEach(item => item.reject(error as Error));
    loadingQueue.splice(loadingQueue.findIndex(item => item.iconName === iconName), 1);
    
    throw error;
  } finally {
    // 清除加载状态
    loadingStates.delete(iconName);
  }
};

/**
 * 预加载常用图标
 * @param iconNames 要预加载的图标名称数组
 */
export const preloadIcons = async (iconNames: string[]): Promise<void> => {
  const promises = iconNames.map(iconName => loadIcon(iconName).catch(() => {
    console.warn(`Failed to preload icon: ${iconName}`);
  }));
  
  await Promise.all(promises);
};

/**
 * 异步图标组件
 */
interface AsyncIconProps {
  iconName: string;
  fallback?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
}

export const AsyncIcon = ({ 
  iconName, 
  fallback = null, 
  style, 
  className 
}: AsyncIconProps): React.ReactElement | null => {
  const [IconComponent, setIconComponent] = useState<IconComponent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let mounted = true;

    const loadIconComponent = async () => {
      if (iconCache.has(iconName)) {
        if (mounted) {
          setIconComponent(iconCache.get(iconName)!);
        }
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const component = await loadIcon(iconName);
        if (mounted) {
          setIconComponent(component);
        }
      } catch (err) {
        if (mounted) {
          setError(err as Error);
          console.warn(`Failed to load icon: ${iconName}`, err);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    loadIconComponent();

    return () => {
      mounted = false;
    };
  }, [iconName]);

  if (loading) {
    return fallback ? React.createElement('span', {}, fallback) : null;
  }

  if (error || !IconComponent) {
    return fallback ? React.createElement('span', {}, fallback) : null;
  }

  return React.createElement(IconComponent, { style, className });
};

/**
 * 获取图标加载状态
 */
export const getIconLoadingStatus = () => {
  return {
    cached: Array.from(iconCache.keys()),
    loading: Array.from(loadingStates.keys()),
    queued: loadingQueue.map(item => item.iconName),
  };
};

/**
 * 清除图标缓存
 */
export const clearIconCache = () => {
  iconCache.clear();
  loadingStates.clear();
  loadingQueue.length = 0;
}; 