import React, { useState, useEffect } from 'react';
import { Button, ButtonProps } from 'antd';
// 临时权限检查函数，后续可以替换为真实的权限服务
const checkPermission = async (permissionCode: string): Promise<boolean> => {
  // 模拟权限检查，实际项目中应该调用真实的权限API
  console.log('Checking permission:', permissionCode);
  return true; // 临时返回true，实际应该根据用户权限判断
};

const batchCheckPermissions = async (permissions: string[]): Promise<Record<string, boolean>> => {
  // 模拟批量权限检查
  console.log('Batch checking permissions:', permissions);
  const result: Record<string, boolean> = {};
  permissions.forEach(perm => {
    result[perm] = true; // 临时返回true
  });
  return result;
};

// 临时认证上下文，后续可以替换为真实的认证服务
const useAuth = () => ({
  isAuthenticated: true, // 临时返回true
  user: null,
  login: () => Promise.resolve(),
  logout: () => Promise.resolve(),
});

// 权限按钮组件属性
export interface PermissionButtonProps extends Omit<ButtonProps, 'children'> {
  children: React.ReactNode;
  permissionCode: string;
  fallback?: React.ReactNode; // 无权限时显示的内容
  showFallback?: boolean; // 是否显示fallback，默认为false（隐藏）
}

/**
 * 权限按钮组件
 * 根据用户权限自动显示/隐藏按钮
 */
export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permissionCode,
  fallback = null,
  showFallback = false,
  ...buttonProps
}) => {
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const checkUserPermission = async () => {
      if (!isAuthenticated) {
        setHasPermission(false);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const result = await checkPermission(permissionCode);
        setHasPermission(result);
      } catch (error) {
        console.error('Failed to check permission:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    checkUserPermission();
  }, [permissionCode, isAuthenticated]);

  // 如果正在加载，显示禁用的按钮
  if (loading) {
    return (
      <Button {...buttonProps} disabled loading>
        {children}
      </Button>
    );
  }

  // 如果有权限，显示正常按钮
  if (hasPermission) {
    return <Button {...buttonProps}>{children}</Button>;
  }

  // 如果没有权限且需要显示fallback
  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  // 默认情况下不显示任何内容
  return null;
};

// 批量权限检查Hook
export interface UseBatchPermissionsOptions {
  permissions: Array<{
    code: string;
    key: string;
  }>;
  enabled?: boolean;
}

export interface UseBatchPermissionsReturn {
  permissions: Record<string, boolean>;
  loading: boolean;
  error: string | null;
  hasPermission: (key: string) => boolean;
  hasAnyPermission: (keys: string[]) => boolean;
  hasAllPermissions: (keys: string[]) => boolean;
  refresh: () => Promise<void>;
}

/**
 * 批量权限检查Hook
 */
export const useBatchPermissions = ({
  permissions,
  enabled = true,
}: UseBatchPermissionsOptions): UseBatchPermissionsReturn => {
  const [permissionResults, setPermissionResults] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  const checkPermissions = async () => {
    if (!isAuthenticated || !enabled || permissions.length === 0) {
      setPermissionResults({});
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const permissionCodes = permissions.map(p => p.code);
      const results = await batchCheckPermissions(permissionCodes);
      setPermissionResults(results);
    } catch (err) {
      console.error('Failed to batch check permissions:', err);
      setError('权限检查失败');
      setPermissionResults({});
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkPermissions();
  }, [permissions, isAuthenticated, enabled]);

  const hasPermission = (key: string): boolean => {
    return permissionResults[key] || false;
  };

  const hasAnyPermission = (keys: string[]): boolean => {
    return keys.some(key => permissionResults[key]);
  };

  const hasAllPermissions = (keys: string[]): boolean => {
    return keys.every(key => permissionResults[key]);
  };

  return {
    permissions: permissionResults,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refresh: checkPermissions,
  };
};

// 权限包装组件属性
export interface PermissionWrapperProps {
  children: React.ReactNode;
  permissionCode: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  loading?: React.ReactNode;
}

/**
 * 权限包装组件
 * 用于包装任意组件进行权限控制
 */
export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permissionCode,
  fallback = null,
  showFallback = false,
  loading: loadingComponent = null,
}) => {
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const checkUserPermission = async () => {
      if (!isAuthenticated) {
        setHasPermission(false);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const result = await checkPermission(permissionCode);
        setHasPermission(result);
      } catch (error) {
        console.error('Failed to check permission:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    checkUserPermission();
  }, [permissionCode, isAuthenticated]);

  // 如果正在加载
  if (loading) {
    return loadingComponent ? <>{loadingComponent}</> : null;
  }

  // 如果有权限，显示子组件
  if (hasPermission) {
    return <>{children}</>;
  }

  // 如果没有权限且需要显示fallback
  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  // 默认情况下不显示任何内容
  return null;
};

// 权限检查高阶组件
export interface WithPermissionOptions {
  permissionCode: string;
  fallback?: React.ComponentType;
  showFallback?: boolean;
}

/**
 * 权限检查高阶组件
 */
export const withPermission = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithPermissionOptions
) => {
  const PermissionHOC: React.FC<P> = (props) => {
    return (
      <PermissionWrapper
        permissionCode={options.permissionCode}
        fallback={options.fallback ? <options.fallback /> : null}
        showFallback={options.showFallback}
      >
        <WrappedComponent {...props} />
      </PermissionWrapper>
    );
  };

  PermissionHOC.displayName = `withPermission(${WrappedComponent.displayName || WrappedComponent.name})`;

  return PermissionHOC;
};

// 导出默认权限按钮
export default PermissionButton;
