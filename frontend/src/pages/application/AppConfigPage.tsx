import React, { useEffect, useMemo, useState } from 'react';
import { Card, Tabs, Form, Input, Switch, InputNumber, Button, Space, Typography, Row, Col, Tag, Spin, Select } from 'antd';
import { useParams } from 'react-router-dom';
import { AppConfigService, PasswordPolicy, RegistrationMethods, TenantInfo } from '../../services/appConfig';
import { getApplication, Application } from '../../services/application';
import { showAPIError } from '../../utils/errorHandler';
import { showError, showSuccess } from '../../utils/messageManager';
import { SUCCESS } from '../../constants/errorCodes';
import { LockOutlined, UserAddOutlined, BankOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import ImageUpload from '../../components/ImageUpload';
import TemplateCodeSelector from '../../components/TemplateCodeSelector';

const { Title, Text } = Typography;
const { Option } = Select;

const AppConfigPage: React.FC = () => {
  const { internalAppId } = useParams<{ internalAppId: string }>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [passwordPolicy, setPasswordPolicy] = useState<PasswordPolicy | null>(null);
  const [registrationMethods, setRegistrationMethods] = useState<RegistrationMethods | null>(null);
  const [tenantInfoConfig, setTenantInfoConfig] = useState<TenantInfo | null>(null);
  const [app, setApp] = useState<Application | null>(null);

  const [passwordForm] = Form.useForm();
  const [registrationForm] = Form.useForm();
  const [tenantInfoForm] = Form.useForm();

  const appIdHeader = useMemo(() => app?.app_id, [app]);

  useEffect(() => {
    const load = async () => {
      if (!internalAppId) return;
      setLoading(true);
      try {
        // 获取应用详情，拿到 app_id 用于头部
        const appRes = await getApplication(parseInt(internalAppId, 10));
        if (appRes.code === SUCCESS && appRes.data) {
          setApp(appRes.data);
        }
        // 拉取三类配置
        const [pwdRes, regRes, infoRes] = await Promise.all([
          AppConfigService.getPasswordPolicy(appRes.data?.app_id || ''),
          AppConfigService.getRegistrationMethods(appRes.data?.app_id || ''),
          AppConfigService.getTenantInfo(appRes.data?.app_id || ''),
        ]);
        if (pwdRes.code === SUCCESS && pwdRes.data) {
          setPasswordPolicy(pwdRes.data.policy);
        }
        if (regRes.code === SUCCESS && regRes.data) {
          setRegistrationMethods(regRes.data.methods);
        }
        if (infoRes.code === SUCCESS && infoRes.data) {
          setTenantInfoConfig(infoRes.data.info);
        }
      } catch (err) {
        showAPIError(err);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [internalAppId, passwordForm, registrationForm, tenantInfoForm]);

  // 监听数据变化，重新设置表单值
  useEffect(() => {
    if (registrationMethods) {
      console.log('Registration methods changed, setting form values:', registrationMethods);
      // 直接使用接口返回的值，不要手动处理
      registrationForm.setFieldsValue(registrationMethods);
    }
  }, [registrationMethods, registrationForm]);

  useEffect(() => {
    if (passwordPolicy) {
      console.log('Password policy changed, setting form values:', passwordPolicy);
      passwordForm.setFieldsValue(passwordPolicy);
    }
  }, [passwordPolicy, passwordForm]);

  useEffect(() => {
    if (tenantInfoConfig) {
      console.log('Tenant info changed, setting form values:', tenantInfoConfig);
      tenantInfoForm.setFieldsValue(tenantInfoConfig);
    }
  }, [tenantInfoConfig, tenantInfoForm]);

  const onSavePassword = async (values: PasswordPolicy) => {
    try {
      setSaving(true);
      const res = await AppConfigService.updatePasswordPolicy({ app_id: app?.app_id || '', policy: values }, appIdHeader);
      if (res.code === SUCCESS) {
        showSuccess('密码策略保存成功');
        setPasswordPolicy(values);
      } else {
        showError(res.message || '保存失败');
      }
    } catch (e) {
      showAPIError(e);
    } finally {
      setSaving(false);
    }
  };

  const onSaveRegistration = async (values: RegistrationMethods) => {
    try {
      setSaving(true);
      console.log('Form values before submission:', values);
      console.log('Email verification template code:', values.email?.verification_template_code);
      console.log('Phone verification template code:', values.phone?.verification_template_code);
      
      // 直接使用表单值，不要手动处理verification_template_code
      const requestData = { app_id: app?.app_id || '', methods: values };
      console.log('Request data:', requestData);
      
      const res = await AppConfigService.updateRegistrationMethods(requestData, appIdHeader);
      if (res.code === SUCCESS) {
        showSuccess('注册方式保存成功');
        setRegistrationMethods(values);
      } else {
        showError(res.message || '保存失败');
      }
    } catch (e) {
      showAPIError(e);
    } finally {
      setSaving(false);
    }
  };

  const onSaveTenantInfo = async (values: TenantInfo) => {
    try {
      setSaving(true);
      const res = await AppConfigService.updateTenantInfo({ app_id: app?.app_id || '', info: values }, appIdHeader);
      if (res.code === SUCCESS) {
        showSuccess('应用配置保存成功');
        setTenantInfoConfig(values);
      } else {
        showError(res.message || '保存失败');
      }
    } catch (e) {
      showAPIError(e);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载配置中...</div>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space align="center">
            <Tag color="blue">InternalAppID: {internalAppId}</Tag>
            {app && (
              <>
                <Tag color="green">AppID: {app.app_id}</Tag>
                <Tag color="purple">应用名称: {app.app_name}</Tag>
              </>
            )}
          </Space>
        </div>

        <Tabs
          defaultActiveKey="password"
          type="card"
          items={[
            {
              key: 'password',
              label: (
                <span>
                  <LockOutlined /> 密码策略
                </span>
              ),
              children: passwordPolicy ? (
                <Form form={passwordForm} layout="vertical" onFinish={onSavePassword} preserve={true}>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item label="最小长度" name="min_length" rules={[{ required: true }]}>
                        <InputNumber min={6} max={128} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="最大长度" name="max_length" rules={[{ required: true }]}>
                        <InputNumber min={6} max={128} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={6}><Form.Item label="需大写" name="require_uppercase" valuePropName="checked"><Switch /></Form.Item></Col>
                    <Col span={6}><Form.Item label="需小写" name="require_lowercase" valuePropName="checked"><Switch /></Form.Item></Col>
                    <Col span={6}><Form.Item label="需数字" name="require_digits" valuePropName="checked"><Switch /></Form.Item></Col>
                    <Col span={6}><Form.Item label="需特殊字符" name="require_special_chars" valuePropName="checked"><Switch /></Form.Item></Col>
                  </Row>
                  <Form.Item style={{ textAlign: 'center' }}>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={saving}>保存</Button>
                      <Button icon={<ReloadOutlined />} onClick={() => passwordForm.resetFields()}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              ) : (
                <div style={{ textAlign: 'center', padding: 20 }}>
                  <Spin size="small" />
                  <div style={{ marginTop: 8 }}>加载密码策略配置中...</div>
                </div>
              )
            },
            {
              key: 'registration',
              label: (
                <span>
                  <UserAddOutlined /> 注册方式
                </span>
              ),
              children: registrationMethods ? (
                <Form form={registrationForm} layout="vertical" onFinish={onSaveRegistration} preserve={true}>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Card size="small" title="邮箱注册">
                        <Form.Item label="启用" name={["email", "enabled"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="需要验证" name={["email", "require_verification"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="手动激活" name={["email", "manual_activation"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="验证模板代码" name={["email", "verification_template_code"]}>
                          <TemplateCodeSelector 
                            tenantId={app?.tenant_id || 0}
                            appId={appIdHeader}
                            placeholder="请选择邮箱验证模板"
                          />
                        </Form.Item>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title="手机注册">
                        <Form.Item label="启用" name={["phone", "enabled"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="需要验证" name={["phone", "require_verification"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="手动激活" name={["phone", "manual_activation"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="验证码模板" name={["phone", "verification_template_code"]}>
                          <Input placeholder="请输入短信验证模板代码" />
                        </Form.Item>
                      </Card>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Card size="small" title="OAuth注册">
                        <Form.Item label="启用" name={["oauth", "enabled"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="手动激活" name={["oauth", "manual_activation"]} valuePropName="checked"><Switch /></Form.Item>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title="管理员创建">
                        <Form.Item label="启用" name={["admin_creation", "enabled"]} valuePropName="checked"><Switch /></Form.Item>
                        <Form.Item label="需要审批" name={["admin_creation", "require_approval"]} valuePropName="checked"><Switch /></Form.Item>
                      </Card>
                    </Col>
                  </Row>
                  <Form.Item style={{ textAlign: 'center' }}>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={saving}>保存</Button>
                      <Button icon={<ReloadOutlined />} onClick={() => registrationForm.resetFields()}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              ) : (
                <div style={{ textAlign: 'center', padding: 20 }}>
                  <Spin size="small" />
                  <div style={{ marginTop: 8 }}>加载注册方式配置中...</div>
                </div>
              )
            },
            {
              key: 'app-info',
              label: (
                <span>
                  <BankOutlined /> 应用配置
                </span>
              ),
              children: tenantInfoConfig ? (
                <Form form={tenantInfoForm} layout="vertical" onFinish={onSaveTenantInfo} preserve={true}>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="客服邮箱" name="service_email" rules={[{ type: 'email', message: '请输入有效邮箱' }]}><Input /></Form.Item></Col>
                    <Col span={12}>
                      <Form.Item label="应用图标" name="app_icon">
                        <ImageUpload 
                          appId={appIdHeader}
                          sceneCode="app_icon"
                          placeholder="上传应用图标"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="密码重置URL" name="password_reset_url"><Input placeholder="密码重置提交地址" /></Form.Item></Col>
                    <Col span={12}><Form.Item label="申请密码重置URL" name="password_reset_request_url"><Input placeholder="申请密码重置地址" /></Form.Item></Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="登录URL" name="login_url"><Input placeholder="登录地址" /></Form.Item></Col>
                    <Col span={12}><Form.Item label="联系人" name="contact_person"><Input placeholder="联系人姓名" /></Form.Item></Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="联系电话" name="contact_phone"><Input placeholder="联系人电话" /></Form.Item></Col>
                    <Col span={12}><Form.Item label="官网地址" name="website"><Input placeholder="官网URL" /></Form.Item></Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="自定义域名" name="custom_domain"><Input placeholder="自定义域名" /></Form.Item></Col>
                    <Col span={12}><Form.Item label="地址" name="address"><Input placeholder="企业/个人地址" /></Form.Item></Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={12}><Form.Item label="营业执照" name="business_license"><Input placeholder="营业执照号码" /></Form.Item></Col>
                    <Col span={12}><Form.Item label="类型" name="type"><Select><Option value="personal">个人</Option><Option value="enterprise">企业</Option></Select></Form.Item></Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}><Form.Item label="描述" name="description"><Input.TextArea rows={3} placeholder="应用描述" /></Form.Item></Col>
                  </Row>
                  <Form.Item style={{ textAlign: 'center' }}>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={saving}>保存</Button>
                      <Button icon={<ReloadOutlined />} onClick={() => tenantInfoForm.resetFields()}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              ) : (
                <div style={{ textAlign: 'center', padding: 20 }}>
                  <Spin size="small" />
                  <div style={{ marginTop: 8 }}>加载应用配置中...</div>
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default AppConfigPage;


