import React, { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Typography,
  Row,
  Col,
  Select,
  Input,
  Modal,
  Form,
  Switch
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  ReloadOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  ClockCircleOutlined,
  KeyOutlined,
  CrownOutlined,
  LinkOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { getApplications, createApplication, updateApplication, enableApplication, disableApplication, Application } from '../../services/application';
import { useNavigate } from 'react-router-dom';
import { showError, showSuccess } from '../../utils/messageManager';
import { showAPIError, applyFieldErrorsToForm } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';
import { useTheme } from '../../contexts/ThemeContext';

const { Option } = Select;
const { Text } = Typography;

const ApplicationPage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();
  
  // 核心数据状态
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 分页和搜索状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  
  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingApplication, setEditingApplication] = useState<Application | null>(null);
  
  // 使用 useMemo 延迟创建表单实例，只在 Modal 显示时创建
  const [form] = Form.useForm();

  // 统计数据（基于实际数据计算）
  const stats = [
    { title: '总应用数', value: pagination.total, icon: <AppstoreOutlined />, color: '#1890ff' },
    { title: '活跃应用', value: Array.isArray(applications) ? applications.filter(a => a.is_active).length : 0, icon: <CheckCircleOutlined />, color: '#52c41a' },
    { title: '系统应用', value: Array.isArray(applications) ? applications.filter(a => a.is_system).length : 0, icon: <CrownOutlined />, color: '#722ed1' },
    { title: '应用类型', value: Array.isArray(applications) ? new Set(applications.map(a => a.app_type)).size : 0, icon: <GlobalOutlined />, color: '#fa8c16' },
  ];

  // 复制应用ID到剪贴板
  const handleCopyAppId = async (appId: string) => {
    try {
      await navigator.clipboard.writeText(appId);
      showSuccess('应用ID已复制到剪贴板');
    } catch (error) {
      showError('复制失败，请手动复制');
    }
  };

  // 加载数据
  useEffect(() => {
    fetchApplications();
  }, [pagination.current, pagination.pageSize, searchText, selectedStatus, selectedType]);

  const fetchApplications = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: pagination.current,
        size: pagination.pageSize,
      };
      if (searchText) params.keyword = searchText;
      if (selectedStatus) params.status = selectedStatus;
      if (selectedType) params.app_type = selectedType;

      const response = await getApplications(params);
      if (response.code === SUCCESS) {
        setApplications(response.data || []);
        setPagination(prev => ({
          ...prev,
          total: response.meta?.pagination?.total || 0,
        }));
      } else {
        showError(response.message || '获取应用列表失败');
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddApplication = () => {
    setEditingApplication(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEditApplication = (application: Application) => {
    setEditingApplication(application);
    setModalVisible(true);
    form.setFieldsValue({
      app_name: application.app_name,
      app_type: application.app_type,
      description: application.description,
      status: application.status,
      is_active: application.is_active,
      is_system: application.is_system,
      callback_urls: application.callback_urls,
      allowed_origins: application.allowed_origins,
      scopes: application.scopes,
      rate_limit: application.rate_limit,
      logo_url: application.logo_url,
      homepage_url: application.homepage_url,
      privacy_policy_url: application.privacy_policy_url,
      terms_of_service_url: application.terms_of_service_url,
      contact_email: application.contact_email,
    });
  };

  const handleEnableApplication = async (application: Application) => {
    try {
      const response = await enableApplication(application.internal_app_id);
      if (response.code === SUCCESS) {
        showSuccess('应用启用成功');
        fetchApplications();
      } else {
        showError(response.message || '启用应用失败');
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleDisableApplication = async (application: Application) => {
    try {
      const response = await disableApplication(application.internal_app_id);
      if (response.code === SUCCESS) {
        showSuccess('应用禁用成功');
        fetchApplications();
      } else {
        showError(response.message || '禁用应用失败');
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingApplication) {
        // 更新应用
        const response = await updateApplication({
          internal_app_id: editingApplication.internal_app_id,
          ...values,
        });
        if (response.code === SUCCESS) {
          showSuccess('应用更新成功');
          setModalVisible(false);
          fetchApplications();
        } else {
          showError(response.message || '更新应用失败');
        }
      } else {
        // 创建应用
        const response = await createApplication({
          ...values,
          tenant_id: 1, // 默认租户ID，实际应该从上下文获取
        });
        if (response.code === SUCCESS) {
          showSuccess('应用创建成功');
          setModalVisible(false);
          fetchApplications();
        } else {
          showError(response.message || '创建应用失败');
        }
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="green">活跃</Tag>;
      case 'inactive':
        return <Tag color="red">禁用</Tag>;
      case 'pending':
        return <Tag color="orange">待审核</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const getTypeTag = (type: string) => {
    switch (type) {
      case 'web':
        return <Tag color="blue">Web应用</Tag>;
      case 'mobile':
        return <Tag color="green">移动应用</Tag>;
      case 'desktop':
        return <Tag color="purple">桌面应用</Tag>;
      case 'api':
        return <Tag color="orange">API服务</Tag>;
      default:
        return <Tag color="default">{type}</Tag>;
    }
  };

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  // 表格列定义
  const columns = [
    {
      title: '应用配置',
      key: 'app_info',
      render: (record: Application) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.app_name}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.description || '暂无描述'}
          </div>
        </div>
      ),
    },
    {
      title: '应用类型',
      key: 'app_type',
      render: (record: Application) => (
        <div>
          {getTypeTag(record.app_type)}
          {record.is_system && <Tag color="gold" style={{ marginLeft: 4 }}>系统</Tag>}
        </div>
      ),
    },
    {
      title: '应用ID',
      key: 'app_id',
      width: 200,
      render: (record: Application) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            fontFamily: 'monospace',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: 160,
          }}>
            {record.app_id}
          </div>
          <Tooltip title="复制应用ID" color={tooltipColor}>
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyAppId(record.app_id)}
              style={{ 
                padding: 0, 
                marginLeft: 4,
                color: isDarkMode ? '#40a9ff' : '#1890ff',
                minWidth: 'auto',
                height: 'auto',
                flexShrink: 0,
              }}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Application) => (
        <div>
          {getStatusTag(record.status)}
          {record.is_active ? (
            <Tag color="green" style={{ marginLeft: 4 }}>启用</Tag>
          ) : (
            <Tag color="red" style={{ marginLeft: 4 }}>禁用</Tag>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      key: 'created_at',
      render: (record: Application) => (
        <Text style={{ fontSize: 12 }}>
          {record.created_at}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Application) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditApplication(record)}
              style={actionButtonStyle}
            >
              编辑
            </Button>
          </Tooltip>
          {record.is_active ? (
            <Tooltip title="禁用" color={tooltipColor}>
              <Button
                type="link"
                size="small"
                icon={<CloseCircleOutlined />}
                onClick={() => handleDisableApplication(record)}
                style={{ ...actionButtonStyle, color: '#ff4d4f' }}
              >
                禁用
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title="启用" color={tooltipColor}>
              <Button
                type="link"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleEnableApplication(record)}
                style={{ ...actionButtonStyle, color: '#52c41a' }}
              >
                启用
              </Button>
            </Tooltip>
          )}
          <Tooltip title="配置" color={tooltipColor}>
            <Button
              type="link"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => navigate(`/application/${record.internal_app_id}/config`)}
              style={actionButtonStyle}
            >
              配置
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="application-page">
      {/* 页面标题区域 - 改为统计展示 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{ fontSize: 24, fontWeight: 600, color: isDarkMode ? '#ffffff' : '#262626' }}>
                    {stat.value}
                  </div>
                  <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* 操作按钮 */}
          <div style={{ display: 'flex', gap: 8 }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchApplications}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddApplication}
            >
              新增应用
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <div style={{ marginBottom: 8, fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
              搜索关键词
            </div>
            <Input
              placeholder="搜索应用名称或代码"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <div style={{ marginBottom: 8, fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
              应用状态
            </div>
            <Select
              placeholder="选择状态"
              value={selectedStatus}
              onChange={setSelectedStatus}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">活跃</Option>
              <Option value="inactive">禁用</Option>
              <Option value="pending">待审核</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <div style={{ marginBottom: 8, fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
              应用类型
            </div>
            <Select
              placeholder="选择类型"
              value={selectedType}
              onChange={setSelectedType}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="web">Web应用</Option>
              <Option value="mobile">移动应用</Option>
              <Option value="desktop">桌面应用</Option>
              <Option value="api">API服务</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 表格区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
      >
        <Table
          columns={columns}
          dataSource={applications}
          rowKey="internal_app_id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10,
              }));
            },
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingApplication ? '编辑应用' : '新增应用'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            is_active: true,
            is_system: false,
            status: 'active',
            app_type: 'web',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="应用名称"
                name="app_name"
                rules={[{ required: true, message: '请输入应用名称' }]}
              >
                <Input placeholder="请输入应用名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="应用类型"
                name="app_type"
                rules={[{ required: true, message: '请选择应用类型' }]}
              >
                <Select placeholder="请选择应用类型">
                  <Option value="web">Web应用</Option>
                  <Option value="mobile">移动应用</Option>
                  <Option value="desktop">桌面应用</Option>
                  <Option value="api">API服务</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="应用状态"
                name="status"
                rules={[{ required: true, message: '请选择应用状态' }]}
              >
                <Select placeholder="请选择应用状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="pending">待审核</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="是否系统应用"
                name="is_system"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="应用描述"
            name="description"
          >
            <Input.TextArea 
              placeholder="请输入应用描述"
              rows={3}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="回调URL"
                name="callback_urls"
              >
                <Input.TextArea 
                  placeholder="请输入回调URL，多个URL用逗号分隔"
                  rows={3}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="允许的域名"
                name="allowed_origins"
              >
                <Input.TextArea 
                  placeholder="请输入允许的域名，多个域名用逗号分隔"
                  rows={3}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="权限范围"
                name="scopes"
              >
                <Input.TextArea 
                  placeholder="请输入权限范围，多个权限用逗号分隔"
                  rows={3}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="速率限制"
                name="rate_limit"
              >
                <Input placeholder="请输入速率限制（每分钟请求数）" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Logo URL"
                name="logo_url"
              >
                <Input placeholder="请输入Logo URL" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="主页 URL"
                name="homepage_url"
              >
                <Input placeholder="请输入主页 URL" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="隐私政策 URL"
                name="privacy_policy_url"
              >
                <Input placeholder="请输入隐私政策 URL" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="服务条款 URL"
                name="terms_of_service_url"
              >
                <Input placeholder="请输入服务条款 URL" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="联系邮箱"
            name="contact_email"
          >
            <Input placeholder="请输入联系邮箱" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingApplication ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApplicationPage; 