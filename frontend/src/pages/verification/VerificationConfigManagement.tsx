import React, { useState } from 'react';
import { 
  Card, Space, Tag, Button, Modal, Form, Select, Input, InputNumber, Switch, 
  message, Tabs, Divider, Table, Popconfirm, Row, Col
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, 
  ReloadOutlined
} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import { VerificationService, VerificationConfig } from '../../services/verification';
import { showAPIError } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';
import TemplateCodeSelector from '../../components/TemplateCodeSelector';
import './VerificationConfigManagement.less';

const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;

// 枚举定义
const PURPOSE_OPTIONS = [
  { value: 1, label: '注册激活' },
  { value: 2, label: '密码重置' },
  { value: 3, label: '邮箱变更' },
  { value: 4, label: '手机变更' },
  { value: 5, label: '登录验证' },
  { value: 6, label: 'MFA验证' },
];

const TARGET_TYPE_OPTIONS = [
  { value: 1, label: '邮箱' },
  { value: 2, label: '手机号' },
  { value: 3, label: 'MFA' },
];

const TOKEN_TYPE_OPTIONS = [
  { value: 1, label: '链接' },
  { value: 2, label: '验证码' },
  { value: 3, label: '混合模式（链接+验证码）' },
];

const VerificationConfigManagement: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [activeTab, setActiveTab] = useState('static');
  const [staticData, setStaticData] = useState<VerificationConfig[]>([]);
  const [dynamicData, setDynamicData] = useState<VerificationConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadedTabs, setLoadedTabs] = useState<Set<string>>(new Set());
  
  // 查询参数状态
  const [searchParams, setSearchParams] = useState({
    purpose: undefined as number | undefined,
    target_type: undefined as number | undefined,
    token_type: undefined as number | undefined,
    is_active: undefined as boolean | undefined,
    keyword: '',
  });
  

  
  // 模态框状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<VerificationConfig | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();



  // 获取验证配置数据
  const fetchConfigs = async (configMode: string, params?: any) => {
    // 如果已经加载过该标签页的数据，则不再重复加载
    if (loadedTabs.has(configMode) && !params) {
      return;
    }
    
    setLoading(true);
    try {
      const requestParams = {
        config_mode: configMode,
        ...params,
      };
      
      const response = await VerificationService.getConfigs(requestParams);
      
      if (response.code === SUCCESS) {
        const configs = response.data || [];
        
        // 处理枚举值显示名称
        const processedConfigs = configs.map((config: VerificationConfig) => ({
          ...config,
          purpose_name: PURPOSE_OPTIONS.find(p => p.value === config.purpose)?.label || '',
          target_type_name: TARGET_TYPE_OPTIONS.find(t => t.value === config.target_type)?.label || '',
          token_type_name: TOKEN_TYPE_OPTIONS.find(t => t.value === config.token_type)?.label || '',
        }));
        
        if (configMode === 'static') {
          setStaticData(processedConfigs);
        } else {
          setDynamicData(processedConfigs);
        }
        
        // 标记该标签页已加载
        setLoadedTabs(prev => new Set(prev).add(configMode));
      } else {
        showAPIError(response);
      }
    } catch (error) {
      showAPIError(error);
      
      // 如果API调用失败，回退到模拟数据以便开发测试
      const mockData: VerificationConfig[] = configMode === 'static' ? [
        {
          id: 1,
          tenant_id: 1,
          config_mode: 'static' as const,
          purpose: 1,
          purpose_name: '注册激活',
          target_type: 1,
          target_type_name: '邮箱',
          token_type: 1,
          token_type_name: '链接',
          token_length: 32,
          expire_minutes: 30,
          max_attempts: 3,
          rate_limit_per_minute: 1,
          rate_limit_per_hour: 5,
          rate_limit_per_day: 20,
          template_code: 'email_register_link',
          priority: 10,
          description: '邮箱注册激活配置',
          is_active: true,
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        }
      ] : [
        {
          id: 2,
          tenant_id: 1,
          config_mode: 'dynamic' as const,
          business_scene: 'high_risk_login',
          judgment_dimension: 'user_risk_score',
          condition_expr: 'user.risk_score > 80',
          verification_level: 3,
          require_verification: true,
          target_type: 1,
          target_type_name: '邮箱',
          token_type: 2,
          token_type_name: '验证码',
          token_length: 6,
          expire_minutes: 5,
          max_attempts: 3,
          rate_limit_per_minute: 1,
          rate_limit_per_hour: 5,
          rate_limit_per_day: 20,
          template_code: 'email_high_risk_verify',
          priority: 20,
          description: '高风险登录动态验证配置',
          is_active: true,
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        }
      ];
      
      if (configMode === 'static') {
        setStaticData(mockData);
      } else {
        setDynamicData(mockData);
      }
      
      // 标记该标签页已加载（即使在错误情况下也标记，避免重复请求）
      setLoadedTabs(prev => new Set(prev).add(configMode));
    } finally {
      setLoading(false);
    }
  };

  // 创建配置
  const createConfig = async (data: any) => {
    try {
      let response;
      
      if (data.config_mode === 'static') {
        response = await VerificationService.createStaticConfig(data);
      } else {
        response = await VerificationService.createDynamicConfig(data);
      }
      
      if (response.code === SUCCESS) {
        const newConfig = response.data;
        
        // 处理枚举值显示名称
        const purposeOption = PURPOSE_OPTIONS.find(p => p.value === newConfig.purpose);
        const targetOption = TARGET_TYPE_OPTIONS.find(t => t.value === newConfig.target_type);
        const tokenOption = TOKEN_TYPE_OPTIONS.find(t => t.value === newConfig.token_type);
        
        newConfig.purpose_name = purposeOption?.label || '';
        newConfig.target_type_name = targetOption?.label || '';
        newConfig.token_type_name = tokenOption?.label || '';
        
        if (data.config_mode === 'static') {
          setStaticData(prev => [...prev, newConfig]);
        } else {
          setDynamicData(prev => [...prev, newConfig]);
        }
        
        message.success('创建成功');
        return response;
      } else {
        showAPIError(response);
        throw new Error(response.message || '创建失败');
      }
    } catch (error) {
      showAPIError(error);
      throw error;
    }
  };

  // 更新配置
  const updateConfig = async (id: number, data: any) => {
    try {
      let response;
      
      if (data.config_mode === 'static') {
        response = await VerificationService.updateStaticConfig(id, data);
      } else {
        response = await VerificationService.updateDynamicConfig(id, data);
      }
      
      if (response.code === SUCCESS) {
        const updateData = (items: VerificationConfig[]) => 
          items.map(item => {
            if (item.id === id) {
              const updated = { ...item, ...data, updated_at: new Date().toISOString() };
              
              if (data.purpose !== undefined) {
                const purposeOption = PURPOSE_OPTIONS.find(p => p.value === data.purpose);
                updated.purpose_name = purposeOption?.label || '';
              }
              
              if (data.target_type !== undefined) {
                const targetOption = TARGET_TYPE_OPTIONS.find(t => t.value === data.target_type);
                updated.target_type_name = targetOption?.label || '';
              }
              
              if (data.token_type !== undefined) {
                const tokenOption = TOKEN_TYPE_OPTIONS.find(t => t.value === data.token_type);
                updated.token_type_name = tokenOption?.label || '';
              }
              
              return updated;
            }
            return item;
          });
        
        setStaticData(updateData);
        setDynamicData(updateData);
        
        message.success('更新成功');
        return response;
      } else {
        showAPIError(response);
        throw new Error(response.message || '更新失败');
      }
    } catch (error) {
      showAPIError(error);
      throw error;
    }
  };

  // 删除配置
  const deleteConfig = async (id: number) => {
    try {
      const response = await VerificationService.deleteConfig(id);
      
      if (response.code === SUCCESS) {
        setStaticData(prev => prev.filter(item => item.id !== id));
        setDynamicData(prev => prev.filter(item => item.id !== id));
        
        message.success('删除成功');
        return response;
      } else {
        showAPIError(response);
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      showAPIError(error);
      throw error;
    }
  };

  // 初始化数据 - 只加载当前激活标签页的数据
  React.useEffect(() => {
    fetchConfigs(activeTab);
  }, [activeTab]);

  // 打开新增模态框
  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 打开编辑模态框
  const handleEdit = (record: VerificationConfig) => {
    setEditingRecord(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  // 删除配置
  const handleDelete = async (id: number) => {
    await deleteConfig(id);
  };

  // 处理查询
  const handleSearch = async (keyword?: string) => {
    try {
      const values = searchForm.getFieldsValue();
      const searchKeyword = keyword || values.keyword || '';
      
      const params = {
        keyword: searchKeyword,
        purpose: values.purpose,
        target_type: values.target_type,
        token_type: values.token_type,
        is_active: values.is_active,
        // 过滤掉空值
        ...Object.fromEntries(
          Object.entries({ keyword: searchKeyword, ...values }).filter(([_, value]) => 
            value !== undefined && value !== null && value !== ''
          )
        )
      };
      
      setSearchParams(params);
      setLoadedTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete(activeTab);
        return newSet;
      });
      fetchConfigs(activeTab, params);
    } catch (error) {
      console.error('查询失败:', error);
    }
  };

  // 重置查询
  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({
      purpose: undefined,
      target_type: undefined,
      token_type: undefined,
      is_active: undefined,
      keyword: '',
    });
    setLoadedTabs(prev => {
      const newSet = new Set(prev);
      newSet.delete(activeTab);
      return newSet;
    });
    fetchConfigs(activeTab);
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const configData = {
        ...values,
        config_mode: activeTab,
        is_active: values.is_active ?? true,
      };

      if (editingRecord) {
        await updateConfig(editingRecord.id as number, configData);
      } else {
        await createConfig(configData);
      }

      setIsModalVisible(false);
      form.resetFields();
      
      // 刷新当前标签页数据
      setLoadedTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete(activeTab);
        return newSet;
      });
      fetchConfigs(activeTab, searchParams);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 静态配置表格列定义 - 紧凑布局
  const staticColumns = [
    {
      title: '主要信息',
      key: 'main_info',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.purpose_name}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            ID: {record.id}
          </div>
        </div>
      ),
    },
    {
      title: '配置详情',
      key: 'config_details',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="blue">{record.target_type_name}</Tag>
            <Tag color="orange" style={{ marginLeft: 4 }}>{record.token_type_name}</Tag>
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.token_length}位 · {record.expire_minutes}分钟 · {record.max_attempts}次
          </div>
        </div>
      ),
    },
    {
      title: '模板与优先级',
      key: 'template_priority',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.template_code}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            优先级: {record.priority}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: VerificationConfig) => (
        <Tag color={record.is_active ? 'green' : 'red'}>
          {record.is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: VerificationConfig) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ color: isDarkMode ? '#40a9ff' : '#1890ff', padding: 0 }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description={`确定要删除配置 "${record.purpose_name} - ${record.target_type_name}" 吗？`}
            onConfirm={() => handleDelete(record.id as number)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 动态配置表格列定义 - 紧凑布局
  const dynamicColumns = [
    {
      title: '主要信息',
      key: 'main_info',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.business_scene}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            ID: {record.id}
          </div>
        </div>
      ),
    },
    {
      title: '动态规则',
      key: 'dynamic_rules',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="purple">{record.judgment_dimension}</Tag>
            <Tag 
              color={(record.verification_level || 1) <= 2 ? 'green' : (record.verification_level || 1) <= 3 ? 'orange' : 'red'}
              style={{ marginLeft: 4 }}
            >
              级别{record.verification_level || 1}
            </Tag>
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            fontFamily: 'monospace',
            background: isDarkMode ? '#2a2a2a' : '#f5f5f5',
            padding: '2px 6px',
            borderRadius: 4,
          }}>
            {record.condition_expr}
          </div>
        </div>
      ),
    },
    {
      title: '配置详情',
      key: 'config_details',
      render: (record: VerificationConfig) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="blue">{record.target_type_name}</Tag>
            <Tag color="orange" style={{ marginLeft: 4 }}>{record.token_type_name}</Tag>
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.token_length}位 · {record.expire_minutes}分钟 · 优先级{record.priority}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: VerificationConfig) => (
        <Tag color={record.is_active ? 'green' : 'red'}>
          {record.is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: VerificationConfig) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ color: isDarkMode ? '#40a9ff' : '#1890ff', padding: 0 }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description={`确定要删除配置 "${record.business_scene}" 吗？`}
            onConfirm={() => handleDelete(record.id as number)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="verification-config-management">


      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索配置名称、模板代码等"
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择验证用途"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => {
                searchForm.setFieldsValue({ purpose: value });
              }}
            >
              {PURPOSE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择目标类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => {
                searchForm.setFieldsValue({ target_type: value });
              }}
            >
              {TARGET_TYPE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" onClick={() => handleSearch()} loading={loading}>
                查询
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增{activeTab === 'static' ? '静态' : '动态'}配置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 配置列表 */}
      <div style={{ marginBottom: 24 }}>
        <Card
          style={{
            borderRadius: 12,
            borderStyle: 'none',
            background: isDarkMode ? '#1f1f1f' : '#ffffff',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          }}
          styles={{ body: { padding: 0 } }}
        >
          <Tabs 
            activeKey={activeTab} 
            onChange={(key) => {
              setActiveTab(key);
              // 切换标签页时，如果有查询参数，则使用查询参数加载数据
              if (Object.keys(searchParams).some(k => searchParams[k as keyof typeof searchParams] !== undefined && searchParams[k as keyof typeof searchParams] !== '')) {
                fetchConfigs(key, searchParams);
              }
            }}
            style={{
              padding: '0 24px',
            }}
            tabBarStyle={{
              margin: 0,
              padding: '16px 0 0 0',
              borderBottom: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
            tabBarGutter={32}
            items={[
              {
                key: 'static',
                label: (
                  <span style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 8,
                    fontSize: 14,
                    fontWeight: 500,
                    padding: '8px 0',
                  }}>
                    <SettingOutlined style={{ fontSize: 16 }} />
                    静态配置
                  </span>
                ),
                children: (
                  <div style={{ padding: '16px 0' }}>
                    <Table
                      columns={staticColumns}
                      dataSource={staticData}
                      loading={loading}
                      rowKey="id"
                      pagination={{ 
                        pageSize: 10, 
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                      }}
                      style={{ background: 'transparent' }}
                    />
                  </div>
                )
              },
              {
                key: 'dynamic',
                label: (
                  <span style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 8,
                    fontSize: 14,
                    fontWeight: 500,
                    padding: '8px 0',
                  }}>
                    <SettingOutlined style={{ fontSize: 16 }} />
                    动态配置
                  </span>
                ),
                children: (
                  <div style={{ padding: '16px 0' }}>
                    <Table
                      columns={dynamicColumns}
                      dataSource={dynamicData}
                      loading={loading}
                      rowKey="id"
                      pagination={{ 
                        pageSize: 10, 
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                      }}
                      style={{ background: 'transparent' }}
                    />
                  </div>
                )
              }
            ]}
          />
        </Card>
      </div>

      {/* 新增/编辑模态框 */}
      <Modal
        title={`${editingRecord ? '编辑' : '新增'}${activeTab === 'static' ? '静态' : '动态'}配置`}
        open={isModalVisible}
        onOk={handleSave}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            token_length: 6,
            expire_minutes: 30,
            max_attempts: 3,
            rate_limit_per_minute: 1,
            rate_limit_per_hour: 10,
            rate_limit_per_day: 50,
            priority: 10,
            is_active: true,
            verification_level: 1,
            require_verification: true,
          }}
        >
          {activeTab === 'static' ? (
            // 静态配置表单
            <>
              <Form.Item
                name="purpose"
                label="验证用途"
                rules={[{ required: true, message: '请选择验证用途' }]}
              >
                <Select placeholder="请选择验证用途">
                  {PURPOSE_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          ) : (
            // 动态配置表单
            <>
              <Form.Item
                name="business_scene"
                label="业务场景"
                rules={[{ required: true, message: '请输入业务场景标识' }]}
              >
                <Input placeholder="请输入业务场景标识" />
              </Form.Item>
              
              <Form.Item
                name="judgment_dimension"
                label="判定维度"
                rules={[{ required: true, message: '请输入判定维度' }]}
              >
                <Input placeholder="请输入判定维度" />
              </Form.Item>
              
              <Form.Item
                name="condition_expr"
                label="条件表达式"
                rules={[{ required: true, message: '请输入条件表达式' }]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="请输入条件表达式，如：user.risk_score > 80" 
                />
              </Form.Item>
              
              <Form.Item
                name="verification_level"
                label="验证级别"
                rules={[{ required: true, message: '请选择验证级别' }]}
              >
                <Select placeholder="请选择验证级别">
                  {[1, 2, 3, 4, 5].map(level => (
                    <Option key={level} value={level}>
                      级别{level} - {level <= 2 ? '低' : level <= 3 ? '中' : '高'}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                name="require_verification"
                label="是否需要验证"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="需要" 
                  unCheckedChildren="不需要" 
                />
              </Form.Item>
            </>
          )}

          {/* 通用配置字段 */}
          <Divider>通用配置</Divider>
          
          <Form.Item
            name="target_type"
            label="目标类型"
            rules={[{ required: true, message: '请选择目标类型' }]}
          >
            <Select placeholder="请选择目标类型">
              {TARGET_TYPE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="token_type"
            label="令牌类型"
            rules={[{ required: true, message: '请选择令牌类型' }]}
          >
            <Select placeholder="请选择令牌类型">
              {TOKEN_TYPE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="token_length"
            label="令牌长度"
            rules={[
              { required: true, message: '请输入令牌长度' },
              { type: 'number', min: 4, max: 32, message: '令牌长度必须在4-32之间' }
            ]}
          >
            <InputNumber 
              min={4} 
              max={32} 
              placeholder="请输入令牌长度(4-32)" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="expire_minutes"
            label="过期时间(分钟)"
            rules={[
              { required: true, message: '请输入过期时间' },
              { type: 'number', min: 1, max: 1440, message: '过期时间必须在1-1440分钟之间' }
            ]}
          >
            <InputNumber 
              min={1} 
              max={1440} 
              placeholder="请输入过期时间(1-1440分钟)" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="max_attempts"
            label="最大尝试次数"
            rules={[
              { required: true, message: '请输入最大尝试次数' },
              { type: 'number', min: 1, max: 10, message: '最大尝试次数必须在1-10之间' }
            ]}
          >
            <InputNumber 
              min={1} 
              max={10} 
              placeholder="请输入最大尝试次数(1-10)" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="rate_limit_per_minute"
            label="每分钟限制"
            rules={[
              { required: true, message: '请输入每分钟发送限制' },
              { type: 'number', min: 0, message: '每分钟限制不能为负数' }
            ]}
          >
            <InputNumber 
              min={0} 
              placeholder="请输入每分钟发送限制" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="rate_limit_per_hour"
            label="每小时限制"
            rules={[
              { required: true, message: '请输入每小时发送限制' },
              { type: 'number', min: 0, message: '每小时限制不能为负数' }
            ]}
          >
            <InputNumber 
              min={0} 
              placeholder="请输入每小时发送限制" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="rate_limit_per_day"
            label="每日限制"
            rules={[
              { required: true, message: '请输入每日发送限制' },
              { type: 'number', min: 0, message: '每日限制不能为负数' }
            ]}
          >
            <InputNumber 
              min={0} 
              placeholder="请输入每日发送限制" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="template_code"
            label="模板代码"
            rules={[{ required: true, message: '请选择模板代码' }]}
          >
            <TemplateCodeSelector 
              tenantId={editingRecord?.tenant_id || 1}
              placeholder="请选择模板代码"
            />
          </Form.Item>
          
          <Form.Item
            name="priority"
            label="优先级"
          >
            <InputNumber 
              placeholder="请输入优先级，数字越大优先级越高" 
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入配置描述" 
            />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用" 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VerificationConfigManagement;