.verification-config-management {
  .ant-tabs-content-holder {
    padding: 0;
  }
  
  .ant-tabs-tabpane {
    padding: 0;
  }
  
  .config-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .form-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #1890ff;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 8px;
      }
    }
  }
  
  .config-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    
    .stat-card {
      flex: 1;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 16px;
      text-align: center;
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .config-mode-badge {
    &.static {
      background: #e6f7ff;
      color: #1890ff;
      border: 1px solid #91d5ff;
    }
    
    &.dynamic {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }
  }
  
  .priority-badge {
    &.high {
      background: #fff2e8;
      color: #fa8c16;
      border: 1px solid #ffb575;
    }
    
    &.medium {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }
    
    &.low {
      background: #f0f0f0;
      color: #666;
      border: 1px solid #d9d9d9;
    }
  }
  
  .condition-expr {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}