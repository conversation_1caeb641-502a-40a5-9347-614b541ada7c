import React, { useState } from 'react';
import { Select, Tag } from 'antd';

const { Option } = Select;

interface FileTypeSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  disabled?: boolean;
}

// 常用文件类型分组
const commonFileTypes = {
  图片: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  文档: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'text/plain'],
  视频: ['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime'],
  音频: ['audio/mpeg', 'audio/ogg', 'audio/wav', 'audio/webm'],
  压缩包: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/gzip'],
};

// 文件类型友好名称映射
const mimeTypeNames: Record<string, string> = {
  'image/jpeg': 'JPEG 图片',
  'image/png': 'PNG 图片',
  'image/gif': 'GIF 图片',
  'image/webp': 'WebP 图片',
  'image/svg+xml': 'SVG 图片',
  'application/pdf': 'PDF 文档',
  'application/msword': 'Word 文档 (.doc)',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word 文档 (.docx)',
  'application/vnd.ms-excel': 'Excel 表格 (.xls)',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel 表格 (.xlsx)',
  'application/vnd.ms-powerpoint': 'PowerPoint 演示文稿 (.ppt)',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint 演示文稿 (.pptx)',
  'text/plain': '纯文本文件',
  'video/mp4': 'MP4 视频',
  'video/webm': 'WebM 视频',
  'video/ogg': 'Ogg 视频',
  'video/quicktime': 'QuickTime 视频',
  'audio/mpeg': 'MP3 音频',
  'audio/ogg': 'Ogg 音频',
  'audio/wav': 'WAV 音频',
  'audio/webm': 'WebM 音频',
  'application/zip': 'ZIP 压缩包',
  'application/x-rar-compressed': 'RAR 压缩包',
  'application/x-7z-compressed': '7Z 压缩包',
  'application/gzip': 'GZip 压缩包',
};

// 获取所有支持的文件类型
const getAllFileTypes = () => {
  const allTypes = new Set<string>();
  Object.values(commonFileTypes).forEach(types => {
    types.forEach(type => allTypes.add(type));
  });
  return Array.from(allTypes);
};

const FileTypeSelector: React.FC<FileTypeSelectorProps> = ({ value = [], onChange, disabled }) => {
  const [customType, setCustomType] = useState<string>('');

  const handleChange = (newValue: string[]) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  const handleInputChange = (inputValue: string) => {
    setCustomType(inputValue);
  };

  const handleCustomTypeAdd = () => {
    if (customType && !value.includes(customType)) {
      const newValue = [...value, customType];
      handleChange(newValue);
      setCustomType('');
    }
  };

  const tagRender = (props: any) => {
    const { label, value: tagValue, closable, onClose } = props;
    const displayName = mimeTypeNames[tagValue] || tagValue;
    return (
      <Tag 
        color="blue" 
        closable={closable} 
        onClose={onClose} 
        style={{ marginRight: 3 }}
      >
        {displayName}
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      style={{ width: '100%' }}
      placeholder="请选择允许的文件类型"
      value={value}
      onChange={handleChange}
      disabled={disabled}
      tagRender={tagRender}
      optionLabelProp="label"
      optionFilterProp="label"
      searchValue={customType}
      onSearch={handleInputChange}
      onInputKeyDown={(e) => {
        if (e.key === 'Enter' && customType) {
          e.preventDefault();
          handleCustomTypeAdd();
        }
      }}
      dropdownRender={(menu) => (
        <div>
          {menu}
        </div>
      )}
    >
      {Object.entries(commonFileTypes).map(([group, types]) => (
        <Select.OptGroup key={group} label={`${group}文件`}>
          {types.map(type => (
            <Option key={type} value={type} label={mimeTypeNames[type] || type}>
              {mimeTypeNames[type] || type}
            </Option>
          ))}
        </Select.OptGroup>
      ))}
      {customType && !getAllFileTypes().includes(customType) && (
        <Option key={customType} value={customType} label={customType}>
          添加: {customType}
        </Option>
      )}
    </Select>
  );
};

export default FileTypeSelector; 