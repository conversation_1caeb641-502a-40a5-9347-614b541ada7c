import React from 'react';
import { Upload } from 'antd';
import type { UploadProps } from 'antd';
import { 
  InboxOutlined
} from '@ant-design/icons';
import { showError, showSuccess } from '../../../utils/messageManager';
import { fileAPI } from '../../../services/file-system';

const { Dragger } = Upload;

interface FileUploadProps {
  scene_code: string;
  max_file_size?: number;
  allowed_types?: string[];
  onSuccess: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  scene_code,
  max_file_size,
  allowed_types = [],
  onSuccess,
}) => {
  const props: UploadProps = {
    name: 'file',
    multiple: false,
    showUploadList: true,
    customRequest: async (options) => {
      const { file, onProgress, onSuccess: onUploadSuccess, onError } = options;
      
      try {
        const result = await fileAPI.upload({
          scene_code,
          file: file as File,
          onProgress: (percent) => {
            onProgress?.({ percent });
          },
        });
        
        onUploadSuccess?.(result);
        onSuccess(); // 不传递参数，确保与props类型一致
        showSuccess('上传成功');
      } catch (error) {
        onError?.(error as Error);
        showError('上传失败', 'file-upload-error');
      }
    },
    beforeUpload: (file) => {
      // 检查文件大小
      if (max_file_size && file.size > max_file_size) {
        showError(`文件大小不能超过 ${max_file_size / 1024 / 1024} MB`, 'file-size-error');
        return false;
      }

      // 检查文件类型
      if (allowed_types.length > 0) {
        const isValidType = allowed_types.some(type => {
          if (type.endsWith('/*')) {
            return file.type.startsWith(type.replace('/*', '/'));
          }
          return type.split(',').includes(file.type);
        });

        if (!isValidType) {
          showError('不支持的文件类型', 'file-type-error');
          return false;
        }
      }

      return true;
    },
  };

  return (
    <Dragger {...props}>
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
      <p className="ant-upload-hint">
        {allowed_types.length > 0 && `支持的文件类型: ${allowed_types.join(', ')}`}
        {max_file_size && `，文件大小限制: ${max_file_size / 1024 / 1024} MB`}
      </p>
    </Dragger>
  );
};

export default FileUpload; 