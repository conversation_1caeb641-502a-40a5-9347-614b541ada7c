import React from 'react';
import { Tag } from 'antd';
import GlobalTooltip from '../../../components/GlobalTooltip';
import { FileStatus } from '../../../types/file-system';

interface FileStatusTagProps {
  status: string;
  showTooltip?: boolean;
}

const FileStatusTag: React.FC<FileStatusTagProps> = ({ status, showTooltip = true }) => {
  let color = '';
  let text = '';
  let tooltipText = '';

  switch (status) {
    case FileStatus.ACTIVE:
      color = 'green';
      text = '有效';
      tooltipText = '文件可以正常访问';
      break;
    case FileStatus.EXPIRED:
      color = 'orange';
      text = '已过期';
      tooltipText = '临时文件已过期，无法访问';
      break;
    case FileStatus.DELETED:
      color = 'red';
      text = '已删除';
      tooltipText = '文件已被删除，无法访问';
      break;
    default:
      color = 'default';
      text = status;
      tooltipText = '未知状态';
  }

  const tag = <Tag color={color}>{text}</Tag>;

  if (showTooltip) {
    return <GlobalTooltip title={tooltipText}>{tag}</GlobalTooltip>;
  }

  return tag;
};

export default FileStatusTag; 