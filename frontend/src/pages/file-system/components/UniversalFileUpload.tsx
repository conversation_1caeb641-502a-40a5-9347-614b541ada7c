import React, { useState, useEffect } from 'react';
import { Upload, Button, Progress, Modal, Form, Select, Typography, Space } from 'antd';
import { 
  DeleteOutlined,
  UploadOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { showError, showSuccess } from '../../../utils/messageManager';
import { showAPIError } from '../../../utils/errorHandler';
import { fileAPI, sceneAPI, fileSystemUtils } from '../../../services/file-system';
import { SceneConfig, UploadFileParams } from '../../../types/file-system';
import { SUCCESS } from '../../../constants/errorCodes';
import './UniversalFileUpload.css';

const { Option } = Select;
const { Dragger } = Upload;
const { Text } = Typography;

interface UniversalFileUploadProps {
  // 基础配置
  scene_code?: string; // 默认场景编码
  max_size?: number; // 最大文件大小（字节）
  allowed_types?: string[]; // 允许的文件类型
  multiple?: boolean; // 是否支持多文件上传
  
  // 回调函数
  onSuccess?: (fileIds: number[]) => void; // 上传成功回调
  onError?: (error: any) => void; // 上传失败回调
  onProgress?: (percent: number) => void; // 上传进度回调
  
  // UI配置
  show_scene_selector?: boolean; // 是否显示场景选择器
  show_progress?: boolean; // 是否显示上传进度
  button_text?: string; // 按钮文字
  modal_title?: string; // 模态框标题
  
  // 样式配置
  style?: React.CSSProperties;
  className?: string;
}

const UniversalFileUpload: React.FC<UniversalFileUploadProps> = ({
  scene_code,
  max_size,
  allowed_types = [],
  multiple = false,
  onSuccess,
  onError,
  onProgress,
  show_scene_selector = true,
  show_progress = true,
  button_text = '上传文件',
  modal_title = '文件上传',
  style,
  className,
}) => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [fileList, setFileList] = useState<any[]>([]);
  const [availableScenes, setAvailableScenes] = useState<SceneConfig[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedScene, setSelectedScene] = useState<SceneConfig | null>(null);

  // 获取可用场景
  useEffect(() => {
    if (show_scene_selector) {
      fetchScenes();
    }
  }, [show_scene_selector]);

  // 设置默认场景
  useEffect(() => {
    if (scene_code && availableScenes.length > 0) {
      const scene = availableScenes.find(s => s.scene_code === scene_code);
      if (scene) {
        setSelectedScene(scene);
        form.setFieldsValue({ scene_code });
      }
    }
  }, [scene_code, availableScenes, form]);

  const fetchScenes = async () => {
    try {
      const response = await sceneAPI.list({ page: 1, pageSize: 100, status: 'active' });
      if (response.code === SUCCESS) {
        // Add null checks for response data
        if (response && typeof response === 'object' && Array.isArray(response.data)) {
          setAvailableScenes(response.data);
        } else {
          // Handle case where data is undefined or items is not an array
          console.warn('Scene API response data is undefined or invalid:', response.data);
          setAvailableScenes([]);
        }
      } else {
        showAPIError(response);
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleUpload = async () => {
    try {
      const values = await form.validateFields();
      
      if (fileList.length === 0) {
        showError('请选择要上传的文件');
        return;
      }

      setUploading(true);
      setProgress(0);

      const uploadPromises = fileList.map(async (fileItem) => {
        const file = fileItem.originFileObj;
        const params: UploadFileParams = {
          scene_code: values.scene_code,
          file,
          onProgress: (percent) => {
            setProgress(percent);
            onProgress?.(percent);
          }
        };

        const response = await fileAPI.upload(params);
        if (response.data.code === SUCCESS) {
          // Add null checks for fileId
          const responseData = response.data.data;
          if (responseData && typeof responseData === 'object' && responseData.fileId) {
            return responseData.fileId;
          } else {
            console.warn('Upload response data is undefined or missing fileId:', response.data);
            throw new Error('上传响应数据无效');
          }
        } else {
          throw new Error(response.data.message || '上传失败');
        }
      });

      const fileIds = await Promise.all(uploadPromises);
      
      showSuccess(`成功上传 ${fileIds.length} 个文件`);
      setFileList([]);
      setModalVisible(false);
      
      if (onSuccess) {
        onSuccess(fileIds);
      }
    } catch (error) {
      showAPIError(error);
      if (onError) {
        onError(error);
      }
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const handleSceneChange = (scene_code: string) => {
    const scene = availableScenes.find(s => s.scene_code === scene_code);
    setSelectedScene(scene || null);
  };

  const uploadProps = {
    onRemove: (file: any) => {
      const newFileList = fileList.filter(item => item.uid !== file.uid);
      setFileList(newFileList);
    },
    beforeUpload: (file: any) => {
      // 检查文件大小
      if (max_size && file.size > max_size) {
        showError(`文件大小不能超过 ${fileSystemUtils.formatFileSize(max_size)}`);
        return false;
      }

      // 检查文件类型
      if (allowed_types.length > 0 && !fileSystemUtils.isFileTypeAllowed(file.type, allowed_types)) {
        showError('不支持的文件类型');
        return false;
      }

      // 检查场景配置
      if (selectedScene) {
        if (!fileSystemUtils.validateFileSize(file.size, selectedScene.max_file_size)) {
          showError(`文件大小不能超过 ${fileSystemUtils.formatFileSize(selectedScene.max_file_size)}`);
          return false;
        }

        if (!fileSystemUtils.isFileTypeAllowed(file.type, selectedScene.allowed_types)) {
          showError('不支持的文件类型');
          return false;
        }
      }

      setFileList(prev => [...prev, file]);
      return false; // 阻止自动上传
    },
    fileList,
    multiple,
  };

  const renderUploadArea = () => (
    <div>
      {show_scene_selector && (
        <Form.Item
          name="scene_code"
          label="上传场景"
          rules={[{ required: true, message: '请选择上传场景' }]}
        >
          <Select 
            placeholder="请选择文件上传场景" 
            onChange={handleSceneChange}
            style={{ marginBottom: 16 }}
          >
            {availableScenes.map(scene => (
              <Option key={scene.scene_code} value={scene.scene_code}>
                <Space>
                  <FileTextOutlined />
                  {scene.scene_name}
                  <Text type="secondary">({scene.scene_code})</Text>
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>
      )}
      
      <Form.Item label="选择文件">
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            {selectedScene && (
              <>
                支持的文件类型: {selectedScene.allowed_types.join(', ')}
                <br />
                文件大小限制: {fileSystemUtils.formatFileSize(selectedScene.max_file_size)}
              </>
            )}
            {!selectedScene && allowed_types.length > 0 && (
              <>
                支持的文件类型: {allowed_types.join(', ')}
                <br />
                {max_size && `文件大小限制: ${fileSystemUtils.formatFileSize(max_size)}`}
              </>
            )}
          </p>
        </Dragger>
      </Form.Item>
      
      {show_progress && uploading && (
        <Form.Item>
          <Progress percent={progress} status="active" />
        </Form.Item>
      )}
      
      <Form.Item>
        <Space>
          <Button
            type="primary"
            onClick={handleUpload}
            disabled={fileList.length === 0 || uploading}
            loading={uploading}
            icon={<UploadOutlined />}
          >
            {uploading ? `上传中 ${progress}%` : '开始上传'}
          </Button>
          <Button
            onClick={() => {
              setFileList([]);
              setProgress(0);
            }}
            disabled={fileList.length === 0 || uploading}
            icon={<DeleteOutlined />}
          >
            清空列表
          </Button>
        </Space>
      </Form.Item>
    </div>
  );

  // 如果不需要场景选择器，直接显示上传按钮
  if (!show_scene_selector && scene_code) {
    return (
      <Upload {...uploadProps} style={style} className={className}>
        <Button icon={<UploadOutlined />} disabled={uploading}>
          {button_text}
        </Button>
      </Upload>
    );
  }

  // 模态框模式
  return (
    <>
      <Button 
        icon={<UploadOutlined />} 
        onClick={() => setModalVisible(true)}
        style={style}
        className={className}
      >
        {button_text}
      </Button>
      
      <Modal
        title={modal_title}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        destroyOnHidden
        width={600}
      >
        <Form form={form} layout="vertical">
          {renderUploadArea()}
        </Form>
      </Modal>
    </>
  );
};

export default UniversalFileUpload; 