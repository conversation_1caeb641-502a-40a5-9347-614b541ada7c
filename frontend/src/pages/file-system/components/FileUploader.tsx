import React, { useState } from 'react';
import { Upload, Button, message, Select, Form, Modal } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { showError } from '../../../utils/messageManager';
import { showAPIError } from '../../../utils/errorHandler';
import { fileAPI, sceneAPI } from '../../../services/file-system';
import { SceneConfig, UploadFileParams } from '../../../types/file-system';
import { SUCCESS } from '../../../constants/errorCodes';


const { Option } = Select;
const { Dragger } = Upload;

interface FileUploaderProps {
  onSuccess?: (fileId: number) => void;
  scenes?: SceneConfig[];
  defaultSceneCode?: string;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onSuccess, scenes = [], defaultSceneCode }) => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [fileList, setFileList] = useState<any[]>([]);
  const [availableScenes, setAvailableScenes] = useState<SceneConfig[]>(scenes);

  // 如果没有传入场景列表，则从API获取
  React.useEffect(() => {
    if (scenes.length === 0) {
      fetchScenes();
    }
  }, [scenes]);

  const fetchScenes = async () => {
    try {
      const response = await sceneAPI.list({ page: 1, pageSize: 100 });
      if (response.code === SUCCESS) {
        // Add null checks for response data
        if (response && typeof response === 'object' && Array.isArray(response.data)) {
          setAvailableScenes(response.data);
          
          // 如果有默认场景且只有一个场景，自动选中
          if (response.data?.length === 1) {
            form.setFieldsValue({ scene_code: response.data[0].scene_code });
          } else if (defaultSceneCode) {
            form.setFieldsValue({ scene_code: defaultSceneCode });
          }
        } else {
          // Handle case where data is undefined or items is not an array
          console.warn('Scene API response data is undefined or invalid:', response.data);
          setAvailableScenes([]);
        }
      } else {
        showAPIError(response.data);
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleUpload = async () => {
    try {
      const values = await form.validateFields();
      
      if (fileList.length === 0) {
        showError('请选择要上传的文件', 'file-upload-error');
        return;
      }

      const file = fileList[0].originFileObj;
      setUploading(true);
      setProgress(0);

      const params: UploadFileParams = {
        scene_code: values.scene_code,
        file,
        onProgress: (percent) => {
          setProgress(percent);
        }
      };

      const response = await fileAPI.upload(params);
      if (response.data.code === SUCCESS) {
        message.success('文件上传成功');
        setFileList([]);
        if (onSuccess) {
          // Add null checks for fileId
          const responseData = response.data.data;
          if (responseData && typeof responseData === 'object' && responseData.fileId) {
            onSuccess(responseData.fileId);
          } else {
            console.warn('Upload response data is undefined or missing fileId:', response.data);
            onSuccess(0); // Provide fallback value
          }
        }
      } else {
        showAPIError(response.data);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const uploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file: any) => {
      setFileList([file]);
      return false;
    },
    fileList,
    multiple: false,
  };

  return (
    <Form form={form} layout="vertical">
      <Form.Item
        name="scene_code"
        label="上传场景"
        rules={[{ required: true, message: '请选择上传场景' }]}
      >
        <Select placeholder="请选择文件上传场景">
          {availableScenes.map(scene => (
            <Option key={scene.scene_code} value={scene.scene_code}>
              {scene.scene_name}
            </Option>
          ))}
        </Select>
      </Form.Item>
      
      <Form.Item label="选择文件">
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个文件上传，请选择符合场景要求的文件类型和大小
          </p>
        </Dragger>
      </Form.Item>
      
      <Form.Item>
        <Button
          type="primary"
          onClick={handleUpload}
          disabled={fileList.length === 0}
          loading={uploading}
          style={{ marginTop: 16 }}
        >
          {uploading ? `上传中 ${progress}%` : '开始上传'}
        </Button>
      </Form.Item>
    </Form>
  );
};

// 导出模态框包装组件，方便使用
export const FileUploaderModal: React.FC<{
  open: boolean;
  onCancel: () => void;
  onSuccess?: (fileId: number) => void;
  scenes?: SceneConfig[];
  defaultSceneCode?: string;
}> = ({ open, onCancel, onSuccess, scenes, defaultSceneCode }) => {
  return (
    <Modal
      title="上传文件"
      open={open}
      onCancel={onCancel}
      footer={null}
      destroyOnHidden
    >
      <FileUploader 
        onSuccess={(fileId) => {
          onSuccess?.(fileId);
          onCancel();
        }}
        scenes={scenes}
        defaultSceneCode={defaultSceneCode}
      />
    </Modal>
  );
};

export default FileUploader; 