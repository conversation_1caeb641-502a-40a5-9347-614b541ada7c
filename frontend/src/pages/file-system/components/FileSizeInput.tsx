import React, { useState, useEffect } from 'react';
import { Input, Select, Space } from 'antd';

const { Option } = Select;

interface FileSizeInputProps {
  value?: number;
  onChange?: (value: number) => void;
}

// 文件大小单位
enum FileSizeUnit {
  B = 'B',
  KB = 'KB',
  MB = 'MB',
  GB = 'GB'
}

// 单位转换系数
const unitFactors = {
  [FileSizeUnit.B]: 1,
  [FileSizeUnit.KB]: 1024,
  [FileSizeUnit.MB]: 1024 * 1024,
  [FileSizeUnit.GB]: 1024 * 1024 * 1024
};

const FileSizeInput: React.FC<FileSizeInputProps> = ({ value, onChange }) => {
  // 根据传入的字节数确定合适的单位和显示值
  const getInitialState = () => {
    if (!value) {
      return { displayValue: '', unit: FileSizeUnit.MB };
    }
    
    if (value < unitFactors[FileSizeUnit.KB]) {
      return { displayValue: value.toString(), unit: FileSizeUnit.B };
    } else if (value < unitFactors[FileSizeUnit.MB]) {
      return { 
        displayValue: (value / unitFactors[FileSizeUnit.KB]).toString(), 
        unit: FileSizeUnit.KB 
      };
    } else if (value < unitFactors[FileSizeUnit.GB]) {
      return { 
        displayValue: (value / unitFactors[FileSizeUnit.MB]).toString(), 
        unit: FileSizeUnit.MB 
      };
    } else {
      return { 
        displayValue: (value / unitFactors[FileSizeUnit.GB]).toString(), 
        unit: FileSizeUnit.GB 
      };
    }
  };

  const [displayValue, setDisplayValue] = useState<string>('');
  const [unit, setUnit] = useState<FileSizeUnit>(FileSizeUnit.MB);

  useEffect(() => {
    const { displayValue: initialDisplayValue, unit: initialUnit } = getInitialState();
    setDisplayValue(initialDisplayValue);
    setUnit(initialUnit);
  }, [value]);

  // 当输入值或单位变化时，计算字节数并触发onChange
  const handleChange = (newDisplayValue: string, newUnit: FileSizeUnit) => {
    setDisplayValue(newDisplayValue);
    setUnit(newUnit);
    
    const numValue = parseFloat(newDisplayValue);
    if (!isNaN(numValue) && onChange) {
      const byteValue = Math.floor(numValue * unitFactors[newUnit]);
      onChange(byteValue);
    }
  };

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Input
        type="number"
        min={0}
        step={0.01}
        value={displayValue}
        onChange={(e) => handleChange(e.target.value, unit)}
        placeholder="请输入文件大小"
        style={{ width: 'calc(100% - 80px)' }}
      />
      <Select
        value={unit}
        onChange={(newUnit) => handleChange(displayValue, newUnit)}
        style={{ width: 80 }}
      >
        <Option value={FileSizeUnit.B}>B</Option>
        <Option value={FileSizeUnit.KB}>KB</Option>
        <Option value={FileSizeUnit.MB}>MB</Option>
        <Option value={FileSizeUnit.GB}>GB</Option>
      </Select>
    </Space.Compact>
  );
};

export default FileSizeInput; 