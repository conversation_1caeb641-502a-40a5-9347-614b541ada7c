import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Form,
  Input, 
  Select, 
  Button, 
  Space, 
  Row,
  Col,
  Spin,
  Alert,
  Typography,
  Tabs,
  Modal
} from 'antd';
import { 
  SaveOutlined, 
  ArrowLeftOutlined, 
  EditOutlined, 
  FileTextOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import CKEditorEmailTemplate from './CKEditorEmailTemplate';
import TemplateVariablesManager from './TemplateVariablesManager';
import { emailTemplateService, emailAccountService } from '../../../services/email';
import { 
  EmailTemplate, 
  EmailAccount,
  SaveOrUpdateTemplateRequest
} from '../../../types/email';
import { showAPIError, applyFieldErrorsToForm } from '../../../utils/errorHandler';
import { showError, showSuccess } from '../../../utils/messageManager';
import { SUCCESS } from '../../../constants/errorCodes';

const { Title, Text } = Typography;

// 表单字段类型
interface TemplateFormData {
  template_name: string;
  scenario_code: string;
  description?: string;
  template_type: number; // 改为数字类型，与服务端保持一致
  status: number;
  account_id: number;
}

// 组件属性
interface TemplateFormProps {
  mode: 'create' | 'edit';
  onSuccess?: () => void;
}

const TemplateForm: React.FC<TemplateFormProps> = ({ mode, onSuccess }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState<EmailTemplate | null>(null);
  const [htmlContent, setHtmlContent] = useState('');
  const [subject, setSubject] = useState(''); // 新增：主题状态
  const [activeTab, setActiveTab] = useState('basic');
  
  // 邮件账户相关状态
  const [emailAccounts, setEmailAccounts] = useState<EmailAccount[]>([]);
  const [accountsLoading, setAccountsLoading] = useState(false);
  const [accountSearchText, setAccountSearchText] = useState('');
  
  // 变量相关状态
  const [localVariables, setLocalVariables] = useState<Record<string, any>>({});
  const [serverVariables, setServerVariables] = useState<Record<string, any>>({});
  
  // 预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewVariables, setPreviewVariables] = useState<Record<string, any>>({});
  
  // 唯一性校验状态
  const [codeValidating, setCodeValidating] = useState(false);
  const [codeValid, setCodeValid] = useState<boolean | null>(null);
  
  // 新增：模板创建状态
  const [templateCreated, setTemplateCreated] = useState(false);
  
  // 编辑器引用
  const contentEditorRef = useRef<any>(null);

  // 获取模板详情（编辑模式）
  const fetchTemplate = useCallback(async () => {
    if (mode !== 'edit' || !id) return;

    setLoading(true);
    try {
      const response = await emailTemplateService.getTemplate(parseInt(id!));
      const templateData = response.data;
      
      setTemplate(templateData);
      setHtmlContent(templateData.html_content || '');
      setSubject(templateData.subject || ''); // 设置主题
      
      // 设置表单初始值
      const formData: Partial<TemplateFormData> = {
        template_name: templateData.name,
        scenario_code: templateData.template_code,
        description: templateData.description,
        template_type: typeof templateData.type === 'string' 
          ? (templateData.type === 'html' || templateData.type === 'HTML' ? 1 : 2)
          : templateData.type,
        status: templateData.status,
        account_id: templateData.account_id,
      };
      
      form.setFieldsValue(formData);
      
      // 初始化服务端变量
      if (templateData.variables) {
        setServerVariables(templateData.variables);
        
        // 初始化预览变量
        const previewVars: Record<string, string> = {};
        Object.keys(templateData.variables).forEach(key => {
          const varInfo = templateData.variables[key];
          if (varInfo.type === 'string') {
            previewVars[key] = `示例${varInfo.description || key}`;
          } else if (varInfo.type === 'number') {
            previewVars[key] = '123';
          } else {
            previewVars[key] = '示例值';
          }
        });
        setPreviewVariables(previewVars);
      }
      
    } catch (error) {
      showError('获取模板失败');
      navigate('/email/templates');
    } finally {
      setLoading(false);
    }
  }, [mode, id, form, navigate]);

  // 获取邮件账户列表
  const fetchEmailAccounts = useCallback(async () => {
    setAccountsLoading(true);
    try {
      const response = await emailAccountService.getEmailAccounts({
        page: 1,
        page_size: 100,
        keyword: accountSearchText
      });
      setEmailAccounts(response.data || []);
    } catch (error) {
      showError('获取邮件账户失败');
    } finally {
      setAccountsLoading(false);
    }
  }, [accountSearchText]);

  // 处理账户搜索
  const handleAccountSearch = useCallback((value: string) => {
    setAccountSearchText(value);
  }, []);

  // 表单字段变化处理
  const handleFormValuesChange = useCallback((changedValues: any, allValues: any) => {
    // 场景代码唯一性校验由服务端处理，这里只做基本的格式校验
    if (changedValues.scenario_code) {
      const code = changedValues.scenario_code.trim();
      if (code.length >= 2) {
        // 可以在这里添加基本的格式校验，但唯一性校验交给服务端
        setCodeValid(null); // 重置状态，让服务端处理
      } else {
        setCodeValid(null);
      }
    }
  }, []);

  // 保存基本信息
  const handleSaveBasic = useCallback(async (values: TemplateFormData) => {
    if (mode === 'edit' && !template) {
      showError('模板数据不存在');
      return;
    }

    setSaving(true);
    try {
      if (mode === 'create') {
        // 新增模式：构建创建请求（只包含基本信息）
        const createData: SaveOrUpdateTemplateRequest = {
          name: values.template_name,
          type: values.template_type, // 使用数字类型
          status: values.status,
          account_id: values.account_id,
          description: values.description,
        };

        const response = await emailTemplateService.saveOrUpdateTemplate(createData);
        
        if (response.code === SUCCESS) {
          showSuccess('模板创建成功');
          // 新增：设置模板创建状态，允许切换到其他tab
          setTemplateCreated(true);
          // 更新模板ID，以便后续操作
          if (response.data) {
            // 将SaveOrUpdateTemplateResponse转换为EmailTemplate格式
            const templateData: EmailTemplate = {
              id: response.data.id,
              template_code: response.data.template_code,
              account_id: response.data.account_id,
              name: response.data.name,
              type: response.data.type,
              subject: response.data.subject,
              description: response.data.description,
              html_content: '',
              plain_text_content: '',
              variables: {},
              rate_limit_per_minute: response.data.rate_limit_per_minute,
              rate_limit_per_hour: response.data.rate_limit_per_hour,
              rate_limit_per_day: response.data.rate_limit_per_day,
              is_responsive: response.data.is_responsive,
              status: response.data.status,
              created_at: response.data.created_at,
              updated_at: response.data.updated_at,
              created_by: 0,
              updated_by: 0,
              version: response.data.version,
              is_system: response.data.is_system,
            };
            setTemplate(templateData);
          }
          // 切换到模板内容tab
          setActiveTab('content');
          onSuccess?.();
        } else {
          // 处理服务端校验错误
          if (response.errors && response.errors.length > 0) {
            applyFieldErrorsToForm(response.errors, form);
            return;
          } else {
            showError(response.message || '创建失败');
          }
        }
      } else {
        // 编辑模式：构建更新请求
        const updateData: SaveOrUpdateTemplateRequest = {
          template_id: template!.id,
          name: values.template_name,
          type: values.template_type, // 使用数字类型
          status: values.status,
          account_id: values.account_id,
          description: values.description,
        };

        const response = await emailTemplateService.saveOrUpdateTemplate(updateData);
        
        if (response.code === SUCCESS) {
          showSuccess('基本信息保存成功');
          setTemplate(prev => prev ? { ...prev, ...updateData } : null);
        } else {
          // 处理服务端校验错误
          if (response.errors && response.errors.length > 0) {
            applyFieldErrorsToForm(response.errors, form);
            return;
          } else {
            showError(response.message || '保存失败');
          }
        }
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  }, [mode, template, htmlContent, localVariables, onSuccess, navigate]);

  // 保存模板内容
  const handleSaveContent = useCallback(async () => {
    if (mode === 'edit' && !id) return;

    setSaving(true);
    try {
      // 获取编辑器中的自定义变量
      let customVariables: Record<string, any> = {};
      if (contentEditorRef.current) {
        customVariables = contentEditorRef.current.getCustomVariables();
      }
      
      if (mode === 'create') {
        if (!templateCreated) {
          // 新增模式且模板未创建：合并本地变量和自定义变量
          const allVariables = { ...localVariables, ...customVariables };
          setLocalVariables(allVariables);
          showSuccess('模板内容已缓存，请保存基本信息以创建模板');
        } else {
          // 新增模式且模板已创建：直接保存到服务端
          const allVariables: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
          
          // 处理本地变量
          Object.entries(localVariables).forEach(([key, value]) => {
            allVariables[key] = {
              label: value.label || key,
              type: value.type || 'string',
              required: value.required || false,
              description: value.description || '',
            };
          });
          
          // 处理自定义变量
          Object.entries(customVariables).forEach(([key, value]) => {
            allVariables[key] = {
              label: value.label || key,
              type: value.type || 'string',
              required: value.required || false,
              description: value.description || '',
            };
          });

          const updateData = {
            template_id: template!.id,
            html_content: htmlContent,
            subject: subject, // 添加subject
            variables: allVariables,
          };

          const response = await emailTemplateService.updateTemplateContent(updateData);
          
          if (response.code !== SUCCESS) {
            showError(response.message || '模板内容保存失败');
            return;
          }

          showSuccess('模板内容保存成功！');
        }
      } else {
        // 编辑模式：直接提交给服务端
        const allVariables: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
        
        // 处理服务端变量
        if (serverVariables) {
          Object.entries(serverVariables).forEach(([key, value]) => {
            allVariables[key] = {
              label: key,
              type: value.type || 'string',
              required: value.required || false,
              description: value.description || '',
            };
          });
        }
        
        // 处理自定义变量
        Object.entries(customVariables).forEach(([key, value]) => {
          allVariables[key] = {
            label: value.label || key,
            type: value.type || 'string',
            required: value.required || false,
            description: value.description || '',
          };
        });

        const updateData = {
          template_id: parseInt(id!),
          html_content: htmlContent,
          subject: subject, // 添加subject
          variables: allVariables,
        };

        const response = await emailTemplateService.updateTemplateContent(updateData);
        
        if (response.code !== SUCCESS) {
          showError(response.message || '模板内容保存失败');
          return;
        }

        showSuccess('模板内容保存成功！');
      }
    } catch (error) {
      showError('模板内容保存失败');
    } finally {
      setSaving(false);
    }
  }, [mode, id, htmlContent, localVariables, serverVariables, subject, templateCreated]);

  // HTML内容变化
  const handleHtmlChange = useCallback((html: string) => {
    setHtmlContent(html);
  }, []);

  // 预览模板
  const handlePreview = useCallback(() => {
    setPreviewVisible(true);
  }, []);

  // 处理编辑器预览
  const handleEditorPreview = useCallback((html: string, variables: Record<string, any>) => {
    setHtmlContent(html);
    setPreviewVariables(variables);
    setPreviewVisible(true);
  }, []);

  // 返回列表页
  const handleBack = useCallback(() => {
    navigate('/email/templates');
  }, [navigate]);

  // 生成预览内容
  const generatePreviewContent = useCallback(() => {
    let previewContent = htmlContent;
    
    Object.entries(previewVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      previewContent = previewContent.replace(regex, value || `{{${key}}}`);
    });
    
    return previewContent;
  }, [htmlContent, previewVariables]);

  // 初始化数据
  useEffect(() => {
    if (mode === 'edit') {
      fetchTemplate();
    }
    fetchEmailAccounts();
  }, [mode, fetchTemplate, fetchEmailAccounts]);

  if (loading) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          {mode === 'edit' ? '加载模板数据中...' : '初始化中...'}
        </div>
      </div>
    );
  }

  if (mode === 'edit' && !template) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Alert
          message="模板不存在"
          description="请检查模板ID是否正确"
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Tabs
        activeKey={activeTab}
        onChange={(key) => {
          // 新增模式下，只有模板创建后才能切换到其他tab
          if (mode === 'create' && !templateCreated && key !== 'basic') {
            showError('请先保存基本信息创建模板');
            return;
          }
          setActiveTab(key);
        }}
        tabBarExtraContent={
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            >
              返回
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={() => {
                if (activeTab === 'basic') {
                  form.submit();
                } else {
                  handleSaveContent();
                }
              }}
            >
              {mode === 'create' 
                ? (templateCreated ? '保存模板' : '创建模板')
                : `保存${activeTab === 'basic' ? '基本信息' : '模板内容'}`
              }
            </Button>
          </Space>
        }
                items={[
          {
            key: 'basic',
            label: (
              <Space>
                <EditOutlined />
                基本信息
              </Space>
            ),
            disabled: false, // 基本信息tab始终可用
            children: (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSaveBasic}
                onValuesChange={handleFormValuesChange}
                preserve={false}
                initialValues={{
                  template_type: 1, // 新增模式下默认选择HTML模板
                  status: 1,
                }}
              >
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="模板名称"
                      name="template_name"
                      rules={[
                        { required: true, message: '请输入模板名称' },
                        { min: 2, max: 100, message: '模板名称长度应在2-100字符之间' }
                      ]}
                    >
                      <Input placeholder="请输入模板名称" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="场景代码"
                      name="scenario_code"
                      rules={[
                        { required: true, message: '请输入场景代码' },
                        { 
                          pattern: /^[a-z_][a-z0-9_]*$/, 
                          message: '场景代码只能包含小写字母、数字和下划线，且以字母或下划线开头' 
                        }
                      ]}
                    >
                      <Input 
                        placeholder="如：user_register, password_reset" 
                      />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="模板类型"
                      name="template_type"
                      rules={[
                        { required: true, message: '请选择模板类型' }
                      ]}
                    >
                      <Select placeholder="请选择模板类型">
                        <Select.Option value={1}>HTML模板</Select.Option>
                        <Select.Option value={2}>纯文本模板</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="模板状态"
                      name="status"
                      rules={[
                        { required: true, message: '请选择模板状态' }
                      ]}
                    >
                      <Select placeholder="请选择模板状态">
                        <Select.Option value={1}>草稿</Select.Option>
                        <Select.Option value={2}>已发布</Select.Option>
                        <Select.Option value={3}>已停用</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="发件账户"
                      name="account_id"
                      rules={[{ required: true, message: '请选择发件账户' }]}
                    >
                      <Select
                        placeholder="请选择发件账户"
                        loading={accountsLoading}
                        showSearch
                        filterOption={false}
                        onSearch={handleAccountSearch}
                        onFocus={() => {
                          if (emailAccounts.length === 0) {
                            fetchEmailAccounts();
                          }
                        }}
                        options={emailAccounts.map(account => ({
                          value: account.id,
                          label: `${account.name} (${account.from_address})`,
                        }))}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label="模板描述"
                      name="description"
                      rules={[
                        { max: 255, message: '模板描述长度不能超过255字符' }
                      ]}
                    >
                      <Input.TextArea 
                        placeholder="请输入模板描述，用于说明模板的用途和使用场景" 
                        rows={3}
                        showCount
                        maxLength={255}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            ),
          },
          {
            key: 'content',
            label: (
              <Space>
                <FileTextOutlined />
                模板内容
              </Space>
            ),
            disabled: mode === 'create' && !templateCreated, // 新增模式下需要先创建模板
            children: (
              <div style={{ marginBottom: 24 }}>
                {/* 主题编辑区域 */}
                <Form.Item
                  label="邮件主题"
                  rules={[
                    { required: true, message: '请输入邮件主题' },
                    { max: 200, message: '邮件主题长度不能超过200字符' }
                  ]}
                >
                  <Input.TextArea
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="请输入邮件主题，支持使用变量，如：{{user_name}}，欢迎使用我们的服务！"
                    rows={3}
                    showCount
                    maxLength={200}
                  />
                </Form.Item>
                
                <CKEditorEmailTemplate
                  value={htmlContent}
                  onChange={handleHtmlChange}
                  loading={loading}
                  showSaveButton={false}
                  showHelpCards={true}
                  templateId={template?.id}
                  onPreview={handlePreview}
                  ref={contentEditorRef}
                />
              </div>
            ),
          },
          {
            key: 'variables',
            label: (
              <Space>
                <FileTextOutlined />
                变量管理
              </Space>
            ),
            disabled: mode === 'create' && !templateCreated, // 新增模式下需要先创建模板
            children: (
              <TemplateVariablesManager
                mode={mode}
                templateId={template?.id}
                localVariables={localVariables}
                onLocalVariablesChange={setLocalVariables}
                onServerVariablesChange={setServerVariables}
                templateCreated={templateCreated}
              />
            ),
          },
        ]}
        size="large"
      />

      {/* 预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: '16px' }}>
          <Title level={5}>预览变量设置</Title>
          <Row gutter={16}>
            {Object.entries(previewVariables).map(([key, value]) => (
              <Col span={8} key={key}>
                <Form.Item label={key}>
                  <Input
                    value={value || ''}
                    onChange={(e) => setPreviewVariables(prev => ({
                      ...prev,
                      [key]: e.target.value
                    }))}
                    placeholder={`请输入${key}的值`}
                  />
                </Form.Item>
              </Col>
            ))}
          </Row>
        </div>
        
        <div style={{ 
          border: '1px solid #d9d9d9', 
          padding: '20px', 
          backgroundColor: '#f5f5f5',
          borderRadius: '6px'
        }}>
          <div 
            style={{ 
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              minHeight: '200px'
            }}
            dangerouslySetInnerHTML={{ __html: generatePreviewContent() }}
          />
        </div>
      </Modal>
    </div>
  );
};

export default TemplateForm; 