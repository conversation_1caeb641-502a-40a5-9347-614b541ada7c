import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Table,
  Tag,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { emailTemplateService } from '../../../services/email';
import { TemplateVariableInfo } from '../../../types/email';
import { showError, showSuccess } from '../../../utils/messageManager';

const { Option } = Select;

// 变量类型选项
const VARIABLE_TYPES = [
  { value: 'string', label: '字符串' },
  { value: 'number', label: '数字' },
  { value: 'boolean', label: '布尔值' },
  { value: 'date', label: '日期' },
];

// 组件属性
interface TemplateVariablesManagerProps {
  mode: 'create' | 'edit';
  templateId?: number;
  localVariables?: Record<string, any>;
  onLocalVariablesChange?: (variables: Record<string, any>) => void;
  onServerVariablesChange?: (variables: Record<string, any>) => void;
  templateCreated?: boolean; // 新增：模板是否已创建
}

const TemplateVariablesManager: React.FC<TemplateVariablesManagerProps> = ({
  mode,
  templateId,
  localVariables = {},
  onLocalVariablesChange,
  onServerVariablesChange,
  templateCreated = false,
}) => {
  const [variablesModalVisible, setVariablesModalVisible] = useState(false);
  const [editingVariable, setEditingVariable] = useState<any>(null);
  const [variablesForm] = Form.useForm();
  
  // 服务端变量状态（编辑模式）
  const [serverVariables, setServerVariables] = useState<TemplateVariableInfo[]>([]);
  const [variablesLoading, setVariablesLoading] = useState(false);
  const [deletingVariables, setDeletingVariables] = useState<Set<string>>(new Set());
  const [addingVariable, setAddingVariable] = useState(false);

  // 获取服务端变量（编辑模式）
  const fetchServerVariables = useCallback(async () => {
    if (mode !== 'edit' || !templateId) return;

    setVariablesLoading(true);
    try {
      const response = await emailTemplateService.getTemplateVariables(templateId);
      if (response.data) {
        setServerVariables(response.data as TemplateVariableInfo[]);
        // 转换为变量对象格式
        const variablesObj: Record<string, any> = {};
        response.data.forEach(v => {
          variablesObj[v.name] = {
            label: v.label,
            type: v.type,
            required: v.required,
            description: v.description,
          };
        });
        onServerVariablesChange?.(variablesObj);
      }
    } catch (error) {
      showError('获取变量失败');
    } finally {
      setVariablesLoading(false);
    }
  }, [mode, templateId, onServerVariablesChange]);

  // 添加变量
  const handleAddVariable = useCallback(async (values: any) => {
    if (mode === 'create') {
      // 新增模式：添加到本地变量
      const newVariable = {
        [values.name]: {
          label: values.label,
          type: values.type,
          required: values.required,
          description: values.description,
        }
      };
      
      const updatedVariables = { ...localVariables, ...newVariable };
      onLocalVariablesChange?.(updatedVariables);
      showSuccess('变量已添加到本地缓存');
    } else {
      // 编辑模式：全量提交所有自定义变量给服务端
      setAddingVariable(true);
      try {
        // 获取当前所有自定义变量（包括新添加的）
        const allCustomVariables = { ...localVariables };
        allCustomVariables[values.name] = {
          label: values.label,
          type: values.type,
          required: values.required,
          description: values.description,
        };
        
        // 全量提交所有自定义变量
        await emailTemplateService.updateTemplateVariables({
          template_id: templateId!,
          variables: allCustomVariables
        });
        
        showSuccess('变量添加成功');
        fetchServerVariables(); // 重新获取变量列表
      } catch (error) {
        showError('添加变量失败');
      } finally {
        setAddingVariable(false);
      }
    }
    
    setVariablesModalVisible(false);
    variablesForm.resetFields();
  }, [mode, templateId, localVariables, onLocalVariablesChange, fetchServerVariables, variablesForm]);

  // 删除变量
  const handleDeleteVariable = useCallback(async (variableName: string) => {
    if (mode === 'create') {
      // 新增模式：从本地变量中删除
      const updatedVariables = { ...localVariables };
      delete updatedVariables[variableName];
      onLocalVariablesChange?.(updatedVariables);
      showSuccess('变量已从本地缓存中删除');
    } else {
      // 编辑模式：全量提交剩余的自定义变量给服务端
      setDeletingVariables(prev => new Set(prev).add(variableName));
      try {
        // 获取删除后的所有自定义变量
        const remainingVariables = { ...localVariables };
        delete remainingVariables[variableName];
        
        // 全量提交剩余的自定义变量
        await emailTemplateService.updateTemplateVariables({
          template_id: templateId!,
          variables: remainingVariables
        });
        
        showSuccess('变量删除成功');
        fetchServerVariables(); // 重新获取变量列表
      } catch (error) {
        showError('删除变量失败');
      } finally {
        setDeletingVariables(prev => {
          const newSet = new Set(prev);
          newSet.delete(variableName);
          return newSet;
        });
      }
    }
  }, [mode, templateId, localVariables, onLocalVariablesChange, fetchServerVariables]);

  // 编辑变量
  const handleEditVariable = useCallback((variable: any) => {
    setEditingVariable(variable);
    variablesForm.setFieldsValue({
      name: variable.name,
      label: variable.label,
      type: variable.type,
      required: variable.required,
      description: variable.description,
    });
    setVariablesModalVisible(true);
  }, [variablesForm]);

  // 处理编辑提交
  const handleEditSubmit = useCallback(async (values: any) => {
    if (mode === 'create') {
      // 新增模式：更新本地变量
      const updatedVariables = { ...localVariables };
      delete updatedVariables[editingVariable.name];
      updatedVariables[values.name] = {
        label: values.label,
        type: values.type,
        required: values.required,
        description: values.description,
      };
      onLocalVariablesChange?.(updatedVariables);
      showSuccess('变量已更新');
    } else {
      // 编辑模式：全量提交所有自定义变量给服务端
      try {
        // 获取更新后的所有自定义变量
        const updatedVariables = { ...localVariables };
        delete updatedVariables[editingVariable.name];
        updatedVariables[values.name] = {
          label: values.label,
          type: values.type,
          required: values.required,
          description: values.description,
        };
        
        // 全量提交所有自定义变量
        await emailTemplateService.updateTemplateVariables({
          template_id: templateId!,
          variables: updatedVariables
        });
        
        showSuccess('变量更新成功');
        fetchServerVariables(); // 重新获取变量列表
      } catch (error) {
        showError('更新变量失败');
      }
    }
    
    setVariablesModalVisible(false);
    setEditingVariable(null);
    variablesForm.resetFields();
  }, [mode, templateId, localVariables, onLocalVariablesChange, editingVariable, fetchServerVariables, variablesForm]);

  // 获取当前显示的变量
  const getCurrentVariables = useCallback(() => {
    if (mode === 'create') {
      // 新增模式：显示本地变量
      return Object.entries(localVariables).map(([name, config]) => ({
        name,
        label: config.label,
        type: config.type,
        required: config.required,
        description: config.description,
      }));
    } else {
      // 编辑模式：显示服务端变量
      return serverVariables;
    }
  }, [mode, localVariables, serverVariables]);

  // 表格列定义
  const columns = [
    {
      title: '变量名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <code>{text}</code>,
    },
    {
      title: '显示名称',
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'string' ? 'blue' : type === 'number' ? 'green' : 'orange'}>
          {VARIABLE_TYPES.find(t => t.value === type)?.label || type}
        </Tag>
      ),
    },
    {
      title: '必填',
      dataIndex: 'required',
      key: 'required',
      render: (required: boolean) => (
        <Tag color={required ? 'red' : 'default'}>
          {required ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="编辑变量">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditVariable(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个变量吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDeleteVariable(record.name)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Tooltip title="删除变量">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                loading={deletingVariables.has(record.name)}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始化
  useEffect(() => {
    if (mode === 'edit') {
      fetchServerVariables();
    }
  }, [mode, fetchServerVariables]);

  const currentVariables = getCurrentVariables();

  // 新增模式下模板未创建时显示提示
  if (mode === 'create' && !templateCreated) {
    return (
      <Card title="变量管理">
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <p style={{ color: '#666', fontSize: '14px' }}>
            请先保存基本信息创建模板，然后才能管理变量
          </p>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card
        title="变量管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingVariable(null);
              variablesForm.resetFields();
              setVariablesModalVisible(true);
            }}
          >
            添加变量
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={currentVariables}
          rowKey="name"
          loading={variablesLoading}
          pagination={false}
          size="small"
          locale={{
            emptyText: '暂无变量，点击上方按钮添加变量'
          }}
        />
      </Card>

      {/* 变量编辑模态框 */}
      <Modal
        title={editingVariable ? '编辑变量' : '添加变量'}
        open={variablesModalVisible}
        onCancel={() => {
          setVariablesModalVisible(false);
          setEditingVariable(null);
          variablesForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={variablesForm}
          layout="vertical"
          onFinish={editingVariable ? handleEditSubmit : handleAddVariable}
          initialValues={{
            type: 'string',
            required: false,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="变量名"
                name="name"
                rules={[
                  { required: true, message: '请输入变量名' },
                  { 
                    pattern: /^[a-z_][a-z0-9_]*$/, 
                    message: '变量名只能包含小写字母、数字和下划线，且以字母或下划线开头' 
                  }
                ]}
              >
                <Input placeholder="如：user_name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="显示名称"
                name="label"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="如：用户姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="变量类型"
                name="type"
                rules={[{ required: true, message: '请选择变量类型' }]}
              >
                <Select placeholder="选择变量类型">
                  {VARIABLE_TYPES.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="是否必填"
                name="required"
                valuePropName="checked"
              >
                <Select placeholder="选择是否必填">
                  <Option value={true}>是</Option>
                  <Option value={false}>否</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="描述"
            name="description"
            rules={[{ max: 255, message: '描述长度不能超过255字符' }]}
          >
            <Input.TextArea 
              placeholder="请输入变量描述" 
              rows={3}
              showCount
              maxLength={255}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setVariablesModalVisible(false);
                setEditingVariable(null);
                variablesForm.resetFields();
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={addingVariable}
              >
                {editingVariable ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TemplateVariablesManager; 