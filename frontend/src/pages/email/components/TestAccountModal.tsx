import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Typography,
  Divider,
  Card,
  Spin,
  Result,
} from 'antd';
import {
  MailOutlined,
  SendOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { EmailAccount, TestEmailAccountRequest } from '../../../types/email';
import { useTheme } from '../../../contexts/ThemeContext';

const { Text, Paragraph } = Typography;

interface TestAccountModalProps {
  visible: boolean;
  onCancel: () => void;
  onTest: (data: TestEmailAccountRequest) => Promise<any>;
  account?: EmailAccount;
}

/**
 * 测试邮件账户模态框组件
 */
const TestAccountModal: React.FC<TestAccountModalProps> = ({
  visible,
  onCancel,
  onTest,
  account,
}) => {
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!account) return;
    
    setLoading(true);
    setTestResult(null);
    
    try {
      const result = await onTest({
        id: account.id,
        test_email: values.test_email,
      });
      
      setTestResult({
        success: result.success,
        message: result.message,
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: '测试失败，请检查网络连接或账户配置',
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理模态框关闭
  const handleClose = () => {
    form.resetFields();
    setTestResult(null);
    onCancel();
  };

  // 重新测试
  const handleRetry = () => {
    setTestResult(null);
  };

  // 渲染测试结果
  const renderTestResult = () => {
    if (!testResult) return null;

    return (
      <Result
        status={testResult.success ? 'success' : 'error'}
        title={testResult.success ? '测试成功' : '测试失败'}
        subTitle={testResult.message}
        extra={[
          <Button key="retry" onClick={handleRetry}>
            重新测试
          </Button>,
          <Button key="close" type="primary" onClick={handleClose}>
            关闭
          </Button>,
        ]}
      />
    );
  };

  // 渲染测试表单
  const renderTestForm = () => {
    return (
      <Form form={form} layout="vertical" onFinish={handleSubmit} name="testAccountForm" preserve={false}>
        <Form.Item
          name="test_email"
          label="测试邮箱"
          rules={[
            { required: true, message: '请输入测试邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            placeholder="请输入接收测试邮件的邮箱地址"
            prefix={<MailOutlined />}
          />
        </Form.Item>

        <Divider />

        <Card
          title="验证邮件预览"
          size="small"
          bordered={false}
          style={{ background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
        >
          <Paragraph>
            <Text strong>主题:</Text> 【服务验证】{account?.name} 邮件服务配置确认
          </Paragraph>
          <Paragraph>
            <Text strong>发件人:</Text> {account?.from_name || '系统'} &lt;{account?.from_address}&gt;
          </Paragraph>
          <Paragraph>
            <Text strong>收件人:</Text> <Text code>{form.getFieldValue('test_email') || '验证邮箱'}</Text>
          </Paragraph>
          <Divider style={{ margin: '12px 0' }} />
          <Paragraph>
            这是一封服务验证邮件，用于确认您的邮件服务配置是否正常工作。
          </Paragraph>
          <Paragraph>
            <Text strong>验证内容:</Text>
          </Paragraph>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>服务配置信息展示</li>
            <li>邮件发送功能验证</li>
            <li>服务可用性确认</li>
          </ul>
          <Paragraph>
            <Text strong>发送时间:</Text> {new Date().toLocaleString()}
          </Paragraph>
          <Paragraph>
            <Text strong>账户信息:</Text> {account?.name} ({account?.type === 4 ? 'API' : 'SMTP'})
          </Paragraph>
        </Card>
      </Form>
    );
  };

  return (
          <Modal
        title="测试邮件账户"
        open={visible}
        onCancel={handleClose}
        destroyOnHidden
        footer={
        testResult ? null : [
          <Button key="cancel" onClick={handleClose}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            icon={<SendOutlined />}
            loading={loading}
            onClick={() => form.submit()}
          >
            发送测试邮件
          </Button>,
        ]
      }
      width={500}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '30px 0' }}>
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
            tip="正在测试邮件账户，请稍候..."
          />
        </div>
      ) : testResult ? (
        renderTestResult()
      ) : (
        renderTestForm()
      )}
    </Modal>
  );
};

export default TestAccountModal; 