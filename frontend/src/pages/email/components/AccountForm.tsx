import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Checkbox,
  InputNumber,
  Button,
  Row,
  Col,
  Typography,
  Card,

} from 'antd';
import {
  LockOutlined,
  MailOutlined,
  UserOutlined,
  ApiOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import {
  EmailAccount,
  EMAIL_ACCOUNT_TYPES,
  EMAIL_PROVIDERS,
  ALIYUN_REGIONS,
} from '../../../types/email';
import { useTheme } from '../../../contexts/ThemeContext';

const { Option } = Select;
const { Title, Text } = Typography;

interface AccountFormProps {
  initialValues?: EmailAccount;
  onFinish: (values: any) => void;
  loading?: boolean;
}

/**
 * 邮件账户表单组件
 * 用于创建和编辑邮件账户
 */
const AccountForm: React.FC<AccountFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const [accountType, setAccountType] = useState<number>(
    initialValues?.type || EMAIL_ACCOUNT_TYPES.SMTP
  );
  const [provider, setProvider] = useState<string>(
    initialValues?.provider || EMAIL_PROVIDERS.GMAIL
  );
  const isEdit = !!initialValues;

  // 当初始值变化时更新表单
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        // 确保阿里云配置正确设置
        config: initialValues.config || {},
      });
      setAccountType(initialValues.type);
      setProvider(initialValues.provider);
    }
  }, [initialValues, form]);

  // 处理账户类型变化
  const handleTypeChange = (value: number) => {
    setAccountType(value);
    // 根据类型设置默认提供商
    if (value === EMAIL_ACCOUNT_TYPES.API) {
      setProvider(EMAIL_PROVIDERS.ALIYUN);
      form.setFieldsValue({ provider: EMAIL_PROVIDERS.ALIYUN });
    } else if (provider === EMAIL_PROVIDERS.ALIYUN) {
      setProvider(EMAIL_PROVIDERS.GMAIL);
      form.setFieldsValue({ provider: EMAIL_PROVIDERS.GMAIL });
    }
  };

  // 处理提供商变化
  const handleProviderChange = (value: string) => {
    setProvider(value);
  };

  // 根据账户类型渲染不同的配置表单
  const renderConfigFields = () => {
    if (accountType === EMAIL_ACCOUNT_TYPES.API && provider === EMAIL_PROVIDERS.ALIYUN) {
      return (
        <Card
          title="阿里云配置"
          className="rounded-md shadow-sm"
          size="small"
          bordered={false}
          style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
        >
          <Form.Item
            name={['config', 'access_key_id']}
            label="AccessKey ID"
            rules={[{ required: true, message: '请输入AccessKey ID' }]}
          >
            <Input
              placeholder="请输入阿里云AccessKey ID"
              prefix={<ApiOutlined />}
            />
          </Form.Item>

          <Form.Item
            name={['config', 'access_key_secret']}
            label="AccessKey Secret"
            rules={[{ required: true, message: '请输入AccessKey Secret' }]}
          >
            <Input.Password
              placeholder="请输入阿里云AccessKey Secret"
              prefix={<LockOutlined />}
            />
          </Form.Item>

          <Form.Item
            name={['config', 'region']}
            label="地域"
            rules={[{ required: true, message: '请选择地域' }]}
          >
            <Select placeholder="请选择阿里云地域" showSearch>
              {ALIYUN_REGIONS.map((region) => (
                <Option key={region.value} value={region.value}>
                  {region.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name={['config', 'domain']}
            label="发信地址"
            rules={[{ required: true, message: '请输入发信地址' }]}
            tooltip="阿里云邮件推送服务中配置的发信地址"
          >
            <Input
              placeholder="例如: your-domain.com"
              prefix={<GlobalOutlined />}
            />
          </Form.Item>
        </Card>
      );
    }

    return (
      <Card
        title="服务器配置"
        className="rounded-md shadow-sm"
        size="small"
        bordered={false}
        style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
      >
        <Form.Item
          name="host"
          label="SMTP服务器"
          rules={[{ required: true, message: '请输入SMTP服务器地址' }]}
        >
          <Input placeholder="例如: smtp.gmail.com" />
        </Form.Item>

        <Form.Item
          name="port"
          label="端口"
          rules={[{ required: true, message: '请输入端口号' }]}
        >
          <InputNumber
            min={1}
            max={65535}
            placeholder="例如: 587"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input
            placeholder="请输入邮箱用户名"
            prefix={<UserOutlined />}
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password
            placeholder="请输入邮箱密码或应用专用密码"
            prefix={<LockOutlined />}
          />
        </Form.Item>

        <Form.Item name="is_ssl" valuePropName="checked">
          <Checkbox>启用SSL/TLS</Checkbox>
        </Form.Item>
      </Card>
    );
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        is_ssl: true,
        is_active: true,
        daily_limit: 100,
        monthly_limit: 3000,
        ...initialValues,
        config: initialValues?.config || {},
      }}
      name="accountForm"
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="基本信息"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Form.Item
              name="name"
              label="账户名称"
              rules={[{ required: true, message: '请输入账户名称' }]}
            >
              <Input placeholder="请输入账户名称" />
            </Form.Item>

            <Form.Item
              name="type"
              label="账户类型"
              rules={[{ required: true, message: '请选择账户类型' }]}
            >
              <Select onChange={handleTypeChange}>
                <Option value={EMAIL_ACCOUNT_TYPES.SMTP}>SMTP</Option>
                <Option value={EMAIL_ACCOUNT_TYPES.IMAP}>IMAP</Option>
                <Option value={EMAIL_ACCOUNT_TYPES.POP3}>POP3</Option>
                <Option value={EMAIL_ACCOUNT_TYPES.API}>API (阿里云等)</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="provider"
              label="邮件提供商"
              rules={[{ required: true, message: '请选择邮件提供商' }]}
            >
              <Select onChange={handleProviderChange}>
                {accountType === EMAIL_ACCOUNT_TYPES.API ? (
                  <>
                    <Option value={EMAIL_PROVIDERS.ALIYUN}>阿里云</Option>
                    <Option value={EMAIL_PROVIDERS.CUSTOM}>自定义</Option>
                  </>
                ) : (
                  <>
                    <Option value={EMAIL_PROVIDERS.GMAIL}>Gmail</Option>
                    <Option value={EMAIL_PROVIDERS.QQ}>QQ邮箱</Option>
                    <Option value={EMAIL_PROVIDERS.EXCHANGE}>Exchange</Option>
                    <Option value={EMAIL_PROVIDERS.CUSTOM}>自定义</Option>
                  </>
                )}
              </Select>
            </Form.Item>
          </Card>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          {renderConfigFields()}
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="发件人设置"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Form.Item
              name="from_address"
              label="发件人地址"
              rules={[
                { required: true, message: '请输入发件人地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                placeholder="请输入发件人邮箱地址"
                prefix={<MailOutlined />}
              />
            </Form.Item>

            <Form.Item
              name="from_name"
              label="发件人名称"
            >
              <Input placeholder="请输入发件人显示名称" />
            </Form.Item>

            <Form.Item
              name="reply_to_address"
              label="回复地址"
              rules={[
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input placeholder="请输入回复地址（可选）" />
            </Form.Item>
          </Card>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="高级设置"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Form.Item
              name="daily_limit"
              label="每日发送限制"
              rules={[{ required: true, message: '请输入每日发送限制' }]}
              tooltip="每日最大发送邮件数量"
            >
              <InputNumber min={1} placeholder="例如: 100" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="monthly_limit"
              label="每月发送限制"
              rules={[{ required: true, message: '请输入每月发送限制' }]}
              tooltip="每月最大发送邮件数量"
            >
              <InputNumber min={1} placeholder="例如: 3000" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item name="is_active" valuePropName="checked">
              <Checkbox>启用账户</Checkbox>
            </Form.Item>
          </Card>
        </Col>
      </Row>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          {isEdit ? '更新账户' : '创建账户'}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default AccountForm; 