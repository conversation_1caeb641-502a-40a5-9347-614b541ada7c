# API管理页面

## 功能概述

API管理页面是一个专门用于维护服务端API接口的管理界面，提供以下功能：

### 主要功能

1. **API列表展示**
   - 显示所有API接口的基本信息
   - 支持分页和搜索
   - 按创建时间倒序排列

2. **API信息维护**
   - 新增API接口
   - 编辑现有API接口
   - 删除API接口

3. **API字段管理**
   - API标识（name）：唯一标识符
   - API名称（display_name）：显示名称
   - 服务名称（service_name）：下拉选择，支持多个服务
   - HTTP方法（api_method）：GET、POST、PUT、DELETE、PATCH
   - API路径（path）：接口路径
   - 请求类型（request_type）：JSON、Form、File、Text、Stream、XML、Binary
   - 响应类型（response_type）：JSON、HTML、XML、Stream、File、Text、Binary
   - 排序（sort_order）：显示顺序
   - 描述（description）：API描述信息

### 服务名称选项

- user-service：用户服务
- email-service：邮件服务
- file-service：文件服务
- auth-service：认证服务
- notification-service：通知服务
- payment-service：支付服务
- order-service：订单服务
- product-service：产品服务

### 技术实现

- 基于React + TypeScript开发
- 使用Ant Design组件库
- 集成现有的资源管理API
- 支持响应式设计
- 统一的错误处理机制

### 页面路由

- 路径：`/api-management`
- 菜单位置：系统管理 > API管理
- 图标：ApiOutlined

### 数据存储

API信息存储在resource表中，resource_type为'api'，包含以下扩展字段：
- service_name：应用服务名称
- request_type：请求数据类型
- response_type：响应数据类型
- api_method：HTTP方法

### 使用说明

1. **查看API列表**
   - 页面加载时自动显示所有API接口
   - 使用搜索框可以按名称或路径搜索
   - 支持分页浏览

2. **新增API**
   - 点击"新增API"按钮
   - 填写必要的API信息
   - 选择服务名称和HTTP方法
   - 提交保存

3. **编辑API**
   - 点击表格中的编辑按钮
   - 修改API信息
   - 保存更新

4. **删除API**
   - 点击表格中的删除按钮
   - 确认删除操作

### 注意事项

- API标识必须唯一，只能包含小写字母、数字、下划线和连字符
- 服务名称和HTTP方法为必填项
- 删除操作不可恢复，请谨慎操作
- 所有操作都有相应的成功/失败提示 