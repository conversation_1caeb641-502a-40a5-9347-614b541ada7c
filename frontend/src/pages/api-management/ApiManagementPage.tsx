import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Tooltip,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import {
  getResources,
  createResource,
  updateResource,
  deleteResource,
  type Resource,
  type CreateResourceRequest,
  type UpdateResourceRequest,
  type ListResourcesRequest,
} from '../../services/resource';
import { showAPIError } from '../../utils/errorHandler';
import { useTheme } from '../../contexts/ThemeContext';
import {SUCCESS} from "../../constants/errorCodes";

const { Option } = Select;
const { Text, Title } = Typography;
const { TextArea } = Input;

// 服务名称选项
const serviceNames = [
  { value: 'user-service', label: '用户服务' },
  { value: 'email-service', label: '邮件服务' },
  { value: 'file-service', label: '文件服务' },
  { value: 'auth-service', label: '认证服务' },
  { value: 'notification-service', label: '通知服务' },
  { value: 'payment-service', label: '支付服务' },
  { value: 'order-service', label: '订单服务' },
  { value: 'product-service', label: '产品服务' },
];

// 请求类型选项
const requestTypes = [
  { value: 'json', label: 'JSON' },
  { value: 'form', label: 'Form' },
  { value: 'file', label: 'File' },
  { value: 'text', label: 'Text' },
  { value: 'stream', label: 'Stream' },
  { value: 'xml', label: 'XML' },
  { value: 'binary', label: 'Binary' },
];

// 响应类型选项
const responseTypes = [
  { value: 'json', label: 'JSON' },
  { value: 'html', label: 'HTML' },
  { value: 'xml', label: 'XML' },
  { value: 'stream', label: 'Stream' },
  { value: 'file', label: 'File' },
  { value: 'text', label: 'Text' },
  { value: 'binary', label: 'Binary' },
];

// HTTP方法选项
const httpMethods = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' },
];

const ApiManagementPage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [apiList, setApiList] = useState<Resource[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingApi, setEditingApi] = useState<Resource | null>(null);
  const [form] = Form.useForm();

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  // 统计数据
  const stats = [
    { title: '总API数', value: total, icon: <ApiOutlined />, color: '#1890ff' },
    { title: '启用API', value: Array.isArray(apiList) ? apiList.filter(api => api.status === 'active').length : 0, icon: <CheckCircleOutlined />, color: '#52c41a' },
    { title: '禁用API', value: Array.isArray(apiList) ? apiList.filter(api => api.status !== 'active').length : 0, icon: <CloseCircleOutlined />, color: '#ff4d4f' },
    { title: '服务数量', value: Array.isArray(apiList) ? new Set(apiList.map(api => api.service_name).filter(Boolean)).size : 0, icon: <AppstoreOutlined />, color: '#722ed1' },
  ];

  // 获取API列表
  const fetchApiList = async () => {
    setLoading(true);
    try {
      const params: ListResourcesRequest = {
        page: currentPage,
        size: pageSize,
        keyword: searchKeyword,
        resource_type: 'api',
        order_by: 'created_at',
        order_dir: 'desc',
      };
      
      const response = await getResources(params);
      if (response.code === SUCCESS) {
        setApiList(response.data || []);
        setTotal(response.meta?.pagination?.total || 0);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApiList();
  }, [currentPage, pageSize, searchKeyword]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1);
  };

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value);
    setCurrentPage(1);
  };

  // 处理新增API
  const handleAddApi = () => {
    setEditingApi(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑API
  const handleEditApi = (record: Resource) => {
    setEditingApi(record);
    form.setFieldsValue({
      name: record.name,
      display_name: record.display_name,
      description: record.description,
      service_name: record.service_name,
      request_type: record.request_type,
      response_type: record.response_type,
      api_method: record.api_method,
      path: record.path,
    });
    setModalVisible(true);
  };

  // 处理删除API
  const handleDeleteApi = async (id: number) => {
    try {
      await deleteResource(id);
      message.success('API删除成功');
      fetchApiList();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingApi) {
        // 更新API
        const updateData: UpdateResourceRequest = {
          id: editingApi.id,
          resource_type: 'api',
          ...values,
        };
        await updateResource(updateData);
        message.success('API更新成功');
      } else {
        // 创建API
        const createData: CreateResourceRequest = {
          resource_type: 'api',
          ...values,
        };
        await createResource(createData);
        message.success('API创建成功');
      }
      setModalVisible(false);
      fetchApiList();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: 'API信息',
      key: 'api_info',
      render: (record: Resource) => (
        <div>
          <div style={{ fontWeight: 500, color: isDarkMode ? '#ffffff' : '#262626' }}>
            {record.display_name}
          </div>
          <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
            {record.name}
          </div>
        </div>
      ),
    },
    {
      title: '服务信息',
      key: 'service_info',
      render: (record: Resource) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="blue">{record.service_name || '-'}</Tag>
          </div>
          <div>
            <Tag color="cyan">{record.request_type || '-'}</Tag>
            <Tag color="magenta" style={{ marginLeft: 4 }}>{record.response_type || '-'}</Tag>
          </div>
        </div>
      ),
    },
    {
      title: '接口信息',
      key: 'interface_info',
      render: (record: Resource) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            {(() => {
              const colorMap: Record<string, string> = {
                GET: 'green',
                POST: 'blue',
                PUT: 'orange',
                DELETE: 'red',
                PATCH: 'purple',
              };
              return <Tag color={colorMap[record.api_method || ''] || 'default'}>{record.api_method || '-'}</Tag>;
            })()}
          </div>
          <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
            <Text code>{record.path || '-'}</Text>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: Resource) => (
        <Tag color={record.status === 'active' ? 'green' : 'red'}>
          {record.status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },

    {
      title: '创建时间',
      key: 'created_at',
      width: 120,
      render: (record: Resource) => (
        <Text style={{ fontSize: 12 }}>
          {new Date(record.created_at).toLocaleDateString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: Resource) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditApi(record)}
              style={actionButtonStyle}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个API吗？"
            onConfirm={() => handleDeleteApi(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除" color={tooltipColor}>
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                style={actionButtonStyle}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="api-management-page">
      {/* 页面标题区域 - 统计展示 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchApiList}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddApi}
            >
              新增API
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索API名称或路径"
              value={searchKeyword}
              onChange={handleSearchChange}
              onSearch={handleSearch}
              allowClear
            />
          </Col>
        </Row>
      </Card>

      {/* 表格区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Table
          columns={columns}
          dataSource={apiList}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑API模态框 */}
      <Modal
        title={editingApi ? '编辑API' : '新增API'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="API标识"
                rules={[
                  { required: true, message: '请输入API标识' },
                  { pattern: /^[a-z0-9_-]+$/, message: '只能包含小写字母、数字、下划线和连字符' },
                ]}
              >
                <Input placeholder="请输入API标识" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="display_name"
                label="API名称"
                rules={[{ required: true, message: '请输入API名称' }]}
              >
                <Input placeholder="请输入API名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="service_name"
                label="服务名称"
                rules={[{ required: true, message: '请选择服务名称' }]}
              >
                <Select placeholder="请选择服务名称">
                  {serviceNames.map(service => (
                    <Option key={service.value} value={service.value}>
                      {service.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="api_method"
                label="HTTP方法"
                rules={[{ required: true, message: '请选择HTTP方法' }]}
              >
                <Select placeholder="请选择HTTP方法">
                  {httpMethods.map(method => (
                    <Option key={method.value} value={method.value}>
                      {method.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="path"
            label="API路径"
            rules={[{ required: true, message: '请输入API路径' }]}
          >
            <Input placeholder="/api/example/path" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="request_type"
                label="请求类型"
              >
                <Select placeholder="请选择请求类型" allowClear>
                  {requestTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="response_type"
                label="响应类型"
              >
                <Select placeholder="请选择响应类型" allowClear>
                  {responseTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>



          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入API描述"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingApi ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApiManagementPage; 