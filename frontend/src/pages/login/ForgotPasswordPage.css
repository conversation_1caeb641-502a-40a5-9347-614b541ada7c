/* 忘记密码页面样式 */
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.forgot-password-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.forgot-password-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 480px;
}

.forgot-password-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 480px;
  width: 100%;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.forgot-password-card.ant-card {
  border-radius: 16px;
}

/* 深色模式样式 */
.forgot-password-container[data-theme="dark"] .forgot-password-card {
  background: rgba(31, 31, 31, 0.95);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 表单样式 */
.forgot-password-container .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.forgot-password-container[data-theme="dark"] .ant-form-item-label > label {
  color: #cccccc;
}

.forgot-password-container .ant-input,
.forgot-password-container .ant-input-password {
  border-radius: 8px;
  height: 48px;
  transition: all 0.3s ease;
}

.forgot-password-container .ant-input:focus,
.forgot-password-container .ant-input-password:focus,
.forgot-password-container .ant-input:hover,
.forgot-password-container .ant-input-password:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.forgot-password-container .ant-btn-primary {
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password-container .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 步骤指示器样式 */
.forgot-password-container .ant-steps {
  margin-bottom: 32px;
}

.forgot-password-container .ant-steps-item-title {
  font-weight: 500;
}

/* 警告框样式 */
.forgot-password-container .ant-alert {
  border-radius: 8px;
  margin-bottom: 24px;
}

/* 成功页面样式 */
.forgot-password-container .success-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-container {
    padding: 16px;
  }
  
  .forgot-password-card {
    max-width: 100%;
  }
  
  .forgot-password-container .ant-steps {
    margin-bottom: 24px;
  }
  
  .forgot-password-container .ant-steps-item-title {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .forgot-password-container {
    padding: 12px;
  }
  
  .forgot-password-container .ant-card-body {
    padding: 24px 16px;
  }
  
  .forgot-password-container .ant-form-item {
    margin-bottom: 16px;
  }
  
  .forgot-password-container .ant-input,
  .forgot-password-container .ant-input-password {
    height: 44px;
  }
  
  .forgot-password-container .ant-btn-primary {
    height: 44px;
    font-size: 14px;
  }
}

/* 动画效果 */
.forgot-password-container .ant-card {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.forgot-password-container .ant-btn-loading {
  pointer-events: none;
}

.forgot-password-container .ant-btn-loading .ant-btn-loading-icon {
  margin-right: 8px;
}

/* 错误状态样式 */
.forgot-password-container .ant-form-item-has-error .ant-input,
.forgot-password-container .ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.forgot-password-container .ant-form-item-has-error .ant-input:focus,
.forgot-password-container .ant-form-item-has-error .ant-input-password:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 成功状态样式 */
.forgot-password-container .ant-form-item-has-success .ant-input,
.forgot-password-container .ant-form-item-has-success .ant-input-password {
  border-color: #52c41a;
}

.forgot-password-container .ant-form-item-has-success .ant-input:focus,
.forgot-password-container .ant-form-item-has-success .ant-input-password:focus {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}
