-- API接口资源表数据 - 完整版
-- 基于前端services中实际提取的109个API接口生成
-- 使用分步执行避免MySQL子查询限制

-- =============================================
-- 第一步：插入API分类资源（顶级）- 14个分类
-- =============================================

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'api-auth', '认证API', 'api', NULL, NULL, '认证相关API接口', 1, true, true, NOW(), NOW()),
(1, 'api-dashboard', '仪表盘API', 'api', NULL, NULL, '仪表盘统计API接口', 2, true, true, NOW(), NOW()),
(1, 'api-users', '用户管理API', 'api', NULL, NULL, '用户管理相关API接口', 3, true, true, NOW(), NOW()),
(1, 'api-departments', '部门管理API', 'api', NULL, NULL, '部门管理相关API接口', 4, true, true, NOW(), NOW()),
(1, 'api-positions', '职位管理API', 'api', NULL, NULL, '职位管理相关API接口', 5, true, true, NOW(), NOW()),
(1, 'api-roles', '角色管理API', 'api', NULL, NULL, '角色管理相关API接口', 6, true, true, NOW(), NOW()),
(1, 'api-permissions', '权限管理API', 'api', NULL, NULL, '权限管理相关API接口', 7, true, true, NOW(), NOW()),
(1, 'api-resource', '资源管理API', 'api', NULL, NULL, '资源管理相关API接口', 8, true, true, NOW(), NOW()),
(1, 'api-tenants', '租户管理API', 'api', NULL, NULL, '租户管理相关API接口', 9, true, true, NOW(), NOW()),
(1, 'api-app-config', '应用配置API', 'api', NULL, NULL, '应用配置相关API接口', 10, true, true, NOW(), NOW()),
(1, 'api-file-system', '文件系统API', 'api', NULL, NULL, '文件系统相关API接口', 11, true, true, NOW(), NOW()),
(1, 'api-email', '邮件管理API', 'api', NULL, NULL, '邮件管理相关API接口', 12, true, true, NOW(), NOW()),
(1, 'api-idgenerator', 'ID生成器API', 'api', NULL, NULL, 'ID生成器相关API接口', 13, true, true, NOW(), NOW()),
(1, 'api-verification', '用户验证API', 'api', NULL, NULL, '用户验证相关API接口', 14, true, true, NOW(), NOW());

-- =============================================
-- 第二步：插入具体API接口资源 - 109个接口
-- =============================================

-- 1. 认证相关API (3个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-auth-login', '用户登录', 'api', r.id, '/api/auth/login', '用户登录接口', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-auth' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-auth-logout', '用户登出', 'api', r.id, '/api/auth/logout', '用户登出接口', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-auth' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-auth-refresh', '刷新token', 'api', r.id, '/api/auth/refresh', '刷新访问令牌接口', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-auth' AND r.tenant_id = 1;

-- 2. 仪表盘API (4个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-dashboard-users-stats', '用户统计', 'api', r.id, '/api/user/stats', '获取用户统计数据', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-dashboard' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-dashboard-departments-stats', '部门统计', 'api', r.id, '/api/user/departments/stats', '获取部门统计数据', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-dashboard' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-dashboard-roles-stats', '角色统计', 'api', r.id, '/api/user/roles/stats', '获取角色统计数据', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-dashboard' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-dashboard-permissions-stats', '权限统计', 'api', r.id, '/api/user/permissions/stats', '获取权限统计数据', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-dashboard' AND r.tenant_id = 1;

-- 3. 用户管理API (6个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-list', '用户列表', 'api', r.id, '/api/user/users/list', '获取用户列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-create', '创建用户', 'api', r.id, '/api/user/users/create', '创建用户', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-update', '更新用户', 'api', r.id, '/api/user/users/update', '更新用户信息', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-delete', '删除用户', 'api', r.id, '/api/user/users/delete', '删除用户', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-enable', '启用用户', 'api', r.id, '/api/user/users/enable', '启用用户账号', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-users-disable', '禁用用户', 'api', r.id, '/api/user/users/disable', '禁用用户账号', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-users' AND r.tenant_id = 1;

-- 4. 部门管理API (5个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-departments-list', '部门列表', 'api', r.id, '/api/user/departments/list', '获取部门列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-departments' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-departments-create', '创建部门', 'api', r.id, '/api/user/departments/create', '创建部门', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-departments' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-departments-update', '更新部门', 'api', r.id, '/api/user/departments/update', '更新部门信息', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-departments' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-departments-delete', '删除部门', 'api', r.id, '/api/user/departments/delete', '删除部门', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-departments' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-departments-tree', '部门树结构', 'api', r.id, '/api/user/departments/tree', '获取部门树结构', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-departments' AND r.tenant_id = 1;

-- 5. 职位管理API (10个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-list', '职位列表', 'api', r.id, '/api/user/positions/list', '获取职位列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-get', '职位详情', 'api', r.id, '/api/user/positions/get', '获取职位详情', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-create', '创建职位', 'api', r.id, '/api/user/positions/create', '创建职位', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-update', '更新职位', 'api', r.id, '/api/user/positions/update', '更新职位信息', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-delete', '删除职位', 'api', r.id, '/api/user/positions/delete', '删除职位', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-by-department', '按部门获取职位', 'api', r.id, '/api/user/positions/by-department', '按部门获取职位列表', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-stats', '职位统计', 'api', r.id, '/api/user/positions/stats', '获取职位统计信息', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-users', '职位用户', 'api', r.id, '/api/user/positions/users', '获取职位用户列表', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-assign', '分配用户', 'api', r.id, '/api/user/positions/assign', '为职位分配用户', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-positions-remove', '移除用户', 'api', r.id, '/api/user/positions/remove', '从职位移除用户', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-positions' AND r.tenant_id = 1;

-- 6. 角色管理API (10个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-list', '角色列表', 'api', r.id, '/api/user/roles/list', '获取角色列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-get', '角色详情', 'api', r.id, '/api/user/roles/get', '获取角色详情', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-create', '创建角色', 'api', r.id, '/api/user/roles/create', '创建角色', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-update', '更新角色', 'api', r.id, '/api/user/roles/update', '更新角色信息', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-delete', '删除角色', 'api', r.id, '/api/user/roles/delete', '删除角色', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-assign-permissions', '分配权限', 'api', r.id, '/api/user/roles/assign-permissions', '为角色分配权限', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-permissions', '角色权限', 'api', r.id, '/api/user/roles/permissions', '获取角色权限列表', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-assign', '角色分配', 'api', r.id, '/api/user/roles/assign', '为用户分配角色', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-users', '角色用户', 'api', r.id, '/api/user/roles/users', '获取角色用户列表', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-roles-stats', '角色统计', 'api', r.id, '/api/user/roles/stats', '获取角色统计信息', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-roles' AND r.tenant_id = 1;

-- 7. 权限管理API (7个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-list', '权限列表', 'api', r.id, '/api/user/permissions/list', '获取权限列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-create', '创建权限', 'api', r.id, '/api/user/permissions/create', '创建权限', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-batch-create', '批量创建权限', 'api', r.id, '/api/user/permissions/batch-create', '批量创建权限', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-update', '更新权限', 'api', r.id, '/api/user/permissions/update', '更新权限信息', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-delete', '删除权限', 'api', r.id, '/api/user/permissions/delete', '删除权限', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-get', '权限详情', 'api', r.id, '/api/user/permissions/get', '获取权限详情', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-permissions-stats', '权限统计', 'api', r.id, '/api/user/permissions/stats', '获取权限统计信息', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-permissions' AND r.tenant_id = 1;

-- 8. 资源管理API (11个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-list', '资源列表', 'api', r.id, '/api/user/resources/list', '获取资源列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-get', '资源详情', 'api', r.id, '/api/user/resources/get', '获取资源详情', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-create', '创建资源', 'api', r.id, '/api/user/resources/create', '创建资源', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-update', '更新资源', 'api', r.id, '/api/user/resources/update', '更新资源信息', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-delete', '删除资源', 'api', r.id, '/api/user/resources/delete', '删除资源', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-tree', '资源树结构', 'api', r.id, '/api/user/resources/tree', '获取资源树结构', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-permissions', '资源权限', 'api', r.id, '/api/user/resources/permissions', '获取资源权限列表', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-api-available', '可用API资源', 'api', r.id, '/api/user/resources/api-resources/available', '获取可用API资源', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-api-assign', '批量分配API资源', 'api', r.id, '/api/user/resources/api-resources/assign', '批量分配API资源', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-stats', '资源统计', 'api', r.id, '/api/user/resources/stats', '获取资源统计信息', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-resource-permissions-configure', '配置资源权限', 'api', r.id, '/api/user/resources/permissions/configure', '配置资源权限', 11, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-resource' AND r.tenant_id = 1;

-- 9. 租户管理API (7个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-create', '创建租户', 'api', r.id, '/api/user/tenants/create', '创建租户', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-update', '更新租户', 'api', r.id, '/api/user/tenants/update', '更新租户信息', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-get', '租户详情', 'api', r.id, '/api/user/tenants/get', '获取租户详情', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-list', '租户列表', 'api', r.id, '/api/user/tenants/list', '获取租户列表', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-delete', '删除租户', 'api', r.id, '/api/user/tenants/delete', '删除租户', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-update-status', '更新租户状态', 'api', r.id, '/api/user/tenants/update-status', '更新租户状态', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-tenants-search', '搜索租户', 'api', r.id, '/api/user/tenants/search', '搜索租户', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-tenants' AND r.tenant_id = 1;

-- 10. 应用配置API (7个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-password-policy-get', '获取密码策略', 'api', r.id, '/api/user/app-config/password-policy', '获取密码策略配置', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-password-policy-update', '更新密码策略', 'api', r.id, '/api/user/app-config/password-policy', '更新密码策略配置', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-registration-methods-get', '获取注册方式', 'api', r.id, '/api/user/app-config/registration-methods', '获取注册方式配置', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-registration-methods-update', '更新注册方式', 'api', r.id, '/api/user/app-config/registration-methods', '更新注册方式配置', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-configs-get', '获取应用配置', 'api', r.id, '/api/user/app-config/configs', '获取应用配置', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-config-update', '更新应用配置', 'api', r.id, '/api/user/app-config/config', '更新应用配置', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-app-config-copy-system', '复制系统配置', 'api', r.id, '/api/user/app-config/copy-system', '复制系统配置到应用', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-app-config' AND r.tenant_id = 1;

-- 11. 文件系统API (12个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-config-list', '场景配置列表', 'api', r.id, '/api/file-system/config/list', '获取场景配置列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-config-create', '创建场景配置', 'api', r.id, '/api/file-system/config/create', '创建场景配置', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-config-update', '更新场景配置', 'api', r.id, '/api/file-system/config/update', '更新场景配置', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-config-delete', '删除场景配置', 'api', r.id, '/api/file-system/config/delete', '删除场景配置', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-config-get', '获取场景配置', 'api', r.id, '/api/file-system/config/get', '获取场景配置详情', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-list', '文件列表', 'api', r.id, '/api/file-system/list', '获取文件列表', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-get', '文件详情', 'api', r.id, '/api/file-system/get', '获取文件详情', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-delete', '删除文件', 'api', r.id, '/api/file-system/delete', '删除文件', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-mark-permanent', '标记为永久文件', 'api', r.id, '/api/file-system/mark-permanent', '标记文件为永久保存', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-upload', '上传文件', 'api', r.id, '/api/file-system/upload', '上传文件', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-upload-token', '获取上传令牌', 'api', r.id, '/api/file-system/upload-token', '获取文件上传令牌', 11, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-file-system-access-token', '创建访问令牌', 'api', r.id, '/api/file-system/access-token', '创建文件访问令牌', 12, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-file-system' AND r.tenant_id = 1;

-- 12. 邮件管理API (26个 = 10个账户 + 16个模板)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-list', '邮件账户列表', 'api', r.id, '/api/email/accounts/list', '获取邮件账户列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-create', '创建邮件账户', 'api', r.id, '/api/email/accounts/create', '创建邮件账户', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-update', '更新邮件账户', 'api', r.id, '/api/email/accounts/update', '更新邮件账户', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-detail', '邮件账户详情', 'api', r.id, '/api/email/accounts/detail', '获取邮件账户详情', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-delete', '删除邮件账户', 'api', r.id, '/api/email/accounts/delete', '删除邮件账户', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-test', '测试邮件账户', 'api', r.id, '/api/email/accounts/test', '测试邮件账户连接', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-types', '邮件账户类型', 'api', r.id, '/api/email/accounts/types', '获取邮件账户类型列表', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-test-status', '账户测试状态', 'api', r.id, '/api/email/accounts/test-status', '获取账户测试状态', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-get', '获取邮件账户', 'api', r.id, '/api/email/accounts/get', '获取指定邮件账户详情', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-accounts-batch-update', '批量更新账户状态', 'api', r.id, '/api/email/accounts/batch-update', '批量更新邮件账户状态', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-select', '智能选择模板', 'api', r.id, '/api/email/templates/select', '智能选择邮件模板', 11, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-list', '邮件模板列表', 'api', r.id, '/api/email/templates/list', '获取邮件模板列表', 12, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-get', '邮件模板详情', 'api', r.id, '/api/email/templates/get', '获取邮件模板详情', 13, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-create', '创建邮件模板', 'api', r.id, '/api/email/templates/create', '创建邮件模板', 14, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-update', '更新邮件模板', 'api', r.id, '/api/email/templates/update', '更新邮件模板', 15, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-delete', '删除邮件模板', 'api', r.id, '/api/email/templates/delete', '删除邮件模板', 16, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-system', '系统模板', 'api', r.id, '/api/email/templates/system', '获取系统邮件模板', 17, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-tenant', '租户模板', 'api', r.id, '/api/email/templates/tenant', '获取租户邮件模板', 18, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-tenant-create', '创建租户模板', 'api', r.id, '/api/email/templates/tenant/create', '创建租户邮件模板', 19, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-clone', '克隆系统模板', 'api', r.id, '/api/email/templates/clone', '克隆系统模板到租户', 20, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-preview', '模板预览', 'api', r.id, '/api/email/templates/preview', '邮件模板预览', 21, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-validate', '模板验证', 'api', r.id, '/api/email/templates/validate', '邮件模板验证', 22, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-compare', '模板比较', 'api', r.id, '/api/email/templates/compare', '邮件模板比较', 23, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-scenario', '按场景获取模板', 'api', r.id, '/api/email/templates/scenario', '按场景获取邮件模板', 24, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-batch-delete', '批量删除模板', 'api', r.id, '/api/email/templates/batch-delete', '批量删除邮件模板', 25, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-email-templates-batch-update', '批量更新模板', 'api', r.id, '/api/email/templates/batch-update', '批量更新邮件模板', 26, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-email' AND r.tenant_id = 1;

-- 13. ID生成器API (25个)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-list', '序列列表', 'api', r.id, '/api/idgenerator/sequences/list', '获取序列列表', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-detail', '序列详情', 'api', r.id, '/api/idgenerator/sequences/detail', '获取序列详情', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-create', '创建序列', 'api', r.id, '/api/idgenerator/sequences/create', '创建ID序列', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-update', '更新序列', 'api', r.id, '/api/idgenerator/sequences/update', '更新序列配置', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-delete', '删除序列', 'api', r.id, '/api/idgenerator/sequences/delete', '删除序列', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-pause', '暂停序列', 'api', r.id, '/api/idgenerator/sequences/pause', '暂停序列生成', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-resume', '恢复序列', 'api', r.id, '/api/idgenerator/sequences/resume', '恢复序列生成', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-sequences-allocate', '分配序列', 'api', r.id, '/api/idgenerator/sequences/allocate', '分配序列号段', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-batch-pause', '批量暂停', 'api', r.id, '/api/idgenerator/batch/pause', '批量暂停序列', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-batch-resume', '批量恢复', 'api', r.id, '/api/idgenerator/batch/resume', '批量恢复序列', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-generate', '生成单个ID', 'api', r.id, '/api/idgenerator/generate', '生成单个ID', 11, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-generate-batch', '批量生成ID', 'api', r.id, '/api/idgenerator/generate/batch', '批量生成ID', 12, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-business-types-list', '业务类型列表', 'api', r.id, '/api/idgenerator/business-types/list', '获取业务类型列表', 13, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-business-types-apply', '申请业务类型', 'api', r.id, '/api/idgenerator/business-types/apply', '申请业务类型', 14, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-business-types-applications', '业务类型申请', 'api', r.id, '/api/idgenerator/business-types/applications', '获取业务类型申请列表', 15, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-business-types-approve', '审批业务类型', 'api', r.id, '/api/idgenerator/business-types/approve', '审批业务类型申请', 16, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-stats', '系统统计', 'api', r.id, '/api/idgenerator/stats', '获取系统统计信息', 17, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-metrics', '序列指标', 'api', r.id, '/api/idgenerator/metrics', '获取序列性能指标', 18, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-health', '健康状态', 'api', r.id, '/api/idgenerator/health', '获取系统健康状态', 19, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-config', '系统配置', 'api', r.id, '/api/idgenerator/config', '获取系统配置', 20, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-config-update', '更新系统配置', 'api', r.id, '/api/idgenerator/config/update', '更新系统配置', 21, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-logs', '操作日志', 'api', r.id, '/api/idgenerator/logs', '获取操作日志', 22, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-validate-business-type', '验证业务类型', 'api', r.id, '/api/idgenerator/validate/business-type', '验证业务类型配置', 23, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-preview-sequence', '预览序列', 'api', r.id, '/api/idgenerator/preview/sequence', '预览序列配置效果', 24, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-idgenerator-export', '导出序列数据', 'api', r.id, '/api/idgenerator/export', '导出序列数据', 25, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-idgenerator' AND r.tenant_id = 1;

-- 14. 用户验证API (23个 = 10个配置 + 8个策略 + 5个服务)
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-create', '创建验证配置', 'api', r.id, '/api/v1/verification/management/configs/create', '创建验证配置', 1, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-list', '验证配置列表', 'api', r.id, '/api/v1/verification/management/configs', '获取验证配置列表', 2, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-detail', '验证配置详情', 'api', r.id, '/api/v1/verification/management/configs/{id}', '获取验证配置详情', 3, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-update', '更新验证配置', 'api', r.id, '/api/v1/verification/management/configs/{id}', '更新验证配置', 4, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-delete', '删除验证配置', 'api', r.id, '/api/v1/verification/management/configs/{id}', '删除验证配置', 5, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-enable', '启用验证配置', 'api', r.id, '/api/v1/verification/management/configs/{id}/enable', '启用验证配置', 6, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-disable', '禁用验证配置', 'api', r.id, '/api/v1/verification/management/configs/{id}/disable', '禁用验证配置', 7, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-batch', '批量更新配置', 'api', r.id, '/api/v1/verification/management/configs/batch', '批量更新验证配置', 8, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-copy-system', '复制系统配置', 'api', r.id, '/api/v1/verification/management/configs/copy-system', '复制系统验证配置', 9, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-configs-statistics', '配置统计', 'api', r.id, '/api/v1/verification/management/configs/statistics', '获取验证配置统计', 10, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-list', '验证策略列表', 'api', r.id, '/api/verification/policies/list', '获取验证策略列表', 11, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-get', '验证策略详情', 'api', r.id, '/api/verification/policies/get', '获取验证策略详情', 12, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-create', '创建验证策略', 'api', r.id, '/api/verification/policies/create', '创建验证策略', 13, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-update', '更新验证策略', 'api', r.id, '/api/verification/policies/update', '更新验证策略', 14, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-delete', '删除验证策略', 'api', r.id, '/api/verification/policies/delete', '删除验证策略', 15, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-set-status', '设置策略状态', 'api', r.id, '/api/verification/policies/set-status', '设置验证策略状态', 16, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-validate-expr', '验证表达式', 'api', r.id, '/api/verification/policies/validate-expr', '验证策略表达式', 17, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-policies-test-expr', '测试表达式', 'api', r.id, '/api/verification/policies/test-expr', '测试策略表达式', 18, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-send', '发送验证', 'api', r.id, '/api/verification/send', '发送验证码', 19, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-verify', '验证令牌', 'api', r.id, '/api/verification/verify', '验证令牌有效性', 20, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-resend', '重发验证', 'api', r.id, '/api/verification/resend', '重新发送验证码', 21, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-status', '检查令牌状态', 'api', r.id, '/api/verification/status', '检查令牌状态', 22, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'api-verification-statistics', '验证统计', 'api', r.id, '/api/verification/statistics', '获取验证统计信息', 23, true, true, NOW(), NOW() FROM resource r WHERE r.name = 'api-verification' AND r.tenant_id = 1;

-- =============================================
-- 统计信息：
-- API分类: 14个
-- 具体接口: 109个
-- 总计: 123个API资源
-- =============================================