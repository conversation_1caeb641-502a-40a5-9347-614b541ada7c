-- 前端路由资源表数据
-- 基于 menuData.json 和 routes.ts 生成的资源数据
-- 使用变量和分步执行避免MySQL子查询限制

-- 资源表字段说明：
-- name: 资源唯一标识名称
-- display_name: 资源显示名称
-- resource_type: 'menu'(菜单), 'page'(页面), 'button'(按钮), 'api'(接口)
-- parent_id: 父级资源ID，顶级菜单为NULL
-- path: 路由路径或API路径
-- sort_order: 排序顺序
-- is_system: 是否系统资源
-- assignable: 是否可分配给用户

-- =============================================
-- 1. 第一步：插入顶级菜单资源
-- =============================================

-- 仪表盘
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'dashboard', '仪表盘', 'menu', NULL, '/dashboard', 'DashboardOutlined', '系统仪表盘', 1, true, true, NOW(), NOW());

-- 用户管理模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'user-management', '用户管理', 'menu', NULL, NULL, 'TeamOutlined', '用户管理模块', 2, true, true, NOW(), NOW());

-- 系统管理模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'system-management', '系统管理', 'menu', NULL, NULL, 'SettingOutlined', '系统管理模块', 3, true, true, NOW(), NOW());

-- 文件系统模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'file-system', '文件系统', 'menu', NULL, NULL, 'FolderOutlined', '文件系统模块', 4, true, true, NOW(), NOW());

-- 通讯管理模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'communication', '通讯管理', 'menu', NULL, NULL, 'MailOutlined', '通讯管理模块', 5, true, true, NOW(), NOW());

-- 用户系统模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'user-system', '用户系统', 'menu', NULL, NULL, 'UserSwitchOutlined', '用户系统模块', 6, true, true, NOW(), NOW());

-- 工具箱模块
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'tools', '工具箱', 'menu', NULL, NULL, 'ThunderboltOutlined', '工具箱模块', 7, true, true, NOW(), NOW());

-- 租户管理
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'tenant', '租户管理', 'menu', NULL, '/tenant', 'BankOutlined', '租户管理', 8, true, true, NOW(), NOW());

-- =============================================
-- 2. 第二步：插入子菜单资源（需要获取父级ID）
-- =============================================

-- 用户管理子菜单
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'user', '用户列表', 'menu', r.id, '/user', 'UserOutlined', '用户列表管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'user-management' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'department', '组织架构', 'menu', r.id, '/department', 'ApartmentOutlined', '组织架构管理', 2, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'user-management' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'position', '职位管理', 'menu', r.id, '/position', 'SolutionOutlined', '职位管理', 3, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'user-management' AND r.tenant_id = 1;

-- 系统管理子菜单
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'role', '角色管理', 'menu', r.id, '/role', 'CrownOutlined', '角色管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'system-management' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'permission', '权限管理', 'menu', r.id, '/permission', 'SafetyCertificateOutlined', '权限管理', 2, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'system-management' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'permission-group', '权限组管理', 'menu', r.id, '/permission-group', 'TeamOutlined', '权限组管理', 3, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'system-management' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'resource', '资源管理', 'menu', r.id, '/resource', 'DatabaseOutlined', '资源管理', 4, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'system-management' AND r.tenant_id = 1;

-- 文件系统子菜单
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'file-system-files', '文件管理', 'menu', r.id, '/file-system/files', 'FileTextOutlined', '文件管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'file-system' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'file-system-scenes', '场景配置', 'menu', r.id, '/file-system/scenes', 'SettingOutlined', '场景配置', 2, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'file-system' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'file-system-upload', '文件上传', 'menu', r.id, '/file-system/upload', 'UploadOutlined', '文件上传', 3, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'file-system' AND r.tenant_id = 1;

-- 通讯管理子菜单  
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-accounts', '邮件账户', 'menu', r.id, '/email/accounts', 'UserOutlined', '邮件账户管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'communication' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates', '邮件模板', 'menu', r.id, '/email/templates', 'FileTextOutlined', '邮件模板管理', 2, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'communication' AND r.tenant_id = 1;

-- 用户系统子菜单
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'users-policies', '验证策略', 'menu', r.id, '/users/policies', 'SecurityScanOutlined', '验证策略管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'user-system' AND r.tenant_id = 1;

-- 工具箱子菜单
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'idgenerator', 'ID生成器', 'menu', r.id, '/idgenerator/sequences', 'KeyOutlined', 'ID生成器管理', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'tools' AND r.tenant_id = 1;

-- =============================================
-- 3. 第三步：插入页面资源
-- =============================================

-- 应用配置页面（迁移自租户配置）
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'app-config', '应用配置', 'page', r.id, '/application/:internalAppId/config', NULL, '应用配置页面', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'application' AND r.tenant_id = 1;

-- 文件详情页面
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'file-system-files-detail', '文件详情', 'page', r.id, '/file-system/files/:id', NULL, '文件详情页面', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'file-system-files' AND r.tenant_id = 1;

-- 邮件模板相关页面
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates-create', '创建模板', 'page', r.id, '/email/templates/create', NULL, '创建邮件模板页面', 1, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'email-templates' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates-edit', '编辑模板', 'page', r.id, '/email/templates/:id/edit', NULL, '编辑邮件模板页面', 2, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'email-templates' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates-detail', '模板详情', 'page', r.id, '/email/templates/:id', NULL, '邮件模板详情页面', 3, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'email-templates' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates-test', '模板测试', 'page', r.id, '/email/templates/test', NULL, '邮件模板测试页面', 4, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'email-templates' AND r.tenant_id = 1;

INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) 
SELECT 1, 'email-templates-demo', '模板演示', 'page', r.id, '/email/templates/demo', NULL, '邮件模板演示页面', 5, true, true, NOW(), NOW()
FROM resource r WHERE r.name = 'email-templates' AND r.tenant_id = 1;

-- 登录页面（公开访问，无父级）
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_public, public_level, is_system, assignable, created_at, updated_at) VALUES
(1, 'login', '登录页面', 'page', NULL, '/login', NULL, '用户登录页面', 0, true, 'anonymous', true, false, NOW(), NOW());

-- 菜单测试页面（开发用，不可分配）
INSERT INTO resource (tenant_id, name, display_name, resource_type, parent_id, path, icon, description, sort_order, is_system, assignable, created_at, updated_at) VALUES
(1, 'menu-test', '菜单测试', 'page', NULL, '/menu-test', NULL, '菜单测试页面(开发用)', 99, true, false, NOW(), NOW());