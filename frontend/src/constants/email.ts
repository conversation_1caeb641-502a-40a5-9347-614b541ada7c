// 邮件模板状态枚举
export const TEMPLATE_STATUSES = [
  { value: 1, label: '草稿', description: '模板处于草稿状态，可以编辑' },
  { value: 2, label: '已发布', description: '模板已发布，可以用于发送邮件' },
  { value: 3, label: '已停用', description: '模板已停用，不能用于发送邮件' },
  { value: 4, label: '已删除', description: '模板已删除' },
] as const;

// 邮件模板类型枚举
export const TEMPLATE_TYPES = [
  { value: 1, label: 'HTML模板', description: '支持HTML格式的邮件模板' },
  { value: 2, label: '纯文本模板', description: '纯文本格式的邮件模板' },
] as const;

// 获取状态标签
export const getTemplateStatusLabel = (status: number): string => {
  const statusItem = TEMPLATE_STATUSES.find(s => s.value === status);
  return statusItem ? statusItem.label : '未知状态';
};

// 获取类型标签
// 支持数字类型：1=HTML模板, 2=纯文本模板
// 支持字符串类型：'html'/'HTML'=HTML模板, 'text'/'TEXT'=纯文本模板
export const getTemplateTypeLabel = (type: number | string): string => {
  // 处理字符串类型
  if (typeof type === 'string') {
    if (type === 'html' || type === 'HTML') {
      return 'HTML模板';
    } else if (type === 'text' || type === 'TEXT') {
      return '纯文本模板';
    }
  }
  
  // 处理数字类型
  if (typeof type === 'number') {
    const typeItem = TEMPLATE_TYPES.find(t => t.value === type);
    return typeItem ? typeItem.label : '未知类型';
  }
  
  return '未知类型';
};

// 获取状态描述
export const getTemplateStatusDescription = (status: number): string => {
  const statusItem = TEMPLATE_STATUSES.find(s => s.value === status);
  return statusItem ? statusItem.description : '';
};

// 获取类型描述
export const getTemplateTypeDescription = (type: number | string): string => {
  // 处理字符串类型
  if (typeof type === 'string') {
    if (type === 'html' || type === 'HTML') {
      return '支持HTML格式的邮件模板';
    } else if (type === 'text' || type === 'TEXT') {
      return '纯文本格式的邮件模板';
    }
  }
  
  // 处理数字类型
  if (typeof type === 'number') {
    const typeItem = TEMPLATE_TYPES.find(t => t.value === type);
    return typeItem ? typeItem.description : '';
  }
  
  return '';
}; 