# 邮件模板字段展示更新总结

## 更新概述

根据服务端API接口规范，对前端邮件模板列表页面的字段展示进行了统一更新，确保前后端字段映射的一致性。

## 主要更新内容

### 1. 字段映射更新

| 前端字段名 | 服务端字段名 | 更新说明 |
|-----------|-------------|----------|
| `template_name` | `name` | 模板名称字段统一使用 `name` |
| `scenario_code` | `template_code` | 场景代码改为模板代码，使用 `template_code` |
| `template_type` | `type` | 模板类型使用数字类型：1=HTML, 2=TEXT |
| `body` | `html_content` | 邮件内容使用 `html_content` |

### 2. 模板列表页面更新

#### 模板名称列
- **更新前**: 显示模板名称 + 模板代码（重复信息）
- **更新后**: 仅显示模板名称，避免信息重复，并添加文本截断功能

```typescript
// 更新前
render: (text: string, record: EmailTemplate) => (
    <Space direction="vertical" size="small">
        <Text strong>{text || '未命名'}</Text>
        <Text type="secondary" style={{fontSize: '12px'}}>
            {record.template_code || '未设置模板代码'}
        </Text>
    </Space>
)

// 更新后
render: (text: string) => (
    <Tooltip title={text || '未命名'}>
        <Text strong style={{ 
            display: 'block',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '180px'
        }}>
            {text || '未命名'}
        </Text>
    </Tooltip>
)
```

#### 模板类型列
- **更新前**: 使用字符串类型（html, text, mixed）
- **更新后**: 使用数字类型（1=HTML, 2=TEXT）

```typescript
// 更新前
render: (type: string) => {
    const colors: Record<string, string> = {
        html: 'green',
        text: 'orange',
        mixed: 'purple',
    };
    return <Tag color={colors[type] || 'default'}>{type.toUpperCase()}</Tag>;
}

// 更新后
render: (type: number) => {
    const typeMap: Record<number, { label: string; color: string }> = {
        1: { label: 'HTML', color: 'green' },
        2: { label: 'TEXT', color: 'orange' },
    };
    const typeInfo = typeMap[type] || { label: 'UNKNOWN', color: 'default' };
    return <Tag color={typeInfo.color}>{typeInfo.label}</Tag>;
}
```

### 3. 文本截断优化

为了防止长文本影响列表样式，为关键字段添加了文本截断功能：

#### 模板名称列
- **宽度限制**: 200px，文本最大宽度 180px
- **截断方式**: 超出部分显示省略号（...）
- **完整显示**: 鼠标悬停时显示 Tooltip 提示完整内容
- **Tooltip样式**: 使用全局统一的 `GlobalTooltip` 组件

#### 模板代码列
- **宽度限制**: 150px，标签最大宽度 130px
- **截断方式**: 超出部分显示省略号（...）
- **完整显示**: 鼠标悬停时显示 Tooltip 提示完整内容
- **Tooltip样式**: 使用全局统一的 `GlobalTooltip` 组件

#### 样式实现
```css
/* 文本截断样式 */
overflow: hidden;
text-overflow: ellipsis;
white-space: nowrap;

/* Tooltip 样式 */
overlayStyle={{
    backgroundColor: '#000',
    color: '#fff',
    fontSize: '12px',
    padding: '8px 12px',
    borderRadius: '4px',
    maxWidth: '300px',
    wordBreak: 'break-word'
}}
```

### 4. 全局 Tooltip 配置

为了实现 Tooltip 颜色的全局统一管理，创建了以下配置：

#### 主题配置更新
在 `frontend/src/styles/theme.ts` 中添加了全局组件配置：

```typescript
// 全局组件配置
export const componentConfig = {
  tooltip: {
    color: '#1890ff',
    darkColor: '#1890ff',
    lightColor: '#1890ff',
  },
};
```

#### 全局 Tooltip 组件
创建了 `frontend/src/components/GlobalTooltip.tsx` 组件：

```typescript
const GlobalTooltip: React.FC<GlobalTooltipProps> = ({ 
  children, 
  title,
  placement = 'top',
  ...props 
}) => {
  const { isDarkMode } = useTheme();
  
  const tooltipColor = isDarkMode 
    ? componentConfig.tooltip.darkColor 
    : componentConfig.tooltip.lightColor;

  return (
    <Tooltip
      title={title}
      placement={placement}
      color={tooltipColor}
      {...props}
    >
      {children}
    </Tooltip>
  );
};
```

#### 使用方式
- **替换前**: `<Tooltip title="文本" color={tooltipColor}>`
- **替换后**: `<GlobalTooltip title="文本">`

#### 优势
1. **统一管理**: 所有 Tooltip 颜色在全局配置中统一管理
2. **主题适配**: 自动根据暗色/亮色主题选择合适颜色
3. **易于维护**: 修改颜色只需更新全局配置
4. **类型安全**: 提供完整的 TypeScript 类型支持

### 5. 筛选器更新

#### 模板类型筛选
- **更新前**: 使用字符串值（html, text, mixed）
- **更新后**: 使用数字值（1, 2）

```typescript
// 更新前
<Option value="html">HTML</Option>
<Option value="text">文本</Option>
<Option value="mixed">混合</Option>

// 更新后
<Option value="1">HTML</Option>
<Option value="2">文本</Option>
```

#### 搜索占位符
- **更新前**: "搜索模板名称、场景代码..."
- **更新后**: "搜索模板名称、模板代码..."

### 4. 类型安全修复

#### handleDelete 函数
- **问题**: `EmailTemplate.id` 是 `number` 类型，但 `handleDelete` 期望 `string` 类型
- **修复**: 修改函数参数类型为 `number`，在调用服务时转换为字符串

```typescript
// 修复前
const handleDelete = async (id: string) => {
    await emailTemplateService.deleteTemplate(id);
}

// 修复后
const handleDelete = async (id: number) => {
    await emailTemplateService.deleteTemplate(id.toString());
}
```

## 服务端API字段规范

根据服务端API文档，邮件模板的主要字段结构：

```json
{
  "id": *********,
  "tenant_id": *********,
  "template_code": "welcome_email",
  "account_id": 1001,
  "name": "欢迎邮件模板",
  "type": 1,
  "subject": "欢迎加入我们！",
  "html_content": "<html><body><h1>欢迎 {{user_name}}！</h1></body></html>",
  "plain_text_content": "欢迎 {{user_name}}！",
  "variables": {
    "user_name": {
      "type": "string",
      "required": true,
      "description": "用户姓名"
    }
  },
  "is_active": true,
  "version": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 更新文件列表

1. **frontend/src/pages/email/TemplateList.tsx**
   - 更新表格列定义
   - 修复类型错误
   - 更新筛选器配置
   - 使用全局 GlobalTooltip 组件

2. **frontend/src/types/email.ts**
   - 已包含正确的字段映射
   - 使用服务端API字段名

3. **frontend/src/styles/theme.ts**
   - 添加全局组件配置
   - 配置 Tooltip 主题样式

4. **frontend/src/components/GlobalTooltip.tsx**
   - 新建全局 Tooltip 组件
   - 支持主题切换
   - 统一颜色管理

## 验证要点

1. **字段显示**: 确保模板名称列只显示名称，不重复显示模板代码
2. **类型映射**: 确保模板类型正确显示（1=HTML, 2=TEXT）
3. **筛选功能**: 确保模板类型筛选使用正确的数字值
4. **搜索功能**: 确保搜索占位符文本准确
5. **删除功能**: 确保删除操作的类型安全
6. **文本截断**: 确保长文本正确截断，Tooltip 正常显示完整内容
7. **列表样式**: 确保表格列宽度合理，不会因长文本导致布局问题
8. **全局Tooltip**: 确保 GlobalTooltip 组件正常工作，颜色配置生效
9. **主题切换**: 确保暗色/亮色主题切换时 Tooltip 颜色正确更新

## 后续建议

1. **统一字段命名**: 建议在整个前端项目中统一使用服务端API的字段名
2. **类型定义**: 确保所有TypeScript接口与服务端API保持一致
3. **文档同步**: 更新相关文档，确保前后端字段映射的一致性
4. **测试覆盖**: 添加单元测试验证字段映射的正确性 