# 前端列表页面设计规范

## 核心原则
- **统一性**：所有列表页面必须保持一致的视觉设计和交互模式
- **简洁性**：移除冗余的描述性文字，突出核心功能
- **信息性**：通过统计信息快速了解数据整体状况
- **响应式**：支持不同屏幕尺寸和深色模式

## 页面结构规范

### 1. 统计信息区域（非必须）
- 位置：页面顶部，标题区域
- 布局：4个统计卡片，水平排列
- 内容：总数量、启用数量、禁用数量、分类数量
- 样式：统一的图标、颜色、字体大小

```typescript
// 统计信息示例
const stats = [
  { title: '总数量', value: total, icon: <MainIcon />, color: '#1890ff' },
  { title: '启用数量', value: activeCount, icon: <CheckCircleOutlined />, color: '#52c41a' },
  { title: '禁用数量', value: inactiveCount, icon: <CloseCircleOutlined />, color: '#ff4d4f' },
  { title: '分类数量', value: categoryCount, icon: <AppstoreOutlined />, color: '#722ed1' },
];
```

### 2. 搜索筛选区域（根据实际情况判断是否需要展示）
- 位置：统计信息下方
- 样式：统一的Card样式，圆角、阴影、边框
- 布局：响应式Grid布局
- 功能：实时搜索，无需点击搜索按钮

```typescript
// 搜索区域样式
const cardStyle = {
  borderRadius: 12,
  borderStyle: 'none',
  background: isDarkMode ? '#1f1f1f' : '#ffffff',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
  marginBottom: 24,
};
```

### 3. 表格区域（必须）
- 位置：搜索区域下方
- 样式：与搜索区域相同的Card样式
- 功能：分页、排序、操作按钮
- 列设计：合并相关信息，避免冗余子标题

## 表格列设计规范

### 1. 信息列设计
- **主要信息列**：头像 + 名称 + 标识
- **详细信息列**：合并相关属性，使用标签展示
- **状态列**：使用颜色标签区分状态
- **时间列**：统一格式，简洁显示
- **操作列**：统一的按钮样式和tooltip

### 2. 列合并原则
- 避免创建过多独立列
- 将相关信息合并到一个列中
- 使用render函数自定义列内容
- 保持视觉层次清晰

```typescript
// 列设计示例
const columns = [
  {
    title: '主要信息',
    key: 'main_info',
    render: (record) => (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <Avatar size={32} icon={<MainIcon />} />
        <div>
          <div style={{ fontWeight: 500 }}>{record.display_name}</div>
          <div style={{ fontSize: 12, color: '#8c8c8c' }}>{record.name}</div>
        </div>
      </div>
    ),
  },
  {
    title: '详细信息',
    key: 'detail_info',
    render: (record) => (
      <div>
        <div style={{ marginBottom: 4 }}>
          <Tag color="blue">{record.category}</Tag>
        </div>
        <div>
          <Tag color="cyan">{record.type1}</Tag>
          <Tag color="magenta" style={{ marginLeft: 4 }}>{record.type2}</Tag>
        </div>
      </div>
    ),
  },
];
```

## 交互设计规范

### 1. 搜索交互
- 实时搜索：输入时立即触发搜索
- 自动重置分页：搜索时重置到第一页
- 清空功能：支持一键清空搜索条件

```typescript
// 搜索处理
const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setSearchKeyword(e.target.value);
  setCurrentPage(1); // 重置分页
};
```

### 2. 操作按钮
- 统一使用text类型按钮
- 添加tooltip提示
- 使用统一的颜色主题
- 支持深色模式

```typescript
// 操作按钮样式
const actionButtonStyle = {
  color: isDarkMode ? '#40a9ff' : '#1890ff',
  fontWeight: 500,
};

const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';
```

### 3. 分页设计
- 显示总数和当前范围
- 支持快速跳转
- 支持页面大小调整
- 响应式设计

## 主题支持规范

### 1. 深色模式
- 所有组件必须支持深色模式
- 使用主题变量控制颜色
- 保持足够的对比度

### 2. 响应式设计
- 支持xs、sm、md、lg、xl屏幕尺寸
- 使用Grid布局自适应
- 移动端友好的交互

## 性能优化规范

### 1. 数据加载
- 使用loading状态
- 支持分页加载
- 避免一次性加载大量数据

### 2. 渲染优化
- 使用React.memo优化组件
- 合理使用useMemo和useCallback
- 避免不必要的重渲染

## 代码组织规范

### 1. 组件结构
```typescript
const ListPage: React.FC = () => {
  // 1. 状态定义
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  
  // 2. 统计数据
  const stats = [/* 统计信息 */];
  
  // 3. 事件处理
  const handleSearch = () => { /* 搜索处理 */ };
  const handleAdd = () => { /* 新增处理 */ };
  
  // 4. 表格列定义
  const columns = [/* 列定义 */];
  
  // 5. 渲染
  return (
    <div>
      {/* 统计信息区域 */}
      {/* 搜索筛选区域 */}
      {/* 表格区域 */}
    </div>
  );
};
```

### 2. 样式定义
- 使用统一的样式变量
- 支持主题切换
- 保持样式的一致性

## 检查清单

在创建或修改列表页面时，必须检查：

- [ ] 是否包含统计信息区域
- [ ] 是否移除了描述性文字
- [ ] 是否使用了统一的Card样式
- [ ] 是否支持深色模式
- [ ] 是否支持响应式布局
- [ ] 是否实现了实时搜索
- [ ] 是否统一了操作按钮样式
- [ ] 是否优化了表格列设计
- [ ] 是否添加了适当的loading状态
- [ ] 是否遵循了性能优化规范

## 参考页面

- 用户管理页面：`src/pages/user/UserPage.tsx`
- 角色管理页面：`src/pages/role/RolePage.tsx`
- API管理页面：`src/pages/api-management/ApiManagementPage.tsx`

这些页面都遵循了上述规范，可以作为新页面开发的参考模板。

## 禁止事项

- ❌ 不要在页面顶部添加描述性文字
- ❌ 不要创建过多独立的表格列
- ❌ 不要使用不一致的样式
- ❌ 不要忽略深色模式支持
- ❌ 不要忽略响应式设计
- ❌ 不要使用同步搜索（必须实时搜索）
- ❌ 不要使用不一致的操作按钮样式
description:
globs:
alwaysApply: false
---
