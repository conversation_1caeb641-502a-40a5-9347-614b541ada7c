package logging

import (
	"context"
	"os"
	"time"

	"gitee.com/heiyee/platforms/common/types"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LogLevel represents log level
type LogLevel string

const (
	DebugLevel LogLevel = "debug"
	InfoLevel  LogLevel = "info"
	WarnLevel  LogLevel = "warn"
	ErrorLevel LogLevel = "error"
	FatalLevel LogLevel = "fatal"
)

// LogConfig represents logging configuration
type LogConfig struct {
	Level       LogLevel `json:"level"`
	Format      string   `json:"format"`   // json, console
	Output      string   `json:"output"`   // stdout, stderr, file path
	MaxSize     int      `json:"max_size"` // megabytes
	MaxBackups  int      `json:"max_backups"`
	MaxAge      int      `json:"max_age"` // days
	Compress    bool     `json:"compress"`
	ServiceName string   `json:"service_name"`
	Version     string   `json:"version"`
}

// DefaultLogConfig returns default logging configuration
func DefaultLogConfig() *LogConfig {
	return &LogConfig{
		Level:       InfoLevel,
		Format:      "json",
		Output:      "stdout",
		MaxSize:     100,
		MaxBackups:  3,
		MaxAge:      28,
		Compress:    true,
		ServiceName: "unknown",
		Version:     "1.0.0",
	}
}

// ZapLogger implements Logger interface using Zap
type ZapLogger struct {
	logger *zap.Logger
	config *LogConfig
}

// NewZapLogger creates a new Zap logger
func NewZapLogger(config *LogConfig) (*ZapLogger, error) {
	if config == nil {
		config = DefaultLogConfig()
	}

	// Configure log level
	level := zap.InfoLevel
	switch config.Level {
	case DebugLevel:
		level = zap.DebugLevel
	case InfoLevel:
		level = zap.InfoLevel
	case WarnLevel:
		level = zap.WarnLevel
	case ErrorLevel:
		level = zap.ErrorLevel
	case FatalLevel:
		level = zap.FatalLevel
	}

	// Configure encoder
	var encoderConfig zapcore.EncoderConfig
	if config.Format == "console" {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05")
	} else {
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.TimeKey = "timestamp"
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	}

	var encoder zapcore.Encoder
	if config.Format == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// Configure output
	var writer zapcore.WriteSyncer
	switch config.Output {
	case "stdout":
		writer = zapcore.AddSync(os.Stdout)
	case "stderr":
		writer = zapcore.AddSync(os.Stderr)
	default:
		// File output would need file rotation logic
		writer = zapcore.AddSync(os.Stdout)
	}

	core := zapcore.NewCore(encoder, writer, level)
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	// Add global fields
	logger = logger.With(
		zap.String("service", config.ServiceName),
		zap.String("version", config.Version),
	)

	return &ZapLogger{
		logger: logger,
		config: config,
	}, nil
}

// Debug logs debug message
func (l *ZapLogger) Debug(ctx context.Context, msg string, fields ...types.Field) {
	l.logWithContext(ctx, l.logger.Debug, msg, fields...)
}

// Info logs info message
func (l *ZapLogger) Info(ctx context.Context, msg string, fields ...types.Field) {
	l.logWithContext(ctx, l.logger.Info, msg, fields...)
}

// Warn logs warning message
func (l *ZapLogger) Warn(ctx context.Context, msg string, fields ...types.Field) {
	l.logWithContext(ctx, l.logger.Warn, msg, fields...)
}

// Error logs error message
func (l *ZapLogger) Error(ctx context.Context, msg string, fields ...types.Field) {
	l.logWithContext(ctx, l.logger.Error, msg, fields...)
}

// Fatal logs fatal message and exits
func (l *ZapLogger) Fatal(ctx context.Context, msg string, fields ...types.Field) {
	l.logWithContext(ctx, l.logger.Fatal, msg, fields...)
}

// With returns a logger with additional fields
func (l *ZapLogger) With(fields ...types.Field) types.Logger {
	zapFields := make([]zap.Field, len(fields))
	for i, field := range fields {
		zapFields[i] = l.convertField(field)
	}

	return &ZapLogger{
		logger: l.logger.With(zapFields...),
		config: l.config,
	}
}

// WithContext returns a logger with context information
func (l *ZapLogger) WithContext(ctx context.Context) types.Logger {
	fields := l.extractContextFields(ctx)
	return l.With(fields...)
}

// logWithContext logs message with context fields
func (l *ZapLogger) logWithContext(ctx context.Context, logFunc func(string, ...zap.Field), msg string, fields ...types.Field) {
	zapFields := make([]zap.Field, 0, len(fields)+10)

	// Add context fields
	contextFields := l.extractContextFields(ctx)
	for _, field := range contextFields {
		zapFields = append(zapFields, l.convertField(field))
	}

	// Add provided fields
	for _, field := range fields {
		zapFields = append(zapFields, l.convertField(field))
	}

	logFunc(msg, zapFields...)
}

// extractContextFields extracts fields from context
func (l *ZapLogger) extractContextFields(ctx context.Context) []types.Field {
	var fields []types.Field

	// Extract trace ID if available
	if traceID := getTraceIDFromContext(ctx); traceID != "" {
		fields = append(fields, types.Field{Key: "trace_id", Value: traceID})
	}

	// Extract span ID if available
	if spanID := getSpanIDFromContext(ctx); spanID != "" {
		fields = append(fields, types.Field{Key: "span_id", Value: spanID})
	}

	// Extract user context if available
	if userCtx := getUserContextFromContext(ctx); userCtx != nil {
		fields = append(fields,
			types.Field{Key: "user_id", Value: userCtx.UserID},
			types.Field{Key: "tenant_id", Value: userCtx.TenantID},
			types.Field{Key: "username", Value: userCtx.Username},
		)
	}

	// Extract request ID if available
	if requestID := getRequestIDFromContext(ctx); requestID != "" {
		fields = append(fields, types.Field{Key: "request_id", Value: requestID})
	}

	return fields
}

// convertField converts types.Field to zap.Field
func (l *ZapLogger) convertField(field types.Field) zap.Field {
	switch v := field.Value.(type) {
	case string:
		return zap.String(field.Key, v)
	case int:
		return zap.Int(field.Key, v)
	case int64:
		return zap.Int64(field.Key, v)
	case float64:
		return zap.Float64(field.Key, v)
	case bool:
		return zap.Bool(field.Key, v)
	case time.Duration:
		return zap.Duration(field.Key, v)
	case time.Time:
		return zap.Time(field.Key, v)
	case error:
		return zap.Error(v)
	default:
		return zap.Any(field.Key, v)
	}
}

// Helper functions to extract context information
// These would need to be implemented based on your context structure

func getTraceIDFromContext(ctx context.Context) string {
	// Implementation depends on your tracing setup
	if val := ctx.Value("trace_id"); val != nil {
		if traceID, ok := val.(string); ok {
			return traceID
		}
	}
	return ""
}

func getSpanIDFromContext(ctx context.Context) string {
	// Implementation depends on your tracing setup
	if val := ctx.Value("span_id"); val != nil {
		if spanID, ok := val.(string); ok {
			return spanID
		}
	}
	return ""
}

func getUserContextFromContext(ctx context.Context) *types.UserContext {
	// Implementation depends on your auth setup
	if val := ctx.Value("user_context"); val != nil {
		if userCtx, ok := val.(*types.UserContext); ok {
			return userCtx
		}
	}
	return nil
}

func getRequestIDFromContext(ctx context.Context) string {
	// Implementation depends on your request ID setup
	if val := ctx.Value("request_id"); val != nil {
		if requestID, ok := val.(string); ok {
			return requestID
		}
	}
	return ""
}

// Global logger instance
var globalLogger types.Logger

// InitGlobalLogger initializes the global logger
func InitGlobalLogger(config *LogConfig) error {
	logger, err := NewZapLogger(config)
	if err != nil {
		return err
	}
	globalLogger = logger
	return nil
}

// GetGlobalLogger returns the global logger
func GetGlobalLogger() types.Logger {
	if globalLogger == nil {
		// Create default logger if not initialized
		logger, _ := NewZapLogger(DefaultLogConfig())
		globalLogger = logger
	}
	return globalLogger
}

// Helper functions for easy logging
func Debug(ctx context.Context, msg string, fields ...types.Field) {
	GetGlobalLogger().Debug(ctx, msg, fields...)
}

func Info(ctx context.Context, msg string, fields ...types.Field) {
	GetGlobalLogger().Info(ctx, msg, fields...)
}

func Warn(ctx context.Context, msg string, fields ...types.Field) {
	GetGlobalLogger().Warn(ctx, msg, fields...)
}

func Error(ctx context.Context, msg string, fields ...types.Field) {
	GetGlobalLogger().Error(ctx, msg, fields...)
}

func Fatal(ctx context.Context, msg string, fields ...types.Field) {
	GetGlobalLogger().Fatal(ctx, msg, fields...)
}

// Field helper functions
func String(key, value string) types.Field {
	return types.Field{Key: key, Value: value}
}

func Int(key string, value int) types.Field {
	return types.Field{Key: key, Value: value}
}

func Int64(key string, value int64) types.Field {
	return types.Field{Key: key, Value: value}
}

func Float64(key string, value float64) types.Field {
	return types.Field{Key: key, Value: value}
}

func Bool(key string, value bool) types.Field {
	return types.Field{Key: key, Value: value}
}

func Duration(key string, value time.Duration) types.Field {
	return types.Field{Key: key, Value: value}
}

func Time(key string, value time.Time) types.Field {
	return types.Field{Key: key, Value: value}
}

func Err(err error) types.Field {
	return types.Field{Key: "error", Value: err}
}

func Any(key string, value interface{}) types.Field {
	return types.Field{Key: key, Value: value}
}
