package grpc

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"gitee.com/heiyee/platforms/common/types"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"
)

// LegacyServerConfig represents legacy gRPC server configuration (for backward compatibility)
type LegacyServerConfig struct {
	Address          string                 `json:"address"`
	Port             int                    `json:"port"`
	MaxRecvMsgSize   int                    `json:"max_recv_msg_size"`
	MaxSendMsgSize   int                    `json:"max_send_msg_size"`
	EnableReflection bool                   `json:"enable_reflection"`
	EnableHealth     bool                   `json:"enable_health"`
	EnableTracing    bool                   `json:"enable_tracing"`
	KeepaliveParams  *LegacyKeepaliveParams `json:"keepalive_params"`
}

// LegacyKeepaliveParams represents legacy keepalive configuration
type LegacyKeepaliveParams struct {
	MaxConnectionIdle     time.Duration `json:"max_connection_idle"`
	MaxConnectionAge      time.Duration `json:"max_connection_age"`
	MaxConnectionAgeGrace time.Duration `json:"max_connection_age_grace"`
	Time                  time.Duration `json:"time"`
	Timeout               time.Duration `json:"timeout"`
}

// DefaultLegacyServerConfig returns default legacy gRPC server configuration
func DefaultLegacyServerConfig() *LegacyServerConfig {
	return &LegacyServerConfig{
		Address:          "0.0.0.0",
		Port:             50051,
		MaxRecvMsgSize:   4 * 1024 * 1024, // 4MB
		MaxSendMsgSize:   4 * 1024 * 1024, // 4MB
		EnableReflection: true,
		EnableHealth:     true,
		EnableTracing:    true,
		KeepaliveParams: &LegacyKeepaliveParams{
			MaxConnectionIdle:     time.Minute * 5,
			MaxConnectionAge:      time.Hour,
			MaxConnectionAgeGrace: time.Minute * 5,
			Time:                  time.Minute,
			Timeout:               time.Second * 20,
		},
	}
}

// LegacyServer represents a legacy gRPC server wrapper (for backward compatibility)
type LegacyServer struct {
	config       *LegacyServerConfig
	server       *grpc.Server
	registry     types.ServiceRegistry
	logger       types.Logger
	healthServer *health.Server
	serviceInfo  *types.ServiceInfo
	interceptors LegacyServerInterceptors
}

// LegacyServerInterceptors contains server interceptors
type LegacyServerInterceptors struct {
	UnaryInterceptors  []grpc.UnaryServerInterceptor
	StreamInterceptors []grpc.StreamServerInterceptor
}

// NewLegacyServer creates a new legacy gRPC server
func NewLegacyServer(config *LegacyServerConfig, registry types.ServiceRegistry, logger types.Logger) *LegacyServer {
	if config == nil {
		config = DefaultLegacyServerConfig()
	}

	s := &LegacyServer{
		config:   config,
		registry: registry,
		logger:   logger,
	}

	s.setupServer()
	return s
}

// setupServer sets up the gRPC server with interceptors and options
func (s *LegacyServer) setupServer() {
	opts := []grpc.ServerOption{
		grpc.MaxRecvMsgSize(s.config.MaxRecvMsgSize),
		grpc.MaxSendMsgSize(s.config.MaxSendMsgSize),
	}

	// Add keepalive parameters
	if s.config.KeepaliveParams != nil {
		opts = append(opts, grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     s.config.KeepaliveParams.MaxConnectionIdle,
			MaxConnectionAge:      s.config.KeepaliveParams.MaxConnectionAge,
			MaxConnectionAgeGrace: s.config.KeepaliveParams.MaxConnectionAgeGrace,
			Time:                  s.config.KeepaliveParams.Time,
			Timeout:               s.config.KeepaliveParams.Timeout,
		}))
	}

	// Setup interceptors
	unaryInterceptors := s.interceptors.UnaryInterceptors
	streamInterceptors := s.interceptors.StreamInterceptors

	// Add default interceptors
	unaryInterceptors = append(unaryInterceptors, s.unaryLoggingInterceptor)
	streamInterceptors = append(streamInterceptors, s.streamLoggingInterceptor)

	// Add tracing interceptor if enabled
	if s.config.EnableTracing {
		unaryInterceptors = append(unaryInterceptors, otelgrpc.UnaryServerInterceptor())
		streamInterceptors = append(streamInterceptors, otelgrpc.StreamServerInterceptor())
	}

	// Chain interceptors
	if len(unaryInterceptors) > 0 {
		opts = append(opts, grpc.ChainUnaryInterceptor(unaryInterceptors...))
	}
	if len(streamInterceptors) > 0 {
		opts = append(opts, grpc.ChainStreamInterceptor(streamInterceptors...))
	}

	s.server = grpc.NewServer(opts...)

	// Setup health server if enabled
	if s.config.EnableHealth {
		s.healthServer = health.NewServer()
		grpc_health_v1.RegisterHealthServer(s.server, s.healthServer)
	}

	// Setup reflection if enabled
	if s.config.EnableReflection {
		reflection.Register(s.server)
	}
}

// AddUnaryInterceptor adds a unary interceptor
func (s *LegacyServer) AddUnaryInterceptor(interceptor grpc.UnaryServerInterceptor) {
	s.interceptors.UnaryInterceptors = append(s.interceptors.UnaryInterceptors, interceptor)
}

// AddStreamInterceptor adds a stream interceptor
func (s *LegacyServer) AddStreamInterceptor(interceptor grpc.StreamServerInterceptor) {
	s.interceptors.StreamInterceptors = append(s.interceptors.StreamInterceptors, interceptor)
}

// RegisterService registers a gRPC service
func (s *LegacyServer) RegisterService(desc *grpc.ServiceDesc, impl interface{}) {
	s.server.RegisterService(desc, impl)
}

// Start starts the gRPC server
func (s *LegacyServer) Start(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.config.Address, s.config.Port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	// Register service if registry is provided
	if s.registry != nil && s.serviceInfo != nil {
		if err := s.registry.Register(ctx, s.serviceInfo); err != nil {
			s.logger.Error(ctx, "Failed to register service", types.Field{Key: "error", Value: err})
		}
	}

	s.logger.Info(ctx, "Starting gRPC server",
		types.Field{Key: "address", Value: addr},
		types.Field{Key: "reflection", Value: s.config.EnableReflection},
		types.Field{Key: "health", Value: s.config.EnableHealth},
	)

	// Set health status to serving
	if s.healthServer != nil {
		s.healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)
	}

	return s.server.Serve(listener)
}

// Stop stops the gRPC server gracefully
func (s *LegacyServer) Stop(ctx context.Context) error {
	// Set health status to not serving
	if s.healthServer != nil {
		s.healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_NOT_SERVING)
	}

	// Deregister service if registry is provided
	if s.registry != nil && s.serviceInfo != nil {
		if err := s.registry.Deregister(ctx, s.serviceInfo.ID); err != nil {
			s.logger.Error(ctx, "Failed to deregister service", types.Field{Key: "error", Value: err})
		}
	}

	s.logger.Info(ctx, "Stopping gRPC server")

	// Graceful stop with timeout
	done := make(chan struct{})
	go func() {
		s.server.GracefulStop()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		s.server.Stop()
		return ctx.Err()
	}
}

// SetServiceInfo sets the service information for registration
func (s *LegacyServer) SetServiceInfo(serviceInfo *types.ServiceInfo) {
	s.serviceInfo = serviceInfo
}

// GetServer returns the underlying gRPC server
func (s *LegacyServer) GetServer() *grpc.Server {
	return s.server
}

// Logging interceptors
func (s *LegacyServer) unaryLoggingInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	start := time.Now()

	s.logger.Debug(ctx, "gRPC request started",
		types.Field{Key: "method", Value: info.FullMethod},
		types.Field{Key: "type", Value: "unary"},
	)

	resp, err := handler(ctx, req)

	duration := time.Since(start)

	if err != nil {
		s.logger.Error(ctx, "gRPC request failed",
			types.Field{Key: "method", Value: info.FullMethod},
			types.Field{Key: "duration", Value: duration},
			types.Field{Key: "error", Value: err},
		)
	} else {
		s.logger.Debug(ctx, "gRPC request completed",
			types.Field{Key: "method", Value: info.FullMethod},
			types.Field{Key: "duration", Value: duration},
		)
	}

	return resp, err
}

func (s *LegacyServer) streamLoggingInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	start := time.Now()

	s.logger.Debug(ss.Context(), "gRPC stream started",
		types.Field{Key: "method", Value: info.FullMethod},
		types.Field{Key: "type", Value: "stream"},
	)

	err := handler(srv, ss)

	duration := time.Since(start)

	if err != nil {
		s.logger.Error(ss.Context(), "gRPC stream failed",
			types.Field{Key: "method", Value: info.FullMethod},
			types.Field{Key: "duration", Value: duration},
			types.Field{Key: "error", Value: err},
		)
	} else {
		s.logger.Debug(ss.Context(), "gRPC stream completed",
			types.Field{Key: "method", Value: info.FullMethod},
			types.Field{Key: "duration", Value: duration},
		)
	}

	return err
}

// LegacyClientConfig represents legacy gRPC client configuration
type LegacyClientConfig struct {
	MaxRecvMsgSize    int           `json:"max_recv_msg_size"`
	MaxSendMsgSize    int           `json:"max_send_msg_size"`
	EnableTracing     bool          `json:"enable_tracing"`
	ConnectTimeout    time.Duration `json:"connect_timeout"`
	EnableRetry       bool          `json:"enable_retry"`
	MaxRetryAttempts  int           `json:"max_retry_attempts"`
	BackoffMultiplier float64       `json:"backoff_multiplier"`
}

// DefaultLegacyClientConfig returns default legacy gRPC client configuration
func DefaultLegacyClientConfig() *LegacyClientConfig {
	return &LegacyClientConfig{
		MaxRecvMsgSize:    4 * 1024 * 1024, // 4MB
		MaxSendMsgSize:    4 * 1024 * 1024, // 4MB
		EnableTracing:     true,
		ConnectTimeout:    time.Second * 10,
		EnableRetry:       true,
		MaxRetryAttempts:  3,
		BackoffMultiplier: 1.5,
	}
}

// LegacyClientManager manages legacy gRPC client connections
type LegacyClientManager struct {
	config      *LegacyClientConfig
	registry    types.ServiceRegistry
	logger      types.Logger
	connections map[string]*grpc.ClientConn
	mutex       sync.RWMutex
}

// NewLegacyClientManager creates a new legacy gRPC client manager
func NewLegacyClientManager(config *LegacyClientConfig, registry types.ServiceRegistry, logger types.Logger) *LegacyClientManager {
	if config == nil {
		config = DefaultLegacyClientConfig()
	}

	return &LegacyClientManager{
		config:      config,
		registry:    registry,
		logger:      logger,
		connections: make(map[string]*grpc.ClientConn),
	}
}

// Connect creates a connection to a service
func (cm *LegacyClientManager) Connect(ctx context.Context, serviceName string) (*grpc.ClientConn, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// Check if connection already exists
	if conn, exists := cm.connections[serviceName]; exists {
		// Check if connection is still healthy
		if conn.GetState().String() != "SHUTDOWN" {
			return conn, nil
		}
		// Remove unhealthy connection
		delete(cm.connections, serviceName)
	}

	// Discover service
	services, err := cm.registry.Discover(ctx, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to discover service %s: %w", serviceName, err)
	}

	if len(services) == 0 {
		return nil, fmt.Errorf("no instances found for service %s", serviceName)
	}

	// Use first available service instance
	service := services[0]
	target := fmt.Sprintf("%s:%d", service.Address, service.Port)

	// Setup dial options
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(cm.config.MaxRecvMsgSize)),
	}

	// Add tracing interceptor if enabled
	if cm.config.EnableTracing {
		opts = append(opts,
			grpc.WithUnaryInterceptor(otelgrpc.UnaryClientInterceptor()),
			grpc.WithStreamInterceptor(otelgrpc.StreamClientInterceptor()),
		)
	}

	// Add logging interceptors
	opts = append(opts,
		grpc.WithUnaryInterceptor(cm.unaryLoggingInterceptor),
		grpc.WithStreamInterceptor(cm.streamLoggingInterceptor),
	)

	// Create connection
	conn, err := grpc.DialContext(ctx, target, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", target, err)
	}

	cm.connections[serviceName] = conn
	cm.logger.Info(ctx, "Connected to gRPC service",
		types.Field{Key: "service", Value: serviceName},
		types.Field{Key: "target", Value: target},
	)

	return conn, nil
}

// GetConnection returns an existing connection or creates a new one
func (cm *LegacyClientManager) GetConnection(ctx context.Context, serviceName string) (*grpc.ClientConn, error) {
	return cm.Connect(ctx, serviceName)
}

// Close closes a specific connection
func (cm *LegacyClientManager) Close(serviceName string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[serviceName]; exists {
		delete(cm.connections, serviceName)
		return conn.Close()
	}

	return nil
}

// CloseAll closes all connections
func (cm *LegacyClientManager) CloseAll() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var lastErr error
	for serviceName, conn := range cm.connections {
		if err := conn.Close(); err != nil {
			lastErr = err
			cm.logger.Error(context.Background(), "Failed to close connection",
				types.Field{Key: "service", Value: serviceName},
				types.Field{Key: "error", Value: err},
			)
		}
	}

	cm.connections = make(map[string]*grpc.ClientConn)
	return lastErr
}

// Client logging interceptors
func (cm *LegacyClientManager) unaryLoggingInterceptor(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	start := time.Now()

	cm.logger.Debug(ctx, "gRPC client request started",
		types.Field{Key: "method", Value: method},
		types.Field{Key: "target", Value: cc.Target()},
	)

	err := invoker(ctx, method, req, reply, cc, opts...)

	duration := time.Since(start)

	if err != nil {
		cm.logger.Error(ctx, "gRPC client request failed",
			types.Field{Key: "method", Value: method},
			types.Field{Key: "target", Value: cc.Target()},
			types.Field{Key: "duration", Value: duration},
			types.Field{Key: "error", Value: err},
		)
	} else {
		cm.logger.Debug(ctx, "gRPC client request completed",
			types.Field{Key: "method", Value: method},
			types.Field{Key: "target", Value: cc.Target()},
			types.Field{Key: "duration", Value: duration},
		)
	}

	return err
}

func (cm *LegacyClientManager) streamLoggingInterceptor(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	start := time.Now()

	cm.logger.Debug(ctx, "gRPC client stream started",
		types.Field{Key: "method", Value: method},
		types.Field{Key: "target", Value: cc.Target()},
	)

	stream, err := streamer(ctx, desc, cc, method, opts...)

	if err != nil {
		duration := time.Since(start)
		cm.logger.Error(ctx, "gRPC client stream failed",
			types.Field{Key: "method", Value: method},
			types.Field{Key: "target", Value: cc.Target()},
			types.Field{Key: "duration", Value: duration},
			types.Field{Key: "error", Value: err},
		)
		return nil, err
	}

	// Wrap stream to log completion
	return &legacyLoggingClientStream{
		ClientStream: stream,
		method:       method,
		target:       cc.Target(),
		start:        start,
		logger:       cm.logger,
	}, nil
}

// legacyLoggingClientStream wraps grpc.ClientStream to add logging
type legacyLoggingClientStream struct {
	grpc.ClientStream
	method string
	target string
	start  time.Time
	logger types.Logger
}

func (s *legacyLoggingClientStream) Context() context.Context {
	ctx := s.ClientStream.Context()

	// Log when stream is done
	go func() {
		<-ctx.Done()
		duration := time.Since(s.start)
		if err := ctx.Err(); err != nil && err != context.Canceled {
			s.logger.Error(ctx, "gRPC client stream failed",
				types.Field{Key: "method", Value: s.method},
				types.Field{Key: "target", Value: s.target},
				types.Field{Key: "duration", Value: duration},
				types.Field{Key: "error", Value: err},
			)
		} else {
			s.logger.Debug(ctx, "gRPC client stream completed",
				types.Field{Key: "method", Value: s.method},
				types.Field{Key: "target", Value: s.target},
				types.Field{Key: "duration", Value: duration},
			)
		}
	}()

	return ctx
}

// Helper function to extract service name from method
func extractServiceName(method string) string {
	parts := strings.Split(method, "/")
	if len(parts) >= 2 {
		return parts[1]
	}
	return "unknown"
}
