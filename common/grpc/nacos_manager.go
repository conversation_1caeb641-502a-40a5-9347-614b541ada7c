package grpc

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"

	"gitee.com/heiyee/platforms/common/config"
	"gitee.com/heiyee/platforms/common/types"
)

// ServiceConfig 服务配置（从Nacos配置中心读取）
type ServiceConfig struct {
	// 服务端配置
	Port           int               `json:"port" toml:"port"`
	ServiceName    string            `json:"service_name" toml:"service_name"`
	Group          string            `json:"group" toml:"group"`
	Namespace      string            `json:"namespace" toml:"namespace"`
	Weight         float64           `json:"weight" toml:"weight"`
	EnableRegister bool              `json:"enable_register" toml:"enable_register"`
	Metadata       map[string]string `json:"metadata" toml:"metadata"`
	LocalIP        string            `json:"local_ip,omitempty" toml:"local_ip,omitempty"`

	// 健康检查配置
	HealthCheck HealthCheckConfig `json:"health_check" toml:"health_check"`

	// 客户端订阅配置
	Subscriptions []SubscriptionConfig `json:"subscriptions" toml:"subscriptions"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled  bool          `json:"enabled" toml:"enabled"`
	Interval time.Duration `json:"interval" toml:"interval"`
	Timeout  time.Duration `json:"timeout" toml:"timeout"`
}

// SubscriptionConfig 服务订阅配置
type SubscriptionConfig struct {
	ServiceName    string               `json:"service_name" toml:"service_name"`
	Group          string               `json:"group" toml:"group"`
	Namespace      string               `json:"namespace" toml:"namespace"`
	Strategy       string               `json:"strategy" toml:"strategy"` // random, round_robin, weighted
	Subscribe      bool                 `json:"subscribe" toml:"subscribe"`
	Retry          RetryConfig          `json:"retry" toml:"retry"`
	HealthCheck    HealthCheckConfig    `json:"health_check" toml:"health_check"`
	CircuitBreaker CircuitBreakerConfig `json:"circuit_breaker" toml:"circuit_breaker"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	Enabled       bool          `json:"enabled" toml:"enabled"`
	MaxRetries    int           `json:"max_retries" toml:"max_retries"`
	InitialDelay  time.Duration `json:"initial_delay" toml:"initial_delay"`
	MaxDelay      time.Duration `json:"max_delay" toml:"max_delay"`
	BackoffFactor float64       `json:"backoff_factor" toml:"backoff_factor"`
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	Enabled          bool          `json:"enabled" toml:"enabled"`
	FailureThreshold int           `json:"failure_threshold" toml:"failure_threshold"`
	SuccessThreshold int           `json:"success_threshold" toml:"success_threshold"`
	Timeout          time.Duration `json:"timeout" toml:"timeout"`
}

// NacosGRPCManager 基于Nacos的gRPC服务管理器
type NacosGRPCManager struct {
	config       *ServiceConfig
	namingClient naming_client.INamingClient
	logger       types.Logger
	server       *grpc.Server
	healthServer *health.Server

	// 客户端连接管理
	clients      map[string]*ClientManager
	clientsMutex sync.RWMutex

	// 服务发现缓存
	serviceCache map[string][]*model.Instance
	cacheMutex   sync.RWMutex

	// 负载均衡器
	loadBalancers map[string]LoadBalancer
	lbMutex       sync.RWMutex

	ctx    context.Context
	cancel context.CancelFunc
}

// NewNacosGRPCManager 创建基于Nacos的gRPC管理器
func NewNacosGRPCManager(configManager *config.ConfigManager, logger types.Logger) (*NacosGRPCManager, error) {
	// 从配置中心读取gRPC配置
	serviceConfig := &ServiceConfig{}
	if err := loadServiceConfigFromNacos(configManager, serviceConfig); err != nil {
		return nil, fmt.Errorf("failed to load service config: %w", err)
	}

	// 创建Nacos naming客户端
	namingClient, err := createNacosNamingClient(configManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos naming client: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &NacosGRPCManager{
		config:        serviceConfig,
		namingClient:  namingClient,
		logger:        logger,
		clients:       make(map[string]*ClientManager),
		serviceCache:  make(map[string][]*model.Instance),
		loadBalancers: make(map[string]LoadBalancer),
		ctx:           ctx,
		cancel:        cancel,
	}

	// 初始化负载均衡器
	manager.initLoadBalancers()

	// 启动服务发现和健康检查
	if err := manager.startServiceDiscovery(); err != nil {
		return nil, fmt.Errorf("failed to start service discovery: %w", err)
	}

	return manager, nil
}

// StartServer 启动gRPC服务器（配置驱动）
func (m *NacosGRPCManager) StartServer() error {
	// 创建gRPC服务器
	m.server = grpc.NewServer()

	// 启用健康检查
	if m.config.HealthCheck.Enabled {
		m.healthServer = health.NewServer()
		grpc_health_v1.RegisterHealthServer(m.server, m.healthServer)
		m.healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)
	}

	// 启用反射（开发环境）
	reflection.Register(m.server)

	// 监听端口
	addr := fmt.Sprintf(":%d", m.config.Port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	// 注册服务到Nacos
	if m.config.EnableRegister {
		if err := m.registerService(); err != nil {
			m.logger.Error(m.ctx, "Failed to register service to nacos", types.Field{Key: "error", Value: err})
		}
	}

	m.logger.Info(m.ctx, "gRPC server starting",
		types.Field{Key: "port", Value: m.config.Port},
		types.Field{Key: "service_name", Value: m.config.ServiceName},
	)

	// 启动服务器
	return m.server.Serve(listener)
}

// RegisterService 注册gRPC服务
func (m *NacosGRPCManager) RegisterService(desc *grpc.ServiceDesc, impl interface{}) {
	if m.server != nil {
		m.server.RegisterService(desc, impl)
	}
}

// GetClient 获取指定服务的gRPC客户端（配置驱动的连接管理）
func (m *NacosGRPCManager) GetClient(serviceName string) (*grpc.ClientConn, error) {
	m.clientsMutex.RLock()
	client, exists := m.clients[serviceName]
	m.clientsMutex.RUnlock()

	if !exists {
		// 查找订阅配置
		var subscriptionConfig *SubscriptionConfig
		for _, sub := range m.config.Subscriptions {
			if sub.ServiceName == serviceName {
				subscriptionConfig = &sub
				break
			}
		}

		if subscriptionConfig == nil {
			return nil, fmt.Errorf("no subscription config found for service: %s", serviceName)
		}

		// 创建客户端管理器
		clientManager, err := m.createClientManager(serviceName, subscriptionConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create client manager: %w", err)
		}

		m.clientsMutex.Lock()
		m.clients[serviceName] = clientManager
		m.clientsMutex.Unlock()

		client = clientManager
	}

	return client.GetConnection()
}

// Stop 停止gRPC管理器
func (m *NacosGRPCManager) Stop() error {
	m.cancel()

	// 停止健康检查
	if m.healthServer != nil {
		m.healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_NOT_SERVING)
	}

	// 注销服务
	if m.config.EnableRegister {
		if err := m.deregisterService(); err != nil {
			m.logger.Error(m.ctx, "Failed to deregister service", types.Field{Key: "error", Value: err})
		}
	}

	// 停止gRPC服务器
	if m.server != nil {
		m.server.GracefulStop()
	}

	// 关闭所有客户端连接
	m.clientsMutex.Lock()
	for _, client := range m.clients {
		client.Close()
	}
	m.clientsMutex.Unlock()

	return nil
}

// loadServiceConfigFromNacos 从Nacos配置中心加载服务配置
func loadServiceConfigFromNacos(configManager *config.ConfigManager, serviceConfig *ServiceConfig) error {
	// 从配置中心读取gRPC相关配置
	serviceConfig.Port = configManager.GetInt("grpc.port")
	serviceConfig.ServiceName = configManager.GetString("grpc.service_name")
	serviceConfig.Group = configManager.GetString("grpc.group")
	serviceConfig.Namespace = configManager.GetString("grpc.namespace")
	serviceConfig.Weight = configManager.GetFloat64("grpc.weight")
	serviceConfig.EnableRegister = configManager.GetBool("grpc.enable_register")
	serviceConfig.LocalIP = configManager.GetString("grpc.local_ip")

	// 读取元数据
	serviceConfig.Metadata = make(map[string]string)
	if version := configManager.GetString("grpc.metadata.version"); version != "" {
		serviceConfig.Metadata["version"] = version
	}
	if protocol := configManager.GetString("grpc.metadata.protocol"); protocol != "" {
		serviceConfig.Metadata["protocol"] = protocol
	}

	// 读取健康检查配置
	serviceConfig.HealthCheck.Enabled = configManager.GetBool("grpc.health_check.enabled")
	serviceConfig.HealthCheck.Interval = configManager.GetDuration("grpc.health_check.interval")
	serviceConfig.HealthCheck.Timeout = configManager.GetDuration("grpc.health_check.timeout")

	// 读取订阅配置
	serviceConfig.Subscriptions = loadSubscriptionConfigs(configManager)

	// 设置默认值
	setDefaultServiceConfig(serviceConfig)

	return nil
}

// loadSubscriptionConfigs 加载订阅配置
func loadSubscriptionConfigs(configManager *config.ConfigManager) []SubscriptionConfig {
	var subscriptions []SubscriptionConfig

	// 从grpcSubscriptions数组配置中读取
	i := 0
	for {
		prefix := fmt.Sprintf("grpcSubscriptions.%d", i)
		serviceName := configManager.GetString(prefix + ".serviceName")
		if serviceName == "" {
			break
		}

		subscription := SubscriptionConfig{
			ServiceName: serviceName,
			Group:       configManager.GetString(prefix + ".group"),
			Namespace:   configManager.GetString(prefix + ".namespace"),
			Strategy:    configManager.GetString(prefix + ".strategy"),
			Subscribe:   configManager.GetBool(prefix + ".subscribe"),
		}

		// 读取重试配置
		subscription.Retry.Enabled = configManager.GetBool(prefix + ".retry.enabled")
		subscription.Retry.MaxRetries = configManager.GetInt(prefix + ".retry.maxRetries")
		subscription.Retry.InitialDelay = configManager.GetDuration(prefix + ".retry.initialDelay")
		subscription.Retry.MaxDelay = configManager.GetDuration(prefix + ".retry.maxDelay")
		subscription.Retry.BackoffFactor = configManager.GetFloat64(prefix + ".retry.backoffFactor")

		// 读取健康检查配置
		subscription.HealthCheck.Enabled = configManager.GetBool(prefix + ".healthCheck.enabled")
		subscription.HealthCheck.Interval = configManager.GetDuration(prefix + ".healthCheck.interval")
		subscription.HealthCheck.Timeout = configManager.GetDuration(prefix + ".healthCheck.timeout")

		// 读取熔断器配置
		subscription.CircuitBreaker.Enabled = configManager.GetBool(prefix + ".circuitBreaker.enabled")
		subscription.CircuitBreaker.FailureThreshold = configManager.GetInt(prefix + ".circuitBreaker.failureThreshold")
		subscription.CircuitBreaker.SuccessThreshold = configManager.GetInt(prefix + ".circuitBreaker.successThreshold")
		subscription.CircuitBreaker.Timeout = configManager.GetDuration(prefix + ".circuitBreaker.timeout")

		subscriptions = append(subscriptions, subscription)
		i++
	}

	return subscriptions
}

// createNacosNamingClient 创建Nacos naming客户端
func createNacosNamingClient(configManager *config.ConfigManager) (naming_client.INamingClient, error) {
	// 从配置中心读取Nacos服务器配置
	serverConfigs := []constant.ServerConfig{}

	// 读取服务器地址
	i := 0
	for {
		prefix := fmt.Sprintf("nacos.server_configs.%d", i)
		host := configManager.GetString(prefix + ".host")
		if host == "" {
			break
		}

		port := uint64(configManager.GetInt(prefix + ".port"))
		serverConfigs = append(serverConfigs, constant.ServerConfig{
			IpAddr: host,
			Port:   port,
		})
		i++
	}

	// 如果没有配置，使用默认值
	if len(serverConfigs) == 0 {
		serverConfigs = []constant.ServerConfig{
			{
				IpAddr: "127.0.0.1",
				Port:   8848,
			},
		}
	}

	// 客户端配置
	clientConfig := constant.ClientConfig{
		NamespaceId:         configManager.GetString("nacos.client_config.namespace_id"),
		TimeoutMs:           uint64(configManager.GetInt("nacos.client_config.timeout_ms")),
		NotLoadCacheAtStart: true,
		LogDir:              "/tmp/nacos/log",
		CacheDir:            "/tmp/nacos/cache",
		LogLevel:            "info",
	}

	// 如果有用户名密码配置
	if username := configManager.GetString("nacos.client_config.username"); username != "" {
		clientConfig.Username = username
	}
	if password := configManager.GetString("nacos.client_config.password"); password != "" {
		clientConfig.Password = password
	}

	return clients.NewNamingClient(vo.NacosClientParam{
		ClientConfig:  &clientConfig,
		ServerConfigs: serverConfigs,
	})
}

// registerService 注册服务到Nacos
func (m *NacosGRPCManager) registerService() error {
	ip := m.config.LocalIP
	if ip == "" {
		// 自动获取本机IP
		var err error
		ip, err = getLocalIP()
		if err != nil {
			return fmt.Errorf("failed to get local IP: %w", err)
		}
	}

	success, err := m.namingClient.RegisterInstance(vo.RegisterInstanceParam{
		Ip:          ip,
		Port:        uint64(m.config.Port),
		ServiceName: m.config.ServiceName,
		GroupName:   m.config.Group,
		Weight:      m.config.Weight,
		Enable:      true,
		Healthy:     true,
		Metadata:    m.config.Metadata,
		Ephemeral:   true,
	})

	if err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}

	if !success {
		return fmt.Errorf("service registration returned false")
	}

	m.logger.Info(m.ctx, "Service registered successfully",
		types.Field{Key: "ip", Value: ip},
		types.Field{Key: "port", Value: m.config.Port},
		types.Field{Key: "service_name", Value: m.config.ServiceName},
		types.Field{Key: "group", Value: m.config.Group},
	)

	return nil
}

// deregisterService 注销服务
func (m *NacosGRPCManager) deregisterService() error {
	ip := m.config.LocalIP
	if ip == "" {
		var err error
		ip, err = getLocalIP()
		if err != nil {
			return fmt.Errorf("failed to get local IP: %w", err)
		}
	}

	success, err := m.namingClient.DeregisterInstance(vo.DeregisterInstanceParam{
		Ip:          ip,
		Port:        uint64(m.config.Port),
		ServiceName: m.config.ServiceName,
		GroupName:   m.config.Group,
		Ephemeral:   true,
	})

	if err != nil {
		return fmt.Errorf("failed to deregister service: %w", err)
	}

	if !success {
		return fmt.Errorf("service deregistration returned false")
	}

	return nil
}

// startServiceDiscovery 启动服务发现
func (m *NacosGRPCManager) startServiceDiscovery() error {
	// 为每个订阅的服务启动发现
	for _, sub := range m.config.Subscriptions {
		if sub.Subscribe {
			if err := m.subscribeService(sub); err != nil {
				m.logger.Error(m.ctx, "Failed to subscribe service",
					types.Field{Key: "service_name", Value: sub.ServiceName},
					types.Field{Key: "error", Value: err},
				)
			}
		}
	}

	return nil
}

// subscribeService 订阅服务变化
func (m *NacosGRPCManager) subscribeService(sub SubscriptionConfig) error {
	return m.namingClient.Subscribe(&vo.SubscribeParam{
		ServiceName: sub.ServiceName,
		GroupName:   sub.Group,
		SubscribeCallback: func(services []model.Instance, err error) {
			if err != nil {
				m.logger.Error(m.ctx, "Service subscription callback error",
					types.Field{Key: "service_name", Value: sub.ServiceName},
					types.Field{Key: "error", Value: err},
				)
				return
			}

			// 更新服务缓存
			m.cacheMutex.Lock()
			instances := make([]*model.Instance, len(services))
			for i, service := range services {
				instances[i] = &service
			}
			m.serviceCache[sub.ServiceName] = instances
			m.cacheMutex.Unlock()

			m.logger.Info(m.ctx, "Service instances updated",
				types.Field{Key: "service_name", Value: sub.ServiceName},
				types.Field{Key: "count", Value: len(services)},
			)
		},
	})
}

// getLocalIP 获取本机IP地址
func getLocalIP() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}

	for _, addr := range addrs {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP.String(), nil
			}
		}
	}

	return "", fmt.Errorf("no valid IP address found")
}

// setDefaultServiceConfig 设置默认配置值
func setDefaultServiceConfig(config *ServiceConfig) {
	if config.Port == 0 {
		config.Port = 50051
	}
	if config.ServiceName == "" {
		config.ServiceName = "platforms-service-grpc"
	}
	if config.Group == "" {
		config.Group = "DEFAULT_GROUP"
	}
	if config.Weight == 0 {
		config.Weight = 1.0
	}
	if config.Metadata == nil {
		config.Metadata = make(map[string]string)
	}

	// 健康检查默认值
	if config.HealthCheck.Interval == 0 {
		config.HealthCheck.Interval = 30 * time.Second
	}
	if config.HealthCheck.Timeout == 0 {
		config.HealthCheck.Timeout = 5 * time.Second
	}

	// 为订阅配置设置默认值
	for i := range config.Subscriptions {
		sub := &config.Subscriptions[i]
		if sub.Group == "" {
			sub.Group = "DEFAULT_GROUP"
		}
		if sub.Strategy == "" {
			sub.Strategy = "random"
		}

		// 重试默认值
		if sub.Retry.MaxRetries == 0 {
			sub.Retry.MaxRetries = 3
		}
		if sub.Retry.InitialDelay == 0 {
			sub.Retry.InitialDelay = 1 * time.Second
		}
		if sub.Retry.MaxDelay == 0 {
			sub.Retry.MaxDelay = 10 * time.Second
		}
		if sub.Retry.BackoffFactor == 0 {
			sub.Retry.BackoffFactor = 2.0
		}

		// 健康检查默认值
		if sub.HealthCheck.Interval == 0 {
			sub.HealthCheck.Interval = 30 * time.Second
		}
		if sub.HealthCheck.Timeout == 0 {
			sub.HealthCheck.Timeout = 5 * time.Second
		}

		// 熔断器默认值
		if sub.CircuitBreaker.FailureThreshold == 0 {
			sub.CircuitBreaker.FailureThreshold = 5
		}
		if sub.CircuitBreaker.SuccessThreshold == 0 {
			sub.CircuitBreaker.SuccessThreshold = 2
		}
		if sub.CircuitBreaker.Timeout == 0 {
			sub.CircuitBreaker.Timeout = 60 * time.Second
		}
	}
}
