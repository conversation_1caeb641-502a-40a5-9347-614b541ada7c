# 基于Nacos的配置驱动gRPC服务管理

## 概述

这是一个完全基于Nacos配置中心的gRPC服务管理方案，通过配置驱动的方式实现：
- 服务注册与发现
- 负载均衡
- 熔断器
- 重试机制  
- 健康检查
- 连接池管理

## 核心特性

### ✅ 配置驱动，零编码
- 所有服务配置通过Nacos配置中心管理
- 支持热更新，无需重启服务
- 统一的配置格式，支持TOML和JSON

### ✅ 自动服务发现和注册
- 基于配置自动注册gRPC服务到Nacos
- 自动发现和订阅依赖的服务
- 支持多种负载均衡策略

### ✅ 企业级可靠性
- 内置熔断器防止雪崩
- 指数退避重试机制
- 连接池和健康检查
- 自动故障恢复

## 快速开始

### 1. 初始化配置管理器

```go
package main

import (
    "context"
    "log"
    
    "platforms-common/config"
    "platforms-common/grpc"
    "platforms-common/logging"
)

func main() {
    ctx := context.Background()
    
    // 初始化日志
    logger, _ := logging.NewZapLogger(logging.DefaultLogConfig())
    
    // 初始化配置管理器
    config.InitGlobalConfigManager(logger)
    
    // 添加应用配置
    nacosConfig, err := config.NewNacosConfigFromEnv("app-config")
    if err != nil {
        log.Fatal(err)
    }
    
    appConfig, err := config.NewNacosConfig(nacosConfig)
    if err != nil {
        log.Fatal(err)
    }
    
    config.AddGlobalConfig("app", appConfig)
    
    // 初始化gRPC管理器
    if err := grpc.InitGlobalGRPCManager(config.GetGlobalConfigManager(), logger); err != nil {
        log.Fatal(err)
    }
    
    // 注册gRPC服务
    grpc.RegisterGRPCService(&your_service.YourService_ServiceDesc, &YourServiceImpl{})
    
    // 启动服务器
    log.Fatal(grpc.StartGRPCServer())
}
```

### 2. Nacos配置示例

在Nacos配置中心创建配置文件，DataID为`app-config`：

```toml
# gRPC服务端配置
[grpc]
port = 50051
service_name = "platforms-user-grpc"
group = "DEFAULT_GROUP"
namespace = ""
weight = 1.0
enable_register = true

[grpc.metadata]
version = "1.0.0"
protocol = "grpc"

# 健康检查配置
[grpc.health_check]
enabled = true
interval = "30s"
timeout = "5s"

# Nacos连接配置
[nacos]
[[nacos.server_configs]]
host = "127.0.0.1"
port = 8848

[nacos.client_config]
namespace_id = ""
timeout_ms = 5000
username = ""
password = ""

# gRPC客户端订阅配置
[[grpcSubscriptions]]
serviceName = "platforms-email"
group = "DEFAULT_GROUP"
namespace = ""
strategy = "weighted"  # random, round_robin, weighted
subscribe = true

# 重试配置
[grpcSubscriptions.retry]
enabled = true
maxRetries = 3
initialDelay = "1s"
maxDelay = "10s"
backoffFactor = 2.0

# 健康检查配置
[grpcSubscriptions.healthCheck]
enabled = true
interval = "30s"
timeout = "5s"

# 熔断器配置
[grpcSubscriptions.circuitBreaker]
enabled = true
failureThreshold = 5
successThreshold = 2
timeout = "60s"

# 添加更多服务订阅
[[grpcSubscriptions]]
serviceName = "platforms-sms"
group = "DEFAULT_GROUP"
strategy = "round_robin"
subscribe = true
# ... 其他配置类似
```

### 3. 使用gRPC客户端

```go
// 获取客户端连接（完全配置驱动）
conn, err := grpc.GetGRPCClient("platforms-email")
if err != nil {
    log.Printf("Failed to get email service client: %v", err)
    return
}

// 创建客户端并调用
client := emailpb.NewEmailServiceClient(conn)
response, err := client.SendEmail(ctx, &emailpb.SendEmailRequest{
    To:      "<EMAIL>",
    Subject: "Welcome",
    Content: "Hello World",
})
```

## 配置说明

### 服务端配置

| 配置项 | 说明 | 默认值 |
|--------|------|---------|
| `grpc.port` | gRPC服务端口 | 50051 |
| `grpc.service_name` | 服务名称 | platforms-service-grpc |
| `grpc.group` | Nacos服务组 | DEFAULT_GROUP |
| `grpc.namespace` | Nacos命名空间 | "" |
| `grpc.weight` | 服务权重 | 1.0 |
| `grpc.enable_register` | 是否注册到Nacos | true |
| `grpc.local_ip` | 本机IP（可选） | 自动检测 |

### 客户端订阅配置

每个`grpcSubscriptions`数组项支持以下配置：

| 配置项 | 说明 | 可选值 |
|--------|------|--------|
| `serviceName` | 要订阅的服务名 | - |
| `group` | 服务组 | DEFAULT_GROUP |
| `strategy` | 负载均衡策略 | random, round_robin, weighted |
| `subscribe` | 是否订阅变化 | true/false |

#### 重试配置

| 配置项 | 说明 | 默认值 |
|--------|------|---------|
| `retry.enabled` | 是否启用重试 | false |
| `retry.maxRetries` | 最大重试次数 | 3 |
| `retry.initialDelay` | 初始延迟 | 1s |
| `retry.maxDelay` | 最大延迟 | 10s |
| `retry.backoffFactor` | 退避因子 | 2.0 |

#### 熔断器配置

| 配置项 | 说明 | 默认值 |
|--------|------|---------|
| `circuitBreaker.enabled` | 是否启用熔断器 | false |
| `circuitBreaker.failureThreshold` | 失败阈值 | 5 |
| `circuitBreaker.successThreshold` | 成功阈值 | 2 |
| `circuitBreaker.timeout` | 熔断超时 | 60s |

## 环境变量支持

支持通过环境变量覆盖Nacos连接配置：

```bash
export NACOS_ADDRESS=*************:8848
export NACOS_USER=nacos
export NACOS_PASSWORD=nacos
export NACOS_GROUP=production
export NACOS_NAMESPACE=prod
export ENV=production
```

## 负载均衡策略

### 1. Random（随机）
```toml
strategy = "random"
```
随机选择健康的服务实例。

### 2. Round Robin（轮询）
```toml
strategy = "round_robin"
```
按顺序轮询选择服务实例。

### 3. Weighted（权重）
```toml
strategy = "weighted"
```
根据服务实例权重进行选择，权重越高被选中概率越大。

## 监控和日志

### 自动日志记录
- 服务注册/注销事件
- 连接创建/销毁
- 负载均衡选择
- 熔断器状态变化
- 重试执行情况

### 示例日志输出
```json
{
  "level": "info",
  "time": "2024-01-20T10:30:00Z",
  "msg": "Service registered successfully",
  "service_name": "platforms-user-grpc",
  "ip": "*************",
  "port": 50051,
  "group": "DEFAULT_GROUP"
}

{
  "level": "info", 
  "time": "2024-01-20T10:30:01Z",
  "msg": "Created new gRPC connection",
  "service": "platforms-email",
  "address": "*************:50052"
}
```

## 最佳实践

### 1. 配置管理
- 使用不同的Nacos命名空间区分环境（dev/test/prod）
- 服务名使用统一的命名规范：`platforms-{service}-grpc`
- 合理设置权重进行流量控制

### 2. 可靠性配置
```toml
# 推荐的可靠性配置
[grpcSubscriptions.retry]
enabled = true
maxRetries = 3
initialDelay = "1s"
maxDelay = "10s"
backoffFactor = 2.0

[grpcSubscriptions.circuitBreaker]
enabled = true
failureThreshold = 5
successThreshold = 2
timeout = "60s"

[grpcSubscriptions.healthCheck]
enabled = true
interval = "30s" 
timeout = "5s"
```

### 3. 性能优化
- 连接会自动复用，无需手动管理
- 使用weighted策略进行流量分配
- 合理设置熔断器阈值避免误触发

### 4. 故障处理
- 熔断器开启时会自动降级
- 连接断开会自动重连
- 服务下线会自动从负载均衡中移除

## 与现有项目集成

### 替换现有gRPC客户端
```go
// 旧方式
conn, err := grpc.Dial("localhost:50051", grpc.WithInsecure())

// 新方式（配置驱动）
conn, err := grpc.GetGRPCClient("platforms-email")
```

### 配置迁移步骤
1. 在Nacos中创建服务配置
2. 添加grpcSubscriptions配置
3. 替换硬编码的连接代码
4. 验证服务发现和负载均衡
5. 逐步启用高可用特性（重试、熔断等）

## 故障排查

### 1. 服务注册失败
检查Nacos连接配置和网络连通性：
```bash
curl http://nacos-server:8848/nacos/v1/ns/operator/health
```

### 2. 服务发现失败
检查服务名和组名配置是否正确：
```bash
curl "http://nacos-server:8848/nacos/v1/ns/instance/list?serviceName=platforms-user-grpc"
```

### 3. 连接失败
检查目标服务的健康状态和网络连通性。

### 4. 熔断器频繁触发
调整`failureThreshold`和`timeout`配置。

这个方案的核心优势是**完全配置驱动**，开发者只需要在Nacos中维护配置，代码中使用简单的API即可享受企业级的gRPC服务治理能力。