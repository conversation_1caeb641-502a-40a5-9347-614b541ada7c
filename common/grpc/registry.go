package grpc

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitee.com/heiyee/platforms/common/types"
)

// ServiceRegistryConfig represents service registry configuration
type ServiceRegistryConfig struct {
	RegistryType      string        `json:"registry_type"` // consul, etcd, nacos, memory
	Endpoints         []string      `json:"endpoints"`
	Namespace         string        `json:"namespace"`
	Timeout           time.Duration `json:"timeout"`
	HealthInterval    time.Duration `json:"health_interval"`
	EnableHealthCheck bool          `json:"enable_health_check"`
}

// DefaultServiceRegistryConfig returns default service registry configuration
func DefaultServiceRegistryConfig() *ServiceRegistryConfig {
	return &ServiceRegistryConfig{
		RegistryType:      "memory",
		Endpoints:         []string{"localhost:8500"},
		Namespace:         "default",
		Timeout:           time.Second * 10,
		HealthInterval:    time.Second * 30,
		EnableHealthCheck: true,
	}
}

// MemoryServiceRegistry implements ServiceRegistry using in-memory storage
type MemoryServiceRegistry struct {
	config   *ServiceRegistryConfig
	logger   types.Logger
	services map[string][]*types.ServiceInfo
	watchers map[string][]func([]*types.ServiceInfo)
	mutex    sync.RWMutex
	stopCh   chan struct{}
}

// NewMemoryServiceRegistry creates a new memory-based service registry
func NewMemoryServiceRegistry(config *ServiceRegistryConfig, logger types.Logger) *MemoryServiceRegistry {
	if config == nil {
		config = DefaultServiceRegistryConfig()
	}

	registry := &MemoryServiceRegistry{
		config:   config,
		logger:   logger,
		services: make(map[string][]*types.ServiceInfo),
		watchers: make(map[string][]func([]*types.ServiceInfo)),
		stopCh:   make(chan struct{}),
	}

	// Start health check routine
	if config.EnableHealthCheck {
		go registry.healthCheckLoop()
	}

	return registry
}

// Register registers a service
func (r *MemoryServiceRegistry) Register(ctx context.Context, service *types.ServiceInfo) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if service.ID == "" {
		service.ID = fmt.Sprintf("%s-%d", service.Name, time.Now().UnixNano())
	}

	if service.CreatedAt.IsZero() {
		service.CreatedAt = time.Now()
	}

	if service.Health == "" {
		service.Health = "healthy"
	}

	// Add or update service
	services := r.services[service.Name]
	found := false
	for i, existing := range services {
		if existing.ID == service.ID {
			services[i] = service
			found = true
			break
		}
	}

	if !found {
		services = append(services, service)
	}

	r.services[service.Name] = services

	r.logger.Info(ctx, "Service registered",
		types.Field{Key: "service_name", Value: service.Name},
		types.Field{Key: "service_id", Value: service.ID},
		types.Field{Key: "address", Value: service.Address},
		types.Field{Key: "port", Value: service.Port},
	)

	// Notify watchers
	r.notifyWatchers(service.Name, services)

	return nil
}

// Deregister removes a service
func (r *MemoryServiceRegistry) Deregister(ctx context.Context, serviceID string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	var serviceName string
	for name, services := range r.services {
		for i, service := range services {
			if service.ID == serviceID {
				serviceName = name
				// Remove service from slice
				r.services[name] = append(services[:i], services[i+1:]...)
				break
			}
		}
		if serviceName != "" {
			break
		}
	}

	if serviceName == "" {
		return fmt.Errorf("service with ID %s not found", serviceID)
	}

	r.logger.Info(ctx, "Service deregistered",
		types.Field{Key: "service_id", Value: serviceID},
		types.Field{Key: "service_name", Value: serviceName},
	)

	// Notify watchers
	r.notifyWatchers(serviceName, r.services[serviceName])

	return nil
}

// Discover finds services by name
func (r *MemoryServiceRegistry) Discover(ctx context.Context, serviceName string) ([]*types.ServiceInfo, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	services := r.services[serviceName]
	if len(services) == 0 {
		return nil, fmt.Errorf("no services found for %s", serviceName)
	}

	// Filter healthy services
	var healthyServices []*types.ServiceInfo
	for _, service := range services {
		if service.Health == "healthy" {
			healthyServices = append(healthyServices, service)
		}
	}

	return healthyServices, nil
}

// Watch watches for service changes
func (r *MemoryServiceRegistry) Watch(ctx context.Context, serviceName string, callback func([]*types.ServiceInfo)) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.watchers[serviceName] = append(r.watchers[serviceName], callback)

	// Send initial services
	if services, exists := r.services[serviceName]; exists {
		go callback(services)
	}

	return nil
}

// Health checks service health
func (r *MemoryServiceRegistry) Health(ctx context.Context, serviceID string) error {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for _, services := range r.services {
		for _, service := range services {
			if service.ID == serviceID {
				if service.Health != "healthy" {
					return fmt.Errorf("service %s is not healthy: %s", serviceID, service.Health)
				}
				return nil
			}
		}
	}

	return fmt.Errorf("service %s not found", serviceID)
}

// notifyWatchers notifies all watchers for a service
func (r *MemoryServiceRegistry) notifyWatchers(serviceName string, services []*types.ServiceInfo) {
	if watchers, exists := r.watchers[serviceName]; exists {
		for _, callback := range watchers {
			go callback(services)
		}
	}
}

// healthCheckLoop runs periodic health checks
func (r *MemoryServiceRegistry) healthCheckLoop() {
	ticker := time.NewTicker(r.config.HealthInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			r.performHealthChecks()
		case <-r.stopCh:
			return
		}
	}
}

// performHealthChecks checks health of all registered services
func (r *MemoryServiceRegistry) performHealthChecks() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for serviceName, services := range r.services {
		updated := false
		for _, service := range services {
			// Simple health check - in production you'd implement actual health checks
			// For now, we'll assume services are healthy unless explicitly marked otherwise
			if service.Health == "" {
				service.Health = "healthy"
				updated = true
			}
		}

		if updated {
			r.notifyWatchers(serviceName, services)
		}
	}
}

// Close closes the registry
func (r *MemoryServiceRegistry) Close() error {
	select {
	case <-r.stopCh:
		// Already closed
	default:
		close(r.stopCh)
	}
	return nil
}

// ServiceDiscoveryClient wraps service registry with load balancing
type ServiceDiscoveryClient struct {
	registry     types.ServiceRegistry
	loadBalancer ServiceInfoLoadBalancer
	logger       types.Logger
}

// ServiceInfoLoadBalancer interface for ServiceInfo load balancing
type ServiceInfoLoadBalancer interface {
	Select(instances []*types.ServiceInfo) *types.ServiceInfo
	Name() string
}

// ServiceInfoRoundRobinLoadBalancer implements round-robin for ServiceInfo
type ServiceInfoRoundRobinLoadBalancer struct {
	counter uint64
}

// NewServiceInfoRoundRobinLoadBalancer creates a new ServiceInfo round-robin load balancer
func NewServiceInfoRoundRobinLoadBalancer() *ServiceInfoRoundRobinLoadBalancer {
	return &ServiceInfoRoundRobinLoadBalancer{}
}

func (lb *ServiceInfoRoundRobinLoadBalancer) Name() string {
	return "round_robin"
}

func (lb *ServiceInfoRoundRobinLoadBalancer) Select(instances []*types.ServiceInfo) *types.ServiceInfo {
	if len(instances) == 0 {
		return nil
	}

	idx := lb.counter % uint64(len(instances))
	lb.counter++
	return instances[idx]
}

// NewServiceDiscoveryClient creates a new service discovery client
func NewServiceDiscoveryClient(registry types.ServiceRegistry, loadBalancer ServiceInfoLoadBalancer, logger types.Logger) *ServiceDiscoveryClient {
	if loadBalancer == nil {
		loadBalancer = NewServiceInfoRoundRobinLoadBalancer()
	}

	return &ServiceDiscoveryClient{
		registry:     registry,
		loadBalancer: loadBalancer,
		logger:       logger,
	}
}

// DiscoverAndSelect discovers services and selects one using load balancer
func (c *ServiceDiscoveryClient) DiscoverAndSelect(ctx context.Context, serviceName string) (*types.ServiceInfo, error) {
	services, err := c.registry.Discover(ctx, serviceName)
	if err != nil {
		return nil, err
	}

	selected := c.loadBalancer.Select(services)
	if selected == nil {
		return nil, fmt.Errorf("no healthy services available for %s", serviceName)
	}

	c.logger.Debug(ctx, "Service selected",
		types.Field{Key: "service_name", Value: serviceName},
		types.Field{Key: "service_id", Value: selected.ID},
		types.Field{Key: "address", Value: selected.Address},
		types.Field{Key: "port", Value: selected.Port},
	)

	return selected, nil
}

// ServiceHealthChecker provides health checking functionality
type ServiceHealthChecker struct {
	registry types.ServiceRegistry
	logger   types.Logger
}

// NewServiceHealthChecker creates a new service health checker
func NewServiceHealthChecker(registry types.ServiceRegistry, logger types.Logger) *ServiceHealthChecker {
	return &ServiceHealthChecker{
		registry: registry,
		logger:   logger,
	}
}

// CheckHealth checks health of a specific service
func (c *ServiceHealthChecker) CheckHealth(ctx context.Context, serviceID string) error {
	return c.registry.Health(ctx, serviceID)
}

// Helper functions for creating service info
func NewServiceInfo(name, address string, port int) *types.ServiceInfo {
	return &types.ServiceInfo{
		Name:      name,
		Address:   address,
		Port:      port,
		Health:    "healthy",
		CreatedAt: time.Now(),
		Tags:      make([]string, 0),
		Metadata:  make(map[string]string),
	}
}

// WithServiceID sets service ID
func WithServiceID(id string) func(*types.ServiceInfo) {
	return func(s *types.ServiceInfo) {
		s.ID = id
	}
}

// WithServiceTags sets service tags
func WithServiceTags(tags ...string) func(*types.ServiceInfo) {
	return func(s *types.ServiceInfo) {
		s.Tags = tags
	}
}

// WithServiceMetadata sets service metadata
func WithServiceMetadata(metadata map[string]string) func(*types.ServiceInfo) {
	return func(s *types.ServiceInfo) {
		s.Metadata = metadata
	}
}

// WithServiceVersion sets service version
func WithServiceVersion(version string) func(*types.ServiceInfo) {
	return func(s *types.ServiceInfo) {
		s.Version = version
	}
}

// ApplyServiceOptions applies options to service info
func ApplyServiceOptions(service *types.ServiceInfo, options ...func(*types.ServiceInfo)) {
	for _, option := range options {
		option(service)
	}
}
