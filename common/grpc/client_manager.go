package grpc

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"

	"gitee.com/heiyee/platforms/common/config"
	"gitee.com/heiyee/platforms/common/types"
)

// LoadBalancer 负载均衡器接口
type LoadBalancer interface {
	Select(instances []*model.Instance) *model.Instance
	Name() string
}

// RandomLoadBalancer 随机负载均衡器
type RandomLoadBalancer struct {
	rand *rand.Rand
	mu   sync.Mutex
}

// NewRandomLoadBalancer 创建随机负载均衡器
func NewRandomLoadBalancer() *RandomLoadBalancer {
	return &RandomLoadBalancer{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

func (lb *RandomLoadBalancer) Name() string {
	return "random"
}

func (lb *RandomLoadBalancer) Select(instances []*model.Instance) *model.Instance {
	if len(instances) == 0 {
		return nil
	}

	lb.mu.Lock()
	defer lb.mu.Unlock()

	idx := lb.rand.Intn(len(instances))
	return instances[idx]
}

// RoundRobinLoadBalancer 轮询负载均衡器
type RoundRobinLoadBalancer struct {
	counter uint64
}

// NewRoundRobinLoadBalancer 创建轮询负载均衡器
func NewRoundRobinLoadBalancer() *RoundRobinLoadBalancer {
	return &RoundRobinLoadBalancer{}
}

func (lb *RoundRobinLoadBalancer) Name() string {
	return "round_robin"
}

func (lb *RoundRobinLoadBalancer) Select(instances []*model.Instance) *model.Instance {
	if len(instances) == 0 {
		return nil
	}

	idx := atomic.AddUint64(&lb.counter, 1) % uint64(len(instances))
	return instances[idx]
}

// WeightedLoadBalancer 权重负载均衡器
type WeightedLoadBalancer struct {
	rand *rand.Rand
	mu   sync.Mutex
}

// NewWeightedLoadBalancer 创建权重负载均衡器
func NewWeightedLoadBalancer() *WeightedLoadBalancer {
	return &WeightedLoadBalancer{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

func (lb *WeightedLoadBalancer) Name() string {
	return "weighted"
}

func (lb *WeightedLoadBalancer) Select(instances []*model.Instance) *model.Instance {
	if len(instances) == 0 {
		return nil
	}

	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 计算总权重
	var totalWeight float64
	for _, instance := range instances {
		totalWeight += instance.Weight
	}

	if totalWeight <= 0 {
		// 如果没有权重配置，随机选择
		idx := lb.rand.Intn(len(instances))
		return instances[idx]
	}

	// 生成随机数
	r := lb.rand.Float64() * totalWeight

	// 按权重选择
	var currentWeight float64
	for _, instance := range instances {
		currentWeight += instance.Weight
		if r <= currentWeight {
			return instance
		}
	}

	// 兜底返回最后一个
	return instances[len(instances)-1]
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	config       CircuitBreakerConfig
	state        CircuitBreakerState
	failureCount int
	successCount int
	lastFailTime time.Time
	mu           sync.RWMutex
}

type CircuitBreakerState int

const (
	CircuitBreakerClosed CircuitBreakerState = iota
	CircuitBreakerOpen
	CircuitBreakerHalfOpen
)

// NewCircuitBreaker 创建熔断器
func NewCircuitBreaker(config CircuitBreakerConfig) *CircuitBreaker {
	return &CircuitBreaker{
		config: config,
		state:  CircuitBreakerClosed,
	}
}

// AllowRequest 检查是否允许请求
func (cb *CircuitBreaker) AllowRequest() bool {
	if !cb.config.Enabled {
		return true
	}

	cb.mu.Lock()
	defer cb.mu.Unlock()

	switch cb.state {
	case CircuitBreakerClosed:
		return true
	case CircuitBreakerOpen:
		// 检查是否可以转为半开状态
		if time.Since(cb.lastFailTime) >= cb.config.Timeout {
			cb.state = CircuitBreakerHalfOpen
			cb.successCount = 0
			return true
		}
		return false
	case CircuitBreakerHalfOpen:
		return true
	}

	return false
}

// RecordSuccess 记录成功
func (cb *CircuitBreaker) RecordSuccess() {
	if !cb.config.Enabled {
		return
	}

	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failureCount = 0

	if cb.state == CircuitBreakerHalfOpen {
		cb.successCount++
		if cb.successCount >= cb.config.SuccessThreshold {
			cb.state = CircuitBreakerClosed
		}
	}
}

// RecordFailure 记录失败
func (cb *CircuitBreaker) RecordFailure() {
	if !cb.config.Enabled {
		return
	}

	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failureCount++
	cb.lastFailTime = time.Now()

	if cb.state == CircuitBreakerClosed && cb.failureCount >= cb.config.FailureThreshold {
		cb.state = CircuitBreakerOpen
	} else if cb.state == CircuitBreakerHalfOpen {
		cb.state = CircuitBreakerOpen
	}
}

// ClientManager 客户端连接管理器
type ClientManager struct {
	serviceName string
	config      SubscriptionConfig
	manager     *NacosGRPCManager
	logger      types.Logger

	// 连接管理
	connections map[string]*grpc.ClientConn
	connMutex   sync.RWMutex

	// 负载均衡
	loadBalancer LoadBalancer

	// 熔断器
	circuitBreaker *CircuitBreaker

	// 重试器
	retrier *Retrier

	// 健康检查
	healthChecker *HealthChecker

	ctx    context.Context
	cancel context.CancelFunc
}

// Retrier 重试器
type Retrier struct {
	config RetryConfig
}

// NewRetrier 创建重试器
func NewRetrier(config RetryConfig) *Retrier {
	return &Retrier{
		config: config,
	}
}

// Execute 执行带重试的操作
func (r *Retrier) Execute(ctx context.Context, operation func() error) error {
	if !r.config.Enabled {
		return operation()
	}

	var lastErr error
	delay := r.config.InitialDelay

	for attempt := 0; attempt <= r.config.MaxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return ctx.Err()
			}

			// 计算下次延迟
			delay = time.Duration(float64(delay) * r.config.BackoffFactor)
			if delay > r.config.MaxDelay {
				delay = r.config.MaxDelay
			}
		}

		if err := operation(); err != nil {
			lastErr = err
			continue
		}

		return nil
	}

	return fmt.Errorf("operation failed after %d retries: %w", r.config.MaxRetries, lastErr)
}

// HealthChecker 健康检查器
type HealthChecker struct {
	config  HealthCheckConfig
	manager *NacosGRPCManager
	logger  types.Logger

	ctx    context.Context
	cancel context.CancelFunc
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(config HealthCheckConfig, manager *NacosGRPCManager, logger types.Logger) *HealthChecker {
	ctx, cancel := context.WithCancel(context.Background())

	hc := &HealthChecker{
		config:  config,
		manager: manager,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
	}

	if config.Enabled {
		go hc.start()
	}

	return hc
}

// start 启动健康检查
func (hc *HealthChecker) start() {
	ticker := time.NewTicker(hc.config.Interval)
	defer ticker.Stop()

	for {
		select {
		case <-hc.ctx.Done():
			return
		case <-ticker.C:
			hc.checkHealth()
		}
	}
}

// checkHealth 执行健康检查
func (hc *HealthChecker) checkHealth() {
	// 实现具体的健康检查逻辑
	// 这里可以调用gRPC健康检查服务
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	hc.cancel()
}

// createClientManager 创建客户端管理器
func (m *NacosGRPCManager) createClientManager(serviceName string, config *SubscriptionConfig) (*ClientManager, error) {
	ctx, cancel := context.WithCancel(m.ctx)

	clientManager := &ClientManager{
		serviceName:    serviceName,
		config:         *config,
		manager:        m,
		logger:         m.logger,
		connections:    make(map[string]*grpc.ClientConn),
		circuitBreaker: NewCircuitBreaker(config.CircuitBreaker),
		retrier:        NewRetrier(config.Retry),
		ctx:            ctx,
		cancel:         cancel,
	}

	// 初始化负载均衡器
	clientManager.loadBalancer = m.getLoadBalancer(config.Strategy)

	// 启动健康检查
	clientManager.healthChecker = NewHealthChecker(config.HealthCheck, m, m.logger)

	return clientManager, nil
}

// GetConnection 获取连接
func (cm *ClientManager) GetConnection() (*grpc.ClientConn, error) {
	// 检查熔断器
	if !cm.circuitBreaker.AllowRequest() {
		return nil, fmt.Errorf("circuit breaker is open for service: %s", cm.serviceName)
	}

	// 获取健康的服务实例
	instances := cm.manager.getHealthyInstances(cm.serviceName)
	if len(instances) == 0 {
		cm.circuitBreaker.RecordFailure()
		return nil, fmt.Errorf("no healthy instances found for service: %s", cm.serviceName)
	}

	// 负载均衡选择实例
	instance := cm.loadBalancer.Select(instances)
	if instance == nil {
		cm.circuitBreaker.RecordFailure()
		return nil, fmt.Errorf("load balancer returned nil instance for service: %s", cm.serviceName)
	}

	// 获取或创建连接
	addr := fmt.Sprintf("%s:%d", instance.Ip, instance.Port)
	conn, err := cm.getOrCreateConnection(addr)
	if err != nil {
		cm.circuitBreaker.RecordFailure()
		return nil, fmt.Errorf("failed to get connection to %s: %w", addr, err)
	}

	// 检查连接状态
	if conn.GetState() == connectivity.TransientFailure || conn.GetState() == connectivity.Shutdown {
		cm.circuitBreaker.RecordFailure()
		return nil, fmt.Errorf("connection to %s is in bad state: %s", addr, conn.GetState())
	}

	cm.circuitBreaker.RecordSuccess()
	return conn, nil
}

// getOrCreateConnection 获取或创建连接
func (cm *ClientManager) getOrCreateConnection(addr string) (*grpc.ClientConn, error) {
	cm.connMutex.RLock()
	conn, exists := cm.connections[addr]
	cm.connMutex.RUnlock()

	if exists && conn.GetState() != connectivity.Shutdown {
		return conn, nil
	}

	cm.connMutex.Lock()
	defer cm.connMutex.Unlock()

	// 双重检查
	if conn, exists = cm.connections[addr]; exists && conn.GetState() != connectivity.Shutdown {
		return conn, nil
	}

	// 创建新连接
	var dialOpts []grpc.DialOption
	dialOpts = append(dialOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))

	// 设置连接超时
	dialCtx, cancel := context.WithTimeout(cm.ctx, 10*time.Second)
	defer cancel()

	newConn, err := grpc.DialContext(dialCtx, addr, dialOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to dial %s: %w", addr, err)
	}

	// 清理旧连接
	if conn != nil {
		go func(oldConn *grpc.ClientConn) {
			oldConn.Close()
		}(conn)
	}

	cm.connections[addr] = newConn

	cm.logger.Info(cm.ctx, "Created new gRPC connection",
		types.Field{Key: "service", Value: cm.serviceName},
		types.Field{Key: "address", Value: addr},
	)

	return newConn, nil
}

// Close 关闭客户端管理器
func (cm *ClientManager) Close() {
	cm.cancel()

	if cm.healthChecker != nil {
		cm.healthChecker.Stop()
	}

	cm.connMutex.Lock()
	defer cm.connMutex.Unlock()

	for addr, conn := range cm.connections {
		if err := conn.Close(); err != nil {
			cm.logger.Error(cm.ctx, "Failed to close connection",
				types.Field{Key: "address", Value: addr},
				types.Field{Key: "error", Value: err},
			)
		}
	}

	cm.connections = make(map[string]*grpc.ClientConn)
}

// initLoadBalancers 初始化负载均衡器
func (m *NacosGRPCManager) initLoadBalancers() {
	m.lbMutex.Lock()
	defer m.lbMutex.Unlock()

	m.loadBalancers["random"] = NewRandomLoadBalancer()
	m.loadBalancers["round_robin"] = NewRoundRobinLoadBalancer()
	m.loadBalancers["weighted"] = NewWeightedLoadBalancer()
}

// getLoadBalancer 获取负载均衡器
func (m *NacosGRPCManager) getLoadBalancer(strategy string) LoadBalancer {
	m.lbMutex.RLock()
	defer m.lbMutex.RUnlock()

	if lb, exists := m.loadBalancers[strategy]; exists {
		return lb
	}

	// 默认使用随机负载均衡
	return m.loadBalancers["random"]
}

// getHealthyInstances 获取健康的服务实例
func (m *NacosGRPCManager) getHealthyInstances(serviceName string) []*model.Instance {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	instances, exists := m.serviceCache[serviceName]
	if !exists {
		// 如果缓存中没有，尝试从Nacos获取
		return m.fetchInstancesFromNacos(serviceName)
	}

	// 过滤健康的实例
	var healthyInstances []*model.Instance
	for _, instance := range instances {
		if instance.Healthy && instance.Enable {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	return healthyInstances
}

// fetchInstancesFromNacos 从Nacos获取服务实例
func (m *NacosGRPCManager) fetchInstancesFromNacos(serviceName string) []*model.Instance {
	// 查找对应的订阅配置
	var group string = "DEFAULT_GROUP"
	for _, sub := range m.config.Subscriptions {
		if sub.ServiceName == serviceName {
			group = sub.Group
			break
		}
	}

	instances, err := m.namingClient.SelectInstances(vo.SelectInstancesParam{
		ServiceName: serviceName,
		GroupName:   group,
		HealthyOnly: true,
	})
	if err != nil {
		m.logger.Error(m.ctx, "Failed to fetch instances from nacos",
			types.Field{Key: "service_name", Value: serviceName},
			types.Field{Key: "error", Value: err},
		)
		return nil
	}

	// 转换为指针切片
	var result []*model.Instance
	for i := range instances {
		result = append(result, &instances[i])
	}

	// 更新缓存
	m.cacheMutex.Lock()
	m.serviceCache[serviceName] = result
	m.cacheMutex.Unlock()

	return result
}

// 全局gRPC管理器实例
var (
	globalGRPCManager *NacosGRPCManager
	globalGRPCMutex   sync.RWMutex
)

// InitGlobalGRPCManager 初始化全局gRPC管理器
func InitGlobalGRPCManager(configManager *config.ConfigManager, logger types.Logger) error {
	globalGRPCMutex.Lock()
	defer globalGRPCMutex.Unlock()

	manager, err := NewNacosGRPCManager(configManager, logger)
	if err != nil {
		return err
	}

	globalGRPCManager = manager
	return nil
}

// GetGlobalGRPCManager 获取全局gRPC管理器
func GetGlobalGRPCManager() *NacosGRPCManager {
	globalGRPCMutex.RLock()
	defer globalGRPCMutex.RUnlock()

	return globalGRPCManager
}

// 便捷函数
func GetGRPCClient(serviceName string) (*grpc.ClientConn, error) {
	if globalGRPCManager == nil {
		return nil, fmt.Errorf("global gRPC manager not initialized")
	}
	return globalGRPCManager.GetClient(serviceName)
}

func RegisterGRPCService(desc *grpc.ServiceDesc, impl interface{}) {
	if globalGRPCManager != nil {
		globalGRPCManager.RegisterService(desc, impl)
	}
}

func StartGRPCServer() error {
	if globalGRPCManager == nil {
		return fmt.Errorf("global gRPC manager not initialized")
	}
	return globalGRPCManager.StartServer()
}

func StopGRPCServer() error {
	if globalGRPCManager == nil {
		return fmt.Errorf("global gRPC manager not initialized")
	}
	return globalGRPCManager.Stop()
}
