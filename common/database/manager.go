package database

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"gitee.com/heiyee/platforms/common/types"
)

// DatabaseType represents database type
type DatabaseType string

const (
	MySQL      DatabaseType = "mysql"
	PostgreSQL DatabaseType = "postgres"
	SQLite     DatabaseType = "sqlite"
)

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Type            DatabaseType  `json:"type"`
	Host            string        `json:"host"`
	Port            int           `json:"port"`
	Username        string        `json:"username"`
	Password        string        `json:"password"`
	Database        string        `json:"database"`
	Schema          string        `json:"schema"`
	SSLMode         string        `json:"ssl_mode"`
	Timezone        string        `json:"timezone"`
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time"`
	LogLevel        string        `json:"log_level"`
	SlowThreshold   time.Duration `json:"slow_threshold"`
}

// DefaultDatabaseConfig returns default database configuration
func DefaultDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Type:            MySQL,
		Host:            "localhost",
		Port:            3306,
		Username:        "root",
		Password:        "",
		Database:        "platform",
		SSLMode:         "disable",
		Timezone:        "UTC",
		MaxOpenConns:    25,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 10,
		LogLevel:        "warn",
		SlowThreshold:   time.Millisecond * 200,
	}
}

// GormDatabaseManager implements DatabaseManager using GORM
type GormDatabaseManager struct {
	config *DatabaseConfig
	db     *gorm.DB
	sqlDB  *sql.DB
	logger types.Logger
}

// NewGormDatabaseManager creates a new GORM database manager
func NewGormDatabaseManager(config *DatabaseConfig, logger types.Logger) (*GormDatabaseManager, error) {
	if config == nil {
		config = DefaultDatabaseConfig()
	}

	manager := &GormDatabaseManager{
		config: config,
		logger: logger,
	}

	if err := manager.connect(); err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return manager, nil
}

// connect establishes database connection
func (m *GormDatabaseManager) connect() error {
	var dialector gorm.Dialector
	var dsn string

	switch m.config.Type {
	case MySQL:
		dsn = m.buildMySQLDSN()
		dialector = mysql.Open(dsn)
	case PostgreSQL:
		dsn = m.buildPostgresDSN()
		dialector = postgres.Open(dsn)
	case SQLite:
		dsn = m.config.Database
		dialector = sqlite.Open(dsn)
	default:
		return fmt.Errorf("unsupported database type: %s", m.config.Type)
	}

	// Configure GORM logger
	gormLogger := m.createGormLogger()

	// Open database connection
	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return fmt.Errorf("failed to open database connection: %w", err)
	}

	// Get underlying sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(m.config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(m.config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(m.config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(m.config.ConnMaxIdleTime)

	m.db = db
	m.sqlDB = sqlDB

	// Test connection
	if err := m.Health(context.Background()); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	if m.logger != nil {
		m.logger.Info(context.Background(), "Database connected successfully",
			types.Field{Key: "type", Value: m.config.Type},
			types.Field{Key: "host", Value: m.config.Host},
			types.Field{Key: "database", Value: m.config.Database},
		)
	}

	return nil
}

// GetDB returns the GORM database instance
func (m *GormDatabaseManager) GetDB(ctx context.Context) interface{} {
	return m.db.WithContext(ctx)
}

// BeginTx begins a new transaction
func (m *GormDatabaseManager) BeginTx(ctx context.Context) (interface{}, error) {
	tx := m.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	return tx, nil
}

// Health checks database health
func (m *GormDatabaseManager) Health(ctx context.Context) error {
	if m.sqlDB == nil {
		return fmt.Errorf("database connection is nil")
	}

	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	if err := m.sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// Close closes the database connection
func (m *GormDatabaseManager) Close() error {
	if m.sqlDB != nil {
		return m.sqlDB.Close()
	}
	return nil
}

// GetStats returns database connection statistics
func (m *GormDatabaseManager) GetStats() sql.DBStats {
	if m.sqlDB != nil {
		return m.sqlDB.Stats()
	}
	return sql.DBStats{}
}

// Migrate runs database migrations
func (m *GormDatabaseManager) Migrate(models ...interface{}) error {
	return m.db.AutoMigrate(models...)
}

// buildMySQLDSN builds MySQL DSN
func (m *GormDatabaseManager) buildMySQLDSN() string {
	// URL 编码密码中的特殊字符
	encodedPassword := url.QueryEscape(m.config.Password)

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=%s",
		m.config.Username,
		encodedPassword,
		m.config.Host,
		m.config.Port,
		m.config.Database,
		m.config.Timezone,
	)

	if m.config.SSLMode != "" {
		dsn += "&tls=" + m.config.SSLMode
	}

	return dsn
}

// buildPostgresDSN builds PostgreSQL DSN
func (m *GormDatabaseManager) buildPostgresDSN() string {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
		m.config.Host,
		m.config.Username,
		m.config.Password,
		m.config.Database,
		m.config.Port,
		m.config.SSLMode,
		m.config.Timezone,
	)

	if m.config.Schema != "" {
		dsn += " search_path=" + m.config.Schema
	}

	return dsn
}

// createGormLogger creates GORM logger
func (m *GormDatabaseManager) createGormLogger() logger.Interface {
	var logLevel logger.LogLevel
	switch m.config.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	default:
		logLevel = logger.Warn
	}

	return &gormLogger{
		logger:        m.logger,
		logLevel:      logLevel,
		slowThreshold: m.config.SlowThreshold,
	}
}

// gormLogger implements GORM logger interface
type gormLogger struct {
	logger        types.Logger
	logLevel      logger.LogLevel
	slowThreshold time.Duration
}

func (l *gormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return &gormLogger{
		logger:        l.logger,
		logLevel:      level,
		slowThreshold: l.slowThreshold,
	}
}

func (l *gormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Info && l.logger != nil {
		l.logger.Info(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l *gormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Warn && l.logger != nil {
		l.logger.Warn(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l *gormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Error && l.logger != nil {
		l.logger.Error(ctx, fmt.Sprintf(msg, data...))
	}
}

func (l *gormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.logLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	fields := []types.Field{
		{Key: "duration", Value: elapsed},
		{Key: "sql", Value: sql},
		{Key: "rows_affected", Value: rows},
	}

	switch {
	case err != nil && l.logLevel >= logger.Error:
		fields = append(fields, types.Field{Key: "error", Value: err})
		if l.logger != nil {
			l.logger.Error(ctx, "Database query failed", fields...)
		}
	case elapsed > l.slowThreshold && l.logLevel >= logger.Warn:
		if l.logger != nil {
			l.logger.Warn(ctx, "Slow database query", fields...)
		}
	case l.logLevel == logger.Info:
		if l.logger != nil {
			l.logger.Debug(ctx, "Database query executed", fields...)
		}
	}
}

// Transaction helper functions

// WithTransaction executes a function within a database transaction
func WithTransaction(ctx context.Context, db *gorm.DB, fn func(*gorm.DB) error) error {
	return db.WithContext(ctx).Transaction(fn)
}

// WithTransactionManager executes a function within a database transaction using DatabaseManager
func WithTransactionManager(ctx context.Context, manager types.DatabaseManager, fn func(interface{}) error) error {
	tx, err := manager.BeginTx(ctx)
	if err != nil {
		return err
	}

	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return fmt.Errorf("transaction is not a GORM transaction")
	}

	defer func() {
		if r := recover(); r != nil {
			gormTx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		gormTx.Rollback()
		return err
	}

	return gormTx.Commit().Error
}

// Repository base implementation

// BaseRepository provides common database operations
type BaseRepository struct {
	db     *gorm.DB
	logger types.Logger
}

// NewBaseRepository creates a new base repository
func NewBaseRepository(db *gorm.DB, logger types.Logger) *BaseRepository {
	return &BaseRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new record
func (r *BaseRepository) Create(ctx context.Context, model interface{}) error {
	result := r.db.WithContext(ctx).Create(model)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to create record",
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return result.Error
	}
	return nil
}

// GetByID gets a record by ID
func (r *BaseRepository) GetByID(ctx context.Context, model interface{}, id interface{}) error {
	result := r.db.WithContext(ctx).First(model, id)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to get record by ID",
				types.Field{Key: "id", Value: id},
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return result.Error
	}
	return nil
}

// Update updates a record
func (r *BaseRepository) Update(ctx context.Context, model interface{}) error {
	result := r.db.WithContext(ctx).Save(model)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to update record",
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return result.Error
	}
	return nil
}

// Delete deletes a record
func (r *BaseRepository) Delete(ctx context.Context, model interface{}) error {
	result := r.db.WithContext(ctx).Delete(model)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to delete record",
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return result.Error
	}
	return nil
}

// List lists records with pagination
func (r *BaseRepository) List(ctx context.Context, models interface{}, offset, limit int) error {
	result := r.db.WithContext(ctx).Offset(offset).Limit(limit).Find(models)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to list records",
				types.Field{Key: "offset", Value: offset},
				types.Field{Key: "limit", Value: limit},
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return result.Error
	}
	return nil
}

// Count counts records
func (r *BaseRepository) Count(ctx context.Context, model interface{}) (int64, error) {
	var count int64
	result := r.db.WithContext(ctx).Model(model).Count(&count)
	if result.Error != nil {
		if r.logger != nil {
			r.logger.Error(ctx, "Failed to count records",
				types.Field{Key: "error", Value: result.Error},
			)
		}
		return 0, result.Error
	}
	return count, nil
}

// Global database manager
var globalDatabaseManager types.DatabaseManager

// InitGlobalDatabaseManager initializes the global database manager
func InitGlobalDatabaseManager(config *DatabaseConfig, logger types.Logger) error {
	manager, err := NewGormDatabaseManager(config, logger)
	if err != nil {
		return err
	}
	globalDatabaseManager = manager
	return nil
}

// GetGlobalDatabaseManager returns the global database manager
func GetGlobalDatabaseManager() types.DatabaseManager {
	return globalDatabaseManager
}

// Helper functions for easy database access
func GetDB(ctx context.Context) *gorm.DB {
	if globalDatabaseManager == nil {
		return nil
	}
	return globalDatabaseManager.GetDB(ctx).(*gorm.DB)
}

func BeginTx(ctx context.Context) (*gorm.DB, error) {
	if globalDatabaseManager == nil {
		return nil, fmt.Errorf("global database manager not initialized")
	}
	tx, err := globalDatabaseManager.BeginTx(ctx)
	if err != nil {
		return nil, err
	}
	return tx.(*gorm.DB), nil
}

func HealthCheck(ctx context.Context) error {
	if globalDatabaseManager == nil {
		return fmt.Errorf("global database manager not initialized")
	}
	return globalDatabaseManager.Health(ctx)
}
