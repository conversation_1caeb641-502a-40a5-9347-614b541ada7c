package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"gitee.com/heiyee/platforms/common/auth"
	"gitee.com/heiyee/platforms/common/errors"
	"gitee.com/heiyee/platforms/common/tracing"
	"gitee.com/heiyee/platforms/common/types"
)

// HTTPMiddleware interface for HTTP middleware
type HTTPMiddleware func(http.Handler) http.Handler

// AuthenticationMiddleware handles HTTP authentication
func AuthenticationMiddleware(authProvider types.AuthProvider) HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Skip authentication for health check and OPTIONS
			if r.URL.Path == "/health" || r.Method == "OPTIONS" {
				next.ServeHTTP(w, r)
				return
			}

			// Extract token from header
			authHeader := r.Header.Get("Authorization")
			token := auth.ExtractTokenFromHeader(authHeader)
			if token == "" {
				http.Error(w, "Missing authentication token", http.StatusUnauthorized)
				return
			}

			// Authenticate
			userCtx, err := authProvider.Authenticate(r.Context(), token)
			if err != nil {
				http.Error(w, "Invalid authentication token", http.StatusUnauthorized)
				return
			}

			// Add user context to request context
			ctx := context.WithValue(r.Context(), "user_context", userCtx)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// AuthorizationMiddleware handles HTTP authorization
func AuthorizationMiddleware(authProvider types.AuthProvider, resource, action string) HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Get user context
			userCtx, ok := r.Context().Value("user_context").(*types.UserContext)
			if !ok {
				http.Error(w, "User context not found", http.StatusUnauthorized)
				return
			}

			// Check authorization
			if err := authProvider.Authorize(r.Context(), userCtx, resource, action); err != nil {
				http.Error(w, "Insufficient permissions", http.StatusForbidden)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// LoggingMiddleware handles HTTP request logging
func LoggingMiddleware(logger types.Logger) HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Generate request ID
			requestID := generateRequestID()

			// Add request ID to context
			ctx := context.WithValue(r.Context(), "request_id", requestID)
			r = r.WithContext(ctx)

			// Add request ID to response header
			w.Header().Set("X-Request-ID", requestID)

			// Wrap response writer to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}

			logger.Info(ctx, "HTTP request started",
				types.Field{Key: "method", Value: r.Method},
				types.Field{Key: "path", Value: r.URL.Path},
				types.Field{Key: "query", Value: r.URL.RawQuery},
				types.Field{Key: "user_agent", Value: r.UserAgent()},
				types.Field{Key: "remote_addr", Value: r.RemoteAddr},
				types.Field{Key: "request_id", Value: requestID},
			)

			next.ServeHTTP(wrapped, r)

			duration := time.Since(start)

			logger.Info(ctx, "HTTP request completed",
				types.Field{Key: "method", Value: r.Method},
				types.Field{Key: "path", Value: r.URL.Path},
				types.Field{Key: "status_code", Value: wrapped.statusCode},
				types.Field{Key: "duration", Value: duration},
				types.Field{Key: "request_id", Value: requestID},
			)
		})
	}
}

// CORSMiddleware handles CORS headers
func CORSMiddleware(allowedOrigins []string, allowedMethods []string, allowedHeaders []string) HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			origin := r.Header.Get("Origin")

			// Check if origin is allowed
			allowed := false
			for _, allowedOrigin := range allowedOrigins {
				if allowedOrigin == "*" || allowedOrigin == origin {
					allowed = true
					break
				}
			}

			if allowed {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			}

			w.Header().Set("Access-Control-Allow-Methods", strings.Join(allowedMethods, ", "))
			w.Header().Set("Access-Control-Allow-Headers", strings.Join(allowedHeaders, ", "))
			w.Header().Set("Access-Control-Max-Age", "86400")

			// Handle preflight requests
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusNoContent)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// RateLimitMiddleware handles basic rate limiting
func RateLimitMiddleware(requestsPerMinute int) HTTPMiddleware {
	// Simple in-memory rate limiter (for production use Redis or similar)
	limiter := make(map[string][]time.Time)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Use IP address as key
			key := r.RemoteAddr
			now := time.Now()

			// Clean old entries
			if times, exists := limiter[key]; exists {
				var validTimes []time.Time
				for _, t := range times {
					if now.Sub(t) < time.Minute {
						validTimes = append(validTimes, t)
					}
				}
				limiter[key] = validTimes
			}

			// Check rate limit
			if len(limiter[key]) >= requestsPerMinute {
				http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
				return
			}

			// Add current request
			limiter[key] = append(limiter[key], now)

			next.ServeHTTP(w, r)
		})
	}
}

// ErrorHandlingMiddleware handles panics and errors
func ErrorHandlingMiddleware(errorHandler *errors.ErrorHandler) HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := errorHandler.RecoverFromPanic(); err != nil {
					response := errorHandler.Handle(err, map[string]interface{}{
						"method": r.Method,
						"path":   r.URL.Path,
					})

					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(response.Code)

					if jsonData, jsonErr := response.ToJSON(); jsonErr == nil {
						w.Write(jsonData)
					}
				}
			}()

			next.ServeHTTP(w, r)
		})
	}
}

// TenantMiddleware extracts tenant information
func TenantMiddleware() HTTPMiddleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract tenant ID from header or query parameter
			tenantID := r.Header.Get("X-Tenant-ID")
			if tenantID == "" {
				tenantID = r.URL.Query().Get("tenant_id")
			}

			if tenantID != "" {
				ctx := context.WithValue(r.Context(), "tenant_id", tenantID)
				r = r.WithContext(ctx)
			}

			next.ServeHTTP(w, r)
		})
	}
}

// gRPC Middleware

// UnaryAuthenticationInterceptor handles gRPC authentication
func UnaryAuthenticationInterceptor(authProvider types.AuthProvider) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Skip authentication for health check
		if strings.Contains(info.FullMethod, "Health") {
			return handler(ctx, req)
		}

		// Extract token from metadata
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Errorf(codes.Unauthenticated, "missing metadata")
		}

		tokens := md.Get("authorization")
		if len(tokens) == 0 {
			return nil, status.Errorf(codes.Unauthenticated, "missing authentication token")
		}

		token := auth.ExtractTokenFromHeader(tokens[0])
		if token == "" {
			return nil, status.Errorf(codes.Unauthenticated, "invalid token format")
		}

		// Authenticate
		userCtx, err := authProvider.Authenticate(ctx, token)
		if err != nil {
			return nil, status.Errorf(codes.Unauthenticated, "authentication failed: %v", err)
		}

		// Add user context
		ctx = context.WithValue(ctx, "user_context", userCtx)

		return handler(ctx, req)
	}
}

// StreamAuthenticationInterceptor handles gRPC stream authentication
func StreamAuthenticationInterceptor(authProvider types.AuthProvider) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// Skip authentication for health check
		if strings.Contains(info.FullMethod, "Health") {
			return handler(srv, ss)
		}

		ctx := ss.Context()

		// Extract token from metadata
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return status.Errorf(codes.Unauthenticated, "missing metadata")
		}

		tokens := md.Get("authorization")
		if len(tokens) == 0 {
			return status.Errorf(codes.Unauthenticated, "missing authentication token")
		}

		token := auth.ExtractTokenFromHeader(tokens[0])
		if token == "" {
			return status.Errorf(codes.Unauthenticated, "invalid token format")
		}

		// Authenticate
		userCtx, err := authProvider.Authenticate(ctx, token)
		if err != nil {
			return status.Errorf(codes.Unauthenticated, "authentication failed: %v", err)
		}

		// Add user context
		ctx = context.WithValue(ctx, "user_context", userCtx)

		// Create wrapped stream with new context
		wrapped := &contextServerStream{ServerStream: ss, ctx: ctx}

		return handler(srv, wrapped)
	}
}

// UnaryAuthorizationInterceptor handles gRPC authorization
func UnaryAuthorizationInterceptor(authProvider types.AuthProvider, permissions map[string]Permission) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Get permission for this method
		perm, exists := permissions[info.FullMethod]
		if !exists {
			// If no permission defined, allow (or you could deny by default)
			return handler(ctx, req)
		}

		// Get user context
		userCtx, ok := ctx.Value("user_context").(*types.UserContext)
		if !ok {
			return nil, status.Errorf(codes.Unauthenticated, "user context not found")
		}

		// Check authorization
		if err := authProvider.Authorize(ctx, userCtx, perm.Resource, perm.Action); err != nil {
			return nil, status.Errorf(codes.PermissionDenied, "insufficient permissions")
		}

		return handler(ctx, req)
	}
}

// UnaryTenantInterceptor extracts tenant information for gRPC
func UnaryTenantInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract tenant ID from metadata
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			tenantIDs := md.Get("tenant-id")
			if len(tenantIDs) > 0 {
				ctx = context.WithValue(ctx, "tenant_id", tenantIDs[0])
			}
		}

		return handler(ctx, req)
	}
}

// Helper types and functions

// Permission represents a resource-action permission
type Permission struct {
	Resource string
	Action   string
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// contextServerStream wraps grpc.ServerStream with new context
type contextServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (css *contextServerStream) Context() context.Context {
	return css.ctx
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// Middleware chain builder
type MiddlewareChain []HTTPMiddleware

// NewMiddlewareChain creates a new middleware chain
func NewMiddlewareChain(middlewares ...HTTPMiddleware) MiddlewareChain {
	return MiddlewareChain(middlewares)
}

// Add adds middleware to the chain
func (chain MiddlewareChain) Add(middleware HTTPMiddleware) MiddlewareChain {
	return append(chain, middleware)
}

// Build builds the middleware chain into a single middleware
func (chain MiddlewareChain) Build() HTTPMiddleware {
	return func(final http.Handler) http.Handler {
		for i := len(chain) - 1; i >= 0; i-- {
			final = chain[i](final)
		}
		return final
	}
}

// Common middleware configurations

// DefaultHTTPMiddlewares returns commonly used HTTP middlewares
func DefaultHTTPMiddlewares(logger types.Logger, errorHandler *errors.ErrorHandler) MiddlewareChain {
	return NewMiddlewareChain(
		ErrorHandlingMiddleware(errorHandler),
		LoggingMiddleware(logger),
		tracing.HTTPMiddleware("api"),
		TenantMiddleware(),
		CORSMiddleware(
			[]string{"*"},
			[]string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			[]string{"Content-Type", "Authorization", "X-Tenant-ID"},
		),
	)
}

// SecureHTTPMiddlewares returns security-focused HTTP middlewares
func SecureHTTPMiddlewares(authProvider types.AuthProvider, logger types.Logger, errorHandler *errors.ErrorHandler) MiddlewareChain {
	return NewMiddlewareChain(
		ErrorHandlingMiddleware(errorHandler),
		LoggingMiddleware(logger),
		tracing.HTTPMiddleware("api"),
		RateLimitMiddleware(100), // 100 requests per minute
		TenantMiddleware(),
		AuthenticationMiddleware(authProvider),
	)
}

// Context helper functions

// GetUserContext extracts user context from context
func GetUserContext(ctx context.Context) (*types.UserContext, bool) {
	userCtx, ok := ctx.Value("user_context").(*types.UserContext)
	return userCtx, ok
}

// GetTenantID extracts tenant ID from context
func GetTenantID(ctx context.Context) (string, bool) {
	tenantID, ok := ctx.Value("tenant_id").(string)
	return tenantID, ok
}

// GetRequestID extracts request ID from context
func GetRequestID(ctx context.Context) (string, bool) {
	requestID, ok := ctx.Value("request_id").(string)
	return requestID, ok
}

// MustGetUserContext extracts user context or panics
func MustGetUserContext(ctx context.Context) *types.UserContext {
	userCtx, ok := GetUserContext(ctx)
	if !ok {
		panic("user context not found in context")
	}
	return userCtx
}

// MustGetTenantID extracts tenant ID or panics
func MustGetTenantID(ctx context.Context) string {
	tenantID, ok := GetTenantID(ctx)
	if !ok {
		panic("tenant ID not found in context")
	}
	return tenantID
}
