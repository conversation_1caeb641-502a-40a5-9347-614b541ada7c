package tracing

import (
	"context"
	"fmt"
	"net/http"

	"gitee.com/heiyee/platforms/common/types"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	tracesdk "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
	"go.opentelemetry.io/otel/trace"
)

// TracingConfig represents tracing configuration
type TracingConfig struct {
	ServiceName    string  `json:"service_name"`
	ServiceVersion string  `json:"service_version"`
	Environment    string  `json:"environment"`
	OTLPEndpoint   string  `json:"otlp_endpoint"`
	SamplingRate   float64 `json:"sampling_rate"`
	Enabled        bool    `json:"enabled"`
}

// DefaultTracingConfig returns default tracing configuration
func DefaultTracingConfig() *TracingConfig {
	return &TracingConfig{
		ServiceName:    "unknown-service",
		ServiceVersion: "1.0.0",
		Environment:    "development",
		OTLPEndpoint:   "http://localhost:4318", // Default OTLP HTTP endpoint
		SamplingRate:   1.0,
		Enabled:        true,
	}
}

// OtelTracer implements Tracer interface using OpenTelemetry
type OtelTracer struct {
	tracer trace.Tracer
	config *TracingConfig
}

// OtelSpan implements Span interface using OpenTelemetry
type OtelSpan struct {
	span trace.Span
	ctx  context.Context
}

// NewOtelTracer creates a new OpenTelemetry tracer
func NewOtelTracer(config *TracingConfig) (*OtelTracer, error) {
	if config == nil {
		config = DefaultTracingConfig()
	}

	if !config.Enabled {
		return &OtelTracer{
			tracer: otel.GetTracerProvider().Tracer("noop"),
			config: config,
		}, nil
	}

	// Create OTLP HTTP exporter
	exp, err := otlptracehttp.New(context.Background(),
		otlptracehttp.WithEndpoint(config.OTLPEndpoint),
		otlptracehttp.WithInsecure(), // Use HTTPS in production
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create OTLP exporter: %w", err)
	}

	// Create resource
	res, err := resource.New(context.Background(),
		resource.WithAttributes(
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.ServiceVersion),
			semconv.DeploymentEnvironment(config.Environment),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	// Create trace provider
	tp := tracesdk.NewTracerProvider(
		tracesdk.WithBatcher(exp),
		tracesdk.WithResource(res),
		tracesdk.WithSampler(tracesdk.TraceIDRatioBased(config.SamplingRate)),
	)

	// Set global trace provider
	otel.SetTracerProvider(tp)

	// Set global propagator
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	tracer := tp.Tracer(config.ServiceName)

	return &OtelTracer{
		tracer: tracer,
		config: config,
	}, nil
}

// StartSpan starts a new span
func (t *OtelTracer) StartSpan(ctx context.Context, operationName string, opts ...types.SpanOption) (types.Span, context.Context) {
	// Apply options
	config := &types.SpanConfig{
		Tags: make(map[string]interface{}),
	}
	for _, opt := range opts {
		opt(config)
	}

	// Convert tags to attributes
	var attributes []attribute.KeyValue
	for key, value := range config.Tags {
		attributes = append(attributes, convertToAttribute(key, value))
	}

	spanCtx, span := t.tracer.Start(ctx, operationName, trace.WithAttributes(attributes...))

	return &OtelSpan{
		span: span,
		ctx:  spanCtx,
	}, spanCtx
}

// SetTag sets a tag on the span
func (s *OtelSpan) SetTag(key string, value interface{}) {
	s.span.SetAttributes(convertToAttribute(key, value))
}

// SetError marks the span as having an error
func (s *OtelSpan) SetError(err error) {
	if err == nil {
		return
	}

	s.span.RecordError(err)
	s.span.SetStatus(codes.Error, err.Error())
}

// Finish finishes the span
func (s *OtelSpan) Finish() {
	s.span.End()
}

// Context returns the span context
func (s *OtelSpan) Context() context.Context {
	return s.ctx
}

// convertToAttribute converts interface{} to OpenTelemetry attribute
func convertToAttribute(key string, value interface{}) attribute.KeyValue {
	switch v := value.(type) {
	case string:
		return attribute.String(key, v)
	case int:
		return attribute.Int(key, v)
	case int64:
		return attribute.Int64(key, v)
	case float64:
		return attribute.Float64(key, v)
	case bool:
		return attribute.Bool(key, v)
	default:
		return attribute.String(key, fmt.Sprintf("%v", v))
	}
}

// SpanOption helper functions
func WithTag(key string, value interface{}) types.SpanOption {
	return func(config *types.SpanConfig) {
		config.Tags[key] = value
	}
}

func WithTags(tags map[string]interface{}) types.SpanOption {
	return func(config *types.SpanConfig) {
		for k, v := range tags {
			config.Tags[k] = v
		}
	}
}

// Global tracer instance
var globalTracer types.Tracer

// InitGlobalTracer initializes the global tracer
func InitGlobalTracer(config *TracingConfig) error {
	tracer, err := NewOtelTracer(config)
	if err != nil {
		return err
	}
	globalTracer = tracer
	return nil
}

// GetGlobalTracer returns the global tracer
func GetGlobalTracer() types.Tracer {
	if globalTracer == nil {
		// Create default tracer if not initialized
		tracer, _ := NewOtelTracer(DefaultTracingConfig())
		globalTracer = tracer
	}
	return globalTracer
}

// Helper functions for easy tracing
func StartSpan(ctx context.Context, operationName string, opts ...types.SpanOption) (types.Span, context.Context) {
	return GetGlobalTracer().StartSpan(ctx, operationName, opts...)
}

// TraceFunction wraps a function with tracing
func TraceFunction(ctx context.Context, functionName string, fn func(context.Context) error) error {
	span, ctx := StartSpan(ctx, functionName)
	defer span.Finish()

	err := fn(ctx)
	if err != nil {
		span.SetError(err)
	}

	return err
}

// TraceFunctionWithResult wraps a function with tracing and returns result
func TraceFunctionWithResult[T any](ctx context.Context, functionName string, fn func(context.Context) (T, error)) (T, error) {
	span, ctx := StartSpan(ctx, functionName)
	defer span.Finish()

	result, err := fn(ctx)
	if err != nil {
		span.SetError(err)
	}

	return result, err
}

// Middleware for HTTP tracing
func HTTPMiddleware(serviceName string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			span, ctx := StartSpan(ctx, fmt.Sprintf("%s %s", r.Method, r.URL.Path),
				WithTag("http.method", r.Method),
				WithTag("http.url", r.URL.String()),
				WithTag("http.user_agent", r.UserAgent()),
				WithTag("service.name", serviceName),
			)
			defer span.Finish()

			// Add trace ID to response headers
			if spanCtx := trace.SpanFromContext(ctx).SpanContext(); spanCtx.IsValid() {
				w.Header().Set("X-Trace-ID", spanCtx.TraceID().String())
			}

			// Create new request with traced context
			r = r.WithContext(ctx)

			// Record response status
			wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}
			next.ServeHTTP(wrapped, r)

			span.SetTag("http.status_code", wrapped.statusCode)
			if wrapped.statusCode >= 400 {
				span.SetTag("error", true)
			}
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// Context helper functions
func WithTraceID(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, "trace_id", traceID)
}

func WithSpanID(ctx context.Context, spanID string) context.Context {
	return context.WithValue(ctx, "span_id", spanID)
}

func GetTraceIDFromContext(ctx context.Context) string {
	if spanCtx := trace.SpanFromContext(ctx).SpanContext(); spanCtx.IsValid() {
		return spanCtx.TraceID().String()
	}

	if val := ctx.Value("trace_id"); val != nil {
		if traceID, ok := val.(string); ok {
			return traceID
		}
	}
	return ""
}

func GetSpanIDFromContext(ctx context.Context) string {
	if spanCtx := trace.SpanFromContext(ctx).SpanContext(); spanCtx.IsValid() {
		return spanCtx.SpanID().String()
	}

	if val := ctx.Value("span_id"); val != nil {
		if spanID, ok := val.(string); ok {
			return spanID
		}
	}
	return ""
}

// Shutdown gracefully shuts down the tracer
func Shutdown(ctx context.Context) error {
	if tp, ok := otel.GetTracerProvider().(*tracesdk.TracerProvider); ok {
		return tp.Shutdown(ctx)
	}
	return nil
}
