package main

import (
	"log"
	"time"

	"gitee.com/heiyee/platforms/common/types"
)

func main() {
	// Test basic type structures
	userCtx := &types.UserContext{
		UserID:      123,
		TenantID:    456,
		Username:    "testuser",
		Email:       "<EMAIL>",
		Roles:       []string{"user", "admin"},
		Permissions: []string{"read", "write"},
		Metadata:    map[string]string{"department": "engineering"},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	log.Printf("✓ UserContext created: %+v", userCtx)

	// Test service info
	serviceInfo := &types.ServiceInfo{
		Name:      "test-service",
		ID:        "test-service-1",
		Address:   "127.0.0.1",
		Port:      8080,
		Tags:      []string{"api", "microservice"},
		Metadata:  map[string]string{"version": "1.0.0"},
		Health:    "healthy",
		Version:   "1.0.0",
		CreatedAt: time.Now(),
	}

	log.Printf("✓ ServiceInfo created: %+v", serviceInfo)

	// Test common response
	response := &types.CommonResponse{
		Code:    200,
		Message: "success",
		Data:    map[string]interface{}{"key": "value"},
		Meta:    &types.PaginationMeta{Page: 1, PageSize: 10, Total: 100, TotalPages: 10},
	}

	jsonData, err := response.ToJSON()
	if err != nil {
		log.Fatalf("Failed to convert response to JSON: %v", err)
	}

	log.Printf("✓ CommonResponse ToJSON working: %s", string(jsonData))

	// Test field structure
	field := types.Field{
		Key:   "test_field",
		Value: "test_value",
	}

	log.Printf("✓ Field structure: %+v", field)

	// Test health status
	health := &types.HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
		Duration:  time.Millisecond * 100,
		Details:   map[string]string{"database": "connected", "cache": "connected"},
	}

	log.Printf("✓ HealthStatus created: %+v", health)

	log.Println("\n🎉 All common module types and interfaces are working correctly!")
	log.Println("Common module compilation and type system tests passed.")
}
