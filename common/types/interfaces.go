package types

import (
	"context"
	"encoding/json"
	"time"
)

// UserContext represents the authenticated user context
type UserContext struct {
	UserID      int64             `json:"user_id"`
	TenantID    int64             `json:"tenant_id"`
	Username    string            `json:"username"`
	Email       string            `json:"email"`
	Roles       []string          `json:"roles"`
	Permissions []string          `json:"permissions"`
	Metadata    map[string]string `json:"metadata"`
	ExpiresAt   time.Time         `json:"expires_at"`
}

// TenantInfo represents tenant information
type TenantInfo struct {
	TenantID   int64  `json:"tenant_id"`
	TenantName string `json:"tenant_name"`
	TenantCode string `json:"tenant_code"`
	Status     string `json:"status"`
}

// ServiceInfo represents service registration information
type ServiceInfo struct {
	Name      string            `json:"name"`
	ID        string            `json:"id"`
	Address   string            `json:"address"`
	Port      int               `json:"port"`
	Tags      []string          `json:"tags"`
	Metadata  map[string]string `json:"metadata"`
	Health    string            `json:"health"`
	Version   string            `json:"version"`
	CreatedAt time.Time         `json:"created_at"`
}

// Logger interface for unified logging
type Logger interface {
	Debug(ctx context.Context, msg string, fields ...Field)
	Info(ctx context.Context, msg string, fields ...Field)
	Warn(ctx context.Context, msg string, fields ...Field)
	Error(ctx context.Context, msg string, fields ...Field)
	Fatal(ctx context.Context, msg string, fields ...Field)
	With(fields ...Field) Logger
	WithContext(ctx context.Context) Logger
}

// Field represents a logging field
type Field struct {
	Key   string
	Value interface{}
}

// Tracer interface for distributed tracing
type Tracer interface {
	StartSpan(ctx context.Context, operationName string, opts ...SpanOption) (Span, context.Context)
}

// Span represents a tracing span
type Span interface {
	SetTag(key string, value interface{})
	SetError(err error)
	Finish()
	Context() context.Context
}

// SpanOption for span configuration
type SpanOption func(*SpanConfig)

// SpanConfig represents span configuration
type SpanConfig struct {
	Tags map[string]interface{}
}

// ConfigProvider interface for configuration management
type ConfigProvider interface {
	Get(key string) interface{}
	GetString(key string) string
	GetInt(key string) int
	GetBool(key string) bool
	GetDuration(key string) time.Duration
	Watch(key string, callback func(interface{})) error
	Close() error
}

// ServiceRegistry interface for service registration and discovery
type ServiceRegistry interface {
	Register(ctx context.Context, service *ServiceInfo) error
	Deregister(ctx context.Context, serviceID string) error
	Discover(ctx context.Context, serviceName string) ([]*ServiceInfo, error)
	Watch(ctx context.Context, serviceName string, callback func([]*ServiceInfo)) error
	Health(ctx context.Context, serviceID string) error
}

// DatabaseManager interface for database operations
type DatabaseManager interface {
	GetDB(ctx context.Context) interface{}
	BeginTx(ctx context.Context) (interface{}, error)
	Health(ctx context.Context) error
	Close() error
}

// CacheManager interface for caching operations
type CacheManager interface {
	Get(ctx context.Context, key string) (interface{}, error)
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Close() error
}

// AuthProvider interface for authentication
type AuthProvider interface {
	Authenticate(ctx context.Context, token string) (*UserContext, error)
	Authorize(ctx context.Context, userCtx *UserContext, resource, action string) error
	GenerateToken(ctx context.Context, userCtx *UserContext) (string, error)
	RefreshToken(ctx context.Context, refreshToken string) (string, error)
	RevokeToken(ctx context.Context, token string) error
}

// CommonResponse represents standardized API response
type CommonResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Errors  interface{} `json:"errors,omitempty"`
	Meta    interface{} `json:"meta,omitempty"`
}

// ToJSON converts the response to JSON bytes
func (r *CommonResponse) ToJSON() ([]byte, error) {
	return json.Marshal(r)
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// HealthStatus represents service health status
type HealthStatus struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Duration  time.Duration     `json:"duration"`
	Details   map[string]string `json:"details,omitempty"`
}

// MetricsCollector interface for metrics collection
type MetricsCollector interface {
	Counter(name string, tags map[string]string) Counter
	Gauge(name string, tags map[string]string) Gauge
	Histogram(name string, tags map[string]string) Histogram
}

// Counter interface for counter metrics
type Counter interface {
	Inc()
	Add(delta float64)
}

// Gauge interface for gauge metrics
type Gauge interface {
	Set(value float64)
	Inc()
	Dec()
	Add(delta float64)
	Sub(delta float64)
}

// Histogram interface for histogram metrics
type Histogram interface {
	Observe(value float64)
}