# Platforms Common Module

基于Nacos的微服务通用组件库，提供配置驱动的企业级服务治理能力。

## 核心特性

### 🚀 配置驱动，零编码
- **Nacos配置中心**: 统一的配置管理，支持热更新
- **gRPC服务治理**: 完全基于配置的服务注册、发现、负载均衡
- **零侵入集成**: 最小化代码修改，最大化配置灵活性

### 🛡️ 企业级可靠性
- **熔断器**: 防止服务雪崩，自动故障恢复
- **重试机制**: 指数退避策略，提高调用成功率
- **健康检查**: 自动检测服务状态，剔除不健康实例
- **连接池**: 自动管理gRPC连接，支持连接复用

### 📊 完整的可观测性
- **分布式追踪**: OpenTelemetry + Jaeger集成
- **结构化日志**: Zap日志库，自动上下文提取
- **性能监控**: 自动记录请求耗时、错误率等指标

### 🔐 安全认证授权
- **JWT认证**: 支持JTI黑名单机制
- **RBAC权限**: 基于角色的访问控制
- **多租户**: 内置租户隔离支持

## 快速开始

### 1. 基础服务初始化

```go
package main

import (
    "context"
    "log"
    
    "platforms-common/config"
    "platforms-common/grpc"
    "platforms-common/logging"
)

func main() {
    ctx := context.Background()
    
    // 1. 初始化日志
    logger, _ := logging.NewZapLogger(&logging.LogConfig{
        ServiceName: "user-service",
        Level:       logging.InfoLevel,
    })
    
    // 2. 初始化配置管理器（基于Nacos）  
    config.InitGlobalConfigManager(logger)
    nacosOpts := config.NewNacosConfigFromEnv("app-config")
    nacosConfig, _ := config.NewNacosConfig(nacosOpts)
    config.AddGlobalConfig("app", nacosConfig)
    
    // 3. 初始化gRPC管理器（配置驱动）
    grpc.InitGlobalGRPCManager(config.GetGlobalConfigManager(), logger)
    
    // 4. 注册gRPC服务
    grpc.RegisterGRPCService(&userpb.UserService_ServiceDesc, &UserServiceImpl{})
    
    // 5. 启动服务器（自动服务注册、健康检查等）
    log.Fatal(grpc.StartGRPCServer())
}
```

### 2. Nacos配置示例

在Nacos配置中心创建DataID为`app-config`的配置：

```toml
# gRPC服务端配置
[grpc]
port = 50051
service_name = "platforms-user-grpc"
group = "DEFAULT_GROUP"
weight = 1.0
enable_register = true

# 客户端订阅配置（零编码服务发现）
[[grpcSubscriptions]]
serviceName = "platforms-email"
group = "DEFAULT_GROUP"
strategy = "weighted"
subscribe = true

# 重试配置
[grpcSubscriptions.retry]
enabled = true
maxRetries = 3
initialDelay = "1s"
maxDelay = "10s"
backoffFactor = 2.0

# 熔断器配置
[grpcSubscriptions.circuitBreaker]  
enabled = true
failureThreshold = 5
successThreshold = 2
timeout = "60s"

# Nacos连接配置
[[nacos.server_configs]]
host = "127.0.0.1"
port = 8848
```

### 3. 使用gRPC客户端（完全配置驱动）

```go
// 获取客户端连接（自动负载均衡、熔断、重试）
conn, err := grpc.GetGRPCClient("platforms-email")
if err != nil {
    return err
}

// 创建客户端并调用
client := emailpb.NewEmailServiceClient(conn)
resp, err := client.SendEmail(ctx, &emailpb.SendEmailRequest{
    To:      "<EMAIL>", 
    Subject: "Welcome",
    Content: "Hello World",
})
```

## 核心优势

### 🎯 极致易用
```go
// 旧方式：硬编码连接
conn, err := grpc.Dial("localhost:50051", grpc.WithInsecure())

// 新方式：配置驱动
conn, err := grpc.GetGRPCClient("user-service")  
```

### 🔧 配置即服务治理
- **服务发现**: 在配置中添加`grpcSubscriptions`即可自动发现服务
- **负载均衡**: 配置`strategy`选择`random`/`round_robin`/`weighted`
- **故障恢复**: 配置熔断器和重试策略，零代码实现高可用

### 📈 生产级可靠性
- **自动重试**: 指数退避，智能错误识别
- **熔断保护**: 防止雪崩，自动恢复
- **连接管理**: 自动重连，连接池复用
- **健康检查**: 实时监控，自动剔除异常节点

## Module Details

### Authentication & Authorization

The auth module provides JWT-based authentication with role-based access control:

```go
// Generate token
userCtx := &types.UserContext{
    UserID:   1,
    TenantID: 1,
    Username: "john",
    Roles:    []string{"admin"},
    Permissions: []string{"users:read", "users:write"},
}

token, err := authProvider.GenerateToken(ctx, userCtx)

// Authenticate token
userCtx, err := authProvider.Authenticate(ctx, token)

// Authorize action
err := authProvider.Authorize(ctx, userCtx, "users", "read")
```

### Logging

Structured logging with automatic context extraction:

```go
logger := logging.GetGlobalLogger()

// Basic logging
logger.Info(ctx, "User created", 
    logging.String("user_id", "123"),
    logging.String("email", "<EMAIL>"),
)

// Context-aware logging (automatically extracts trace ID, user context, etc.)
logger.WithContext(ctx).Info("Processing request")

// Field helpers
logger.Error(ctx, "Database error",
    logging.Err(err),
    logging.Int("retry_count", 3),
    logging.Duration("elapsed", time.Since(start)),
)
```

### Tracing

Distributed tracing with OpenTelemetry:

```go
// Start span
span, ctx := tracing.StartSpan(ctx, "process-user-request",
    tracing.WithTag("user_id", "123"),
    tracing.WithTag("operation", "create"),
)
defer span.Finish()

// Trace function
err := tracing.TraceFunction(ctx, "validate-user", func(ctx context.Context) error {
    // validation logic
    return nil
})

// Trace with result
result, err := tracing.TraceFunctionWithResult(ctx, "fetch-user", func(ctx context.Context) (*User, error) {
    // fetch logic
    return user, nil
})
```

### Configuration

Multi-source configuration with hot-reload:

```go
// Get configuration values
dbHost := config.GetString("database.host")
dbPort := config.GetInt("database.port")
enableDebug := config.GetBool("debug")
timeout := config.GetDuration("api.timeout")

// Watch for changes
config.Watch("database.host", func(value interface{}) {
    logger.Info(ctx, "Database host changed", logging.Any("new_value", value))
})

// Environment variables are automatically loaded with dot notation
// DB_HOST=localhost -> database.host
// API_TIMEOUT=30s -> api.timeout
```

### Error Handling

Standardized error handling with HTTP status mapping:

```go
// Create typed errors
err := errors.NewValidationError("Invalid email format")
err = errors.NewNotFoundError("user")
err = errors.NewUnauthorizedError("Invalid credentials")

// Add context to errors
err = errors.NewInternalError("Database connection failed").
    WithField("database", "users").
    WithSeverity(errors.SeverityHigh).
    WithRetryable(true)

// Handle errors in HTTP handlers
func userHandler(w http.ResponseWriter, r *http.Request) {
    user, err := getUserByID(r.Context(), userID)
    if err != nil {
        response := errors.HandleError(err)
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(response.Code)
        json, _ := response.ToJSON()
        w.Write(json)
        return
    }
    
    // Success response
    response := errors.Success().WithData(user).Build()
    w.Header().Set("Content-Type", "application/json")
    json, _ := response.ToJSON()
    w.Write(json)
}
```

### Middleware

HTTP and gRPC middleware for common concerns:

```go
// HTTP middleware chain
middlewares := middleware.NewMiddlewareChain(
    middleware.ErrorHandlingMiddleware(errorHandler),
    middleware.LoggingMiddleware(logger),
    middleware.AuthenticationMiddleware(authProvider),
    middleware.AuthorizationMiddleware(authProvider, "users", "read"),
    middleware.RateLimitMiddleware(100), // 100 requests per minute
    middleware.CORSMiddleware([]string{"*"}, []string{"GET", "POST"}, []string{"Authorization"}),
)

handler := middlewares.Build()(mux)

// gRPC interceptors
server := grpc.NewServer(config, registry, logger)
server.AddUnaryInterceptor(middleware.UnaryAuthenticationInterceptor(authProvider))
server.AddUnaryInterceptor(middleware.UnaryTenantInterceptor())
```

## Configuration

### Environment Variables

The common module automatically loads environment variables with dot notation:

```bash
# Database configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=secret
DB_DATABASE=platform

# Service configuration  
SERVICE_NAME=user-service
SERVICE_PORT=8080
LOG_LEVEL=info
TRACE_ENABLED=true
```

These become available as:
- `database.host`
- `database.port`
- `service.name`
- `log.level`

### JSON Configuration

Create `config.json` in your service root:

```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "username": "root",
    "database": "platform"
  },
  "auth": {
    "jwt_secret": "your-secret-key",
    "token_ttl": "2h"
  },
  "tracing": {
    "enabled": true,
    "jaeger_endpoint": "http://localhost:14268/api/traces"
  }
}
```

## Best Practices

### 1. Context Propagation

Always pass context through your application:

```go
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    span, ctx := tracing.StartSpan(ctx, "UserService.CreateUser")
    defer span.Finish()
    
    // Get user context from middleware
    userCtx, _ := middleware.GetUserContext(ctx)
    
    // Use context in database operations
    user := &User{
        Name:     req.Name,
        Email:    req.Email,
        TenantID: userCtx.TenantID,
    }
    
    if err := s.repo.Create(ctx, user); err != nil {
        span.SetError(err)
        return nil, errors.NewInternalError("Failed to create user").WithCause(err)
    }
    
    s.logger.Info(ctx, "User created successfully", logging.Int64("user_id", user.ID))
    return user, nil
}
```

### 2. Error Handling

Use typed errors and proper error propagation:

```go
func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*User, error) {
    var user User
    result := r.db.WithContext(ctx).Where("email = ?", email).First(&user)
    
    if result.Error != nil {
        if errors.Is(result.Error, gorm.ErrRecordNotFound) {
            return nil, errors.NewNotFoundError("user")
        }
        return nil, errors.NewInternalError("Database query failed").WithCause(result.Error)
    }
    
    return &user, nil
}
```

### 3. Resource Cleanup

Always clean up resources:

```go
func main() {
    // ... initialization
    
    // Graceful shutdown
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)
    
    go func() {
        <-c
        logger.Info(context.Background(), "Shutting down...")
        
        // Cleanup resources
        if err := database.GetGlobalDatabaseManager().Close(); err != nil {
            logger.Error(context.Background(), "Failed to close database", logging.Err(err))
        }
        
        if err := tracing.Shutdown(context.Background()); err != nil {
            logger.Error(context.Background(), "Failed to shutdown tracer", logging.Err(err))
        }
        
        os.Exit(0)
    }()
    
    // ... start server
}
```

## Testing

The common module includes test utilities and mock implementations:

```go
func TestUserService(t *testing.T) {
    // Setup test logger
    logger, _ := logging.NewZapLogger(&logging.LogConfig{Level: logging.DebugLevel})
    
    // Setup mock auth provider
    authProvider := auth.NewJWTAuthProvider(auth.DefaultJWTConfig(), nil, nil)
    
    // Setup in-memory database
    db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    db.AutoMigrate(&User{})
    
    // Create service
    service := NewUserService(db, logger, authProvider)
    
    // Test user creation
    ctx := context.Background()
    user, err := service.CreateUser(ctx, &CreateUserRequest{
        Name:  "Test User",
        Email: "<EMAIL>",
    })
    
    assert.NoError(t, err)
    assert.Equal(t, "Test User", user.Name)
}
```

## Contributing

1. Follow the established patterns and interfaces
2. Add comprehensive tests for new functionality
3. Update documentation for public APIs
4. Ensure backward compatibility

## License

This module is part of the internal platform and follows company licensing policies.