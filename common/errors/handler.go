package errors

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"

	"gitee.com/heiyee/platforms/common/types"
)

// ErrorCode represents standardized error codes
type ErrorCode string

const (
	// General errors
	CodeInternalError    ErrorCode = "INTERNAL_ERROR"
	CodeInvalidRequest   ErrorCode = "INVALID_REQUEST"
	CodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	CodeNotFound         ErrorCode = "NOT_FOUND"
	CodeConflict         ErrorCode = "CONFLICT"
	CodeForbidden        ErrorCode = "FORBIDDEN"
	CodeUnauthorized     ErrorCode = "UNAUTHORIZED"
	CodeTooManyRequests  ErrorCode = "TOO_MANY_REQUESTS"
	CodeServiceUnavail   ErrorCode = "SERVICE_UNAVAILABLE"

	// Authentication & Authorization
	CodeInvalidToken     ErrorCode = "INVALID_TOKEN"
	CodeTokenExpired     ErrorCode = "TOKEN_EXPIRED"
	CodeInsufficientPerm ErrorCode = "INSUFFICIENT_PERMISSIONS"

	// Database errors
	CodeDatabaseError  ErrorCode = "DATABASE_ERROR"
	CodeRecordNotFound ErrorCode = "RECORD_NOT_FOUND"
	CodeDuplicateKey   ErrorCode = "DUPLICATE_KEY"

	// External service errors
	CodeExternalService ErrorCode = "EXTERNAL_SERVICE_ERROR"
	CodeTimeout         ErrorCode = "TIMEOUT"
)

// Severity represents error severity level
type Severity string

const (
	SeverityLow      Severity = "low"
	SeverityMedium   Severity = "medium"
	SeverityHigh     Severity = "high"
	SeverityCritical Severity = "critical"
)

// AppError represents a standardized application error
type AppError struct {
	Code       ErrorCode              `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Fields     map[string]interface{} `json:"fields,omitempty"`
	Cause      error                  `json:"-"`
	Severity   Severity               `json:"severity"`
	Retryable  bool                   `json:"retryable"`
	Timestamp  time.Time              `json:"timestamp"`
	TraceID    string                 `json:"trace_id,omitempty"`
	RequestID  string                 `json:"request_id,omitempty"`
	UserID     string                 `json:"user_id,omitempty"`
	TenantID   string                 `json:"tenant_id,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s - %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// WithField adds a field to the error
func (e *AppError) WithField(key string, value interface{}) *AppError {
	if e.Fields == nil {
		e.Fields = make(map[string]interface{})
	}
	e.Fields[key] = value
	return e
}

// WithFields adds multiple fields to the error
func (e *AppError) WithFields(fields map[string]interface{}) *AppError {
	if e.Fields == nil {
		e.Fields = make(map[string]interface{})
	}
	for k, v := range fields {
		e.Fields[k] = v
	}
	return e
}

// WithCause sets the underlying cause
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// WithTrace sets trace information
func (e *AppError) WithTrace(traceID, requestID string) *AppError {
	e.TraceID = traceID
	e.RequestID = requestID
	return e
}

// WithUser sets user information
func (e *AppError) WithUser(userID, tenantID string) *AppError {
	e.UserID = userID
	e.TenantID = tenantID
	return e
}

// IsRetryable returns whether the error is retryable
func (e *AppError) IsRetryable() bool {
	return e.Retryable
}

// GetHTTPStatus returns the appropriate HTTP status code
func (e *AppError) GetHTTPStatus() int {
	switch e.Code {
	case CodeInvalidRequest, CodeValidationFailed:
		return http.StatusBadRequest
	case CodeUnauthorized, CodeInvalidToken, CodeTokenExpired:
		return http.StatusUnauthorized
	case CodeForbidden, CodeInsufficientPerm:
		return http.StatusForbidden
	case CodeNotFound, CodeRecordNotFound:
		return http.StatusNotFound
	case CodeConflict, CodeDuplicateKey:
		return http.StatusConflict
	case CodeTooManyRequests:
		return http.StatusTooManyRequests
	case CodeServiceUnavail:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// ToJSON converts error to JSON
func (e *AppError) ToJSON() []byte {
	data, _ := json.Marshal(e)
	return data
}

// NewAppError creates a new application error
func NewAppError(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:      code,
		Message:   message,
		Severity:  SeverityMedium,
		Retryable: false,
		Timestamp: time.Now(),
	}
}

// NewInternalError creates a new internal error
func NewInternalError(message string) *AppError {
	return NewAppError(CodeInternalError, message).
		WithSeverity(SeverityHigh).
		WithStackTrace()
}

// NewValidationError creates a new validation error
func NewValidationError(message string) *AppError {
	return NewAppError(CodeValidationFailed, message).
		WithSeverity(SeverityLow)
}

// NewNotFoundError creates a new not found error
func NewNotFoundError(resource string) *AppError {
	return NewAppError(CodeNotFound, fmt.Sprintf("%s not found", resource)).
		WithSeverity(SeverityLow)
}

// NewUnauthorizedError creates a new unauthorized error
func NewUnauthorizedError(message string) *AppError {
	if message == "" {
		message = "Unauthorized access"
	}
	return NewAppError(CodeUnauthorized, message).
		WithSeverity(SeverityMedium)
}

// NewForbiddenError creates a new forbidden error
func NewForbiddenError(message string) *AppError {
	if message == "" {
		message = "Access forbidden"
	}
	return NewAppError(CodeForbidden, message).
		WithSeverity(SeverityMedium)
}

// NewExternalServiceError creates a new external service error
func NewExternalServiceError(service, message string) *AppError {
	return NewAppError(CodeExternalService, fmt.Sprintf("External service error: %s", message)).
		WithField("service", service).
		WithSeverity(SeverityHigh).
		WithRetryable(true)
}

// NewTimeoutError creates a new timeout error
func NewTimeoutError(operation string) *AppError {
	return NewAppError(CodeTimeout, fmt.Sprintf("Operation timeout: %s", operation)).
		WithSeverity(SeverityMedium).
		WithRetryable(true)
}

// WithSeverity sets the error severity
func (e *AppError) WithSeverity(severity Severity) *AppError {
	e.Severity = severity
	return e
}

// WithRetryable sets whether the error is retryable
func (e *AppError) WithRetryable(retryable bool) *AppError {
	e.Retryable = retryable
	return e
}

// WithStackTrace adds stack trace to the error
func (e *AppError) WithStackTrace() *AppError {
	buf := make([]byte, 1024)
	for {
		n := runtime.Stack(buf, false)
		if n < len(buf) {
			e.StackTrace = string(buf[:n])
			break
		}
		buf = make([]byte, 2*len(buf))
	}
	return e
}

// WrapError wraps an existing error into AppError
func WrapError(err error, code ErrorCode, message string) *AppError {
	if err == nil {
		return nil
	}

	// If it's already an AppError, return it
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	return NewAppError(code, message).
		WithCause(err).
		WithField("original_error", err.Error())
}

// IsAppError checks if error is an AppError
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError converts error to AppError
func GetAppError(err error) *AppError {
	if err == nil {
		return nil
	}

	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	return NewInternalError(err.Error()).WithCause(err)
}

// ResponseBuilder helps build standardized API responses
type ResponseBuilder struct {
	response *types.CommonResponse
}

// NewResponseBuilder creates a new response builder
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{
		response: &types.CommonResponse{
			Code:    http.StatusOK,
			Message: "Success",
		},
	}
}

// Success creates a success response
func Success() *ResponseBuilder {
	return NewResponseBuilder()
}

// Error creates an error response
func Error(err error) *ResponseBuilder {
	appErr := GetAppError(err)
	return &ResponseBuilder{
		response: &types.CommonResponse{
			Code:    appErr.GetHTTPStatus(),
			Message: appErr.Message,
			Errors:  appErr,
		},
	}
}

// WithCode sets the response code
func (b *ResponseBuilder) WithCode(code int) *ResponseBuilder {
	b.response.Code = code
	return b
}

// WithMessage sets the response message
func (b *ResponseBuilder) WithMessage(message string) *ResponseBuilder {
	b.response.Message = message
	return b
}

// WithData sets the response data
func (b *ResponseBuilder) WithData(data interface{}) *ResponseBuilder {
	b.response.Data = data
	return b
}

// WithMeta sets the response meta
func (b *ResponseBuilder) WithMeta(meta interface{}) *ResponseBuilder {
	b.response.Meta = meta
	return b
}

// WithPagination sets pagination meta
func (b *ResponseBuilder) WithPagination(page, pageSize int, total int64) *ResponseBuilder {
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	b.response.Meta = &types.PaginationMeta{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}
	return b
}

// Build builds the final response
func (b *ResponseBuilder) Build() *types.CommonResponse {
	return b.response
}

// ToJSON converts response to JSON
func (b *ResponseBuilder) ToJSON() ([]byte, error) {
	return json.Marshal(b.response)
}

// ErrorHandler provides centralized error handling
type ErrorHandler struct {
	logger types.Logger
}

// NewErrorHandler creates a new error handler
func NewErrorHandler(logger types.Logger) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
	}
}

// Handle handles an error and returns appropriate response
func (h *ErrorHandler) Handle(err error, context map[string]interface{}) *types.CommonResponse {
	if err == nil {
		return Success().Build()
	}

	appErr := GetAppError(err)

	// Log the error
	if h.logger != nil {
		fields := []types.Field{
			{Key: "error_code", Value: appErr.Code},
			{Key: "error_message", Value: appErr.Message},
			{Key: "severity", Value: appErr.Severity},
			{Key: "retryable", Value: appErr.Retryable},
		}

		if appErr.TraceID != "" {
			fields = append(fields, types.Field{Key: "trace_id", Value: appErr.TraceID})
		}

		if appErr.UserID != "" {
			fields = append(fields, types.Field{Key: "user_id", Value: appErr.UserID})
		}

		for k, v := range context {
			fields = append(fields, types.Field{Key: k, Value: v})
		}

		switch appErr.Severity {
		case SeverityCritical, SeverityHigh:
			h.logger.Error(nil, "Application error occurred", fields...)
		case SeverityMedium:
			h.logger.Warn(nil, "Application warning occurred", fields...)
		default:
			h.logger.Info(nil, "Application info occurred", fields...)
		}
	}

	return Error(appErr).Build()
}

// RecoverFromPanic recovers from panic and converts to error
func (h *ErrorHandler) RecoverFromPanic() error {
	if r := recover(); r != nil {
		var err error
		switch v := r.(type) {
		case error:
			err = v
		default:
			err = fmt.Errorf("panic recovered: %v", v)
		}

		return NewInternalError("Internal server error").
			WithCause(err).
			WithSeverity(SeverityCritical).
			WithStackTrace()
	}
	return nil
}

// ValidationError represents field validation errors
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

// Error implements error interface
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", ve[0].Message)
}

// NewValidationErrors creates validation errors from field errors
func NewValidationErrors(fieldErrors ...ValidationError) *AppError {
	return NewValidationError("Validation failed").
		WithField("validation_errors", ValidationErrors(fieldErrors))
}

// Global error handler
var globalErrorHandler *ErrorHandler

// InitGlobalErrorHandler initializes the global error handler
func InitGlobalErrorHandler(logger types.Logger) {
	globalErrorHandler = NewErrorHandler(logger)
}

// GetGlobalErrorHandler returns the global error handler
func GetGlobalErrorHandler() *ErrorHandler {
	if globalErrorHandler == nil {
		globalErrorHandler = NewErrorHandler(nil)
	}
	return globalErrorHandler
}

// Helper functions
func HandleError(err error, context ...map[string]interface{}) *types.CommonResponse {
	ctx := make(map[string]interface{})
	if len(context) > 0 {
		ctx = context[0]
	}
	return GetGlobalErrorHandler().Handle(err, ctx)
}

func RecoverPanic() error {
	return GetGlobalErrorHandler().RecoverFromPanic()
}
