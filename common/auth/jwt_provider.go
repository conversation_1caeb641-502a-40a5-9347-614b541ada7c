package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/common/types"
	"github.com/golang-jwt/jwt/v5"
)

var (
	ErrInvalidToken     = errors.New("invalid token")
	ErrTokenExpired     = errors.New("token expired")
	ErrInsufficientPerm = errors.New("insufficient permissions")
	ErrUserNotFound     = errors.New("user not found")
)

// JWTConfig represents JWT configuration
type JWTConfig struct {
	SecretKey       string        `json:"secret_key"`
	AccessTokenTTL  time.Duration `json:"access_token_ttl"`
	RefreshTokenTTL time.Duration `json:"refresh_token_ttl"`
	Issuer          string        `json:"issuer"`
	EnableJTI       bool          `json:"enable_jti"`
	EnableBlacklist bool          `json:"enable_blacklist"`
}

// DefaultJWTConfig returns default JWT configuration
func DefaultJWTConfig() *JWTConfig {
	return &JWTConfig{
		SecretKey:       generateSecretKey(),
		AccessTokenTTL:  time.Hour * 2,
		RefreshTokenTTL: time.Hour * 24 * 7,
		Issuer:          "gitee.com/heiyee/platforms/common",
		EnableJTI:       true,
		EnableBlacklist: true,
	}
}

// JWTAuthProvider implements AuthProvider interface using JWT
type JWTAuthProvider struct {
	config          *JWTConfig
	blacklistStore  BlacklistStore
	permissionStore PermissionStore
}

// BlacklistStore interface for token blacklist management
type BlacklistStore interface {
	Add(ctx context.Context, jti string, expiration time.Time) error
	IsBlacklisted(ctx context.Context, jti string) (bool, error)
	Clean(ctx context.Context) error
}

// PermissionStore interface for permission management
type PermissionStore interface {
	GetUserPermissions(ctx context.Context, userID, tenantID int64) ([]string, error)
	GetUserRoles(ctx context.Context, userID, tenantID int64) ([]string, error)
	HasPermission(ctx context.Context, userID, tenantID int64, resource, action string) (bool, error)
}

// NewJWTAuthProvider creates a new JWT auth provider
func NewJWTAuthProvider(config *JWTConfig, blacklistStore BlacklistStore, permissionStore PermissionStore) *JWTAuthProvider {
	if config == nil {
		config = DefaultJWTConfig()
	}

	return &JWTAuthProvider{
		config:          config,
		blacklistStore:  blacklistStore,
		permissionStore: permissionStore,
	}
}

// Claims represents JWT claims
type Claims struct {
	UserID      int64             `json:"user_id"`
	TenantID    int64             `json:"tenant_id"`
	Username    string            `json:"username"`
	Email       string            `json:"email"`
	Roles       []string          `json:"roles"`
	Permissions []string          `json:"permissions"`
	Metadata    map[string]string `json:"metadata"`
	JTI         string            `json:"jti,omitempty"`
	jwt.RegisteredClaims
}

// Authenticate validates a JWT token and returns user context
func (p *JWTAuthProvider) Authenticate(ctx context.Context, tokenString string) (*types.UserContext, error) {
	// Parse and validate token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(p.config.SecretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, ErrInvalidToken
	}

	// Check if token is blacklisted (if enabled)
	if p.config.EnableBlacklist && p.blacklistStore != nil && claims.JTI != "" {
		blacklisted, err := p.blacklistStore.IsBlacklisted(ctx, claims.JTI)
		if err != nil {
			return nil, fmt.Errorf("failed to check blacklist: %w", err)
		}
		if blacklisted {
			return nil, ErrInvalidToken
		}
	}

	// Create user context
	userCtx := &types.UserContext{
		UserID:      claims.UserID,
		TenantID:    claims.TenantID,
		Username:    claims.Username,
		Email:       claims.Email,
		Roles:       claims.Roles,
		Permissions: claims.Permissions,
		Metadata:    claims.Metadata,
		ExpiresAt:   claims.ExpiresAt.Time,
	}

	return userCtx, nil
}

// Authorize checks if user has permission for resource and action
func (p *JWTAuthProvider) Authorize(ctx context.Context, userCtx *types.UserContext, resource, action string) error {
	if p.permissionStore == nil {
		// If no permission store, allow all authenticated users
		return nil
	}

	hasPermission, err := p.permissionStore.HasPermission(ctx, userCtx.UserID, userCtx.TenantID, resource, action)
	if err != nil {
		return fmt.Errorf("failed to check permission: %w", err)
	}

	if !hasPermission {
		return ErrInsufficientPerm
	}

	return nil
}

// GenerateToken creates a new JWT token for user
func (p *JWTAuthProvider) GenerateToken(ctx context.Context, userCtx *types.UserContext) (string, error) {
	now := time.Now()
	expiresAt := now.Add(p.config.AccessTokenTTL)

	claims := &Claims{
		UserID:      userCtx.UserID,
		TenantID:    userCtx.TenantID,
		Username:    userCtx.Username,
		Email:       userCtx.Email,
		Roles:       userCtx.Roles,
		Permissions: userCtx.Permissions,
		Metadata:    userCtx.Metadata,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    p.config.Issuer,
			Subject:   fmt.Sprintf("%d", userCtx.UserID),
			Audience:  []string{"platforms"},
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	// Add JTI if enabled
	if p.config.EnableJTI {
		jti, err := generateJTI()
		if err != nil {
			return "", fmt.Errorf("failed to generate JTI: %w", err)
		}
		claims.JTI = jti
		claims.RegisteredClaims.ID = jti
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(p.config.SecretKey))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// RefreshToken generates a new token from refresh token
func (p *JWTAuthProvider) RefreshToken(ctx context.Context, refreshToken string) (string, error) {
	// For now, just re-authenticate and generate new token
	// In production, you might want separate refresh token logic
	userCtx, err := p.Authenticate(ctx, refreshToken)
	if err != nil {
		return "", err
	}

	return p.GenerateToken(ctx, userCtx)
}

// RevokeToken adds token to blacklist
func (p *JWTAuthProvider) RevokeToken(ctx context.Context, tokenString string) error {
	if !p.config.EnableBlacklist || p.blacklistStore == nil {
		return nil
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(p.config.SecretKey), nil
	})

	if err != nil {
		return fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || claims.JTI == "" {
		return nil
	}

	return p.blacklistStore.Add(ctx, claims.JTI, claims.ExpiresAt.Time)
}

// generateSecretKey generates a random secret key
func generateSecretKey() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateJTI generates a unique JWT ID
func generateJTI() (string, error) {
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// ExtractTokenFromHeader extracts token from Authorization header
func ExtractTokenFromHeader(header string) string {
	if header == "" {
		return ""
	}

	parts := strings.Split(header, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return ""
	}

	return parts[1]
}

// MemoryBlacklistStore implements BlacklistStore using in-memory storage
type MemoryBlacklistStore struct {
	blacklist map[string]time.Time
}

// NewMemoryBlacklistStore creates a new memory blacklist store
func NewMemoryBlacklistStore() *MemoryBlacklistStore {
	return &MemoryBlacklistStore{
		blacklist: make(map[string]time.Time),
	}
}

// Add adds a token to blacklist
func (s *MemoryBlacklistStore) Add(ctx context.Context, jti string, expiration time.Time) error {
	s.blacklist[jti] = expiration
	return nil
}

// IsBlacklisted checks if token is blacklisted
func (s *MemoryBlacklistStore) IsBlacklisted(ctx context.Context, jti string) (bool, error) {
	expiration, exists := s.blacklist[jti]
	if !exists {
		return false, nil
	}

	// Check if token has expired (should be removed from blacklist)
	if time.Now().After(expiration) {
		delete(s.blacklist, jti)
		return false, nil
	}

	return true, nil
}

// Clean removes expired tokens from blacklist
func (s *MemoryBlacklistStore) Clean(ctx context.Context) error {
	now := time.Now()
	for jti, expiration := range s.blacklist {
		if now.After(expiration) {
			delete(s.blacklist, jti)
		}
	}
	return nil
}

// MockPermissionStore implements PermissionStore for testing
type MockPermissionStore struct {
	userPermissions map[string][]string
	userRoles       map[string][]string
}

// NewMockPermissionStore creates a new mock permission store
func NewMockPermissionStore() *MockPermissionStore {
	return &MockPermissionStore{
		userPermissions: make(map[string][]string),
		userRoles:       make(map[string][]string),
	}
}

// SetUserPermissions sets permissions for a user
func (s *MockPermissionStore) SetUserPermissions(userID, tenantID int64, permissions []string) {
	key := fmt.Sprintf("%d:%d", userID, tenantID)
	s.userPermissions[key] = permissions
}

// SetUserRoles sets roles for a user
func (s *MockPermissionStore) SetUserRoles(userID, tenantID int64, roles []string) {
	key := fmt.Sprintf("%d:%d", userID, tenantID)
	s.userRoles[key] = roles
}

// GetUserPermissions returns user permissions
func (s *MockPermissionStore) GetUserPermissions(ctx context.Context, userID, tenantID int64) ([]string, error) {
	key := fmt.Sprintf("%d:%d", userID, tenantID)
	return s.userPermissions[key], nil
}

// GetUserRoles returns user roles
func (s *MockPermissionStore) GetUserRoles(ctx context.Context, userID, tenantID int64) ([]string, error) {
	key := fmt.Sprintf("%d:%d", userID, tenantID)
	return s.userRoles[key], nil
}

// HasPermission checks if user has specific permission
func (s *MockPermissionStore) HasPermission(ctx context.Context, userID, tenantID int64, resource, action string) (bool, error) {
	permissions, _ := s.GetUserPermissions(ctx, userID, tenantID)

	requiredPerm := fmt.Sprintf("%s:%s", resource, action)
	wildcard := fmt.Sprintf("%s:*", resource)

	for _, perm := range permissions {
		if perm == requiredPerm || perm == wildcard || perm == "*" {
			return true, nil
		}
	}

	return false, nil
}
