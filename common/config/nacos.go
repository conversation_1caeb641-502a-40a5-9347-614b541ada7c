package config

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"github.com/pelletier/go-toml/v2"

	"gitee.com/heiyee/platforms/common/types"
)

// NacosConfig Nacos配置客户端
type NacosConfig struct {
	client     config_client.IConfigClient
	logger     types.Logger
	dataId     string
	group      string
	namespace  string
	env        string
	watchers   map[string][]func(string)
	mutex      sync.RWMutex
	cache      map[string]interface{}
	cacheMutex sync.RWMutex
}

// NacosConfigOptions Nacos配置选项
type NacosConfigOptions struct {
	DataId       string
	Group        string
	Namespace    string
	Env          string
	ServerConfig []constant.ServerConfig
	ClientConfig constant.ClientConfig
	Logger       types.Logger
}

// DefaultNacosConfigOptions 默认Nacos配置选项
func DefaultNacosConfigOptions() *NacosConfigOptions {
	return &NacosConfigOptions{
		DataId:    "app-config",
		Group:     "DEFAULT_GROUP",
		Namespace: "",
		Env:       "dev",
		ServerConfig: []constant.ServerConfig{
			{
				IpAddr: "127.0.0.1",
				Port:   8848,
			},
		},
		ClientConfig: constant.ClientConfig{
			NamespaceId:         "",
			TimeoutMs:           5000,
			NotLoadCacheAtStart: true,
			LogDir:              "/tmp/nacos/log",
			CacheDir:            "/tmp/nacos/cache",
			LogLevel:            "info",
		},
	}
}

// NewNacosConfigFromEnv 从环境变量创建Nacos配置
func NewNacosConfigFromEnv(dataId string) *NacosConfigOptions {
	opts := DefaultNacosConfigOptions()
	opts.DataId = dataId

	// 从环境变量读取配置
	if address := os.Getenv("NACOS_ADDRESS"); address != "" {
		parts := strings.Split(address, ":")
		if len(parts) == 2 {
			port, _ := strconv.Atoi(parts[1])
			opts.ServerConfig = []constant.ServerConfig{
				{
					IpAddr: parts[0],
					Port:   uint64(port),
				},
			}
		}
	}

	if group := os.Getenv("NACOS_GROUP"); group != "" {
		opts.Group = group
	}

	if namespace := os.Getenv("NACOS_NAMESPACE"); namespace != "" {
		opts.Namespace = namespace
		opts.ClientConfig.NamespaceId = namespace
	}

	if env := os.Getenv("ENV"); env != "" {
		opts.Env = env
	}

	if user := os.Getenv("NACOS_USER"); user != "" {
		opts.ClientConfig.Username = user
	}

	if password := os.Getenv("NACOS_PASSWORD"); password != "" {
		opts.ClientConfig.Password = password
	}

	return opts
}

// NewNacosConfig 创建Nacos配置客户端
func NewNacosConfig(opts *NacosConfigOptions) (*NacosConfig, error) {
	if opts == nil {
		opts = DefaultNacosConfigOptions()
	}

	client, err := clients.NewConfigClient(vo.NacosClientParam{
		ClientConfig:  &opts.ClientConfig,
		ServerConfigs: opts.ServerConfig,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos config client: %w", err)
	}

	nc := &NacosConfig{
		client:    client,
		logger:    opts.Logger,
		dataId:    opts.DataId,
		group:     opts.Group,
		namespace: opts.Namespace,
		env:       opts.Env,
		watchers:  make(map[string][]func(string)),
		cache:     make(map[string]interface{}),
	}

	// 初始加载配置到缓存
	if err := nc.loadConfigToCache(); err != nil {
		if nc.logger != nil {
			nc.logger.Warn(context.Background(), "Failed to load initial config", types.Field{Key: "error", Value: err})
		}
	}

	return nc, nil
}

// Get 实现ConfigProvider接口 - 从缓存获取配置值
func (nc *NacosConfig) Get(key string) interface{} {
	nc.cacheMutex.RLock()
	defer nc.cacheMutex.RUnlock()

	return nc.getValueFromCache(key)
}

// GetString 获取字符串配置值
func (nc *NacosConfig) GetString(key string) string {
	value := nc.Get(key)
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int64, float64:
		return fmt.Sprintf("%v", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		return fmt.Sprintf("%v", v)
	}
}

// GetInt 获取整数配置值
func (nc *NacosConfig) GetInt(key string) int {
	value := nc.Get(key)
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}

	return 0
}

// GetBool 获取布尔配置值
func (nc *NacosConfig) GetBool(key string) bool {
	value := nc.Get(key)
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		return strings.ToLower(v) == "true" || v == "1"
	case int, int64:
		return v != 0
	}

	return false
}

// GetDuration 获取时间段配置值
func (nc *NacosConfig) GetDuration(key string) time.Duration {
	value := nc.GetString(key)
	if value == "" {
		return 0
	}

	if duration, err := time.ParseDuration(value); err == nil {
		return duration
	}

	// 尝试解析为秒数
	if seconds, err := strconv.Atoi(value); err == nil {
		return time.Duration(seconds) * time.Second
	}

	return 0
}

// Watch 监听配置变化
func (nc *NacosConfig) Watch(key string, callback func(interface{})) error {
	nc.mutex.Lock()
	defer nc.mutex.Unlock()

	// 添加监听器到本地缓存
	nc.watchers[key] = append(nc.watchers[key], func(content string) {
		// 解析新配置
		newConfig := make(map[string]interface{})
		if err := nc.parseConfig(content, newConfig); err != nil {
			if nc.logger != nil {
				nc.logger.Error(context.Background(), "Failed to parse config on watch", types.Field{Key: "error", Value: err})
			}
			return
		}

		// 获取新值
		newValue := nc.getValueFromData(newConfig, key)
		callback(newValue)
	})

	// 如果这是第一个监听器，启动Nacos监听
	if len(nc.watchers[key]) == 1 {
		return nc.client.ListenConfig(vo.ConfigParam{
			DataId: nc.dataId,
			Group:  nc.group,
			OnChange: func(namespace, group, dataId, data string) {
				// 更新缓存
				nc.updateCache(data)

				// 通知所有监听器
				nc.notifyWatchers(data)
			},
		})
	}

	return nil
}

// Close 关闭配置客户端
func (nc *NacosConfig) Close() error {
	// Nacos客户端会自动清理资源
	return nil
}

// GetConfig 直接从Nacos获取完整配置
func (nc *NacosConfig) GetConfig() (string, error) {
	content, err := nc.client.GetConfig(vo.ConfigParam{
		DataId: nc.dataId,
		Group:  nc.group,
	})
	if err != nil {
		return "", fmt.Errorf("failed to get config from nacos: %w", err)
	}

	return content, nil
}

// PublishConfig 发布配置到Nacos
func (nc *NacosConfig) PublishConfig(content string) error {
	success, err := nc.client.PublishConfig(vo.ConfigParam{
		DataId:  nc.dataId,
		Group:   nc.group,
		Content: content,
	})
	if err != nil {
		return fmt.Errorf("failed to publish config: %w", err)
	}
	if !success {
		return fmt.Errorf("failed to publish config: nacos returned false")
	}

	return nil
}

// loadConfigToCache 加载配置到本地缓存
func (nc *NacosConfig) loadConfigToCache() error {
	content, err := nc.GetConfig()
	if err != nil {
		return err
	}

	return nc.updateCache(content)
}

// updateCache 更新本地缓存
func (nc *NacosConfig) updateCache(content string) error {
	nc.cacheMutex.Lock()
	defer nc.cacheMutex.Unlock()

	newCache := make(map[string]interface{})
	if err := nc.parseConfig(content, newCache); err != nil {
		return err
	}

	nc.cache = newCache
	return nil
}

// parseConfig 解析配置内容
func (nc *NacosConfig) parseConfig(content string, target map[string]interface{}) error {
	content = strings.TrimSpace(content)

	// 检测配置格式
	if strings.HasPrefix(content, "#") || strings.Contains(content, "[") {
		// TOML格式
		var config map[string]interface{}
		if err := toml.Unmarshal([]byte(content), &config); err != nil {
			return fmt.Errorf("failed to parse TOML config: %w", err)
		}
		nc.flattenConfig(config, "", target)
	} else if strings.HasPrefix(content, "{") {
		// JSON格式
		var config map[string]interface{}
		if err := json.Unmarshal([]byte(content), &config); err != nil {
			return fmt.Errorf("failed to parse JSON config: %w", err)
		}
		nc.flattenConfig(config, "", target)
	} else {
		return fmt.Errorf("unsupported config format")
	}

	return nil
}

// flattenConfig 将嵌套配置拍平为点分隔的键值对
func (nc *NacosConfig) flattenConfig(config map[string]interface{}, prefix string, target map[string]interface{}) {
	for key, value := range config {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}

		switch v := value.(type) {
		case map[string]interface{}:
			nc.flattenConfig(v, fullKey, target)
		default:
			target[fullKey] = v
		}
	}
}

// getValueFromCache 从缓存获取值
func (nc *NacosConfig) getValueFromCache(key string) interface{} {
	return nc.cache[key]
}

// getValueFromData 从指定数据中获取值
func (nc *NacosConfig) getValueFromData(data map[string]interface{}, key string) interface{} {
	return data[key]
}

// notifyWatchers 通知所有监听器
func (nc *NacosConfig) notifyWatchers(content string) {
	nc.mutex.RLock()
	defer nc.mutex.RUnlock()

	for _, callbacks := range nc.watchers {
		for _, callback := range callbacks {
			go callback(content)
		}
	}
}

// ConfigManager 配置管理器 - 统一管理多个配置数据源
type ConfigManager struct {
	configs map[string]*NacosConfig
	logger  types.Logger
	mutex   sync.RWMutex
}

// NewConfigManager 创建配置管理器
func NewConfigManager(logger types.Logger) *ConfigManager {
	return &ConfigManager{
		configs: make(map[string]*NacosConfig),
		logger:  logger,
	}
}

// AddConfig 添加配置数据源
func (cm *ConfigManager) AddConfig(name string, config *NacosConfig) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.configs[name] = config
}

// GetConfig 获取指定配置数据源
func (cm *ConfigManager) GetConfig(name string) *NacosConfig {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return cm.configs[name]
}

// Get 从所有配置源获取值（按添加顺序优先级）
func (cm *ConfigManager) Get(key string) interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	for _, config := range cm.configs {
		if value := config.Get(key); value != nil {
			return value
		}
	}

	return nil
}

// GetString 从所有配置源获取字符串值
func (cm *ConfigManager) GetString(key string) string {
	if value := cm.Get(key); value != nil {
		switch v := value.(type) {
		case string:
			return v
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	return ""
}

// GetInt 从所有配置源获取整数值
func (cm *ConfigManager) GetInt(key string) int {
	if value := cm.Get(key); value != nil {
		switch v := value.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			if i, err := strconv.Atoi(v); err == nil {
				return i
			}
		}
	}
	return 0
}

// GetBool 从所有配置源获取布尔值
func (cm *ConfigManager) GetBool(key string) bool {
	if value := cm.Get(key); value != nil {
		switch v := value.(type) {
		case bool:
			return v
		case string:
			return strings.ToLower(v) == "true" || v == "1"
		case int, int64:
			return v != 0
		}
	}
	return false
}

// GetFloat64 从所有配置源获取浮点数值
func (cm *ConfigManager) GetFloat64(key string) float64 {
	if value := cm.Get(key); value != nil {
		switch v := value.(type) {
		case float64:
			return v
		case float32:
			return float64(v)
		case int:
			return float64(v)
		case int64:
			return float64(v)
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				return f
			}
		}
	}
	return 0
}

// GetDuration 从所有配置源获取时间段值
func (cm *ConfigManager) GetDuration(key string) time.Duration {
	value := cm.GetString(key)
	if value == "" {
		return 0
	}

	if duration, err := time.ParseDuration(value); err == nil {
		return duration
	}

	if seconds, err := strconv.Atoi(value); err == nil {
		return time.Duration(seconds) * time.Second
	}

	return 0
}

// Watch 监听配置变化
func (cm *ConfigManager) Watch(key string, callback func(interface{})) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	// 在所有配置源上添加监听
	for _, config := range cm.configs {
		if err := config.Watch(key, callback); err != nil {
			return err
		}
	}

	return nil
}

// Close 关闭所有配置源
func (cm *ConfigManager) Close() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var lastErr error
	for _, config := range cm.configs {
		if err := config.Close(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// 全局配置管理器实例
var (
	globalConfigManager *ConfigManager
	globalMutex         sync.RWMutex
)

// InitGlobalConfigManager 初始化全局配置管理器
func InitGlobalConfigManager(logger types.Logger) {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	globalConfigManager = NewConfigManager(logger)
}

// GetGlobalConfigManager 获取全局配置管理器
func GetGlobalConfigManager() *ConfigManager {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	if globalConfigManager == nil {
		globalConfigManager = NewConfigManager(nil)
	}

	return globalConfigManager
}

// 便捷函数 - 直接从全局配置管理器获取值
func Get(key string) interface{} {
	return GetGlobalConfigManager().Get(key)
}

func GetString(key string) string {
	return GetGlobalConfigManager().GetString(key)
}

func GetInt(key string) int {
	return GetGlobalConfigManager().GetInt(key)
}

func GetBool(key string) bool {
	return GetGlobalConfigManager().GetBool(key)
}

func GetDuration(key string) time.Duration {
	return GetGlobalConfigManager().GetDuration(key)
}

func Watch(key string, callback func(interface{})) error {
	return GetGlobalConfigManager().Watch(key, callback)
}

// AddGlobalConfig 添加全局配置源
func AddGlobalConfig(name string, config *NacosConfig) {
	GetGlobalConfigManager().AddConfig(name, config)
}

// GetGlobalConfig 获取全局配置源
func GetGlobalConfig(name string) *NacosConfig {
	return GetGlobalConfigManager().GetConfig(name)
}
