package config

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitee.com/heiyee/platforms/common/types"
)

// ConfigSource represents configuration source type
type ConfigSource string

const (
	SourceEnv    ConfigSource = "env"
	SourceFile   ConfigSource = "file"
	SourceConsul ConfigSource = "consul"
	SourceEtcd   ConfigSource = "etcd"
	SourceNacos  ConfigSource = "nacos"
)

// ConfigProviderConfig represents configuration provider settings
type ConfigProviderConfig struct {
	Sources   []ConfigSource `json:"sources"`
	FilePaths []string       `json:"file_paths"`
	Endpoints []string       `json:"endpoints"`
	Namespace string         `json:"namespace"`
	Group     string         `json:"group"`
	Timeout   time.Duration  `json:"timeout"`
	WatchMode bool           `json:"watch_mode"`
}

// DefaultConfigProviderConfig returns default configuration
func DefaultConfigProviderConfig() *ConfigProviderConfig {
	return &ConfigProviderConfig{
		Sources:   []ConfigSource{SourceEnv, SourceFile},
		FilePaths: []string{"config.json", "config.yaml"},
		Namespace: "default",
		Group:     "DEFAULT_GROUP",
		Timeout:   time.Second * 10,
		WatchMode: true,
	}
}

// MultiSourceConfigProvider implements ConfigProvider with multiple sources
type MultiSourceConfigProvider struct {
	config   *ConfigProviderConfig
	logger   types.Logger
	data     map[string]interface{}
	watchers map[string][]func(interface{})
	mutex    sync.RWMutex
	stopCh   chan struct{}
}

// NewMultiSourceConfigProvider creates a new multi-source configuration provider
func NewMultiSourceConfigProvider(config *ConfigProviderConfig, logger types.Logger) (*MultiSourceConfigProvider, error) {
	if config == nil {
		config = DefaultConfigProviderConfig()
	}

	provider := &MultiSourceConfigProvider{
		config:   config,
		logger:   logger,
		data:     make(map[string]interface{}),
		watchers: make(map[string][]func(interface{})),
		stopCh:   make(chan struct{}),
	}

	// Load initial configuration
	if err := provider.loadConfiguration(); err != nil {
		return nil, fmt.Errorf("failed to load initial configuration: %w", err)
	}

	// Start watch mode if enabled
	if config.WatchMode {
		go provider.watchLoop()
	}

	return provider, nil
}

// Get returns a configuration value
func (p *MultiSourceConfigProvider) Get(key string) interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return p.getValue(key)
}

// GetString returns a string configuration value
func (p *MultiSourceConfigProvider) GetString(key string) string {
	value := p.Get(key)
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int64, float64:
		return fmt.Sprintf("%v", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		return fmt.Sprintf("%v", v)
	}
}

// GetInt returns an integer configuration value
func (p *MultiSourceConfigProvider) GetInt(key string) int {
	value := p.Get(key)
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}

	return 0
}

// GetBool returns a boolean configuration value
func (p *MultiSourceConfigProvider) GetBool(key string) bool {
	value := p.Get(key)
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		return strings.ToLower(v) == "true" || v == "1"
	case int, int64:
		return v != 0
	}

	return false
}

// GetDuration returns a duration configuration value
func (p *MultiSourceConfigProvider) GetDuration(key string) time.Duration {
	value := p.GetString(key)
	if value == "" {
		return 0
	}

	if duration, err := time.ParseDuration(value); err == nil {
		return duration
	}

	// Try parsing as seconds
	if seconds, err := strconv.Atoi(value); err == nil {
		return time.Duration(seconds) * time.Second
	}

	return 0
}

// Watch watches for configuration changes
func (p *MultiSourceConfigProvider) Watch(key string, callback func(interface{})) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.watchers[key] = append(p.watchers[key], callback)

	// Send initial value
	if value := p.getValue(key); value != nil {
		go callback(value)
	}

	return nil
}

// Close closes the configuration provider
func (p *MultiSourceConfigProvider) Close() error {
	select {
	case <-p.stopCh:
		// Already closed
	default:
		close(p.stopCh)
	}
	return nil
}

// getValue gets value from configuration data with dot notation support
func (p *MultiSourceConfigProvider) getValue(key string) interface{} {
	keys := strings.Split(key, ".")
	current := p.data

	for i, k := range keys {
		if i == len(keys)-1 {
			// Last key, return the value
			return current[k]
		}

		// Navigate deeper into nested structure
		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

// loadConfiguration loads configuration from all sources
func (p *MultiSourceConfigProvider) loadConfiguration() error {
	ctx := context.Background()

	for _, source := range p.config.Sources {
		switch source {
		case SourceEnv:
			if err := p.loadFromEnvironment(); err != nil {
				p.logger.Error(ctx, "Failed to load from environment", types.Field{Key: "error", Value: err})
			}
		case SourceFile:
			if err := p.loadFromFiles(); err != nil {
				p.logger.Error(ctx, "Failed to load from files", types.Field{Key: "error", Value: err})
			}
		default:
			p.logger.Warn(ctx, "Unsupported configuration source", types.Field{Key: "source", Value: source})
		}
	}

	p.logger.Info(ctx, "Configuration loaded", types.Field{Key: "keys_count", Value: len(p.data)})
	return nil
}

// loadFromEnvironment loads configuration from environment variables
func (p *MultiSourceConfigProvider) loadFromEnvironment() error {
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.ToLower(parts[0])
		value := parts[1]

		// Convert nested keys (e.g., DB_HOST -> db.host)
		if strings.Contains(key, "_") {
			key = strings.ReplaceAll(key, "_", ".")
		}

		p.setValue(key, value)
	}

	return nil
}

// loadFromFiles loads configuration from files
func (p *MultiSourceConfigProvider) loadFromFiles() error {
	for _, filePath := range p.config.FilePaths {
		if err := p.loadFromFile(filePath); err != nil {
			p.logger.Warn(context.Background(), "Failed to load config file",
				types.Field{Key: "file", Value: filePath},
				types.Field{Key: "error", Value: err},
			)
			continue
		}
	}
	return nil
}

// loadFromFile loads configuration from a single file
func (p *MultiSourceConfigProvider) loadFromFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	var config map[string]interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse JSON config: %w", err)
	}

	// Merge configuration
	p.mergeConfig(config, "")

	return nil
}

// setValue sets a value in the configuration data with dot notation support
func (p *MultiSourceConfigProvider) setValue(key string, value interface{}) {
	keys := strings.Split(key, ".")
	current := p.data

	for i, k := range keys {
		if i == len(keys)-1 {
			// Last key, set the value
			current[k] = value
			return
		}

		// Navigate or create nested structure
		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			next := make(map[string]interface{})
			current[k] = next
			current = next
		}
	}
}

// mergeConfig merges configuration data recursively
func (p *MultiSourceConfigProvider) mergeConfig(config map[string]interface{}, prefix string) {
	for key, value := range config {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}

		switch v := value.(type) {
		case map[string]interface{}:
			p.mergeConfig(v, fullKey)
		default:
			p.setValue(fullKey, v)
		}
	}
}

// watchLoop watches for configuration changes
func (p *MultiSourceConfigProvider) watchLoop() {
	ticker := time.NewTicker(time.Second * 30) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.reloadConfiguration()
		case <-p.stopCh:
			return
		}
	}
}

// reloadConfiguration reloads configuration and notifies watchers
func (p *MultiSourceConfigProvider) reloadConfiguration() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	oldData := make(map[string]interface{})
	for k, v := range p.data {
		oldData[k] = v
	}

	// Reload configuration
	p.data = make(map[string]interface{})
	if err := p.loadConfiguration(); err != nil {
		p.logger.Error(context.Background(), "Failed to reload configuration", types.Field{Key: "error", Value: err})
		// Restore old data
		p.data = oldData
		return
	}

	// Notify watchers of changes
	p.notifyWatchers(oldData)
}

// notifyWatchers notifies watchers of configuration changes
func (p *MultiSourceConfigProvider) notifyWatchers(oldData map[string]interface{}) {
	for key, callbacks := range p.watchers {
		oldValue := p.getValueFromData(oldData, key)
		newValue := p.getValue(key)

		// Check if value changed
		if !p.valuesEqual(oldValue, newValue) {
			for _, callback := range callbacks {
				go callback(newValue)
			}
		}
	}
}

// getValueFromData gets value from specific data map
func (p *MultiSourceConfigProvider) getValueFromData(data map[string]interface{}, key string) interface{} {
	keys := strings.Split(key, ".")
	current := data

	for i, k := range keys {
		if i == len(keys)-1 {
			return current[k]
		}

		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

// valuesEqual compares two values for equality
func (p *MultiSourceConfigProvider) valuesEqual(a, b interface{}) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}

	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// Global configuration provider
var globalConfigProvider types.ConfigProvider

// InitGlobalConfigProvider initializes the global configuration provider
func InitGlobalConfigProvider(config *ConfigProviderConfig, logger types.Logger) error {
	provider, err := NewMultiSourceConfigProvider(config, logger)
	if err != nil {
		return err
	}
	globalConfigProvider = provider
	return nil
}

// GetGlobalConfigProvider returns the global configuration provider
func GetGlobalConfigProvider() types.ConfigProvider {
	if globalConfigProvider == nil {
		// Create default provider if not initialized
		provider, _ := NewMultiSourceConfigProvider(DefaultConfigProviderConfig(), nil)
		globalConfigProvider = provider
	}
	return globalConfigProvider
}

// ConfigBuilder helps build configuration objects
type ConfigBuilder struct {
	data map[string]interface{}
}

// NewConfigBuilder creates a new configuration builder
func NewConfigBuilder() *ConfigBuilder {
	return &ConfigBuilder{
		data: make(map[string]interface{}),
	}
}

// Set sets a configuration value
func (b *ConfigBuilder) Set(key string, value interface{}) *ConfigBuilder {
	b.data[key] = value
	return b
}

// SetString sets a string configuration value
func (b *ConfigBuilder) SetString(key, value string) *ConfigBuilder {
	return b.Set(key, value)
}

// SetInt sets an integer configuration value
func (b *ConfigBuilder) SetInt(key string, value int) *ConfigBuilder {
	return b.Set(key, value)
}

// SetBool sets a boolean configuration value
func (b *ConfigBuilder) SetBool(key string, value bool) *ConfigBuilder {
	return b.Set(key, value)
}

// SetDuration sets a duration configuration value
func (b *ConfigBuilder) SetDuration(key string, value time.Duration) *ConfigBuilder {
	return b.Set(key, value.String())
}

// Build builds the configuration map
func (b *ConfigBuilder) Build() map[string]interface{} {
	return b.data
}

// ToJSON converts configuration to JSON
func (b *ConfigBuilder) ToJSON() ([]byte, error) {
	return json.MarshalIndent(b.data, "", "  ")
}
