#!/bin/bash

# 生成 protobuf 文件
# 确保安装了 protoc 和相关插件

set -e

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)

# API 目录
API_DIR="$PROJECT_ROOT/api"

# 输出目录
OUTPUT_DIR="$API_DIR"

echo "Generating protobuf files..."
echo "Project root: $PROJECT_ROOT"
echo "API directory: $API_DIR"
echo "Output directory: $OUTPUT_DIR"

# 生成 Go 代码
protoc \
    --proto_path="$API_DIR" \
    --go_out="$OUTPUT_DIR" \
    --go_opt=paths=source_relative \
    --go-grpc_out="$OUTPUT_DIR" \
    --go-grpc_opt=paths=source_relative \
    "$API_DIR/emailpb/email_service.proto"

echo "Protobuf files generated successfully!"