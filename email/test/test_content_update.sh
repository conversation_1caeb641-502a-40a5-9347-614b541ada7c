#!/bin/bash

# ==============================================
# 测试脚本: 验证模板内容更新修复
# 创建时间: 2025-01-27
# 说明: 测试 UpdateTemplateContent 方法是否正确处理空字符串
# ==============================================

# 配置
DB_HOST="**************"
DB_PORT="3308"
DB_NAME="platforms-email"
DB_USER="root"
DB_PASSWORD="Pu0cF6KVs]7AockCKVC"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_database_connection() {
    log_info "测试数据库连接..."
    
    if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

test_current_content_state() {
    log_info "检查当前模板内容状态..."
    
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        SELECT 
            id,
            name,
            CASE 
                WHEN html_content IS NOT NULL AND html_content != '' THEN 'HTML'
                WHEN plain_text_content IS NOT NULL AND plain_text_content != '' THEN 'TEXT'
                ELSE 'EMPTY'
            END as content_type,
            LENGTH(html_content) as html_length,
            LENGTH(plain_text_content) as text_length
        FROM email_templates 
        WHERE id = 3;
    "
}

test_content_update_simulation() {
    log_info "模拟内容更新测试..."
    
    # 获取当前状态
    local current_html=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT html_content FROM email_templates WHERE id = 3
    ")
    
    local current_text=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT plain_text_content FROM email_templates WHERE id = 3
    ")
    
    log_info "当前状态:"
    log_info "  HTML内容长度: ${#current_html}"
    log_info "  文本内容长度: ${#current_text}"
    
    # 模拟更新HTML内容（应该清空文本内容）
    log_info "模拟更新HTML内容..."
    
    # 这里应该调用API，但为了测试，我们直接更新数据库
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        UPDATE email_templates 
        SET 
            html_content = CONCAT(html_content, ' <!-- Updated -->'),
            plain_text_content = '',
            updated_at = NOW()
        WHERE id = 3;
    "
    
    # 检查更新结果
    local updated_html=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT html_content FROM email_templates WHERE id = 3
    ")
    
    local updated_text=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT plain_text_content FROM email_templates WHERE id = 3
    ")
    
    log_info "更新后状态:"
    log_info "  HTML内容长度: ${#updated_html}"
    log_info "  文本内容长度: ${#updated_text}"
    
    # 验证结果
    if [ "${#updated_text}" -eq 0 ]; then
        log_success "文本内容已正确清空"
        return 0
    else
        log_error "文本内容未被清空，长度: ${#updated_text}"
        return 1
    fi
}

test_gorm_behavior() {
    log_info "测试GORM行为..."
    
    # 创建一个测试记录
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        INSERT INTO email_templates (
            tenant_id, template_code, account_id, name, type, subject,
            html_content, plain_text_content, status, version
        ) VALUES (
            1, 'test_gorm_behavior', 1, 'GORM测试模板', 1, '测试主题',
            '原始HTML内容', '原始文本内容', 1, 1
        );
    "
    
    local test_id=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT id FROM email_templates WHERE template_code = 'test_gorm_behavior'
    ")
    
    log_info "创建测试记录，ID: $test_id"
    
    # 模拟GORM的Updates行为（不指定字段）
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        UPDATE email_templates 
        SET 
            html_content = '更新的HTML内容',
            plain_text_content = ''
        WHERE id = $test_id;
    "
    
    # 检查结果
    local result_html=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT html_content FROM email_templates WHERE id = $test_id
    ")
    
    local result_text=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -s -N -e "
        SELECT plain_text_content FROM email_templates WHERE id = $test_id
    ")
    
    log_info "GORM Updates 测试结果:"
    log_info "  HTML内容: $result_html"
    log_info "  文本内容长度: ${#result_text}"
    
    # 清理测试数据
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        DELETE FROM email_templates WHERE id = $test_id
    "
    
    log_info "清理测试数据完成"
}

# 主函数
main() {
    log_info "开始测试模板内容更新修复..."
    
    # 测试数据库连接
    if ! test_database_connection; then
        exit 1
    fi
    
    # 检查当前状态
    test_current_content_state
    
    # 测试GORM行为
    test_gorm_behavior
    
    # 测试内容更新
    if ! test_content_update_simulation; then
        exit 1
    fi
    
    log_success "所有测试完成！"
}

# 执行主函数
main "$@" 