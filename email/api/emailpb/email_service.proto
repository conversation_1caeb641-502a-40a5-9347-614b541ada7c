syntax = "proto3";

package email;

option go_package = "gitee.com/heiyee/platforms/email/api/emailpb";

// 邮件服务
service EmailService {
    // 发送模板邮件
    rpc SendTemplateEmail(SendTemplateEmailRequest) returns (SendTemplateEmailResponse);
    
    // 检查模板是否存在
    rpc CheckTemplateExists(CheckTemplateExistsRequest) returns (CheckTemplateExistsResponse);
}

// 发送模板邮件请求
message SendTemplateEmailRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
    // 模板代码
    string template_code = 3;
    // 收件人邮箱列表
    repeated string to = 4;
    // 模板变量
    map<string, string> variables = 5;
    // 请求ID（可选，用于日志追踪）
    string request_id = 6;
}

// 发送模板邮件响应
message SendTemplateEmailResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 邮件发送结果数据
    EmailSendResult data = 3;
}

// 邮件发送结果
message EmailSendResult {
    // 消息ID
    string message_id = 1;
    // 发送状态
    string status = 2;
    // 发送时间
    int64 send_time = 3;
}

// 检查模板是否存在请求
message CheckTemplateExistsRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
    // 模板代码
    string template_code = 3;
}

// 检查模板是否存在响应
message CheckTemplateExistsResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 模板存在结果数据
    TemplateExistsResult data = 3;
}

// 模板存在结果
message TemplateExistsResult {
    // 是否存在
    bool exists = 1;
    // 模板信息（如果存在）
    TemplateInfo template_info = 2;
}

// 模板信息
message TemplateInfo {
    // 模板ID
    int64 id = 1;
    // 模板代码
    string template_code = 2;
    // 模板名称
    string name = 3;
    // 模板类型
    string type = 4;
    // 是否启用
    bool is_enabled = 5;
    // 创建时间
    int64 created_at = 6;
    // 更新时间
    int64 updated_at = 7;
}