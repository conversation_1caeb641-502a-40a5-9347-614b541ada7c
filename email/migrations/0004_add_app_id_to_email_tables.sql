-- ==============================================
-- 迁移: 为email模块相关表添加appId字段
-- 版本: 0004
-- 创建时间: 2025-01-27
-- 说明: 为包含tenantId的表添加appId字段，对外使用appId，对内使用internal_app_id
-- 优化：系统内部表全部使用bigint类型的appId字段，提升存储性能
-- ==============================================

-- 1. 为email_accounts表添加appId字段
ALTER TABLE `email_accounts` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_email_accounts_app_id` ON `email_accounts`(`app_id`);
CREATE INDEX `idx_email_accounts_internal_app_id` ON `email_accounts`(`internal_app_id`);
CREATE INDEX `idx_email_accounts_tenant_internal_app` ON `email_accounts`(`tenant_id`, `internal_app_id`);

-- 2. 为email_messages表添加appId字段
ALTER TABLE `email_messages` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_email_messages_app_id` ON `email_messages`(`app_id`);
CREATE INDEX `idx_email_messages_internal_app_id` ON `email_messages`(`internal_app_id`);
CREATE INDEX `idx_email_messages_tenant_internal_app` ON `email_messages`(`tenant_id`, `internal_app_id`);

-- 3. 为email_templates表添加appId字段
ALTER TABLE `email_templates` 
ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- 为appId字段创建索引，优化查询性能
CREATE INDEX `idx_email_templates_app_id` ON `email_templates`(`app_id`);
CREATE INDEX `idx_email_templates_internal_app_id` ON `email_templates`(`internal_app_id`);
CREATE INDEX `idx_email_templates_tenant_internal_app` ON `email_templates`(`tenant_id`, `internal_app_id`);

-- 4. 为tenant_configs表添加appId字段（如果存在）
-- 注意：如果tenant_configs表不存在，请注释掉以下语句
-- ALTER TABLE `tenant_configs` 
-- ADD COLUMN `app_id` varchar(64) DEFAULT NULL COMMENT '对外公开的应用ID，UUID格式' AFTER `tenant_id`,
-- ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 1 COMMENT '内部应用ID，系统内部使用，bigint类型提升性能' AFTER `app_id`;

-- CREATE INDEX `idx_tenant_configs_app_id` ON `tenant_configs`(`app_id`);
-- CREATE INDEX `idx_tenant_configs_internal_app_id` ON `tenant_configs`(`internal_app_id`);
-- CREATE INDEX `idx_tenant_configs_tenant_internal_app` ON `tenant_configs`(`tenant_id`, `internal_app_id`);

-- 5. 更新现有数据，设置默认应用ID
-- 为现有数据设置默认的系统应用ID
UPDATE `email_accounts` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `email_messages` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
UPDATE `email_templates` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;
-- UPDATE `tenant_configs` SET `app_id` = 'sys-default-app-001', `internal_app_id` = 1 WHERE `app_id` IS NULL;

-- 6. 验证修改结果
SHOW COLUMNS FROM `email_accounts` LIKE 'app_id';
SHOW COLUMNS FROM `email_accounts` LIKE 'internal_app_id';
SHOW COLUMNS FROM `email_messages` LIKE 'app_id';
SHOW COLUMNS FROM `email_messages` LIKE 'internal_app_id';
SHOW COLUMNS FROM `email_templates` LIKE 'app_id';
SHOW COLUMNS FROM `email_templates` LIKE 'internal_app_id'; 