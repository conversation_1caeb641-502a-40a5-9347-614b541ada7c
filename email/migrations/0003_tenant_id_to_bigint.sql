-- ==============================================
-- 迁移: 将tenant_id字段修改为bigint类型
-- 版本: 0003
-- 创建时间: 2025-07-28
-- 说明: 将email_accounts和email_messages表的tenant_id字段从varchar(50)修改为bigint
-- ==============================================

-- 1. 备份现有数据（可选）
CREATE TABLE email_accounts_backup_tenant_id AS SELECT * FROM email_accounts;
CREATE TABLE email_messages_backup_tenant_id AS SELECT * FROM email_messages;

-- 2. 修改email_accounts表的tenant_id字段
-- 首先删除相关索引
DROP INDEX idx_email_accounts_tenant_id ON email_accounts;
DROP INDEX idx_email_accounts_is_system ON email_accounts;

-- 修改tenant_id字段类型
ALTER TABLE email_accounts
MODIFY COLUMN `tenant_id` BIGINT NOT NULL COMMENT '租户ID';

-- 重新创建索引
CREATE INDEX idx_email_accounts_tenant_id ON email_accounts(tenant_id);
CREATE INDEX idx_email_accounts_is_system ON email_accounts(tenant_id, is_system);

-- 3. 修改email_messages表的tenant_id字段
-- 首先删除相关索引
DROP INDEX idx_email_messages_tenant_id ON email_messages;
DROP INDEX idx_tenant_status_created ON email_messages;
DROP INDEX idx_tenant_sent_at ON email_messages;

-- 修改tenant_id字段类型
ALTER TABLE email_messages
MODIFY COLUMN `tenant_id` BIGINT NOT NULL COMMENT '租户ID';

-- 重新创建索引
CREATE INDEX idx_email_messages_tenant_id ON email_messages(tenant_id);
CREATE INDEX idx_tenant_status_created ON email_messages(tenant_id, status, created_at);
CREATE INDEX idx_tenant_sent_at ON email_messages(tenant_id, sent_at);

-- 4. 更新现有数据（如果需要）
-- 注意：这里需要根据实际情况更新现有数据
-- 如果现有数据是字符串格式的租户ID，需要转换为数字
-- UPDATE email_accounts SET tenant_id = CAST(tenant_id AS UNSIGNED) WHERE tenant_id REGEXP '^[0-9]+$';
-- UPDATE email_messages SET tenant_id = CAST(tenant_id AS UNSIGNED) WHERE tenant_id REGEXP '^[0-9]+$';

-- 5. 验证修改结果
SHOW COLUMNS FROM email_accounts LIKE 'tenant_id';
SHOW COLUMNS FROM email_messages LIKE 'tenant_id';

-- 6. 验证索引
SHOW INDEX FROM email_accounts WHERE Key_name LIKE '%tenant_id%';
SHOW INDEX FROM email_messages WHERE Key_name LIKE '%tenant_id%';

COMMIT; 