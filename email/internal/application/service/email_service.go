package service

import (
	"context"

	emailAppService "gitee.com/heiyee/platforms/email/internal/application/email/service"
)

// EmailService is a wrapper service for gRPC interface that adapts the existing EmailApplicationService
type EmailService struct {
	emailAppService *emailAppService.EmailApplicationService
}

// NewEmailService creates a new EmailService wrapper
func NewEmailService(emailAppService *emailAppService.EmailApplicationService) *EmailService {
	return &EmailService{
		emailAppService: emailAppService,
	}
}

// SendTemplateEmail sends template email using the existing application service
// Adapts the gRPC interface to the existing application service interface
func (s *EmailService) SendTemplateEmail(ctx context.Context, tenantID int64, templateCode string, to []string, variables map[string]string) (string, error) {
	// Convert variables from map[string]string to map[string]interface{}
	variablesInterface := make(map[string]interface{})
	for k, v := range variables {
		variablesInterface[k] = v
	}

	// Call the application service with correct parameters
	// The application service will get appId from context
	emailID, err := s.emailAppService.SendTemplateEmail(ctx, tenantID, templateCode, to, variablesInterface)
	if err != nil {
		return "", err
	}

	return emailID, nil
}

// CheckTemplateExists checks if a template exists
func (s *EmailService) CheckTemplateExists(ctx context.Context, tenantID int64, templateCode string) (bool, error) {
	// Call the application service
	return s.emailAppService.CheckTemplateExists(ctx, tenantID, templateCode)
}
