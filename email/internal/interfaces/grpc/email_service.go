package grpc

import (
	"context"

	"gitee.com/heiyee/platforms/email/api/emailpb"
	"gitee.com/heiyee/platforms/email/internal/application/service"
	"gitee.com/heiyee/platforms/pkg/common/errors"
	"go.uber.org/zap"
)

// EmailServiceServer implements the gRPC EmailService interface
type EmailServiceServer struct {
	emailpb.UnimplementedEmailServiceServer
	emailService *service.EmailService
	logger       *zap.Logger
}

// NewEmailServiceServer creates a new EmailServiceServer
func NewEmailServiceServer(emailService *service.EmailService, logger *zap.Logger) *EmailServiceServer {
	return &EmailServiceServer{
		emailService: emailService,
		logger:       logger,
	}
}

// SendTemplateEmail implements the SendTemplateEmail gRPC method
func (s *EmailServiceServer) SendTemplateEmail(ctx context.Context, req *emailpb.SendTemplateEmailRequest) (*emailpb.SendTemplateEmailResponse, error) {
	// Log the request
	s.logger.Info("Received SendTemplateEmail request",
		zap.Int64("tenant_id", req.TenantId),
		zap.Int64("internal_app_id", req.InternalAppId),
		zap.String("template_code", req.TemplateCode),
		zap.Strings("to", req.To),
		zap.String("request_id", req.RequestId),
	)
	// Validate request
	if req.TenantId < 0 {
		return &emailpb.SendTemplateEmailResponse{
			Code:    errors.CodeValidationError,
			Message: "tenant_id must be non-negative",
		}, nil
	}

	if req.InternalAppId <= 0 {
		return &emailpb.SendTemplateEmailResponse{
			Code:    errors.CodeValidationError,
			Message: "internal_app_id must be positive",
		}, nil
	}

	if req.TemplateCode == "" {
		return &emailpb.SendTemplateEmailResponse{
			Code:    errors.CodeValidationError,
			Message: "template_code is required",
		}, nil
	}

	if len(req.To) == 0 {
		return &emailpb.SendTemplateEmailResponse{
			Code:    errors.CodeValidationError,
			Message: "to field is required",
		}, nil
	}
	// Call the email service to send template email
	messageID, err := s.emailService.SendTemplateEmail(ctx, req.TenantId, req.TemplateCode, req.To, req.Variables)
	if err != nil {
		s.logger.Error("Failed to send template email",
			zap.Int64("tenant_id", req.TenantId),
			zap.String("template_code", req.TemplateCode),
			zap.Error(err),
		)

		return &emailpb.SendTemplateEmailResponse{
			Code:    errors.CodeInternalError,
			Message: "Failed to send email ",
		}, nil
	}

	s.logger.Info("Successfully sent template email",
		zap.Int64("tenant_id", req.TenantId),
		zap.String("template_code", req.TemplateCode),
		zap.String("message_id", messageID),
	)

	return &emailpb.SendTemplateEmailResponse{
		Code:    errors.CodeSuccess,
		Message: "Email sent successfully",
		Data: &emailpb.EmailSendResult{
			MessageId: messageID,
			Status:    "sent",
		},
	}, nil
}

// CheckTemplateExists implements the CheckTemplateExists gRPC method
func (s *EmailServiceServer) CheckTemplateExists(ctx context.Context, req *emailpb.CheckTemplateExistsRequest) (*emailpb.CheckTemplateExistsResponse, error) {
	// Log the request
	s.logger.Info("Received CheckTemplateExists request",
		zap.Int64("tenant_id", req.TenantId),
		zap.Int64("internal_app_id", req.InternalAppId),
		zap.String("template_code", req.TemplateCode),
	)

	// Validate request
	if req.TenantId < 0 {
		return &emailpb.CheckTemplateExistsResponse{
			Code:    errors.CodeValidationError,
			Message: "tenant_id must be non-negative",
		}, nil
	}

	if req.InternalAppId <= 0 {
		return &emailpb.CheckTemplateExistsResponse{
			Code:    errors.CodeValidationError,
			Message: "internal_app_id must be positive",
		}, nil
	}

	if req.TemplateCode == "" {
		return &emailpb.CheckTemplateExistsResponse{
			Code:    errors.CodeValidationError,
			Message: "template_code is required",
		}, nil
	}

	// Call the application service to check template existence
	exists, err := s.emailService.CheckTemplateExists(ctx, req.TenantId, req.TemplateCode)
	if err != nil {
		s.logger.Error("Failed to check template existence",
			zap.Error(err),
			zap.Int64("tenant_id", req.TenantId),
			zap.String("template_code", req.TemplateCode),
		)
		return &emailpb.CheckTemplateExistsResponse{
			Code:    errors.CodeInternalError,
			Message: "internal server error",
		}, nil
	}

	// Build response
	response := &emailpb.CheckTemplateExistsResponse{
		Code:    200,
		Message: "success",
		Data: &emailpb.TemplateExistsResult{
			Exists: exists,
		},
	}

	// If template exists, try to get template info
	if exists {
		// TODO: Get template info from repository and populate TemplateInfo
		// For now, just set basic info
		response.Data.TemplateInfo = &emailpb.TemplateInfo{
			TemplateCode: req.TemplateCode,
			IsEnabled:    true,
		}
	}

	s.logger.Info("Template existence check completed",
		zap.Int64("tenant_id", req.TenantId),
		zap.String("template_code", req.TemplateCode),
		zap.Bool("exists", exists),
	)

	return response, nil
}
