# Emails模块错误处理机制

## 概述

Emails模块实现了类似users模块的错误处理机制，提供了统一的错误码定义、错误类型和错误处理流程。

## 文件结构

```
email/internal/domain/errors/
├── email_errors.go          # 错误定义和错误类型
└── README.md               # 本文档

email/internal/interfaces/http/handlers/
├── error_handler.go        # 错误处理器
└── template_handler_example.go  # 使用示例
```

## 错误码范围

邮件模块使用错误码范围：**200000-299999**

### 错误码分类

- **200000-200099**: 邮件发送相关错误
- **200100-200199**: 邮件模板相关错误
- **200200-200299**: 邮件账号相关错误
- **200300-200399**: 租户相关错误
- **200400-200499**: 订阅者相关错误
- **200500-200599**: 邮件列表相关错误
- **200600-200699**: 邮件活动相关错误
- **200700-200799**: 统计分析相关错误
- **200900-200999**: 系统错误

## 核心组件

### 1. EmailError 错误类型

```go
type EmailError struct {
    Code    int    `json:"code"`              // 错误码
    Message string `json:"message"`           // 错误消息
    Details string `json:"details,omitempty"` // 错误详情
}
```

### 2. 错误创建函数

```go
// 基础错误创建
NewEmailError(code int, details ...string) *EmailError

// 便捷错误创建函数
NewEmailNotFoundError(emailID interface{}) *EmailError
NewTemplateNameExistsError(name string) *EmailError
NewAccountNotFoundError(accountID interface{}) *EmailError
NewTenantNotFoundError(tenantID interface{}) *EmailError
// ... 更多便捷函数
```

### 3. 错误处理器

```go
// 处理邮件模块自定义错误
HandleEmailError(c *gin.Context, err error)

// 检查是否为邮件模块错误
IsEmailError(err error) bool

// 获取错误码和消息
GetEmailErrorCode(err error) int
GetEmailErrorMessage(err error) string
```

## 使用方法

### 1. 在Handler中使用

```go
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
    var req dto.CreateTemplateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonResponse.GinValidationError(c, err)
        return
    }

    result, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
    if err != nil {
        // 使用新的错误处理机制
        HandleEmailError(c, err)
        return
    }

    commonResponse.Created(c, result)
}
```

### 2. 在业务逻辑中抛出具体错误

```go
// 检查模板名称是否已存在
if templateName == "existing_template" {
    customErr := emailErrors.NewTemplateNameExistsError(templateName)
    return customErr
}

// 检查账号是否未激活
if account.Status != "active" {
    customErr := emailErrors.NewAccountInactiveError(accountID)
    return customErr
}
```

### 3. 抛出系统错误

```go
// 数据库错误
if err != nil {
    return emailErrors.NewDatabaseError("create_template", err.Error())
}

// 第三方服务错误
if err != nil {
    return emailErrors.NewThirdPartyError("smtp_service", err.Error())
}
```

## 错误响应示例

### 1. 资源不存在错误

```json
{
    "code": 404,
    "message": "模板不存在",
    "data": null,
    "meta": {
        "request_id": "req_123456",
        "timestamp": **********
    }
}
```

### 2. 资源已存在错误

```json
{
    "code": 409,
    "message": "模板名称已存在，请使用其他名称",
    "data": null,
    "meta": {
        "request_id": "req_123456",
        "timestamp": **********
    }
}
```

### 3. 字段验证错误

```json
{
    "code": 400,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "name": "账号名称无效",
            "host": "账号主机无效"
        }
    },
    "meta": {
        "request_id": "req_123456",
        "timestamp": **********
    }
}
```

### 4. 系统错误

```json
{
    "code": 500,
    "message": "系统错误",
    "data": null,
    "meta": {
        "request_id": "req_123456",
        "timestamp": **********
    }
}
```

## 最佳实践

### 1. 错误码使用

- 使用预定义的错误码常量，避免硬编码
- 为每个业务场景选择合适的错误码
- 在错误详情中提供有用的调试信息

### 2. 错误消息

- 错误消息应该对用户友好
- 避免暴露系统内部信息
- 提供有意义的错误提示

### 3. 错误处理

- 在Handler层统一使用`HandleEmailError`处理错误
- 在业务逻辑层抛出具体的`EmailError`
- 记录详细的错误信息到日志系统

### 4. 错误传播

- 使用`fmt.Errorf`包装错误，保留错误上下文
- 在错误链中保持错误码的一致性
- 避免重复的错误处理逻辑

## 迁移指南

### 从现有错误处理迁移

1. **替换通用错误响应**
   ```go
   // 旧方式
   commonResponse.InternalError(c, err)
   
   // 新方式
   HandleEmailError(c, err)
   ```

2. **替换具体错误**
   ```go
   // 旧方式
   return errors.New("模板名称已存在")
   
   // 新方式
   return emailErrors.NewTemplateNameExistsError(name)
   ```

3. **添加错误详情**
   ```go
   // 旧方式
   return emailErrors.NewEmailError(CodeEmailNotFound)
   
   // 新方式
   return emailErrors.NewEmailNotFoundError(emailID)
   ```

## 扩展指南

### 添加新的错误码

1. 在`email_errors.go`中添加错误码常量
2. 在`errorMessages`映射中添加错误消息
3. 在`error_handler.go`中添加错误处理逻辑
4. 创建便捷的错误创建函数

### 添加新的错误类型

1. 定义新的错误码范围
2. 添加相应的错误消息
3. 在错误处理器中添加处理逻辑
4. 创建便捷的错误创建函数

## 注意事项

1. **错误码唯一性**: 确保错误码在模块内唯一
2. **错误消息一致性**: 保持错误消息的格式和风格一致
3. **错误处理完整性**: 确保所有错误都有相应的处理逻辑
4. **性能考虑**: 避免在错误处理中执行耗时操作
5. **安全性**: 不要在错误消息中暴露敏感信息 