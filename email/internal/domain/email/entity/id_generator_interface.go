package entity

import (
	"context"
	"fmt"
	"time"
)

// IDGenerator ID生成器接口 - 为依赖注入设计
type IDGenerator interface {
	// 业务类型特定的ID生成方法
	GenerateEmailAccountID(ctx context.Context, tenantID int64) (int64, error)
	GenerateEmailMessageID(ctx context.Context, tenantID int64) (int64, error)
	GenerateEmailTemplateID(ctx context.Context, tenantID int64) (int64, error)

	// 通用ID生成方法
	GenerateID(ctx context.Context, businessType string, tenantID int64) (int64, error)

	// 临时ID生成方法（使用雪花算法）
	GenerateSessionID(ctx context.Context) (string, error)
	GenerateRequestID(ctx context.Context) (string, error)
}

// EntityFactory 实体工厂 - 使用依赖注入的ID生成器
type EntityFactory struct {
	idGenerator IDGenerator
}

// NewEntityFactory 创建实体工厂
func NewEntityFactory(idGenerator IDGenerator) *EntityFactory {
	return &EntityFactory{
		idGenerator: idGenerator,
	}
}

// NewEmailAccount 创建新邮件账号 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewEmailAccount(ctx context.Context, tenantID, internalAppID int64, name string, accountType AccountType, provider, fromAddress string) (*EmailAccount, error) {
	if tenantID == 0 {
		return nil, NewValidationError("tenant_id", "租户ID不能为空")
	}
	if internalAppID <= 0 {
		return nil, NewValidationError("internal_app_id", "内部应用ID不能为空")
	}
	if name == "" {
		return nil, NewValidationError("name", "账号名称不能为空")
	}
	if fromAddress == "" {
		return nil, NewValidationError("from_address", "发送地址不能为空")
	}

	// 使用依赖注入的ID生成器生成账号ID
	accountID, err := f.idGenerator.GenerateEmailAccountID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate email account ID: %w", err)
	}

	now := time.Now()
	return &EmailAccount{
		ID:            accountID,
		TenantID:      tenantID, // 直接使用int64类型
		InternalAppID: internalAppID,
		Name:          name,
		Type:          accountType,
		Provider:      provider,
		FromAddress:   fromAddress,
		IsSSL:         true,
		IsActive:      true,
		TestStatus:    TestStatusUntested,
		Config:        make(map[string]interface{}),
		CreatedAt:     now,
		UpdatedAt:     now,
		Version:       1,
	}, nil
}

// NewEmailMessage 创建新邮件消息 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewEmailMessage(ctx context.Context, tenantID, internalAppID int64, toAddress, subject string) (*EmailMessage, error) {
	if tenantID == 0 {
		return nil, NewValidationError("tenant_id", "租户ID不能为空")
	}
	if internalAppID <= 0 {
		return nil, NewValidationError("internal_app_id", "内部应用ID不能为空")
	}
	if toAddress == "" {
		return nil, NewValidationError("to_address", "收件人地址不能为空")
	}
	if subject == "" {
		return nil, NewValidationError("subject", "邮件主题不能为空")
	}

	// 使用依赖注入的ID生成器生成邮件ID
	messageID, err := f.idGenerator.GenerateEmailMessageID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate email message ID: %w", err)
	}

	now := time.Now()
	return &EmailMessage{
		ID:            messageID,
		EmailID:       messageID, // 使用分布式ID作为业务主键
		TenantID:      tenantID,  // 直接使用int64类型
		InternalAppID: internalAppID,
		ToAddress:     toAddress,
		Subject:       subject,
		Status:        "pending",
		RetryCount:    0,
		MaxRetries:    3,
		CreatedAt:     now,
		UpdatedAt:     now,
	}, nil
}

// NewTemplateEmailMessage 创建模板邮件消息 - 使用依赖注入的ID生成器
func (f *EntityFactory) NewTemplateEmailMessage(ctx context.Context, tenantID, internalAppID int64, templateID int64, toAddress string, variables map[string]interface{}) (*EmailMessage, error) {
	if tenantID == 0 {
		return nil, NewValidationError("tenant_id", "租户ID不能为空")
	}
	if internalAppID <= 0 {
		return nil, NewValidationError("internal_app_id", "内部应用ID不能为空")
	}
	if templateID == 0 {
		return nil, NewValidationError("template_id", "模板ID不能为空")
	}
	if toAddress == "" {
		return nil, NewValidationError("to_address", "收件人地址不能为空")
	}

	// 使用依赖注入的ID生成器生成邮件ID
	messageID, err := f.idGenerator.GenerateEmailMessageID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate template email message ID: %w", err)
	}

	now := time.Now()
	return &EmailMessage{
		ID:            messageID,
		EmailID:       messageID, // 使用分布式ID作为业务主键
		TenantID:      tenantID,  // 直接使用int64类型
		InternalAppID: internalAppID,
		TemplateID:    fmt.Sprintf("%d", templateID),
		ToAddress:     toAddress,
		Variables:     variables,
		Status:        "pending",
		RetryCount:    0,
		MaxRetries:    3,
		CreatedAt:     now,
		UpdatedAt:     now,
	}, nil
}
