package event

import (
	"time"
)

// DomainEvent 是领域事件的最小通用接口
type DomainEvent interface {
	// Name 返回事件名（用于路由/观测）
	Name() string
	// OccurredAt 返回事件发生时间
	OccurredAt() time.Time
}

// baseEvent 提供通用字段
type baseEvent struct {
	occurredAt time.Time
}

func (e baseEvent) OccurredAt() time.Time { return e.occurredAt }

// EmailSentEvent 邮件发送成功事件
type EmailSentEvent struct {
	baseEvent
	EmailID       int64
	TenantID      int64
	InternalAppID int64
	ToAddress     string
	Subject       string
}

func NewEmailSentEvent(emailID, tenantID, internalAppID int64, to, subject string) *EmailSentEvent {
	return &EmailSentEvent{
		baseEvent:     baseEvent{occurredAt: time.Now()},
		EmailID:       emailID,
		TenantID:      tenantID,
		InternalAppID: internalAppID,
		ToAddress:     to,
		Subject:       subject,
	}
}

func (e *EmailSentEvent) Name() string { return "email.sent" }

// EmailFailedEvent 邮件发送失败事件
type EmailFailedEvent struct {
	baseEvent
	EmailID       int64
	TenantID      int64
	InternalAppID int64
	ToAddress     string
	Subject       string
	ErrorMessage  string
}

func NewEmailFailedEvent(emailID, tenantID, internalAppID int64, to, subject, errMsg string) *EmailFailedEvent {
	return &EmailFailedEvent{
		baseEvent:     baseEvent{occurredAt: time.Now()},
		EmailID:       emailID,
		TenantID:      tenantID,
		InternalAppID: internalAppID,
		ToAddress:     to,
		Subject:       subject,
		ErrorMessage:  errMsg,
	}
}

func (e *EmailFailedEvent) Name() string { return "email.failed" }
