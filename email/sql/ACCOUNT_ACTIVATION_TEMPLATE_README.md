# 账户激活邮件模板

## 概述

本目录包含账户激活邮件模板的数据库初始化SQL语句，用于将账户激活邮件模板存储到数据库中，实现模板的动态管理和维护。

## 文件说明

### 1. `insert_account_activation_template.sql`
- **用途**: 插入账户激活邮件模板（完整版）
- **模板代码**: `account_activation`
- **类型**: 系统级模板（`is_system = 1`, `tenant_id = 0`）
- **特点**: 完整的SQL语句，包含详细注释和验证查询

### 2. `insert_account_activation_template_simple.sql`
- **用途**: 插入账户激活邮件模板（简化版）
- **特点**: 精简的SQL语句，便于直接执行
- **适用场景**: 快速部署和测试

## 模板信息

### 账户激活模板 (`account_activation`)

#### 基本信息
- **模板代码**: `account_activation`
- **模板名称**: 账户激活邮件模板
- **模板类型**: HTML (type = 1)
- **邮件主题**: 【{{appName}}】激活您的账户
- **系统级**: 是 (`is_system = 1`)

#### 模板变量
| 变量名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `link` | string | ✅ | 账户激活链接 |
| `appName` | string | ✅ | 应用名称 |
| `expireTime` | string | ✅ | 激活链接过期时间 |
| `current_time` | string | ✅ | 当前发送时间 |

#### 模板特点
- **响应式设计**: 支持移动端和桌面端显示
- **现代化UI**: 使用渐变色彩和阴影效果
- **安全性提醒**: 明确标注链接过期时间
- **用户友好**: 提供清晰的激活说明和操作指引

## 使用示例

### 1. 执行SQL语句
```sql
-- 连接到数据库
mysql -h 139.224.32.190 -P 3308 -u root -p platforms-email

-- 选择数据库
USE platforms-email;

-- 执行完整版INSERT语句
source /path/to/insert_account_activation_template.sql;

-- 或执行简化版INSERT语句
source /path/to/insert_account_activation_template_simple.sql;
```

### 2. 验证插入结果
```sql
-- 查询插入的模板
SELECT 
    id,
    template_code,
    name,
    is_system,
    is_active,
    created_at
FROM email_templates 
WHERE template_code = 'account_activation' 
AND is_system = 1;
```

### 3. 在代码中使用
```go
// 发送激活邮件示例
variables := map[string]interface{}{
    "link":        "https://example.com/activate?token=abc123",
    "appName":     "我的应用",
    "expireTime":  "2025-01-28 12:00:00",
    "current_time": time.Now().Format("2006-01-02 15:04:05"),
}

// 调用邮件发送服务
err := emailService.SendWithTemplate(
    ctx,
    "account_activation",
    "<EMAIL>",
    variables,
)
```

## 模板预览

### HTML版本特点
- 🎨 **现代化设计**: 渐变背景、圆角按钮、阴影效果
- 📱 **响应式布局**: 自适应不同屏幕尺寸
- 🔐 **安全提醒**: 突出显示过期时间警告
- 📋 **信息展示**: 清晰的信息框展示关键信息
- 🎯 **行动导向**: 醒目的激活按钮引导用户操作

### 纯文本版本特点
- 📝 **简洁明了**: 去除格式，保留核心信息
- 🔗 **链接展示**: 完整显示激活链接
- ⚠️ **重要提醒**: 突出显示过期时间
- 📋 **信息整理**: 结构化的信息展示

## 自定义配置

### 1. 修改样式
可以通过修改HTML内容中的CSS样式来自定义模板外观：
- 修改 `.header` 的背景渐变色
- 调整 `.activation-button` 的按钮样式
- 更改 `.info-box` 的信息框样式

### 2. 添加新变量
如需添加新的模板变量，需要：
1. 在HTML和纯文本内容中添加变量占位符
2. 在 `variables` JSON字段中定义变量类型和描述
3. 在发送时提供对应的变量值

### 3. 多语言支持
可以通过创建不同语言版本的模板来实现多语言支持：
- 创建 `account_activation_en` 英文版本
- 创建 `account_activation_ja` 日文版本
- 根据用户语言偏好选择对应模板

## 注意事项

1. **系统级模板**: 该模板设置为系统级模板（`is_system = 1`），所有租户都可以使用
2. **租户ID**: 系统级模板的租户ID为0
3. **账户ID**: 模板不绑定特定账户（`account_id = 0`），可以在发送时动态指定
4. **变量验证**: 发送前请确保所有必填变量都已提供
5. **链接安全**: 激活链接应包含适当的token验证机制
6. **过期时间**: 建议设置合理的链接过期时间（如24小时）

## 相关文档

- [邮件模板管理API文档](../docs/openapi.yaml)
- [邮件发送服务文档](../internal/application/service/README.md)
- [模板变量系统说明](../internal/domain/template/README.md) 