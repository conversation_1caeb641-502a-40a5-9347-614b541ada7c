<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置</title>
    <style type="text/css">
        body {
            margin: 0;
            padding: 30px 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            font-size: 15px;
            line-height: 1.7;
            color: #333;
            background-color: #fff;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 25px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        h1 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            color: #333;
            letter-spacing: 0.5px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            margin: 0 0 25px 0;
            color: #333;
        }
        
        p {
            margin: 0 0 20px 0;
            line-height: 1.7;
        }
        
        .link-section {
            margin: 30px 0;
            padding: 25px;
            background-color: #fafafa;
            border: 1px dashed #d0d0d0;
            border-radius: 6px;
            text-align: center;
        }
        
        .link {
            color: #333;
            text-decoration: underline;
            word-break: break-all;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            display: inline-block;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            margin-top: 10px;
        }
        
        .info {
            margin: 30px 0;
            padding: 25px;
            background-color: #f8f8f8;
            border-left: 4px solid #666;
            border-radius: 0 4px 4px 0;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 0;
            color: #333;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .info-item {
            margin: 12px 0;
            display: flex;
            align-items: flex-start;
        }
        
        .info-label {
            font-weight: 500;
            color: #555;
            min-width: 80px;
            margin-right: 15px;
        }
        
        .info-value {
            color: #666;
            word-break: break-all;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 13px;
        }
        
        .warning {
            margin: 30px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            position: relative;
        }
        
        .warning::before {
            content: '⚠';
            position: absolute;
            left: 20px;
            top: 20px;
            font-size: 18px;
            color: #666;
        }
        
        .warning-content {
            margin-left: 35px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 25px;
            border-top: 1px solid #f0f0f0;
            text-align: center;
            font-size: 12px;
            color: #888;
            line-height: 1.5;
        }
        
        .footer p {
            margin: 8px 0;
        }
        
        .divider {
            margin: 35px 0;
            height: 1px;
            background: linear-gradient(to right, transparent, #ddd, transparent);
        }
        
        /* 移动端适配 */
        @media only screen and (max-width: 640px) {
            body {
                padding: 15px 10px;
            }
            
            .container {
                padding: 25px 20px;
                border: 1px solid #e5e5e5;
            }
            
            h1 {
                font-size: 20px;
            }
            
            .greeting {
                font-size: 16px;
            }
            
            .link-section {
                padding: 20px 15px;
            }
            
            .link {
                padding: 10px 15px;
                font-size: 13px;
            }
            
            .info {
                padding: 20px 15px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-label {
                margin-bottom: 5px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>密码重置</h1>
        </div>
        
        <p class="greeting">您好，{{username}}！</p>
        
        <p>我们收到了您的密码重置请求。请点击以下链接完成密码重置操作：</p>
        
        <div class="link-section">
            <div>点击下方链接重置密码</div>
            <a href="{{link}}" class="link">{{link}}</a>
        </div>
        
        <div class="info">
            <div class="info-title">重置信息</div>
            <div class="info-item">
                <span class="info-label">过期时间：</span>
                <span class="info-value">{{expire_time}}</span>
            </div>
            <div class="info-item">
                <span class="info-label">请求时间：</span>
                <span class="info-value">{{current_time}}</span>
            </div>
        </div>
        
        <div class="warning">
            <div class="warning-content">
                <strong>安全提醒：</strong>如果您没有发起密码重置请求，请忽略此邮件。请勿将重置链接分享给他人，以确保账户安全。
            </div>
        </div>
        
        <div class="divider"></div>
        
        <p>如果上方链接无法点击，请复制链接地址到浏览器地址栏中访问。</p>
        
        <div class="footer">
            <p>本邮件由系统自动发送，请勿回复</p>
            <p>{{company_name}} | {{current_time}}</p>
        </div>
    </div>
</body>
</html>