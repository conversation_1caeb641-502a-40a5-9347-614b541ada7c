<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置邮件模板预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .preview-header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .preview-content {
            padding: 20px;
        }
        .template-frame {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background: white;
        }
        iframe {
            width: 100%;
            height: 800px;
            border: none;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>密码重置邮件模板预览</h1>
            <p>现代化设计 • 响应式布局 • 美观界面</p>
        </div>
        <div class="preview-content">
            <div class="controls">
                <button class="btn" onclick="loadTemplate()">加载模板</button>
                <button class="btn" onclick="toggleMobile()">切换移动端视图</button>
            </div>
            <div class="template-frame">
                <iframe id="templateFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        let isMobile = false;
        
        function loadTemplate() {
            const templateContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置 - 示例公司</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
        }
        
        .email-wrapper {
            max-width: 680px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            padding: 50px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }
        
        .logo svg {
            width: 40px;
            height: 40px;
            fill: white;
        }
        
        .header-title {
            color: white;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 60px 40px;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .message {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.7;
            margin-bottom: 40px;
            text-align: center;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-section {
            text-align: center;
            margin: 50px 0;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 18px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .reset-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
        }
        
        .reset-button:hover::before {
            left: 100%;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 30px;
            margin: 40px 0;
            position: relative;
        }
        
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 16px 16px 0 0;
        }
        
        .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .info-title svg {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            fill: #4f46e5;
        }
        
        .info-grid {
            display: grid;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 15px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #374151;
            min-width: 100px;
            flex-shrink: 0;
        }
        
        .info-value {
            color: #6b7280;
            text-align: right;
            word-break: break-all;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
            display: flex;
            align-items: flex-start;
        }
        
        .security-notice svg {
            width: 24px;
            height: 24px;
            fill: #d97706;
            margin-right: 15px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .security-text {
            color: #92400e;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .footer {
            background: #f8fafc;
            padding: 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .footer-text {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .company-info {
            color: #9ca3af;
            font-size: 12px;
            font-weight: 500;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 30px 0;
        }
        
        @media only screen and (max-width: 640px) {
            body {
                padding: 20px 10px;
            }
            
            .email-wrapper {
                border-radius: 16px;
            }
            
            .header {
                padding: 40px 30px;
            }
            
            .header-title {
                font-size: 28px;
            }
            
            .header-subtitle {
                font-size: 16px;
            }
            
            .content {
                padding: 40px 30px;
            }
            
            .greeting {
                font-size: 22px;
            }
            
            .message {
                font-size: 15px;
            }
            
            .reset-button {
                padding: 16px 32px;
                font-size: 15px;
            }
            
            .info-card {
                padding: 25px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .info-value {
                text-align: left;
            }
            
            .footer {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="header">
            <div class="logo">
                <svg viewBox="0 0 24 24">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1M12 7C13.4 7 14.8 8.6 14.8 10V11.5C15.4 11.5 16 12.1 16 12.7V16.7C16 17.4 15.4 18 14.8 18H9.2C8.6 18 8 17.4 8 16.8V12.8C8 12.1 8.6 11.5 9.2 11.5V10C9.2 8.6 10.6 7 12 7M12 8.2C11.2 8.2 10.5 8.7 10.5 10V11.5H13.5V10C13.5 8.7 12.8 8.2 12 8.2Z"/>
                </svg>
            </div>
            <h1 class="header-title">密码重置</h1>
            <p class="header-subtitle">安全验证请求</p>
        </div>
        
        <div class="content">
            <h2 class="greeting">您好，张三！</h2>
            
            <p class="message">
                我们收到了您的密码重置请求。为了确保您的账户安全，请点击下方按钮来重置您的密码。此操作需要在有效期内完成。
            </p>
            
            <div class="cta-section">
                <a href="#" class="reset-button">
                    立即重置密码
                </a>
            </div>
            
            <div class="info-card">
                <h3 class="info-title">
                    <svg viewBox="0 0 24 24">
                        <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
                    </svg>
                    重置信息
                </h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">过期时间</span>
                        <span class="info-value">2024-12-28 15:30:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">请求时间</span>
                        <span class="info-value">2024-12-28 14:30:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">重置链接</span>
                        <span class="info-value">https://example.com/reset?token=abc123...</span>
                    </div>
                </div>
            </div>
            
            <div class="security-notice">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2M12,21.5C12,21.5 6.5,18.5 6.5,12V6.5L12,4.5L17.5,6.5V12C17.5,18.5 12,21.5 12,21.5Z"/>
                </svg>
                <div class="security-text">
                    <strong>安全提醒：</strong>如果您没有请求重置密码，请忽略此邮件。为了保护您的账户安全，请不要将此重置链接分享给任何人。
                </div>
            </div>
            
            <div class="divider"></div>
            
            <p class="message">
                如果上方按钮无法点击，您也可以复制上述链接地址到浏览器地址栏中访问。
            </p>
        </div>
        
        <div class="footer">
            <div class="footer-content">
                <p class="footer-text">此邮件由系统自动发送，请勿直接回复</p>
                <p class="company-info">示例科技公司 • 发送时间：2024-12-28 14:30:00</p>
            </div>
        </div>
    </div>
</body>
</html>
            `;
            
            const iframe = document.getElementById('templateFrame');
            const doc = iframe.contentDocument || iframe.contentWindow.document;
            doc.open();
            doc.write(templateContent);
            doc.close();
        }
        
        function toggleMobile() {
            const iframe = document.getElementById('templateFrame');
            if (!isMobile) {
                iframe.style.width = '375px';
                iframe.style.margin = '0 auto';
                iframe.style.display = 'block';
                isMobile = true;
            } else {
                iframe.style.width = '100%';
                iframe.style.margin = '0';
                isMobile = false;
            }
        }
        
        // 自动加载模板
        window.onload = function() {
            loadTemplate();
        };
    </script>
</body>
</html>