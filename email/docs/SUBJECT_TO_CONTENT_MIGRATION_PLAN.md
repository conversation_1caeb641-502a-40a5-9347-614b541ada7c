# 邮件主题维护移动到模板内容修改范围文档

## 一、修改背景

当前系统中邮件主题（Subject）是独立维护的，但主题同样支持变量替换功能。为了统一管理，将邮件主题的维护移动到模板内容中，使其与HTML内容和纯文本内容保持一致的管理方式。

**新的设计思路**：
- 保存基本信息时不再填写subject
- 在邮件模板TAB中填写subject
- 通过`templates/content`接口提交subject
- **subject也要做参数校验，变量必须是存在的，底层可以和模版的校验共用**
- 参数验证使用 go-playground/validator 如果无法满足视情况 增加额外验证逻辑

## 二、当前状态分析

### 2.1 数据库现状
- **表名**: `email_templates`
- **字段**: `subject` VARCHAR(200) NOT NULL
- **数据**: 现有模板数据包含subject字段内容
- **索引**: 无subject相关索引

### 2.2 后端现状
- **实体层**: `EmailTemplate.Subject` 字段存在
- **DTO层**: 所有模板相关DTO都包含subject字段
- **服务层**: 模板渲染时独立处理subject变量替换
- **API层**: 模板保存/更新接口支持subject参数

### 2.3 前端现状
- **类型定义**: `EmailTemplate.subject` 字段存在
- **表单组件**: 独立的主题输入框（在基本信息TAB中）
- **详情页面**: 单独显示主题内容
- **编辑器**: 主题与内容分离编辑

## 三、修改范围详细规划

### 3.1 数据库层面（保持现状）

#### 3.1.1 数据库结构
- **保持字段**: `subject` VARCHAR(200) NOT NULL
- **原因**: 数据库字段已存在，无需修改结构
- **策略**: 通过应用层逻辑实现主题内容化

#### 3.1.2 数据迁移需求
- **无需迁移**: 现有数据保持不变
- **策略**: 通过前端界面调整实现主题内容化

### 3.2 后端代码层面

#### 3.2.1 实体层修改
**文件**: `email/internal/domain/template/entity/email_template.go`

**修改内容**:
- **保持字段**: `Subject string` 字段保留
- **新增方法**: 
  - `ValidateSubjectInContent()` - 验证内容中的主题格式
  - `SetSubjectFromContent()` - 根据内容设置主题
  - `ValidateSubjectVariables()` - 验证主题中的变量是否存在

**新增方法示例**:
```go
// ValidateSubjectInContent 验证内容中的主题格式
func (t *EmailTemplate) ValidateSubjectInContent(subject string) error {
    if len(subject) > 200 { // 使用 
        return errors.New("subject length exceeds 200 characters")
    }
    return nil
}

// ValidateSubjectVariables 验证主题中的变量是否存在
func (t *EmailTemplate) ValidateSubjectVariables(subject string) error {
    // 提取主题中的变量
    variables := t.ExtractVariablesFromText(subject)
    
    // 检查每个变量是否在模板变量定义中存在
    for _, variable := range variables {
        if _, exists := t.Variables[variable]; !exists {
            return fmt.Errorf("variable '%s' in subject is not defined in template variables", variable)
        }
    }
    return nil
}

// SetSubjectFromContent 根据内容设置主题
func (t *EmailTemplate) SetSubjectFromContent(subject string) error {
    if err := t.ValidateSubjectInContent(subject); err != nil {
        return err
    }
    
    // 验证主题中的变量
    if err := t.ValidateSubjectVariables(subject); err != nil {
        return err
    }
    
    t.Subject = subject
    return nil
}

// ExtractVariablesFromText 从文本中提取变量（与HTML内容共用）
func (t *EmailTemplate) ExtractVariablesFromText(text string) []string {
    // 使用现有的变量提取逻辑
    return t.ExtractTemplateVariables(text)
}
```

#### 3.2.2 DTO层修改
**文件**: `email/internal/application/template/dto/template_dto.go`

**修改内容**:
- **移除**: `SaveOrUpdateTemplateRequest.Subject` 字段
- **保持**: `UpdateTemplateContentRequest` 中的subject字段
- **修改验证**: 移除基本信息中subject的必填验证
- **增强验证**: 在content接口中添加subject变量验证

**修改示例**:
```go
// 移除基本信息中的subject字段
type SaveOrUpdateTemplateRequest struct {
    TemplateID  int64  `json:"template_id,omitempty"`
    Name        string `json:"name" validate:"required,min=1,max=100"`
    Type        int    `json:"type" validate:"required,min=1,max=6"`
    Status      int    `json:"status" validate:"required,min=1,max=4"`
    AccountID   int64  `json:"account_id" validate:"required"`
    Description string `json:"description,omitempty" validate:"max=255"`
    // 移除: Subject string `json:"subject" validate:"required,min=1,max=200"`
}

// 保持内容更新中的subject字段，增强验证
type UpdateTemplateContentRequest struct {
    TemplateID       int64  `json:"template_id" validate:"required"`
    HTMLContent      string `json:"html_content,omitempty" validate:"max=50000"`
    PlainTextContent string `json:"plain_text_content,omitempty" validate:"max=10000"`
    Subject          string `json:"subject,omitempty" validate:"max=200"` // 保持此字段，但验证逻辑增强
    Variables        map[string]TemplateVariable `json:"variables,omitempty"`
}
```

#### 3.2.3 应用服务层修改
**文件**: `email/internal/application/template/service/template_application_service.go`

**修改内容**:
- **修改方法**: 
  - `SaveOrUpdateTemplate()` - 移除subject处理逻辑
  - `UpdateTemplateContent()` - 增强subject处理逻辑，添加变量验证
- **新增方法**:
  - `validateSubjectTemplate()` - 验证主题模板
  - `validateSubjectVariables()` - 验证主题中的变量

**修改示例**:
```go
func (s *TemplateApplicationService) SaveOrUpdateTemplate(ctx context.Context, req *dto.SaveOrUpdateTemplateRequest) (*dto.SaveOrUpdateTemplateResponse, error) {
    // ... 现有逻辑 ...
    
    // 移除subject相关处理
    // 不再处理req.Subject
    
    // ... 继续现有逻辑 ...
}

func (s *TemplateApplicationService) UpdateTemplateContent(ctx context.Context, req *dto.UpdateTemplateContentRequest) (*dto.UpdateTemplateContentResponse, error) {
    // ... 现有逻辑 ...
    
    // 增强subject处理，添加变量验证
    if req.Subject != "" {
        // 验证主题格式
        if err := template.ValidateSubjectInContent(req.Subject); err != nil {
            return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, fmt.Sprintf("subject validation failed: %v", err))
        }
        
        // 验证主题中的变量是否存在于模板变量定义中
        if err := template.ValidateSubjectVariables(req.Subject); err != nil {
            return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, fmt.Sprintf("subject variables validation failed: %v", err))
        }
        
        // 设置主题
        if err := template.SetSubjectFromContent(req.Subject); err != nil {
            return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
        }
    }
    
    // ... 继续现有逻辑 ...
}

// 新增：验证主题模板
func (s *TemplateApplicationService) validateSubjectTemplate(subject string, variables map[string]TemplateVariable) error {
    // 提取主题中的变量
    subjectVariables := s.extractVariablesFromText(subject)
    
    // 检查每个变量是否在模板变量定义中存在
    for _, variable := range subjectVariables {
        if _, exists := variables[variable]; !exists {
            return fmt.Errorf("variable '%s' in subject is not defined in template variables", variable)
        }
    }
    return nil
}

// 新增：从文本中提取变量（与模板内容共用逻辑）
func (s *TemplateApplicationService) extractVariablesFromText(text string) []string {
    // 使用正则表达式提取 {{variable}} 格式的变量
    re := regexp.MustCompile(`\{\{([^}]+)\}\}`)
    matches := re.FindAllStringSubmatch(text, -1)
    
    variables := make([]string, 0)
    for _, match := range matches {
        if len(match) > 1 {
            variables = append(variables, match[1])
        }
    }
    
    return variables
}
```

#### 3.2.4 邮件发送服务修改
**文件**: 
- `email/internal/application/email/service/email_application_service.go`
- `email/internal/application/email/service/email_account_application_service.go`

**修改内容**:
- **保持**: 现有主题渲染逻辑不变
- **新增方法**:
  - `validateSubjectContent()` - 验证主题内容
  - `validateSubjectVariables()` - 验证主题变量

**修改示例**:
```go
func (s *EmailApplicationService) renderTemplate(tmpl *templateEntity.EmailTemplate, variables map[string]interface{}) (string, string, string, error) {
    // ... 现有逻辑 ...
    
    // 验证主题中的变量
    if err := s.validateSubjectVariables(tmpl.Subject, tmpl.Variables); err != nil {
        return "", "", "", fmt.Errorf("subject variables validation failed: %v", err)
    }
    
    // 保持现有主题渲染逻辑
    subject := s.renderTemplateContent(tmpl.Subject, stringVariables)
    
    return subject, htmlContent, textContent, nil
}

// 新增：验证主题变量
func (s *EmailApplicationService) validateSubjectVariables(subject string, templateVariables map[string]TemplateVariable) error {
    // 提取主题中的变量
    subjectVariables := s.extractVariablesFromText(subject)
    
    // 检查每个变量是否在模板变量定义中存在
    for _, variable := range subjectVariables {
        if _, exists := templateVariables[variable]; !exists {
            return fmt.Errorf("variable '%s' in subject is not defined in template variables", variable)
        }
    }
    return nil
}

// 新增：从文本中提取变量（与模板内容共用逻辑）
func (s *EmailApplicationService) extractVariablesFromText(text string) []string {
    // 使用与模板内容相同的变量提取逻辑
    re := regexp.MustCompile(`\{\{([^}]+)\}\}`)
    matches := re.FindAllStringSubmatch(text, -1)
    
    variables := make([]string, 0)
    for _, match := range matches {
        if len(match) > 1 {
            variables = append(variables, match[1])
        }
    }
    
    return variables
}
```

#### 3.2.5 数据模型层修改
**文件**: `email/internal/infrastructure/persistence/model/email_template_model.go`

**修改内容**:
- **保持字段**: `Subject string` 字段保留
- **新增方法**: 无需修改，保持现有映射

### 3.3 前端代码层面

#### 3.3.1 类型定义修改
**文件**: `frontend/src/types/email.ts`

**修改内容**:
- **保持字段**: `EmailTemplate.subject` 字段保留
- **修改接口**:
  - `SaveOrUpdateTemplateRequest` - 移除subject字段
  - `UpdateTemplateContentRequest` - 保持subject字段

**修改示例**:
```typescript
// 移除基本信息中的subject字段
export interface SaveOrUpdateTemplateRequest {
  template_id?: number;
  name: string;
  type: number;
  status: number;
  account_id: number;
  description?: string;
  // 移除: subject: string;
}

// 保持内容更新中的subject字段
export interface UpdateTemplateContentRequest {
  template_id: number;
  html_content?: string;
  plain_text_content?: string;
  subject?: string; // 保持此字段
  variables?: Record<string, any>;
}
```

#### 3.3.2 服务层修改
**文件**: `frontend/src/services/email.ts`

**修改内容**:
- **保持方法**: 现有方法保持不变
- **无需新增**: 使用现有的updateTemplateContent方法

#### 3.3.3 组件层修改

**文件**: `frontend/src/pages/email/components/TemplateForm.tsx`

**修改内容**:
- **移除**: 基本信息TAB中的主题输入框
- **新增**: 模板内容TAB中的主题输入框
- **修改**: 保存逻辑，通过content接口提交subject
- **新增**: 主题变量验证提示

**修改示例**:
```typescript
// 移除基本信息TAB中的主题输入框
// 在基本信息TAB中删除：
// <Form.Item label="邮件主题" name="subject">...</Form.Item>

// 在模板内容TAB中新增：
<Form.Item 
  label="邮件主题" 
  name="subject"
  rules={[
    { max: 200, message: '邮件主题长度不能超过200字符' },
    { 
      validator: (_, value) => {
        if (value) {
          // 验证主题中的变量是否存在于模板变量中
          const variables = extractVariablesFromText(value);
          const templateVariables = Object.keys(allVariables);
          const invalidVariables = variables.filter(v => !templateVariables.includes(v));
          
          if (invalidVariables.length > 0) {
            return Promise.reject(new Error(`主题中的变量 ${invalidVariables.join(', ')} 未在模板变量中定义`));
          }
        }
        return Promise.resolve();
      }
    }
  ]}
>
  <Input 
    placeholder="请输入邮件主题，支持变量替换如：{{appName}} - {{title}}"
    maxLength={200}
    showCount
  />
</Form.Item>

// 新增：从文本中提取变量的辅助函数
const extractVariablesFromText = (text: string): string[] => {
  const regex = /\{\{([^}]+)\}\}/g;
  const matches = text.match(regex);
  if (!matches) return [];
  
  return matches.map(match => match.slice(2, -2)); // 移除 {{ 和 }}
};

// 修改保存逻辑
const handleSaveContent = useCallback(async () => {
  // ... 现有逻辑 ...
  
  const updateData = {
    template_id: parseInt(id!),
    html_content: htmlContent,
    subject: form.getFieldValue('subject'), // 新增subject字段
    variables: allVariables,
  };

  const response = await emailTemplateService.updateTemplateContent(updateData);
  // ... 继续现有逻辑 ...
}, [mode, id, htmlContent, localVariables, serverVariables, form]);
```

**文件**: `frontend/src/pages/email/TemplateDetail.tsx`

**修改内容**:
- **保持**: 主题显示区域不变
- **新增**: 主题模板预览功能

**修改示例**:
```typescript
// 保持主题显示区域
<Card title="邮件主题" style={{ marginBottom: 16, height: 300 }}>
  <TextArea
    value={template.subject || ''}
    rows={8}
    readOnly
    style={{ resize: 'none', height: '100%' }}
    placeholder="未设置主题"
  />
</Card>
```

#### 3.3.4 编辑器组件修改
**文件**: `frontend/src/pages/email/components/CKEditorEmailTemplate.tsx`

**修改内容**:
- **新增**: 主题输入区域
- **新增**: 主题变量支持
- **新增**: 主题预览功能
- **新增**: 主题变量验证

**新增功能示例**:
```typescript
// 新增主题输入区域
<div className="subject-input-area">
  <Title level={5}>邮件主题</Title>
  <Input
    value={subject}
    onChange={(e) => {
      setSubject(e.target.value);
      // 实时验证主题中的变量
      validateSubjectVariables(e.target.value);
    }}
    placeholder="支持变量替换，如：{{appName}} - {{title}}"
    maxLength={200}
    showCount
  />
  <div className="subject-variables">
    <Text type="secondary">可用变量:</Text>
    {allVariables.map(variable => (
      <Tag 
        key={variable.name}
        onClick={() => insertSubjectVariable(variable.name)}
        style={{ cursor: 'pointer' }}
      >
        {variable.name}
      </Tag>
    ))}
  </div>
  {subjectValidationError && (
    <Alert
      message="主题变量验证失败"
      description={subjectValidationError}
      type="error"
      showIcon
      style={{ marginTop: 8 }}
    />
  )}
</div>

// 新增：主题变量验证
const validateSubjectVariables = (subjectText: string) => {
  const variables = extractVariablesFromText(subjectText);
  const templateVariables = allVariables.map(v => v.name);
  const invalidVariables = variables.filter(v => !templateVariables.includes(v));
  
  if (invalidVariables.length > 0) {
    setSubjectValidationError(`主题中的变量 ${invalidVariables.join(', ')} 未在模板变量中定义`);
  } else {
    setSubjectValidationError('');
  }
};

// 新增：从文本中提取变量的辅助函数
const extractVariablesFromText = (text: string): string[] => {
  const regex = /\{\{([^}]+)\}\}/g;
  const matches = text.match(regex);
  if (!matches) return [];
  
  return matches.map(match => match.slice(2, -2)); // 移除 {{ 和 }}
};
```

### 3.4 API接口修改

#### 3.4.1 修改现有接口
**文件**: `email/docs/email-template-save-update-api.md`

**修改内容**:
- **更新**: 移除基本信息接口中的subject参数
- **保持**: 内容更新接口中的subject参数
- **更新**: 响应示例，移除基本信息中的subject
- **新增**: subject变量验证说明

**修改示例**:
```markdown
## 保存/更新基本信息接口

### 请求参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| name | string | ✅ | min:1, max:100 | 模板名称 |
| type | integer | ✅ | min:1, max:6 | 模板类型 |
| status | integer | ✅ | min:1, max:4 | 模板状态 |
| account_id | integer | ✅ | required | 发件账户ID |
| description | string | ❌ | max:255 | 模板描述 |
| 移除: subject | string | ❌ | min:1, max:200 | 邮件主题 |

## 更新模板内容接口

### 请求参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| template_id | integer | ✅ | required | 模板ID |
| html_content | string | ❌ | max:50000 | HTML内容 |
| plain_text_content | string | ❌ | max:10000 | 纯文本内容 |
| subject | string | ❌ | max:200, 变量验证 | 邮件主题（新增，需验证变量存在性） |
| variables | object | ❌ | max:50 fields | 变量定义 |

### 特殊验证规则
- **subject变量验证**: 主题中使用的变量必须在variables中定义
- **变量格式**: 支持 {{variable_name}} 格式的变量
- **错误提示**: 未定义的变量会返回详细的错误信息
```

#### 3.4.2 无需新增接口
- 使用现有的`/templates/content`接口
- 在请求参数中添加subject字段
- 增强subject的验证逻辑

### 3.5 系统模板修改

#### 3.5.1 系统模板文件
**文件**: 
- `email/sql/insert_account_verification_template.sql`
- `email/sql/insert_account_activation_template.sql`

**修改内容**:
- **保持**: subject字段内容不变
- **无需修改**: 系统模板数据保持不变

## 四、实施策略

### 4.1 渐进式迁移
1. **第一阶段**: 后端修改，移除基本信息中的subject处理，增强content接口的subject验证
2. **第二阶段**: 前端界面调整，将subject移到内容TAB，添加变量验证
3. **第三阶段**: 测试和验证，确保变量验证功能正常
4. **第四阶段**: 部署上线

### 4.2 向后兼容
- **保持**: 现有API接口结构不变
- **修改**: 仅调整字段位置，不改变功能
- **兼容**: 支持新旧两种主题管理方式
- **增强**: 添加subject变量验证功能

### 4.3 数据安全
- **保持**: 现有subject数据不变
- **备份**: 实施前备份模板数据
- **回滚**: 提供回滚机制

## 五、测试计划

### 5.1 单元测试
- 基本信息保存（无subject）测试
- 内容更新（包含subject）测试
- 主题验证功能测试
- **新增**: subject变量验证测试
- **新增**: 变量提取逻辑测试

### 5.2 集成测试
- API接口测试
- 前端组件测试
- 数据库操作测试
- **新增**: subject变量验证集成测试

### 5.3 端到端测试
- 模板创建流程测试
- 主题编辑功能测试
- 邮件发送流程测试
- **新增**: subject变量验证端到端测试

## 六、风险评估

### 6.1 低风险项
- 数据库结构无需修改
- 现有API接口保持兼容
- 现有数据无需迁移

### 6.2 中风险项
- 前端界面重构
- 用户体验调整
- 字段位置变更
- **新增**: subject变量验证逻辑

### 6.3 高风险项
- 主题编辑逻辑变更
- 用户习惯调整
- 功能测试覆盖
- **新增**: 变量验证准确性

## 七、实施时间表

### 7.1 第一周：后端功能开发
- 修改DTO层，移除基本信息中的subject
- 修改应用服务层，调整subject处理逻辑
- **新增**: 实现subject变量验证逻辑
- 更新API文档

### 7.2 第二周：前端界面开发
- 更新类型定义
- 修改表单组件，将subject移到内容TAB
- **新增**: 实现前端subject变量验证
- 更新编辑器组件

### 7.3 第三周：测试和优化
- 执行完整测试
- **新增**: 重点测试subject变量验证功能
- 用户体验优化
- 文档更新

### 7.4 第四周：部署和监控
- 部署上线
- 监控功能使用情况
- 收集用户反馈

## 八、成功标准

### 8.1 功能标准
- 基本信息保存不再包含subject
- 主题编辑移到模板内容TAB
- 通过content接口提交subject
- **新增**: subject中的变量必须存在于模板变量定义中
- 保持现有功能完全兼容

### 8.2 性能标准
- 不影响现有模板操作性能
- 主题编辑响应时间正常
- 内存使用无明显增加
- **新增**: subject变量验证响应时间 < 50ms

### 8.3 用户体验标准
- 用户界面直观易用
- 主题编辑功能完善
- 错误提示清晰明确
- **新增**: 实时显示subject变量验证结果

## 九、具体修改文件清单

### 9.1 后端文件（5个）
1. `email/internal/application/template/dto/template_dto.go` - 移除SaveOrUpdateTemplateRequest中的subject字段
2. `email/internal/application/template/service/template_application_service.go` - 调整subject处理逻辑，新增变量验证
3. `email/internal/domain/template/entity/email_template.go` - 新增subject验证方法，包括变量验证
4. `email/docs/email-template-save-update-api.md` - 更新API文档，添加变量验证说明
5. `email/internal/interfaces/http/handlers/template_handler.go` - 调整handler逻辑

### 9.2 前端文件（4个）
1. `frontend/src/types/email.ts` - 更新类型定义
2. `frontend/src/pages/email/components/TemplateForm.tsx` - 移动subject到内容TAB，新增变量验证
3. `frontend/src/pages/email/components/CKEditorEmailTemplate.tsx` - 新增主题编辑功能，包括变量验证
4. `frontend/src/pages/email/TemplateDetail.tsx` - 保持显示逻辑

### 9.3 新增验证逻辑
- **后端**: 在实体层和应用服务层新增subject变量验证方法
- **前端**: 在表单组件和编辑器组件中新增实时变量验证
- **共用**: 变量提取逻辑与模板内容验证共用底层实现

这个修改范围文档详细说明了将邮件主题从基本信息移动到模板内容TAB的具体实施计划，并增加了subject变量验证功能，确保变量必须是存在的，底层与模板的校验共用逻辑。
