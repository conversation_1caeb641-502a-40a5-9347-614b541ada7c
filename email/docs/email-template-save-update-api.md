# 邮件模板保存/更新接口文档

## 接口概述

邮件模板保存/更新接口提供模板的创建、编辑和版本管理功能。该接口支持HTML和纯文本格式的邮件模板，提供变量替换、响应式设计等功能。

### 基础信息
- **基础路径**: `/api/email/templates`
- **认证方式**: <PERSON><PERSON>
- **响应格式**: JSON
- **字符编码**: UTF-8
- **HTTP方法**: 仅支持POST和GET

## 统一响应结构

所有接口都使用统一的响应结构：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000,
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100
    }
  }
}
```

## 接口详细设计

### 1. 创建模板

#### 接口信息
- **路径**: `POST /api/email/templates/create`
- **描述**: 创建新的邮件模板
- **权限**: 需要租户访问权限

#### 请求参数
```json
{
  "tenant_id": *********,
  "name": "欢迎邮件模板",
  "type": 1,
  "subject": "欢迎加入我们！",
  "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1></body></html>",
  "plain_text_content": "欢迎 {{subscriber.first_name}}！",
  "variables": {
    "subscriber.first_name": {
      "type": "string",
      "description": "订阅者名字",
      "default_value": "用户"
    }
  },
  "thumbnail_url": "https://example.com/thumbnails/welcome.jpg",
  "is_responsive": true
}
```

#### 参数验证规则

##### 基础验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| tenant_id | integer | ✅ | min:1 | 租户ID，用于多租户隔离 |
| name | string | ✅ | min:1, max:100, pattern:^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$ | 模板名称，支持中英文、数字、空格、下划线、连字符 |
| type | integer | ✅ | min:1, max:6 | 模板类型(1:欢迎,2:新闻,3:促销,4:通知,5:自定义,6:自动化) |

##### 内容验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| html_content | string | ❌ | max:50000, XSS过滤 | HTML内容，支持变量替换，自动过滤危险标签 |
| plain_text_content | string | ❌ | max:10000 | 纯文本内容，用于不支持HTML的邮件客户端 |
| thumbnail_url | string | ❌ | url format, max:500, 图片格式验证 | 缩略图URL，支持jpg/png/gif格式 |

##### 变量验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| variables | object | ❌ | max:50 fields, 变量名格式验证 | 变量定义对象 |
| variables.*.type | string | ❌ | enum:string,number,date,boolean,array | 变量类型，影响变量替换逻辑 |
| variables.*.description | string | ❌ | max:200 | 变量描述，用于前端显示 |
| variables.*.default_value | string | ❌ | max:500, 类型匹配验证 | 默认值，必须与变量类型匹配 |

##### 功能验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| is_responsive | boolean | ❌ | default:false | 是否响应式设计，影响邮件渲染 |

##### 创建时的特殊验证
- **名称唯一性验证**: 同一租户下模板名称必须唯一
- **内容完整性验证**: HTML内容和纯文本内容至少提供一个
- **变量一致性验证**: HTML和纯文本内容中的变量必须在variables中定义
- **XSS防护验证**: HTML内容自动过滤script、iframe等危险标签

#### 响应示例
```json
{
  "code": 200,
  "message": "模板创建成功",
  "data": {
    "template_id": *********,
    "tenant_id": *********,
    "name": "欢迎邮件模板",
    "type": 1,
    "subject": "欢迎加入我们！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      }
    },
    "thumbnail_url": "https://example.com/thumbnails/welcome.jpg",
    "is_responsive": true,
    "is_active": true,
    "version": 1,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 2. 更新模板

#### 接口信息
- **路径**: `POST /api/email/templates/update`
- **描述**: 更新模板（创建新版本）
- **权限**: 需要租户访问权限

#### 请求参数
```json
{
  "tenant_id": *********,
  "template_id": *********,
  "name": "更新后的欢迎邮件模板",
  "type": 1,
  "subject": "欢迎加入我们的大家庭！",
  "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1><p>感谢您的加入。</p></body></html>",
  "plain_text_content": "欢迎 {{subscriber.first_name}}！感谢您的加入。",
  "variables": {
    "subscriber.first_name": {
      "type": "string",
      "description": "订阅者名字",
      "default_value": "用户"
    },
    "company.name": {
      "type": "string",
      "description": "公司名称",
      "default_value": "我们公司"
    }
  },
  "is_responsive": true
}
```

#### 参数验证规则

##### 基础验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| tenant_id | integer | ✅ | min:1 | 租户ID，用于多租户隔离 |
| template_id | integer | ✅ | min:1 | 模板ID，用于标识要更新的模板 |
| name | string | ❌ | min:1, max:100, pattern:^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$ | 模板名称，支持中英文、数字、空格、下划线、连字符 |
| type | integer | ❌ | min:1, max:6 | 模板类型(1:欢迎,2:新闻,3:促销,4:通知,5:自定义,6:自动化) |
| subject | string | ❌ | min:1, max:200 | 邮件主题，支持变量替换 |

##### 内容验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| html_content | string | ❌ | max:50000, XSS过滤 | HTML内容，支持变量替换，自动过滤危险标签 |
| plain_text_content | string | ❌ | max:10000 | 纯文本内容，用于不支持HTML的邮件客户端 |

##### 变量验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| variables | object | ❌ | max:50 fields, 变量名格式验证 | 变量定义对象 |
| variables.*.type | string | ❌ | enum:string,number,date,boolean,array | 变量类型，影响变量替换逻辑 |
| variables.*.description | string | ❌ | max:200 | 变量描述，用于前端显示 |
| variables.*.default_value | string | ❌ | max:500, 类型匹配验证 | 默认值，必须与变量类型匹配 |

##### 功能验证参数
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| is_responsive | boolean | ❌ | - | 是否响应式设计，影响邮件渲染 |

##### 更新时的特殊验证
- **模板存在性验证**: 验证template_id对应的模板是否存在且属于当前租户
- **版本冲突验证**: 检查是否有其他用户正在编辑同一模板
- **内容变更验证**: 至少有一个字段发生变更才会创建新版本
- **变量兼容性验证**: 新增变量不能与现有变量冲突，删除变量需确保内容中不再使用
- **发布状态验证**: 如果模板已发布，更新会创建草稿版本

#### 响应示例
```json
{
  "code": 200,
  "message": "模板更新成功",
  "data": {
    "template_id": *********,
    "tenant_id": *********,
    "name": "更新后的欢迎邮件模板",
    "type": 1,
    "subject": "欢迎加入我们的大家庭！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1><p>感谢您的加入。</p></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！感谢您的加入。",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      },
      "company.name": {
        "type": "string",
        "description": "公司名称",
        "default_value": "我们公司"
      }
    },
    "is_responsive": true,
    "is_active": false,
    "version": 2,
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 3. 获取模板详情

#### 接口信息
- **路径**: `POST /api/email/templates/get`
- **描述**: 获取模板详细信息
- **权限**: 需要租户访问权限

#### 请求参数
```json
{
  "tenant_id": *********,
  "template_id": *********
}
```

#### 参数验证规则
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| tenant_id | integer | ✅ | min:1 | 租户ID |
| template_id | integer | ✅ | min:1 | 模板ID |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "template_id": *********,
    "tenant_id": *********,
    "name": "欢迎邮件模板",
    "type": 1,
    "subject": "欢迎加入我们！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      }
    },
    "thumbnail_url": "https://example.com/thumbnails/welcome.jpg",
    "is_responsive": true,
    "is_active": true,
    "version": 1,
    "statistics": {
      "usage_count": 25,
      "last_used": "2024-01-01T12:00:00Z"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 4. 更新模板内容

#### 接口信息
- **路径**: `POST /api/email/templates/content`
- **描述**: 更新模板的HTML内容、纯文本内容和邮件主题
- **权限**: 需要租户访问权限

#### 请求参数
```json
{
  "template_id": *********,
  "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1><p>感谢您的加入。</p></body></html>",
  "plain_text_content": "欢迎 {{subscriber.first_name}}！感谢您的加入。",
  "subject": "欢迎加入我们的大家庭！",
  "is_responsive": true
}
```

#### 参数验证规则
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| template_id | integer | ✅ | min:1 | 模板ID |
| html_content | string | ❌ | max:50000, XSS过滤 | HTML内容，支持变量替换 |
| plain_text_content | string | ❌ | max:10000 | 纯文本内容，用于不支持HTML的邮件客户端 |
| subject | string | ❌ | max:200, 变量验证 | 邮件主题，支持变量替换，需验证变量存在性 |
| is_responsive | boolean | ❌ | - | 是否响应式设计 |

#### 特殊验证规则
- **subject变量验证**: 主题中使用的变量必须在模板变量定义中存在
- **变量格式**: 支持 `{{variable_name}}` 格式的变量
- **错误提示**: 未定义的变量会返回详细的错误信息

#### 响应示例
```json
{
  "code": 200,
  "message": "模板内容更新成功",
  "data": {
    "template_id": *********,
    "tenant_id": *********,
    "name": "欢迎邮件模板",
    "type": 1,
    "subject": "欢迎加入我们的大家庭！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1><p>感谢您的加入。</p></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！感谢您的加入。",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      }
    },
    "is_responsive": true,
    "version": 2,
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 5. 发布模板

#### 接口信息
- **路径**: `POST /api/email/templates/publish`
- **描述**: 发布草稿版本为当前版本
- **权限**: 需要租户访问权限

#### 请求参数
```json
{
  "tenant_id": *********,
  "template_id": *********
}
```

#### 参数验证规则
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| tenant_id | integer | ✅ | min:1 | 租户ID |
| template_id | integer | ✅ | min:1 | 模板ID |

#### 响应示例
```json
{
  "code": 200,
  "message": "模板发布成功",
  "data": {
    "template_id": *********,
    "tenant_id": *********,
    "version": 1,
    "published_at": "2024-01-01T12:00:00Z",
    "is_active": true
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

## 验证逻辑详细说明

### 创建与更新的区分逻辑

#### 1. 创建模板 (Create)
- **触发条件**: 不提供template_id或template_id为0
- **验证重点**: 
  - 名称唯一性检查
  - 内容完整性验证
  - 变量定义完整性
- **业务逻辑**: 创建新模板，版本号从1开始

#### 2. 更新模板 (Update)
- **触发条件**: 提供有效的template_id
- **验证重点**:
  - 模板存在性检查
  - 版本冲突检测
  - 内容变更检测
- **业务逻辑**: 创建新版本，原版本保持不变

### 参数验证层次结构

#### 第一层：基础格式验证
```go
// 基础字段验证
if tenantID <= 0 {
    return errors.New("租户ID无效")
}
if len(name) == 0 || len(name) > 100 {
    return errors.New("模板名称长度必须在1-100字符之间")
}
```

#### 第二层：业务规则验证
```go
// 名称唯一性验证（仅创建时）
if isCreate && templateExists(name, tenantID) {
    return errors.New("模板名称已存在")
}

// 模板存在性验证（仅更新时）
if !isCreate && !templateExists(templateID, tenantID) {
    return errors.New("模板不存在")
}
```

#### 第三层：内容安全验证
```go
// XSS防护验证
if htmlContent != "" {
    htmlContent = sanitizeHTML(htmlContent)
}

// 变量一致性验证
if !validateVariables(htmlContent, plainTextContent, variables) {
    return errors.New("内容中的变量未在variables中定义")
}
```

#### 第四层：变量定义验证
```go
// 变量名格式验证
for varName := range variables {
    if !isValidVariableName(varName) {
        return errors.New("变量名格式无效")
    }
}

// 变量类型匹配验证
for varName, varDef := range variables {
    if !isValidVariableType(varDef.Type, varDef.DefaultValue) {
        return errors.New("变量类型与默认值不匹配")
    }
}
```

### 变量验证详细规则

#### 变量名格式规范
- **格式**: `[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*`
- **示例**: `user.name`, `company.address`, `order.total`
- **限制**: 最多3级嵌套，每级最多20字符

#### 变量类型验证
| 类型 | 默认值要求 | 示例 |
|------|------------|------|
| string | 字符串或空 | `"用户"`, `""` |
| number | 数字字符串 | `"123"`, `"3.14"` |
| date | ISO日期格式 | `"2024-01-01"` |
| boolean | true/false字符串 | `"true"`, `"false"` |
| array | JSON数组字符串 | `"[\"a\",\"b\"]"` |

#### 变量使用验证
```go
// 提取内容中的变量
variablesInContent := extractVariables(htmlContent + plainTextContent)

// 验证所有变量都已定义
for varName := range variablesInContent {
    if _, exists := variables[varName]; !exists {
        return errors.New("变量 " + varName + " 未定义")
    }
}
```

### 内容安全验证

#### HTML内容过滤规则
- **移除标签**: `<script>`, `<iframe>`, `<object>`, `<embed>`
- **移除属性**: `onclick`, `onload`, `javascript:`, `data:`
- **保留标签**: `<div>`, `<p>`, `<span>`, `<h1-h6>`, `<table>`, `<img>`
- **保留属性**: `class`, `style`, `src`, `alt`, `width`, `height`

#### 图片URL验证
- **协议限制**: 仅允许 `http://`, `https://`
- **格式限制**: 仅允许 `.jpg`, `.jpeg`, `.png`, `.gif`
- **大小限制**: URL长度不超过500字符

### 版本管理验证

#### 版本冲突检测
```go
// 检查是否有其他用户正在编辑
if isTemplateBeingEdited(templateID) {
    return errors.New("模板正在被其他用户编辑")
}

// 检查版本号是否匹配
if currentVersion != expectedVersion {
    return errors.New("模板版本已过期，请刷新后重试")
}
```

#### 内容变更检测
```go
// 比较新旧内容
if !hasContentChanged(oldTemplate, newTemplate) {
    return errors.New("内容未发生变更，无需更新")
}
```

## 错误码定义

### 业务错误码
| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 200 | 成功 | 200 |
| 400 | 请求参数错误 | 400 |
| 401 | 未授权 | 401 |
| 403 | 禁止访问 | 403 |
| 404 | 资源不存在 | 404 |
| 409 | 资源冲突 | 409 |
| 422 | 验证失败 | 422 |
| 429 | 请求过于频繁 | 429 |
| 500 | 内部服务器错误 | 500 |

### 具体错误码
| 错误码 | 说明 | 错误消息 |
|--------|------|----------|
| 1001 | 租户ID无效 | 租户ID无效 |
| 1002 | 模板名称已存在 | 模板名称已存在 |
| 1003 | 模板不存在 | 模板不存在 |
| 1004 | 模板类型无效 | 模板类型无效 |
| 1005 | 变量定义无效 | 变量定义无效 |
| 1006 | HTML内容格式错误 | HTML内容格式错误 |
| 1007 | 模板版本冲突 | 模板版本冲突 |
| 1008 | 模板正在使用中 | 模板正在使用中，无法删除 |
| 1009 | 权限不足 | 权限不足 |
| 1010 | 系统错误 | 系统错误，请稍后重试 |

## 错误响应示例

### 参数验证错误
```json
{
  "code": 422,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "name",
        "message": "模板名称不能为空"
      },
      {
        "field": "subject",
        "message": "邮件主题长度必须在1-200字符之间"
      }
    ]
  },
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 业务逻辑错误
```json
{
  "code": 1002,
  "message": "模板名称已存在",
  "data": null,
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

### 系统错误
```json
{
  "code": 500,
  "message": "系统错误，请稍后重试",
  "data": null,
  "meta": {
    "request_id": "req_*********",
    "timestamp": 1640995200000
  }
}
```

## 使用示例

### 创建模板
```bash
curl -X POST http://localhost:8080/api/email/templates/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "tenant_id": *********,
    "name": "欢迎邮件模板",
    "type": 1,
    "subject": "欢迎加入我们！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      }
    },
    "is_responsive": true
  }'
```

### 更新模板
```bash
curl -X POST http://localhost:8080/api/email/templates/update \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "tenant_id": *********,
    "template_id": *********,
    "name": "更新后的欢迎邮件模板",
    "subject": "欢迎加入我们的大家庭！",
    "html_content": "<html><body><h1>欢迎 {{subscriber.first_name}}！</h1><p>感谢您的加入。</p></body></html>",
    "plain_text_content": "欢迎 {{subscriber.first_name}}！感谢您的加入。",
    "variables": {
      "subscriber.first_name": {
        "type": "string",
        "description": "订阅者名字",
        "default_value": "用户"
      },
      "company.name": {
        "type": "string",
        "description": "公司名称",
        "default_value": "我们公司"
      }
    },
    "is_responsive": true
  }'
```

## 验证流程图

```
开始
  ↓
接收请求参数
  ↓
第一层：基础格式验证
  ├─ 租户ID验证
  ├─ 模板名称格式验证
  ├─ 模板类型验证
  └─ 内容长度验证
  ↓
第二层：业务规则验证
  ├─ 创建模式：名称唯一性检查
  └─ 更新模式：模板存在性检查
  ↓
第三层：内容安全验证
  ├─ HTML内容XSS过滤
  ├─ 变量一致性验证
  └─ 图片URL安全验证
  ↓
第四层：变量定义验证
  ├─ 变量名格式验证
  ├─ 变量类型匹配验证
  └─ 变量使用完整性验证
  ↓
第五层：版本管理验证
  ├─ 版本冲突检测
  └─ 内容变更检测
  ↓
保存/更新操作
  ↓
返回结果
```

## 验证示例

### 创建模板验证示例

#### 请求参数
```json
{
  "tenant_id": *********,
  "name": "欢迎邮件模板",
  "type": 1,
  "subject": "欢迎 {{user.name}} 加入我们！",
  "html_content": "<h1>欢迎 {{user.name}}！</h1><p>您的会员等级：{{user.level}}</p>",
  "plain_text_content": "欢迎 {{user.name}}！您的会员等级：{{user.level}}",
  "variables": {
    "user.name": {
      "type": "string",
      "description": "用户姓名",
      "default_value": "用户"
    },
    "user.level": {
      "type": "string",
      "description": "会员等级",
      "default_value": "普通会员"
    }
  }
}
```

#### 验证过程
1. **基础验证**: ✅ 所有必填字段完整
2. **名称唯一性**: ✅ 租户下无重名模板
3. **内容完整性**: ✅ HTML和纯文本内容都提供
4. **变量一致性**: ✅ 内容中的变量都在variables中定义
5. **XSS过滤**: ✅ HTML内容安全
6. **变量格式**: ✅ 变量名符合规范
7. **类型匹配**: ✅ 变量类型与默认值匹配

### 更新模板验证示例

#### 请求参数
```json
{
  "tenant_id": *********,
  "template_id": *********,
  "name": "更新后的欢迎邮件模板",
  "subject": "欢迎 {{user.name}} 加入我们的大家庭！",
  "html_content": "<h1>欢迎 {{user.name}}！</h1><p>您的会员等级：{{user.level}}</p><p>注册时间：{{user.register_date}}</p>",
  "variables": {
    "user.name": {
      "type": "string",
      "description": "用户姓名",
      "default_value": "用户"
    },
    "user.level": {
      "type": "string",
      "description": "会员等级",
      "default_value": "普通会员"
    },
    "user.register_date": {
      "type": "date",
      "description": "注册日期",
      "default_value": "2024-01-01"
    }
  }
}
```

#### 验证过程
1. **模板存在性**: ✅ 模板存在且属于当前租户
2. **版本冲突**: ✅ 无其他用户正在编辑
3. **内容变更**: ✅ 新增了register_date变量
4. **变量兼容性**: ✅ 新增变量不影响现有内容
5. **变量格式**: ✅ 新增变量符合命名规范
6. **类型匹配**: ✅ 日期类型与默认值匹配

## 注意事项

1. **版本管理**: 更新模板时会创建新版本，原版本保持不变
2. **变量验证**: 系统会验证HTML和纯文本内容中的变量是否在variables中定义
3. **内容安全**: HTML内容会进行安全过滤，移除潜在的危险标签和属性
4. **性能考虑**: 大文件上传建议使用分块上传
5. **缓存策略**: 模板内容会被缓存以提高性能
6. **审计日志**: 所有模板操作都会记录审计日志
7. **并发控制**: 同一模板同时只能有一个用户编辑
8. **回滚机制**: 支持版本回滚到历史版本

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2024-01-01 | 初始版本 |
| 1.1.0 | 2024-01-15 | 添加变量验证功能 |
| 1.2.0 | 2024-02-01 | 添加响应式设计支持 |
| 1.3.0 | 2024-02-15 | 优化错误处理和响应格式 | 