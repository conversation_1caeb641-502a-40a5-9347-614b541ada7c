# 邮件队列发送方案设计

## 1. 核心设计原则

基于对[email_messages](file:///Users/<USER>/personal/platforms/email/platforms-email.sql#L76-L107)表结构和相关代码的分析，设计一个可靠的邮件队列发送方案，确保：
- 避免重复发送
- 100%投递可靠性
- 正确处理锁竞争
- 支持失败重试机制

## 2. 邮件状态流转设计

根据现有代码分析，邮件有以下状态：
- `pending`：待发送（初始状态）
- `sending`：发送中
- `sent`：已发送（成功）
- `failed`：发送失败
- `canceled`：已取消

状态流转图：
```
[创建邮件]
    ↓
pending ←─┐
    ↓     │ (重试)
sending   │
    ↓     │
sent  failed (达到重试上限)
      ↓
   canceled
```

## 3. 队列处理流程

### 3.1 邮件创建流程
1. 应用服务创建邮件实体，初始状态为`pending`
2. 邮件保存到[email_messages](file:///Users/<USER>/personal/platforms/email/platforms-email.sql#L76-L107)表中

### 3.2 邮件处理流程
1. 定时任务定期扫描状态为`pending`的邮件
2. 获取待处理邮件列表（限制批次大小）
3. 使用工作池并发处理邮件
4. 每封邮件处理时使用数据库行级锁确保唯一性

### 3.3 邮件发送流程
1. 获取邮件锁，防止重复处理
2. 更新状态为`sending`
3. 获取邮件账户信息
4. 调用邮件发送服务发送邮件
5. 根据发送结果：
   - 成功：更新状态为`sent`
   - 失败且重试次数未达上限：增加重试次数，重置为`pending`
   - 失败且重试次数已达上限：更新状态为`failed`

## 4. 并发控制方案

### 4.1 数据库行级锁
使用`SELECT ... FOR UPDATE`语句获取邮件记录的排他锁，确保同一时间只有一个工作者能处理特定邮件。

### 4.2 状态检查机制
在获取锁后再次检查邮件状态，确保没有被其他进程处理。

### 4.3 工作池设计
使用固定大小的工作池并发处理邮件，避免系统过载。

## 5. 失败重试机制

### 5.1 重试策略
- 每封邮件最多重试3次（根据`max_retries`字段）
- 每次重试增加重试计数器
- 重试次数未达上限时，重置状态为`pending`等待下次处理

### 5.2 重试时机控制
- 指数退避策略：第n次重试等待2^(n-1)分钟后再尝试
- 可配置重试间隔时间，避免网络临时故障导致发送失败
- 对于特定类型的错误（如邮箱不存在），可选择不重试

### 5.3 重试时的并发处理
- 重试时同样使用数据库行级锁确保唯一性
- 增加重试计数器前先获取锁，防止并发更新导致计数错误
- 重试时检查是否达到最大重试次数，避免无限重试

### 5.4 错误处理
- 记录详细的错误信息到`error_msg`字段
- 区分临时性错误和永久性错误（可扩展）
- 对于临时性错误采用重试机制
- 对于永久性错误直接标记为失败

## 6. 定时任务设计

### 6.1 扫描策略
- 固定间隔扫描（如每30秒）
- 批量获取待处理邮件（如每次100封）
- 支持动态调整扫描间隔和批次大小

### 6.2 分布式部署
- 支持多个工作者实例同时运行
- 通过数据库锁机制确保同一封邮件不会被多个实例重复处理

## 7. 监控和告警

### 7.1 关键指标
- 待发送队列长度
- 发送成功率
- 失败率
- 平均处理延迟

### 7.2 日志记录
- 详细记录每封邮件的处理过程
- 记录错误和异常情况
- 记录重试情况

## 8. 性能优化建议

### 8.1 数据库索引优化
确保以下字段有合适的索引：
- [status](file:///Users/<USER>/personal/platforms/frontend/src/services/user.ts#L9-L9)字段（用于快速查找待发送邮件）
- [tenant_id](file:///Users/<USER>/personal/platforms/frontend/src/services/user.ts#L26-L26)和[status](file:///Users/<USER>/personal/platforms/frontend/src/services/user.ts#L9-L9)的复合索引
- [created_at](file:///Users/<USER>/personal/platforms/frontend/src/services/user.ts#L15-L15)字段（用于时间范围查询）

### 8.2 批量处理
- 批量获取待处理邮件
- 批量更新状态（可选）

### 8.3 连接池管理
- 合理配置数据库连接池
- 避免连接泄露

## 9. 容错和恢复机制

### 9.1 崩溃恢复
- 工作者重启后自动继续处理
- `sending`状态的邮件需要特殊处理（可能需要重置为`pending`）

### 9.2 死锁处理
- 设置合理的锁超时时间
- 检测和处理死锁情况

## 10. 扩展性考虑

### 10.1 水平扩展
- 支持部署多个工作者实例
- 通过数据库锁协调多个实例

### 10.2 优先级处理
- 可以根据[priority](file:///Users/<USER>/personal/platforms/frontend/src/services/verification.ts#L31-L31)字段优先处理高优先级邮件

### 10.3 延迟发送
- 利用[scheduled_at](file:///Users/<USER>/personal/platforms/users/internal/domain/verification/entity/verification.go#L27-L27)字段支持定时发送功能

这套方案通过数据库行级锁、状态管理和重试机制，能够确保邮件在高并发环境下也能100%可靠投递，同时避免重复发送问题。