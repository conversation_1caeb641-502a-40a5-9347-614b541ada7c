跨境电商邮件营销系统设计方案（v1.0）

版本：v1.0  
日期：2025-08-08  
作者：AI Research Team

目录
- 1. 背景与目标
- 2. 竞品调研与差异化策略
- 3. KPI/SLI 指标体系
- 4. 功能范围与优先级（MVP → Plus）
- 5. 领域模型与数据设计（含核心 SQL）
- 6. 系统架构与技术选型（贴合现有 Go/DDD/微服务规范）
- 7. API 设计规范（仅 GET/POST，统一响应）
- 8. 合规与送达率（Gmail/Yahoo 2024、RFC、FBL）
- 9. 发送策略与信誉治理
- 10. 自动化工作流与智能编排
- 11. AI 能力设计（文案/视觉/预测/洞察）
- 12. 可观测性与运维（OTel、日志、指标、告警）
- 13. 安全与隐私（GDPR/CCPA/数据最小化）
- 14. 交付路径与实施计划（里程碑）
- 15. 风险清单与预案
- 16. 参考资料

---

1. 背景与目标

目标用户：
- 中国独立站卖家、亚马逊/速卖通多平台卖家、品牌出海 DTC 团队

核心场景：
- 企业邮箱运营（客服、供应商沟通、对账）
- EDM 营销（促销、上新、弃购挽回、会员关怀）

产品目标（面向 12 个月）：
- 送达率（Gmail/Yahoo/QQ/163）≥ 99%
- 打开率较基线提升 ≥ 30%（AI 个性化）
- 人效提升 ≥ 50%（自动化 + 中文智能客服）

设计取向：
- 与现有仓库保持一致：Go + 微服务 + DDD + Clean Architecture；统一响应结构；OpenTelemetry 可观测；GORM 查询规范；依赖注入不做 nil 检查；HTTP 只提供 GET/POST，不使用 path 参数。
- 发送链路不强依赖 MQ：偏向使用数据库 `email_messages` 表实现“发送请求与执行隔离”（用户偏好）。

---

2. 竞品调研与差异化策略

对标产品与关键特性：
- Klaviyo：深度电商集成（Shopify/BigCommerce）、行为触发、分群与预测分析、个性化强。
- Mailchimp：模板生态、营销自动化、A/B 测试、渠道整合（邮件/广告）。
- Omnisend：电邮 + SMS + Push，多渠道自动化与可视化编排、场景模板丰富。
- Brevo（Sendinblue）：事务类与营销邮件、CRM 与可视化编辑器、性价比高。
- SendGrid/AWS SES：高可用发送基础设施、信誉与投递治理能力、反馈回路与事件追踪。

差异化策略：
- 极致合规与送达工程：内置 Gmail/Yahoo 2024 大发件人规范校验、自动生成 DNS 修复脚本、域名/IP 预热向导、RFC 8058 一键退订、FBL 自动对接。
- 电商深集成与实时商品：Shopify/WooCommerce/跨境平台订单与会员数据实时入湖，动态商品模块秒级渲染。
- 中文卖家友好型 AI：中文提示词 → 多语文案与本地化、文化敏感词审校、非技术用户的 DNS 截图 AI 诊断。
- 无队列轻依赖：默认数据库型队列（email_messages）满足成本敏感和简化运维；可选 MQ 插件平滑升级。

---

3. KPI/SLI 指标体系

业务 KPI：
- 投递成功率 ≥ 99%，垃圾箱率 ≤ 0.4%
- 打开率提升 ≥ 30%，点击率提升 ≥ 20%
- 退订率 ≤ 0.2%，投诉率 ≤ 0.1%
- 自动化触发占比 ≥ 60%，人均每日手动操作 ≤ 10 分钟

SLI/SLO（可观测性对齐 OTel）：
- 发送链路 P95 延迟 < 300ms（入队）
- API 错误率（5xx）< 0.5%
- 模板渲染 P95 < 120ms
- 事件落库丢失率 < 0.01%

---

4. 功能范围与优先级

MVP（0-3 个月）：
- 账户&域名：多域名/子域名绑定；SPF/DKIM/DMARC 向导；AI 域名健康扫描（基础版）
- 联系人：CSV/Excel 导入、自动去重、黑名单/退订即刻生效、标签/分组（地域/订单/RFM）
- 模板&内容：可视化编辑器、响应式预览、动态商品模块（Shopify/Woo）、多语言占位符
- 发送：SMTP/REST API/Webhook，数据库队列（email_messages），限速与预热策略
- 报告：打开/点击/退订/投诉实时看板，UTM/GA4 打通
- 合规：List-Unsubscribe、One-Click（RFC 2369/8058）、GDPR/CCPA 一键退订页

Plus（4-9 个月）：
- 自动化编排：触发器（注册/下单/发货/弃购/评价）、延迟、条件、A/B 测试
- AI：文案助手（多语）、视觉生成（头图/配色）、最佳发送时间预测、流失预测、投递洞察
- 集成：亚马逊/速卖通/Shopify Flow、钉钉/飞书机器人通知
- 高级发送：AI 弹性通道（信誉分驱动 IP/域名切换）、ISP 白名单申请指引、域级与用户级配额

---

5. 领域模型与数据设计（核心 SQL）

说明：以 MySQL 为主（现网使用 MySQL），严控外键（由应用维护一致性）。重要表含必要索引与枚举约束。以下为关键表（片段）：

```sql
-- 1) 域名与身份认证
CREATE TABLE domains (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  app_id BIGINT NULL,
  domain VARCHAR(255) NOT NULL UNIQUE,
  dkim_selector VARCHAR(64) NULL,
  spf_record TEXT NULL,
  dkim_public_key TEXT NULL,
  dmarc_policy VARCHAR(32) NULL,
  health_score TINYINT NOT NULL DEFAULT 100,
  is_active TINYINT NOT NULL DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant (tenant_id)
);

-- 2) 发件身份（From 地址/别名）
CREATE TABLE senders (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  domain_id BIGINT NOT NULL,
  from_name VARCHAR(128) NOT NULL,
  from_email VARCHAR(255) NOT NULL,
  reply_to VARCHAR(255) NULL,
  status VARCHAR(16) NOT NULL DEFAULT 'active', -- active|paused|blocked
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_from (tenant_id, from_email),
  INDEX idx_domain (domain_id)
);

-- 3) 联系人与分组
CREATE TABLE contacts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  email VARCHAR(255) NOT NULL,
  locale VARCHAR(16) NULL,
  country VARCHAR(64) NULL,
  tags JSON NULL,
  rfm_segment VARCHAR(32) NULL,
  status VARCHAR(16) NOT NULL DEFAULT 'subscribed', -- subscribed|unsubscribed|bounced|complained|blacklisted
  properties JSON NULL, -- 扩展属性
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_email (tenant_id, email),
  INDEX idx_status (status)
);

CREATE TABLE segments (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  name VARCHAR(128) NOT NULL,
  definition JSON NOT NULL, -- 条件树/DSL
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_name (tenant_id, name)
);

-- 4) 模板与素材
CREATE TABLE templates (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  code VARCHAR(64) NOT NULL,
  name VARCHAR(128) NOT NULL,
  locale VARCHAR(16) NULL,
  subject VARCHAR(255) NOT NULL,
  html MEDIUMTEXT NOT NULL,
  design JSON NULL, -- 拖拽编辑器结构
  is_system TINYINT NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_code (tenant_id, code)
);

-- 5) 活动（Campaign）
CREATE TABLE campaigns (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  name VARCHAR(128) NOT NULL,
  template_id BIGINT NOT NULL,
  sender_id BIGINT NOT NULL,
  segment_id BIGINT NULL,
  schedule_time DATETIME NULL,
  status VARCHAR(16) NOT NULL DEFAULT 'draft', -- draft|scheduled|sending|paused|completed|failed
  a_b_test JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_status (tenant_id, status)
);

-- 6) 数据库队列表（用户偏好：替代 MQ）
CREATE TABLE email_messages (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  app_id BIGINT NULL,
  campaign_id BIGINT NULL,
  automation_id BIGINT NULL,
  sender_id BIGINT NOT NULL,
  contact_id BIGINT NOT NULL,
  to_email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  html MEDIUMTEXT NOT NULL,
  headers JSON NULL,
  list_unsubscribe VARCHAR(512) NULL, -- <mailto:..>, <https://..>
  send_after DATETIME NULL,
  retry_count INT NOT NULL DEFAULT 0,
  max_retry INT NOT NULL DEFAULT 5,
  priority TINYINT NOT NULL DEFAULT 5, -- 1 高 9 低
  status VARCHAR(16) NOT NULL DEFAULT 'queued', -- queued|processing|sent|failed|skipped
  error TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_status_priority (status, priority, send_after),
  INDEX idx_tenant_status (tenant_id, status)
);

-- 7) 事件与合规（打开/点击/退订/投诉/退信）
CREATE TABLE email_events (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  message_id BIGINT NOT NULL,
  event_type VARCHAR(16) NOT NULL, -- delivered|open|click|bounce|complaint|unsubscribe
  event_time DATETIME NOT NULL,
  meta JSON NULL,
  INDEX idx_msg_event (message_id, event_type, event_time),
  INDEX idx_tenant_eventtime (tenant_id, event_time)
);

CREATE TABLE suppressions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  email VARCHAR(255) NOT NULL,
  reason VARCHAR(16) NOT NULL, -- unsubscribe|bounce|complaint|manual
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_email (tenant_id, email),
  INDEX idx_reason (reason)
);

-- 8) 自动化
CREATE TABLE automations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  name VARCHAR(128) NOT NULL,
  definition JSON NOT NULL, -- 编排 DSL
  status VARCHAR(16) NOT NULL DEFAULT 'active', -- active|paused
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

数据策略：
- 严禁 GORM 自动预加载，按业务手动批量查询，避免 N+1（与仓库规范一致）。
- 发送与事件写入采用幂等键（如 message dedup key），保障重复请求安全。
- 全量审计：日志与事件数据分层（操作日志→MySQL，行为事件→MySQL/ClickHouse 按量级扩展）。

---

6. 系统架构与技术选型

架构风格：
- 微服务 + DDD + Clean Architecture；按领域分服务：身份/域名、联系人、模板、活动、发送、自动化、事件/分析、合规、集成。
- 代码组织遵循：`cmd/`、`internal/`、`pkg/`、`api/`、`configs/`、`test/`。

主要服务：
- Domain Service（域名与认证）：DNS 校验、SPF/DKIM/DMARC 校验、健康评分。
- Contact Service：导入/去重/标签/分群/抑制清单。
- Template Service：可视化模板、设计结构、动态商品渲染。
- Campaign Service：活动管理、A/B、排期。
- Sending Service：数据库队列拉取 → 速率控制 → MTA/SMTP/ESP 调用；回执解析。
- Automation Service：编排执行引擎（触发/定时/条件/A/B）。
- Event Service：打开/点击/退订/投诉/退信收集与聚合。
- Analytics Service：指标聚合、仪表盘、AI 洞察。
- Integration Service：Shopify/Woo/亚马逊/速卖通连接器；GA4/UTM。

技术选型（贴合现有仓库与规范）：
- 语言：Go（接口驱动 + 构造函数依赖注入，禁止运行期 nil 检查）。
- 传输：HTTP（仅 GET/POST，不使用 path 参数）；内部 gRPC 可选。
- 存储：MySQL（核心）、Redis（缓存/分布式锁/速率限流令牌桶）。
- 队列：默认 MySQL 表 `email_messages` 轮询/抢占消费；可选 MQ 插件（Kafka/RabbitMQ）适配器。
- 可观测：OpenTelemetry（Trace/Metrics/Logs），TraceID 注入日志，导出至 OTel Collector/Jaeger/Prometheus。
- 配置：Nacos（已有能力），分环境灰度发布。

性能与扩展：
- 发送速率调度器：租户/域名/ISP 维度令牌桶；优先级与速率自适应。
- 无锁或低锁批处理：队列批量拉取、分片并行；失败重试指数退避。

---

7. API 设计规范（仅 GET/POST，统一响应）

统一响应（与仓库规范一致）：

```go
type Response struct {
  Code    int         `json:"code"`
  Message string      `json:"message"`
  Data    interface{} `json:"data,omitempty"`
  Meta    *Meta       `json:"meta,omitempty"`
}

type Meta struct {
  RequestID  string                 `json:"request_id,omitempty"`
  Timestamp  int64                  `json:"timestamp"`
  Pagination *Pagination            `json:"pagination,omitempty"`
  Extra      map[string]interface{} `json:"extra,omitempty"`
}
```

错误处理：
- 不暴露系统错误；日志记录详细错误，响应返回友好消息与业务码。

接口约束：
- 仅 GET/POST；无 path 参数。所有参数放入 body（POST）或 query（GET）。

接口示例（片段）：

```http
POST /api/domain.bind
{"domain":"example.com"}

POST /api/domain.dns_check
{"domain":"example.com"}

POST /api/template.create
{"code":"welcome_cn","name":"欢迎模板","subject":"欢迎加入","html":"..."}

POST /api/campaign.schedule
{"campaign_id":123,"schedule_time":"2025-08-10T10:00:00Z"}

POST /api/sending.enqueue
{"campaign_id":123}

GET  /api/report.overview?from=2025-08-01&to=2025-08-08
```

---

8. 合规与送达率（关键要求）

大发件人（2024+）：
- Gmail/Yahoo：发件域强制 SPF、DKIM、DMARC；一键退订（RFC 8058）；列表退订（RFC 2369）；投诉率极低；TLS 必须；对齐身份与品牌一致性。

协议与头部：
- List-Unsubscribe、List-Unsubscribe-Post: "One-Click"；ARC 可选；From/Reply-To 一致性。

FBL（反馈回路）：
- 与主流 ISP 对接（支持 AWS/SparkPost/SendGrid 回调也可统一落库）。投诉即刻加入 suppressions 并停止后续发送。

追踪与隐私：
- 打开（像素）与点击（重定向）可配置；遵守 GDPR/CCPA，提供数据导出与删除。

---

9. 发送策略与信誉治理

预热与配额：
- 新域/新 IP 低速预热，逐日提升；基于域与 ISP 建立独立令牌桶。

速率与退避：
- 动态调速（投递失败/暂退/延迟上升 → 自动降速）；指数退避重试，最大尝试次数按租户级策略。

抑制与灰度：
- 垃圾投递信号（硬退/投诉/高退订）触发模板/主题/受众灰度与暂停策略。

参与度过滤：
- 低活跃人群（长期未打开）自动降权或停发；AI 推荐“重新唤醒”方案。

---

10. 自动化工作流与智能编排

触发器：
- 注册、下单、发货、弃购、评价、生日/纪念日、RFM 变更、首次购买/二次购买。

节点：
- 延迟、条件（属性/行为/库存/价格）、A/B、发送、Webhook、分流、结束。

DSL（示例）：

```json
{
  "triggers":[{"type":"cart_abandon","threshold_min":30}],
  "flow":[
    {"type":"delay","minutes":30},
    {"type":"condition","expr":"contact.rfm_segment in ['H','M']"},
    {"type":"send","template_code":"abandon_cn","channel":"email"},
    {"type":"ab_test","variants":[{"subject":"A"},{"subject":"B"}]}
  ]
}
```

执行器：
- 事件驱动 + 定时补偿；幂等执行；状态持久化与重放能力。

---

11. AI 能力设计

文案助手（多语）：
- 输入中文短语/商品信息/节日主题 → 生成 5 套多语标题+正文；术语库与敏感词库校验；基调（品牌/地区）参数化。

视觉生成：
- 商品图 → 背景/配色/版式候选头图；自适应移动端切图；风格（极简/节日/科技）。

最佳发送时间（BTS）：
- 依据个人历史打开时间分布 + 全局先验；在线推断每人日粒度 30 分钟窗口；与配额冲突时智能排队。

流失预测：
- RFM + 行为序列特征；二分类（流失/非流失）；触发挽回路径（激励/新品/内容）。

投递洞察：
- 模板层异常（异常高退订/投诉/硬退）自动标注；建议优化主题/频率/人群。

MLOps：
- 训练离线（数据脱敏/分区抽样）；推理在线（模型服务化）；指标回灌（AUC、Lift、增益）。

---

12. 可观测性与运维

Tracing：
- 入队、渲染、发送、回执、事件入库全链路埋点，跨服务传播 context 与 TraceID。

Metrics：
- 关键指标：入队速率、发送速率、投递成功率、退信/投诉/退订率、渲染耗时、事件积压、队列滞留时长、API P95/P99 等。

Logging：
- JSON 结构化，注入 request_id/trace_id/tenant_id；错误必须带上下文与封装（fmt.Errorf("ctx: %w", err)）。

告警：
- 5xx、速率异常、投诉突增、退信飙升、域名健康度下降触发报警；看板（Prometheus/Grafana）。

---

13. 安全与隐私

权限：
- RBAC（租户/角色/资源）；敏感操作审计日志。

隐私：
- PII 加密存储（邮箱 hash 索引 + 明文加密分层）；最小化采集；数据导出/删除（GDPR/CCPA）。

传输与存储：
- 全链路 TLS；密钥托管；配置密文（Nacos 加密字段）。

---

14. 交付路径与实施计划

阶段一（0-6 周，MVP 核心）：
- 域名/发送/模板/联系人/报告最小闭环；数据库队列发送；合规头部与退订完成；GA4/UTM 接入。

阶段二（7-14 周）：
- 自动化编排、AI 文案与基础视觉、BTS 预测（beta）、事件分析面板 v1。

阶段三（15-24 周）：
- AI 洞察与流失预测、连接器生态扩展、AI 弹性通道、白名单与预热向导可视化。

迁移与集成：
- 复用现有 `email/` 子项目结构与迁移目录；遵循 GORM 查询规范与统一响应；中间件注入用户上下文，禁止处理器内随意注入。

---

15. 风险清单与预案

送达率不达标：
- 预案：加严预热策略、收紧受众过滤、模板/主题灰度、域名分池；FBL 联动与快速抑制。

合规风险：
- 预案：启用强制 One-Click、投诉/退订 T+0 生效；法律评审与区域策略。

性能瓶颈：
- 预案：批处理与分片、热点索引、读写隔离、事件落仓可切至 ClickHouse；发送通道水平扩展。

团队交付：
- 预案：明确 DOD 与质量门禁（lint/test/cover/bench/trace）；蓝绿/灰发布与回滚脚本。

---

16. 参考资料（精选）

- Gmail Bulk Sender/Deliverability（2024+）
- Yahoo Bulk Sender Requirements（2024+）
- RFC 2369 List-Unsubscribe
- RFC 8058 One-Click Unsubscribe
- Google Postmaster Tools
- SendGrid Deliverability Best Practices
- AWS SES Warm-up Guidance
- Shopify/WooCommerce API 文档

（研发实现时在代码注释与 README 中补充具体链接与版本）

---

附录 A：请求/响应示例（统一响应）

```http
POST /api/sending.enqueue
Content-Type: application/json

{"campaign_id":123}

HTTP/1.1 200 OK
{"code":0,"message":"OK","data":{"enqueued": 1542},"meta":{"timestamp": 1733700000}}
```

附录 B：发送工作器（数据库队列）伪代码

```text
loop:
  now = current_time()
  batch = SELECT * FROM email_messages
          WHERE status='queued' AND send_after <= now
          ORDER BY priority ASC, id ASC
          LIMIT N FOR UPDATE SKIP LOCKED;
  mark batch -> processing
  parallel for msg in batch:
    render template -> html
    build headers (List-Unsubscribe/One-Click)
    send via SMTP/ESP with per-domain token bucket
    on success: status -> sent; event(delivered)
    on temp-fail: retry with backoff; status -> queued
    on perm-fail: status -> failed; add suppression if bounce
```

附录 C：GORM 查询规范要点（执行层）

- 禁止 Preload 自动关联；批量查询关联 ID 再集中查询并组装。
- 上下文传递 `context.Context`；显式错误处理并封装 `fmt.Errorf("ctx: %w", err)`。
- 依赖由构造函数注入，禁止运行期 nil 检查。


